import logging
import logging.handlers


def log_setting(log_file='', log_level=logging.DEBUG, backup_cnt=5, is_init=False):
    logger = logging.getLogger()
    handler = logging.StreamHandler()
    formatter = logging.Formatter(
        "%(asctime)s %(levelname)s %(filename)s:%(lineno)s %(message)s", "%Y-%m-%d %H:%M:%S")
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    if log_file:
        handler = logging.handlers.TimedRotatingFileHandler(
            filename=log_file,
            when="midnight",
            backupCount=backup_cnt)
        handler.setFormatter(formatter)
        if is_init:
            logger.handlers = []
        logger.addHandler(handler)
    logger.setLevel(log_level)
    return logger
