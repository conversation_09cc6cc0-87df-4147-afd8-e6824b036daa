def remove_properties_default(k, v):
    if k == "properties":
        propertiesKV = {}
        for ppKey, ppV in v.items():
            if "type" not in ppV:
                ppV["type"] = ppV["anyOf"][0]["type"]
            propertiesKV[ppKey] = ppV
        propertiesKV = remove_key_in_blacklist(
            v, ["additionalProperties", "anyOf", "$schema"]
        )
        return propertiesKV
    return v


def remove_key_in_blacklist(dd: dict, key_black_list: list[str]) -> dict:
    """
    递归遍历 json（最外层必须为 dict），删除所有 key 在 key_black_list 中的项。

    对于字典：如果 key 在黑名单中则跳过，否则递归处理其值。
    对于列表：递归处理列表中的每个元素；如果元素处理结果是空字典，则从列表中移除（符合示例）。
    其他类型（如字符串、数字等）直接返回原值。
    """
    black_set = set(key_black_list)

    def process(obj):
        if isinstance(obj, dict):
            new_obj = {}
            for key, value in obj.items():
                if key in black_set:
                    # 如果 key 在黑名单中则跳过
                    continue
                new_val = process(value)
                new_obj[key] = new_val
            return new_obj
        elif isinstance(obj, list):
            new_list = []
            for item in obj:
                new_item = process(item)
                # 如果处理结果是空字典，则跳过这个元素
                if isinstance(new_item, dict) and not new_item:
                    continue
                new_list.append(new_item)
            return new_list
        else:
            return obj

    return process(dd)


def words_in_string(string, words):
    for word in words:
        if word in string:
            return True
    return False
