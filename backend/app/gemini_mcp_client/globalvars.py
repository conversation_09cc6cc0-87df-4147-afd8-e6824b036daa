import os
from mcp.client.stdio import StdioServerParameters

# model_type = "gemini-2.5-pro-exp-03-25"
model_type = "gemini-2.0-flash-001"

init_prompt = """This is a multi-step task, please complete it in order:
1. Please first Get all Jira Tasks or Stories, condition: assignee or report is z<PERSON><PERSON>@moego.pet, all projects, up to 10 items. 
2. Please optimize the summaries of these tasks from the user perspective, outputs, and outcomes. A good example is: As a user role, I hope to do outputs and achieve outcomes. 
3. outputs the jira key, summary of the original jira task and the optimized summary(convert it to Chinese) in the previous step.
"""

# 确保设置了 API 密钥
api_key = os.getenv("GEMINI_API_KEY")
if not api_key:
    raise ValueError("GEMINI_API_KEY environment variable not set.")


# --- 定义多个 MCP 服务器参数 ---
# 假设你有另一个 MCP 服务器，例如一个计算器
# 你需要提供实际的命令和参数来启动它
mcp_servers_config = {
    # "unix_timestamps_mcp": StdioServerParameters(
    #     command="npx",
    #     args=["-y", "github:Ivor/unix-timestamps-mcp"],
    #     env={
    #         "http_proxy": os.getenv("http_proxy"),
    #         "https_proxy": os.getenv("https_proxy"),
    #     },
    # ),
    # "time": StdioServerParameters(
    #     command="uvx",
    #     args=["mcp-server-time", "--local-timezone=Asia/Shanghai"],
    #     env={
    #         "http_proxy": os.getenv("http_proxy"),
    #         "https_proxy": os.getenv("https_proxy"),
    #     },
    # ),
    "mcp-atlassian": StdioServerParameters(
        command="uvx",
        args=[
            "mcp-atlassian",
            "--jira-url=https://moego.atlassian.net",
            "--jira-username=<EMAIL>",
            f"--jira-token={os.getenv("JIRA_TOKEN")}",
        ],
        env=None,
    ),
    # 可以添加更多 MCP...
}
