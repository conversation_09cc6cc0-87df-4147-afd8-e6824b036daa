import os
import logging
import json
from typing import List, Dict, Any
import asyncio
from mcp import ClientSession
from mcp.client.stdio import stdio_client, StdioServerParameters
from openai import OpenAI


class MCPIntegration:
    def __init__(self, mcp_params_list: List[StdioServerParameters]):
        self.mcp_params_list = mcp_params_list
        self.tools = []
        self._contexts = []
        self._tool_to_session_map: Dict[str, ClientSession] = {}

    async def __aenter__(self):
        for params in self.mcp_params_list:
            stdio_context = stdio_client(params)
            read, write = await stdio_context.__aenter__()
            session_context = ClientSession(read, write)
            mcp_session = await session_context.__aenter__()

            await mcp_session.initialize()

            tools_response = await mcp_session.list_tools()

            for tool in tools_response.tools:
                self._tool_to_session_map[tool.name] = mcp_session
                function_spec = {
                    "name": tool.name,
                    "description": tool.description,
                }
                if hasattr(tool, "inputSchema") and tool.inputSchema:
                    function_spec["parameters"] = tool.inputSchema

                self.tools.append({"type": "function", "function": function_spec})

            self._contexts.append((stdio_context, session_context))

        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        for stdio_context, session_context in reversed(self._contexts):
            try:
                if session_context:
                    await session_context.__aexit__(exc_type, exc_val, exc_tb)
                if stdio_context:
                    await stdio_context.__aexit__(exc_type, exc_val, exc_tb)
            except* RuntimeError as eg:
                import logging

                if all(
                    "Attempted to exit cancel scope in a different task" in str(e)
                    for e in eg.exceptions
                ):
                    logging.warning(
                        f"Ignoring expected anyio task scope error during shutdown: {eg}"
                    )
                else:
                    raise

    async def call_tool(self, name: str, arguments: Dict[str, Any]):
        if name not in self._tool_to_session_map:
            raise Exception(f"Tool '{name}' not found in any MCP session.")

        session = self._tool_to_session_map[name]
        result = await session.call_tool(name, arguments)
        return result


class ChatManager:
    def __init__(
        self,
        mcp_integration: MCPIntegration,
        model_name: str,
        api_key: str,
        base_url: str,
    ):
        self.mcp = mcp_integration
        self.client = OpenAI(api_key=api_key, base_url=base_url)
        self.model_name = model_name
        self.messages: List[Dict[str, Any]] = []

    async def run(self, initial_prompt: str) -> List[str]:
        self.messages = [{"role": "user", "content": initial_prompt}]

        response = self.client.chat.completions.create(
            model=self.model_name,
            messages=self.messages,
            tools=self.mcp.tools,
            tool_choice="auto",
        )

        self.messages.append(response.choices[0].message)

        while response.choices[0].finish_reason == "tool_calls":
            tool_calls = response.choices[0].message.tool_calls

            for tool_call in tool_calls:
                function_name = tool_call.function.name
                try:
                    function_args = json.loads(tool_call.function.arguments)
                except json.JSONDecodeError:
                    function_args = {}

                try:
                    tool_response = await self.mcp.call_tool(
                        function_name, function_args
                    )

                    if hasattr(tool_response, "content") and tool_response.content:
                        content = (
                            tool_response.content[0]
                            if isinstance(tool_response.content, list)
                            else tool_response.content
                        )
                        tool_result = (
                            content.text
                            if hasattr(content, "text")
                            else str(tool_response)
                        )
                    else:
                        tool_result = str(tool_response)

                    self.messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": function_name,
                            "content": tool_result,
                        }
                    )
                except Exception as e:
                    self.messages.append(
                        {
                            "tool_call_id": tool_call.id,
                            "role": "tool",
                            "name": function_name,
                            "content": f"Error: {str(e)}",
                        }
                    )

            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=self.messages,
                tools=self.mcp.tools,
                tool_choice="auto",
            )

            self.messages.append(response.choices[0].message)

        final_response = response.choices[0].message.content
        if final_response:
            logging.info("FINAL RESPONSE: " + final_response)
        else:
            logging.info("No content in final response")

        ret = []
        for x in self.messages:
            if isinstance(x, Dict):
                ret.append(x.get("content", "NA"))
            else:
                ret.append(x.content)
        return ret
