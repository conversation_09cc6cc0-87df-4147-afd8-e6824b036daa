import asyncio
import json
import os
from datetime import datetime
from google import genai
from google.genai import types
from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client
from mcp_logic import LogicRunner
from mcp_manage_session import MCPSessionManager

# 使用LiteLLM代理URL和API密钥
LITELLM_API_KEY = os.getenv("LITELLM_KEY")  # 你需要设置这个环境变量
# LITELLM_API_KEY = "AIzaSyBFjCEU8GBqUJCLtNZ4gfg4aXn2MdisMLw"  # 你需要设置这个环境变量
os.environ["GOOGLE_GEMINI_BASE_URL"] = "https://llmproxy-dev.devops.moego.pet"

# model_type = "gemini-2.5-flash"
model_type = "gemini/gemini-2.5-flash"
# model_type = "zihao-moego-email-gemini-2.5-flash"

jira_token = os.getenv("JIRA_TOKEN")

# Initial prompt for the model
initial_prompt = "获取***************最近的10个jira issues， 并输出key和summary"


async def run():
    # Create server parameters for stdio connection
    print(f"jira token:{jira_token}")
    mcp_servers_config = {
        "mcp-atlassian": StdioServerParameters(
            command="uvx",
            args=[
                "mcp-atlassian",
                "--jira-url=https://moego.atlassian.net",
                "--jira-username=<EMAIL>",
                f"--jira-token={jira_token}",
            ],
            env=None,
        )
    }
    client = genai.Client(api_key=LITELLM_API_KEY)
    logic_runner = LogicRunner(model_type, initial_prompt, client)

    session_manager = MCPSessionManager(mcp_servers_config, logic_runner)
    dialogue_history = await session_manager.start_sessions_and_run()
    print(dialogue_history)


# Start the asyncio event loop and run the main function
asyncio.run(run())
