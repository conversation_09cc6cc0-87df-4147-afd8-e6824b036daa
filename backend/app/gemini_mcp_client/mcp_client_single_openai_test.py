import os
import asyncio
from mcp.client.stdio import StdioServerParameters
from mcp_logic_openai import MCPIntegration, ChatManager

# Get tokens from environment
jira_token = os.getenv(
    "JIRA_TOKEN",
    "",
)
slack_token = os.getenv("SLACK_TOKEN", "")
api_key = os.getenv("LITELLM_KEY", "")


async def main():
    mcp_servers = [
        StdioServerParameters(
            command="uvx",
            args=[
                "mcp-atlassian",
                "--jira-url=https://moego.atlassian.net",
                "--jira-username=<EMAIL>",
                f"--jira-token={jira_token}",
            ],
            env=None,
        ),
        StdioServerParameters(
            command="npx",
            args=["-y", "@modelcontextprotocol/server-slack"],
            env={
                "SLACK_BOT_TOKEN": f"{slack_token}",
                "SLACK_TEAM_ID": "T011CF3CMJN",
            },
        ),
    ]

    async with MCPIntegration(mcp_servers) as mcp:
        chat_manager = ChatManager(
            mcp_integration=mcp,
            model_name="zihao-moego-email-gemini-2.5-flash",
            api_key=api_key,
            base_url="https://llmproxy-dev.devops.moego.pet/v1",
        )
        await chat_manager.run(
            """1. 获取channel C096T0J3LP7, thread: 1753868378.824019 的主内容和thread内容
2. 主消息和回复的内容统一用中文总结为一段话，评论到jira CS-32064下面。格式：$slack_url\n总结内容: $CONTENT
            """
        )


if __name__ == "__main__":
    asyncio.run(main())
