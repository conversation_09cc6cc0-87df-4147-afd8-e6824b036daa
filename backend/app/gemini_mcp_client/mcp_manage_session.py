from google.genai import types
from mcp.client.stdio import stdio_client
from mcp.client.session import ClientSession
from mcp.client.stdio import StdioServerParameters
import logging  # 增加日志记录
from typing import Callable
import traceback
from tools import remove_properties_default
from mcp_logic import LogicRunner
import json


class MCPSessionManager:
    """
    管理 MCP 会话的类，负责启动 MCP、建立会话并获取其工具。
    """

    def __init__(
        self,
        mcp_servers_config: dict[str, StdioServerParameters],
        logic_runner: LogicRunner,
    ):
        # 存储 MCP 名称 -> ClientSession 的映射
        self.mcp_sessions: dict[str, ClientSession] = {}
        # 存储所有 MCP 提供的 Gemini 工具
        self.all_gemini_tools: list[ClientSession] = []
        # 存储工具名称 -> MCP 名称的映射
        self.tool_to_mcp_router: dict[str, str] = {}
        # 存储逻辑
        self.logic_runner = logic_runner

        mcp_servers_config_list = [
            (name, params) for name, params in mcp_servers_config.items()
        ]
        # 所有预定义的MCP列表
        self.mcp_servers_config_list = mcp_servers_config_list

    async def start_sessions_and_run(self):
        """异步函数，用于启动一个 MCP、建立会话并获取其工具"""
        if len(self.mcp_servers_config_list) == 0:
            # 执行逻辑
            dialogue_history = await self.logic_runner.run_logic(
                self.mcp_sessions, self.all_gemini_tools, self.tool_to_mcp_router
            )
            return dialogue_history

        # 从mcp_servers_config 中获取出第一个 MCP 服务器参数
        name, params = self.mcp_servers_config_list.pop(0)
        logging.info(f"Attempting to connect to MCP: {name}")
        try:
            async with stdio_client(params) as (read, write):
                async with ClientSession(read, write) as session:
                    logging.info(f"Successfully create session: {name}")
                    await session.initialize()
                    logging.info(f"Successfully initialized session for MCP: {name}")
                    mcp_tools_response = await session.list_tools()
                    logging.info(
                        f"Retrieved {len(mcp_tools_response.tools)} tools from MCP: {name}"
                    )

                    # logging.info(f"{mcp_tools_response.tools}")

                    # 将 MCP 工具转换为 Gemini 工具格式
                    # gemini_tools = []
                    # for tool in mcp_tools_response.tools:
                    #     # logging.info(f"{tool.name} is load "),
                    #     parameters = {}
                    #     for k, v in tool.inputSchema.items():
                    #         if k not in ["additionalProperties", "$schema"]:
                    #             parameters[k] = remove_properties_default(k, v)
                    #     gemini_tools.append(
                    #         types.Tool(
                    #             function_declarations=[
                    #                 {
                    #                     "name": tool.name,
                    #                     "description": tool.description,
                    #                     "parameters": parameters,
                    #                 }
                    #             ]
                    #         ),
                    #     )
                    # 返回会话和该会话提供的 Gemini 工具列表
                    # 同时存储工具名称到会话名称的映射，用于后续路由
                    tmp_tool_name_to_mcp_map = {
                        tool.name: name for tool in mcp_tools_response.tools
                    }
                    self.tool_to_mcp_router.update(tmp_tool_name_to_mcp_map)
                    self.mcp_sessions[name] = session
                    self.all_gemini_tools.append(session)
                    dialogue_history = await self.start_sessions_and_run()
                    logging.info(f"Successfully processed connection for MCP: {name}")
                    return dialogue_history
        except Exception as e:
            logging.error(
                f"Failed to connect or get tools from MCP '{name}': {traceback.format_exc()}"
            )
            return [
                f"Failed to connect or get tools from MCP '{name}', please check your MCP configuration is correct.",
            ]
