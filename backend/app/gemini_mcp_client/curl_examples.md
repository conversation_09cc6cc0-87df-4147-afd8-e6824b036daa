# cURL Command for openai_mcp

Here is a `curl` command to call the `/api/v1/openai_mcp` endpoint.

**Note:** Make sure the Flask application in `http_services.py` is running before executing this command. You will also need to replace `YOUR_JIRA_TOKEN` with your actual Jira API token.

```bash
curl -X POST http://localhost:8080/api/v1/openai_mcp \
-H "Content-Type: application/json" \
-d '{
	"model_type": "gemini-2.5-flash",
	"init_prompt": "1. 获取channel C096T0J3LP7, thread: 1753868378.824019 的主内容和thread内容\n2. 主消息和回复的内容统一用中文总结为一段话，评论到jira CS-32064下面。格式：$slack_url\n总结内容: $CONTENT",
	"mcp_servers_config": {
		"mcp-atlassian": {
			"timeout": 60,
			"command": "uvx",
			"args": ["mcp-atlassian", "--jira-url=https://moego.atlassian.net", "--jira-username=<EMAIL>", "--jira-token=KEY"],
			"env": {},
			"transportType": "stdio"
		},
		"mcp-slack": {
			"timeout": 60,
			"command": "npx",
			"args": ["-y", "@modelcontextprotocol/server-slack"],
			"env": {
				"SLACK_BOT_TOKEN": "KEY",
				"SLACK_TEAM_ID": "T011CF3CMJN"
			},
			"transportType": "stdio"
		}
	},
	"api_key": "KEY",
	"base_url": "https://llmproxy-dev.devops.moego.pet/v1"
}'
```
