from typing import LiteralString
from google.genai import types
import logging  # 增加日志记录

from tools import words_in_string
import traceback


def get_text_from_response(response) -> str:
    if response and response.candidates and response.candidates[0].content.parts:
        # 获取 Gemini 的回复内容
        response_text = "".join(
            str(p.text)
            for p in response.candidates[0].content.parts
            if hasattr(p, "text")
        )
        return response_text
    return "ERROR: No response from <PERSON>."


def get_text_from_content(content) -> LiteralString:
    if content:
        # 获取 Gemini 的回复内容
        response_text = "".join(p.text for p in content.parts if hasattr(p, "text"))
        return response_text
    return "ERROR: No response from <PERSON>."


class LogicRunner:
    def __init__(self, model_str, init_prompt, client):
        self.model_str = model_str
        self.init_prompt = init_prompt
        self.client = client

    async def _call_gemini_api(
        self, contents: list[types.Content], all_gemini_tools: list
    ) -> types.GenerateContentResponse:
        """
        Calls the Gemini API to generate content based on the provided contents and tools.
        """
        try:

            # logging.info(f"will init {len(all_gemini_tools)} tools")  # 31
            # tp_all_gemini_tools = all_gemini_tools[15:17]
            # tp_all_gemini_tools = [all_gemini_tools[16]]
            # with open("./gemini_tools.txt", mode="w+") as f:
            #     f.write(str(all_gemini_tools))
            logging.info(f"process: will call gemini api")
            response = await self.client.aio.models.generate_content(
                model=self.model_str,
                contents=contents,
                config=types.GenerateContentConfig(
                    temperature=0,
                    tools=all_gemini_tools,
                ),
            )
            return response
        except Exception as e:
            logging.error(
                f"An error occurred during Gemini API call: {traceback.format_exc()}"
            )
            raise

    async def _process_function_call(
        self,
        function_call: types.FunctionCall,
        mcp_sessions: dict,
        tool_to_mcp_router: dict,
        dialogue_history: list,
        contents: list[types.Content],
    ) -> None:
        """
        Processes a function call requested by Gemini.
        """
        call_text = f"process: gemini requested function call: {function_call.name} with args: {function_call.args}"
        logging.info(call_text)
        dialogue_history.append(call_text)

        # --- 路由函数调用到正确的 MCP 会话 ---
        target_mcp_name = tool_to_mcp_router.get(function_call.name)

        if target_mcp_name and target_mcp_name in mcp_sessions:
            target_session = mcp_sessions[target_mcp_name]
            try:
                # 调用 MCP 服务器上的工具
                result = await target_session.call_tool(
                    function_call.name,
                    arguments=function_call.args,  # 确保参数是字典
                )
                function_call_result_text = (
                    f"Result from {function_call.name}: {result.content[0].text}"
                )
                logging.info(function_call_result_text)
                dialogue_history.append(function_call_result_text)

                # 将工具返回的结果发送回 Gemini
                contents.append(result.content[0])

            except Exception as e:
                msg = f"fix the error:{e}"
                dialogue_history.append(msg)
                contents.append(
                    types.Content(
                        role="user",
                        parts=[types.Part(text=msg)],
                    )
                )
                return
        else:
            logging.error(
                f"Could not find a session for tool: {function_call.name}. Routing map: {tool_to_mcp_router}, Available sessions: {list(mcp_sessions.keys())}"
            )
            return

    async def run_logic(
        self, mcp_sessions: dict, all_gemini_tools: list, tool_to_mcp_router: dict
    ) -> list[str]:
        if not mcp_sessions:
            logging.error("No MCP sessions could be established. Exiting.")
            return []

        if not all_gemini_tools:
            logging.error("No tools were retrieved from any MCP. Exiting.")
            return []

        dialogue_history = []

        contents = [
            types.Content(role="user", parts=[types.Part(text=self.init_prompt)])
        ]

        dialogue_history.append(self.init_prompt)

        logging.info(f"Sending init prompt to Gemini: {self.init_prompt}")

        max_iterations = 8  # 设置最大迭代次数，防止无限循环
        iteration = 0
        final_response_received = False

        while iteration < max_iterations and not final_response_received:
            try:
                response = await self._call_gemini_api(contents, all_gemini_tools)
                if not response.candidates or not response.candidates[0].content:
                    logging.info(
                        "process: No candidates or content found in the response from Gemini."
                    )
                    logging.info(f"process: response from gemini: {response}")
                    final_response_received = True
                    break

                gemini_content = response.candidates[0].content
                contents.append(gemini_content)
                response_text = get_text_from_response(response)
                if response_text:
                    dialogue_history.append(response_text)

                function_call_parts = []
                if gemini_content.parts:
                    function_call_parts = [
                        part for part in gemini_content.parts if part.function_call
                    ]

                if function_call_parts:
                    for part in function_call_parts:
                        if part.function_call:  # Ensure function_call is not None
                            await self._process_function_call(
                                part.function_call,
                                mcp_sessions,
                                tool_to_mcp_router,
                                dialogue_history,
                                contents,
                            )
                    # Try again after processing function calls
                    continue

                # 没有函数调用，直接打印 Gemini 的回复
                logging.info("process: No function call found in the response.")
                gemini_resp_text = get_text_from_response(response)
                logging.info(f"process: Response from Gemini:\\n{gemini_resp_text}")
                if words_in_string(
                    gemini_resp_text,
                    [
                        "Would you",
                        "Do you",
                        "Could you",
                        "Can you",
                    ],
                ):
                    msg = "Yes, and you make the choice you think is best."
                    logging.info(f"process: will send: {msg}")
                    dialogue_history.append(msg)
                    contents.append(
                        types.Content(
                            role="user",
                            parts=[types.Part(text=msg)],
                        )
                    )
                else:
                    final_response_received = True
                    break
            except Exception as e:
                logging.error(
                    f"An error occurred during Gemini API call: {traceback.format_exc()}"
                )
                dialogue_history.append(
                    f"An error occurred during Gemini API call: {e}"
                )
                final_response_received = True
                break
            finally:
                iteration += 1

        # --- 关闭所有 MCP 会话 ---
        logging.info("Attempting to ensure all MCP connections are closed.")
        return dialogue_history
