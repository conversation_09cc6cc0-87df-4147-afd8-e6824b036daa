import json
import logging
import pytest
import os
from flask import Flask
from http_services import app  # 导入你的 Flask app


@pytest.fixture
def client():
    app.config["TESTING"] = True
    with app.test_client() as client:
        yield client


def test_openai_mcp_success(client):
    data = {
        "model_name": "zihao-moego-email-gemini-2.5-flash",
        "init_prompt": "What are my 10 most recent tasks in Jira? Please use the appropriate tools to find this information.",
        "mcp_servers_config": [
            {
                "command": "uvx",
                "args": [
                    "mcp-atlassian",
                    "--jira-url=https://moego.atlassian.net",
                    "--jira-username=<EMAIL>",
                    "--jira-token=" + (os.environ.get("JIRA_TOKEN") or ""),
                ],
                "env": None,
            }
        ],
        "api_key": f"{os.getenv("LITELLM_KEY")}",
        "base_url": "https://llmproxy-dev.devops.moego.pet/v1",
    }
    response = client.post("/api/v1/openai_mcp", json=data)
    assert response.status_code == 200
    result = json.loads(response.data.decode("utf-8"))
    assert "result" in result
    print(result)


def test_gemini_mcp_datadog(client):
    data = {
        "model_type": "gemini-2.5-flash-preview-04-17",
        "init_prompt": "1. time:2025-04-23 21:00:00,8 East\n2. Use Datadog to query the trace span data within 3 minutes before and after the corresponding time, query: service:moego-authz env:ns-production (@duration:>=100ms OR status:error)\n3. Summarize these data and give suggestions.",
        "mcp_servers_config": {
            "datadog": {
                "command": "npx",
                "args": ["-y", "@winor30/mcp-server-datadog@v1.5.0"],
                "env": {
                    "DATADOG_API_KEY": os.getenv("DATADOG_API_KEY"),
                    "DATADOG_APP_KEY": os.getenv("DATADOG_APP_KEY"),
                    "DATADOG_SITE": "us5.datadoghq.com",
                },
            },
            "unix_timestamps_mcp": {
                "command": "npx",
                "args": ["-y", "github:Ivor/unix-timestamps-mcp"],
                "env": {},
            },
        },
        "api_key": os.environ.get("GEMINI_API_KEY") or "",
    }
    # 发送 POST 请求
    response = client.post("/api/v1/gemini_mcp", json=data)
    # 验证状态码
    assert response.status_code == 200

    # 验证返回数据
    result = json.loads(response.data.decode("utf-8"))
    logging.info(result)


def test_gemini_mcp_success(client):
    # 准备测试数据
    data = {
        "model_type": "gemini-2.0-flash-001",
        "init_prompt": """This is a multi-step task, please complete it in order:
1. Please first Get all Jira Tasks or Stories, condition: assignee or <NAME_EMAIL>, all projects, up to 10 items.
2. Please optimize the summaries of these tasks from the user perspective, outputs, and outcomes. A good example is: As a user role, I hope to do outputs and achieve outcomes.
3. outputs the jira key, summary of the original jira task and the optimized summary(convert it to Chinese) in the previous step.""",
        "mcp_servers_config": {
            "mcp-atlassian": {
                "command": "uvx",
                "args": [
                    "mcp-atlassian",
                    "--jira-url=https://moego.atlassian.net",
                    "--jira-username=<EMAIL>",
                    "--jira-token=" + (os.environ.get("JIRA_TOKEN") or ""),
                ],
                "env": None,
            }
        },
        "api_key": os.environ.get("GEMINI_API_KEY") or "",
    }
    # 发送 POST 请求
    response = client.post("/api/v1/gemini_mcp", json=data)

    # 验证状态码
    assert response.status_code == 200

    # 验证返回数据
    result = json.loads(response.data.decode("utf-8"))
    assert "IFRBE" in result.get("result", "")
    print(result)


def test_gemini_mcp_missing_parameters(client):
    # 准备测试数据 (缺少 model_type)
    data = {
        "init_prompt": "This is a multi-step task...",
        "mcp_servers_config": {
            "mcp-atlassian": {
                "command": "uvx",
                "args": [
                    "mcp-atlassian",
                    "--jira-url=https://moego.atlassian.net",
                    "--jira-username=<EMAIL>",
                    "--jira-token=" + (os.environ.get("JIRA_TOKEN") or ""),
                ],
                "env": None,
            }
        },
        "api_key": "",
    }
    # 发送 POST 请求
    response = client.post("/api/v1/gemini_mcp", json=data)

    # 验证状态码
    assert response.status_code == 400

    # 验证返回数据
    result = json.loads(response.data.decode("utf-8"))
    assert "error" in result
