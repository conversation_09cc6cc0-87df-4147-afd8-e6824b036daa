load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "cs_page_watcher_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/cs_page_watcher",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/cs_page_watcher/configloader",
        "//backend/app/cs_page_watcher/logic/jiraslareminder",
        "//backend/app/cs_page_watcher/repo/entity",
        "//backend/app/cs_page_watcher/repo/jira",
        "//backend/app/cs_page_watcher/repo/slack",
        "//backend/app/cs_page_watcher/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/config/nacos",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/filters/validation",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/cs_page_watcher/v1:cs_page_watcher",
    ],
)

go_binary(
    name = "cs_page_watcher",
    embed = [":cs_page_watcher_lib"],
    visibility = ["//visibility:public"],
)
