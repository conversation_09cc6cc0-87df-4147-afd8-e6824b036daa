# CS Page Watcher - 项目上下文

## 概述

CS Page Watcher 是一个监控与客户支持相关的 Jira 工单并根据工单属性执行自动化操作的服务。该系统可以触发 Datadog 事件、创建 Slack 频道、将工单分配给适当的团队以及处理数据导入任务。

## 核心组件

### 1. 主服务 (`service/page_watcher.go`)

- **PageWatcherIns**: CS Page Watcher 服务的主要实现
- 实现了 `pb.CSPageWatcherServiceServer` 接口
- 核心方法：
  - `MonitorAndPage()`: 主监控功能
  - `TriggerPage()`: 从 Jira 工单触发页面
  - `CompleteIncident()`: 完成事件
  - `CompleteJira()`: 完成 Jira 工单
  - `TriggerAdminTask()`: 将工单分配给适当的负责人
  - `RunTask()`: 执行特定任务
  - `runDataImportTask()`: 处理数据导入任务
  - `dmSubTaskReminder()`: 处理 DM 项目子任务状态更新提醒

### 2. DM 子任务提醒 (`service/dm_reminder.go`)

- **dmSubTaskReminder**: 当 DM 项目子任务状态更新时发送 Slack 提醒

### 2. 配置 (`configloader/`)

- **Config**: 主配置结构，从 cs_page_watcher.yaml 加载基础设施配置
- **CSPageConfig**: Nacos 动态配置结构，包含业务规则和映射关系
- **GlobalNacosCsPageConfig**: 全局 Nacos 配置实例，支持热更新
- 支持三层配置架构：
  - 本地TRPC配置 (config.yaml)，业务无关
  - 本地业务配置 (cs_page_watcher.yaml)
  - Nacos 动态配置 (cs-page-watcher.yaml)
- Nacos 配置支持实时监听和动态更新

### 3. 全局常量 (`global/`)

- Jira 自定义字段名称 (T1OrGeneral, IssuePriority 等)
- Jira 优先级层级 (TierT1, TierOther)
- Slack 表情符号常量
- ~~团队时间表和映射 (TeamSchedules)~~ **已迁移到 Nacos 配置**
- 联系人映射 (MoegoContacts)
- Squad 常量定义

### 4. 数据模型 (`repo/entity/`)

- **CsPage**: 表示 cs_page 表
- **CsDataImportTask**: 表示 cs_data_import_task 表
- 两个实体的读写接口和实现

### 5. 外部集成

#### Jira (`repo/jira/`)

- **IssueRepository**: Jira 操作接口
- **IssueRepositoryIns**: 使用 go-jira 库的实现
- **Issue**: Jira 工单数据结构
- 获取工单、更新字段、关闭工单等方法

#### Slack (`repo/slack/`)

- **Client**: Slack 操作接口
- **slackClient**: 使用 slack-go 库的实现
- 创建频道、添加成员、发送消息等方法

#### Datadog (`repo/datadog/`)

- **IncidentGateway**: Datadog 操作接口
- **IncidentGatewayImpl**: 实现类，通过依赖注入接收 nacos 配置
- 触发事件、获取根本原因等方法
- 构造函数: `NewIncidentGateway(cfg, slackClient, csPageReaderWriter, nacosCsConfig)`

### 6. 逻辑组件 (`logic/`)

- **JiraIncidentEvaluator**: 评估 Jira 工单以确定是否应触发事件

### 7. 定时任务 (`service/jira_sla_reminder.go`)

- **ScheduledJiraReminder**: 处理 SLA 提醒任务
- 提醒开放和逾期的工单
- 支持从 Nacos 配置中动态获取 Slack 频道映射
- 按团队分组发送提醒消息

## 核心数据结构

### Issue (Jira 工单)

```go
type Issue struct {
    ID                   string
    Key                  string
    Summary              string
    Status               string
    IssueDescription     string
    Description          string
    Created              time.Time
    Updated              time.Time
    Assignee             UserInfo
    Reporter             UserInfo
    Parent               *Parent  // 添加Parent字段用于子任务
    T1OrGeneral          string
    IssuePriority        string
    CustomerStage        string
    Components           []string
    JiraSquad            string
    SLABreach            string
    LogoName             string
    LocationName         string
    DevEngineer          []UserInfo
    ResolutionTimeCustom string
    CreatedByCustom      string
    FeatureDomains       string
}
```

### Parent (Jira 工单父问题)

```go
type Parent struct {
    ID  string `json:"id,omitempty" structs:"id,omitempty"`
    Key string `json:"key,omitempty" structs:"key,omitempty"`
}
```

### Schedule (团队时间表配置)

```go
type Schedule struct {
    Name                string `yaml:"name"`
    ScheduleID          string `yaml:"schedule_id"`
    AdminTaskScheduleID string `yaml:"admin_task_schedule_id"`
    TeamHandle          string `yaml:"team_handle"`
}
```

### CSPageConfig (Nacos 配置)

```go
type CSPageConfig struct {
    ExampleKey               string              `yaml:"example_key"`
    ComponentsSquadsMapping  map[string]string   `yaml:"components_squads_mapping"`
    RefUser                  []string            `yaml:"ref_user"`
    DataImportUserEmails     []string            `yaml:"data_import_user_emails"`
    SquadSlackChannelMapping map[string]string   `yaml:"squad_slack_channel_mapping"`
    TeamSchedules            map[string]Schedule `yaml:"team_schedules"`
}
```

### CsPage

```go
type CsPage struct {
    CsPageJiraTicket         string
    DatadogIncidentID        string
    IncidentSlackChannelID   string
    T1NotifyMessageTimestamp string
    CreateTime               time.Time
}
```

### CsDataImportTask

```go
type CsDataImportTask struct {
    JiraKey        string
    SlackChannelID string
    CreateTime     time.Time
}
```

## 核心方法

### PageWatcherIns 方法

1. **TriggerPage**: 为符合条件的 Jira 工单创建 Datadog 事件
2. **CompleteIncident**: 完成事件并更新 Jira 工单
3. **CompleteJira**: 完成没有关联事件的 Jira 工单
4. **TriggerAdminTask**: 将工单分配给适当的负责人
5. **RunTask**: 执行定时任务
6. **runDataImportTask**: 为数据导入任务创建 Slack 频道
7. **dmSubTaskReminder**: 当 DM 项目子任务状态更新时发送 Slack 提醒

### RunTask 支持的任务名称

- `Open Tasks: Bugs`: 提醒开放的Bug工单
- `Overdue Open Tasks: Bugs`: 提醒逾期的开放Bug工单
- `Open Tasks: AdminTasks`: 提醒开放的管理任务工单
- `Overdue Open Tasks: AdminTasks`: 提醒逾期的开放管理任务工单
- `Data Import Task`: 处理数据导入任务（需要提供JiraKey参数）
- `DM Subtask Reminder`: 处理DM子任务状态更新提醒（需要提供JiraKey参数）

### Jira Repository 方法

1. **GetIssueDetails**: 获取 Jira 工单的详细信息
2. **GetNewOrUpdatedBugTickets**: 获取符合条件的工单
3. **CloseIssue**: 关闭 Jira 工单
4. **SetAssignee**: 将工单分配给用户
5. **SetCauseAndSolution**: 设置原因和解决方案字段
6. **SetFeatureDomains**: 设置功能域字段

### Slack Client 方法

1. **CreateChannel**: 创建新的 Slack 频道
2. **AddMembersToChannel**: 向频道添加成员
3. **SendMessage**: 向频道发送消息
4. **SendMessageToThread**: 向线程发送消息
5. **SendMessageToPerson**: 发送直接消息
6. **AddEmojiToMessage**: 为消息添加表情符号反应
7. **JoinChannel**: 加入频道
8. **LookUpByEmail**: 通过邮箱查找用户 ID

## 数据库模式

### cs_page 表

```sql
CREATE TABLE cs_page (
    cs_page_jira_ticket VARCHAR(255) PRIMARY KEY UNIQUE,
    datadog_incident_id VARCHAR(255) NOT NULL DEFAULT '',
    incident_slack_channel_id VARCHAR(255) NOT NULL DEFAULT '',
    t1_notify_message_timestamp VARCHAR(255) NOT NULL DEFAULT '',
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### cs_data_import_task 表

```sql
CREATE TABLE cs_data_import_task (
    jira_key VARCHAR(255) PRIMARY KEY UNIQUE,
    slack_channel_id VARCHAR(255) NOT NULL DEFAULT '',
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

## Protobuf 服务定义

该服务实现了 `cs_page_watcher.proto` 中定义的 `CSPageWatcherService`：

- `TriggerPage`: 从 Jira 工单启动 CS 页面
- `TriggerAdminTask`: 将 Jira 工单分配给适当的负责人
- `CompleteIncident`: 完成事件和相关的 Jira 工单
- `CompleteJira`: 完成 Jira 工单
- `RunTask`: 按名称执行特定任务

## 核心特性

1. **事件管理**: 与 Datadog 集成进行事件创建和管理
2. **团队分配**: 根据组件自动将工单分配给适当的团队
3. **SLA 监控**: 为开放和逾期的工单安排提醒
4. **Slack 集成**: 在 Slack 中创建频道和通知
5. **数据导入任务**: 特殊处理数据导入工单，包括频道创建
6. **Jira 自动化**: 在工作流中自动更新 Jira 工单
7. **DM 子任务提醒**: 当 DM 项目子任务状态更新时自动发送 Slack 提醒
8. **动态配置管理**: 支持 Nacos 配置热更新，业务规则可实时调整

## 重要更新

### TeamSchedules 配置迁移

- **原位置**: `global/global.go` 中的 `TeamSchedules` 变量
- **新位置**: Nacos 配置中的 `team_schedules` 字段
- **访问方式**: 通过依赖注入的 `nacosCsConfig.Load().TeamSchedules` 替代 `global.TeamSchedules`
- **优势**: 支持热更新，无需重启服务即可调整团队时间表配置

### 迁移影响的文件

- `repo/datadog/incident_triger.go`: 更新构造函数，添加 nacos 配置依赖注入
- `repo/datadog/incident_triger_test.go`: 测试文件同步更新
- `service/page_watcher.go`: 更新 `NewIncidentGateway` 调用，传入 nacos 配置
- `global/global.go`: 原 `TeamSchedules` 标记为 Deprecated
- `configloader/nacos.go`: 移除全局 `GetTeamSchedules()` 函数

## 配置

该服务采用三层配置架构：

### 1. 本地主配置文件 (config.yaml)

- **文件路径**: `./config/{environment}/config.yaml`
- **内容**: 包含 服务级别的组件(nacos/service/client/secrets)，和业务无关
- **特点**: 静态配置，需要重启服务才能生效
- **用途**: 敏感信息和基础服务配置

### 2. 本地业务配置文件 (cs_page_watcher.yaml)

- **文件路径**: `./config/{environment}/cs_page_watcher.yaml`
- **内容**: 包含本地环境特定的业务配置
- **特点**: 静态配置，需要重启服务才能生效
- **用途**: 环境特定的业务规则和映射

### 3. Nacos 动态配置

- **配置键名**: `cs-page-watcher.yaml`
- **内容**: 包含可动态调整的业务规则和映射关系
- **特点**: 支持热更新，无需重启服务
- **用途**: 运行时可调整的业务配置
- **配置格式**: YAML

#### 配置内容说明

**本地主配置 (config.yaml)**:

- 数据库连接信息
- 服务端口和基础设施配置

**本地业务配置 (cs_page_watcher.yaml)**:

- 环境特定的业务规则
- 本地开发测试配置

**Nacos 动态配置字段**:

- `components_squads_mapping`: Jira 组件到团队的映射，用于自动分配工单
- `squad_slack_channel_mapping`: 团队到 Slack 频道 ID 的映射
- `team_schedules`: 团队时间表配置，包含 Datadog 调度 ID 和团队句柄（从 global.TeamSchedules 迁移）
- `ref_user`: 参考用户列表
- `data_import_user_emails`: 数据导入任务相关用户邮箱

#### Nacos 配置示例

```yaml
# CS Page Watcher Nacos Configuration
# 配置键名: cs-page-watcher.yaml
example_key: "cs_page_watcher_config"

components_squads_mapping:
  "Payments, Finance & QuickBooks": "Fintech"
  "Client/pet/leads": "CRM"
  "Mobile Grooming(SS/CACD/Map view)": "ERP"
  "Platform(Mobile App performance/System)": "BE-Platform"
  "System notification": "FE-Platform"

ref_user:
  - "<EMAIL>"

data_import_user_emails:
  - "<EMAIL>"

squad_slack_channel_mapping:
  "CRM": "C07TH3PJGEP"
  "ERP": "C070M3QGJTV"
  "Fintech": "C07S1UNAK9Q"
  "BE-Platform": "C086JA2E7HB"
  "FE-Platform": "C06HHN24LGM"
  "ZihaoTest": ""

# 团队时间表配置（从 global.TeamSchedules 迁移）
team_schedules:
  "Dedicate":
    name: "Dedicate – Primary"
    schedule_id: "d91cf44c-b034-45eb-ae38-da98407041df"
    admin_task_schedule_id: "d91cf44c-b034-45eb-ae38-da98407041df"
    team_handle: "dedicate"
  
  "CRM":
    name: "BE CRM – Primary"
    schedule_id: "50043a7f-4075-4d8c-b63f-61af497e23db"
    admin_task_schedule_id: "5652b4c6-eaa1-4c4c-945f-6343876180f7"
    team_handle: "be-crm"
  
  "ERP":
    name: "ERP CS Page Primary"
    schedule_id: "3e9d052f-18ae-4d5a-a24c-a1eb7b058435"
    admin_task_schedule_id: "62db0311-8300-4522-b4df-452fb41dd13f"
    team_handle: "erp-oncall"
  
  "Fintech":
    name: "Fintech-BE-Primary"
    schedule_id: "97a0acb2-3659-4448-a2fb-5a331ef5d658"
    admin_task_schedule_id: "3b6cc9b0-715a-4972-9f77-a20fdf6fb758"
    team_handle: "fintech"
  
  "BE-Platform":
    name: "BE Platform Primary"
    schedule_id: "01754bba-2bf5-4207-9a3a-f04ed441c3c7"
    admin_task_schedule_id: "01754bba-2bf5-4207-9a3a-f04ed441c3c7"
    team_handle: "be-platform"
  
  "FE-Platform":
    name: "FE Platform – Default"
    schedule_id: "199499f7-aad5-4b7e-bab0-214756e81f68"
    admin_task_schedule_id: "199499f7-aad5-4b7e-bab0-214756e81f68"
    team_handle: "fe-platform"
  
  "ZihaoTest":
    name: "Zihao's Test Schedule"
    schedule_id: "a5e8d984-6747-44de-aaaa-0d0ba3e7b74e"
    admin_task_schedule_id: "a5e8d984-6747-44de-aaaa-0d0ba3e7b74e"
    team_handle: "oncall-test"
```
