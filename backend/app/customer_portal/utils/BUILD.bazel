load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "utils",
    srcs = [
        "json.go",
        "ptr.go",
        "slack.go",
        "utils.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer_portal/utils",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/log",
        "@com_github_bytedance_sonic//:sonic",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)
