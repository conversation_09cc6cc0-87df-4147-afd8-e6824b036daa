package customerportalutils

import (
	"github.com/bytedance/sonic"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// ToPointer takes any variable and returns a pointer to it
func ToPointer[T any](v T) *T {
	return &v
}

// ToValue 返回一个指针指向的值，如果指针为 nil，则返回类型 T 的零值。
func ToValue[T any](ptr *T) T {
	if ptr == nil {
		// 返回类型 T 的零值
		var zeroValue T

		return zeroValue
	}

	return *ptr
}

// CopyProtoByJSON 使用 Proto JSON 序列化和反序列化进行深拷贝
func CopyProtoByJSON(src proto.Message, dst proto.Message) error {
	data, err := protojson.Marshal(src)
	if err != nil {
		return err
	}

	return protojson.Unmarshal(data, dst)
}

// JSONDeepCopy 使用 JSON 序列化和反序列化进行深拷贝
func JSONDeepCopy(src interface{}, dst interface{}) error {
	bytes, err := sonic.Marshal(src)
	if err != nil {
		return err
	}
	err = sonic.Unmarshal(bytes, dst)
	if err != nil {
		return err
	}

	return nil
}
