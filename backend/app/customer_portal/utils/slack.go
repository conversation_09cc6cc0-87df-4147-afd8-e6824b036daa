package customerportalutils

import (
	"bytes"
	"context"
	"fmt"
	"net/http"

	"github.com/bytedance/sonic"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

const (
	SlackReviewOBScriptChannelID = "C099YTUFH8S"
	SlackHarvieBotToken          = "*********************************************************"
)

// SlackPostMessage 发送 Slack 消息，带日志
func SlackPostMessage(ctx context.Context, channelID, token, message string) error {
	payload := map[string]string{
		"channel": channelID,
		"text":    message,
	}

	jsonData, err := sonic.MarshalString(payload)
	if err != nil {
		log.ErrorContextf(ctx, "PostMessage json.MarshalString err:%v", err)

		return err
	}

	req, err := http.NewRequest("POST", "https://slack.com/api/chat.postMessage", bytes.NewBuffer([]byte(jsonData)))
	if err != nil {
		log.ErrorContextf(ctx, "PostMessage NewRequest err:%v", err)

		return err
	}

	req.Header.Set("Authorization", "Bearer "+token)
	req.Header.Set("Content-Type", "application/json")

	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		log.ErrorContextf(ctx, "PostMessage client.Do err:%v", err)

		return err
	}
	defer resp.Body.Close()

	respBytes := new(bytes.Buffer)
	if _, err := respBytes.ReadFrom(resp.Body); err != nil {
		log.ErrorContextf(ctx, "PostMessage resp.ReadFrom err:%v", err)

		return err
	}

	var respBody struct {
		Ok    bool   `json:"ok"`
		Error string `json:"error,omitempty"`
	}

	if err := sonic.UnmarshalString(respBytes.String(), &respBody); err != nil {
		log.ErrorContextf(ctx, "PostMessage json.UnmarshalString err:%v", err)

		return err
	}

	if !respBody.Ok {
		errMsg := fmt.Errorf("slack API error: %s", respBody.Error)
		log.ErrorContextf(ctx, "PostMessage Slack API error:%v", errMsg)

		return errMsg
	}

	// 打印成功日志
	log.InfoContextf(ctx, "PostMessage Success, channelID:%s, message:%s", channelID, message)

	return nil
}
