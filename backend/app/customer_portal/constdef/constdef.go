package constdef

import organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"

var CompanyOwnerEmployCategoryMap = map[organizationpb.StaffEmployeeCategory]bool{
	organizationpb.StaffEmployeeCategory_COMPANY_OWNER:    true,
	organizationpb.StaffEmployeeCategory_ENTERPRISE_OWNER: true,
}

const (
	OBScriptPermissionName = "manageCustomCode"
)

const (
	OBScriptFeatureCode = "addCssJs"
)
