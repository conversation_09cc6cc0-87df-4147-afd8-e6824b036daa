load("@io_bazel_rules_go//go:def.bzl", "go_binary", "go_library")

go_library(
    name = "customer_portal_lib",
    srcs = ["main.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer_portal",
    visibility = ["//visibility:private"],
    deps = [
        "//backend/app/customer_portal/service",
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/config/nacos",
        "//backend/common/rpc/database/gorm",
        "//backend/common/rpc/filters/debuglog",
        "//backend/common/rpc/filters/opentelemetry",
        "//backend/common/rpc/filters/recovery",
        "//backend/common/rpc/filters/validation",
        "//backend/common/rpc/framework",
        "//backend/common/rpc/framework/log",
        "//backend/proto/customer_portal/v1:customer_portal",
    ],
)

go_binary(
    name = "customer_portal",
    embed = [":customer_portal_lib"],
    visibility = ["//visibility:public"],
)
