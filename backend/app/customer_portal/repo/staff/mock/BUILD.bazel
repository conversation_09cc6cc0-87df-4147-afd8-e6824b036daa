load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["staff_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/staff/mock",
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@org_uber_go_mock//gomock",
    ],
)
