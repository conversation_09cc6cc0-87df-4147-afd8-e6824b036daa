package staff

import (
	"context"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	GetStaffDetail(ctx context.Context, staffID, companyID, enterpriseID int64) (
		*organizationpb.StaffModel, error)
	ListOwnerStaffInfoRequest(ctx context.Context, companyIDs []int64) (
		map[int64]*organizationpb.OwnerStaffDef, error)
}

type impl struct {
	staff organizationsvcpb.StaffServiceClient
}

func New() ReadWriter {
	return &impl{
		staff: grpc.NewClient("moego-svc-organization", organizationsvcpb.NewStaffServiceClient),
	}
}

func (i *impl) GetStaffDetail(ctx context.Context, staffID, companyID, enterpriseID int64) (
	*organizationpb.StaffModel, error) {
	resp, err := i.staff.GetStaffDetail(ctx, &organizationsvcpb.GetStaffDetailRequest{
		Id:           staffID,
		CompanyId:    companyID,
		EnterpriseId: enterpriseID,
	})
	if err != nil {
		log.ErrorContextf(ctx, "GetStaffDetail err, staffID:%d, companyID:%d, enterpriseID:%d, err:%v",
			staffID, companyID, enterpriseID, err)

		return nil, err
	}

	return resp.GetStaff(), err
}

func (i *impl) ListOwnerStaffInfoRequest(ctx context.Context, companyIDs []int64) (
	map[int64]*organizationpb.OwnerStaffDef, error) {
	resp, err := i.staff.ListOwnerStaffInfo(ctx, &organizationsvcpb.ListOwnerStaffInfoRequest{
		CompanyId: companyIDs,
	})
	if err != nil {
		log.ErrorContextf(ctx, "ListOwnerStaffInfoRequest err, companyIDs:%+v, err:%v",
			companyIDs, err)

		return nil, err
	}

	return resp.GetOwnStaffs(), err
}
