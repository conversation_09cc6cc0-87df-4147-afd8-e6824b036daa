// Code generated by MockGen. DO NOT EDIT.
// Source: ./permission/permission.go
//
// Generated by this command:
//
//	mockgen -source=./permission/permission.go -destination=./permission/mock/permission_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// GetNoPermissionList mocks base method.
func (m *MockReadWriter) GetNoPermissionList(ctx context.Context, roleID, companyID, enterpriseID int64, permissionNameList []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNoPermissionList", ctx, roleID, companyID, enterpriseID, permissionNameList)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNoPermissionList indicates an expected call of GetNoPermissionList.
func (mr *MockReadWriterMockRecorder) GetNoPermissionList(ctx, roleID, companyID, enterpriseID, permissionNameList any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNoPermissionList", reflect.TypeOf((*MockReadWriter)(nil).GetNoPermissionList), ctx, roleID, companyID, enterpriseID, permissionNameList)
}
