load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "permission",
    srcs = ["permission.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/permission",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/codec/grpc",
        "//backend/common/rpc/framework/log",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/service/permission/v1:permission",
    ],
)
