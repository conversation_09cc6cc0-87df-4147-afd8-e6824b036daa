package permission

import (
	"context"

	permissionsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/permission/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/codec/grpc"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	GetNoPermissionList(ctx context.Context, roleID, companyID, enterpriseID int64,
		permissionNameList []string) ([]string, error)
}

type impl struct {
	permission permissionsvcpb.PermissionServiceClient
}

// New creates a new impl repo
func New() ReadWriter {
	return &impl{
		permission: grpc.NewClient("moego-svc-permission", permissionsvcpb.NewPermissionServiceClient),
	}
}

func (i *impl) GetNoPermissionList(ctx context.Context, roleID, companyID, enterpriseID int64,
	permissionNameList []string) ([]string, error) {
	req := &permissionsvcpb.CheckPermissionRequest{
		RoleId:             roleID,
		PermissionNameList: permissionNameList,
	}
	if companyID > 0 {
		req.CompanyId = companyID
	}
	if enterpriseID > 0 {
		req.EnterpriseId = &enterpriseID
	}

	resp, err := i.permission.CheckPermission(ctx, req)
	if err != nil {
		log.ErrorContextf(ctx, "CheckPermission err, roleID:%d, companyID:%d, "+
			"EnterpriseId:%d, permissionNameList:%v, err:%v",
			roleID, companyID, enterpriseID, permissionNameList, err)

		return nil, err
	}

	return resp.GetNoPermissionList(), nil
}
