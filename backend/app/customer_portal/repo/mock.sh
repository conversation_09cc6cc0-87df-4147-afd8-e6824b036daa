#!/bin/bash

# 遍历当前目录下的所有go文件
exclude_files=("entity.go" "entity_test.go" "*_mock.go")
exclude_dirs=("mock")
for file in $(find . -name "*.go"); do
    # 获取文件名（不含路径）
    filename=$(basename "$file")

    # 排除不需要mock 的文件
    skip=false
    for pattern in "${exclude_files[@]}"; do
        if [[ "$filename" == $pattern ]]; then
            skip=true
            break
        fi
    done
    if [ "$skip" = true ]; then
        continue
    fi

    # 检查文件是否在排除的目录中
    for dir in "${exclude_dirs[@]}"; do
        if [[ "$file" == *"/$dir/"* ]]; then
            skip=true
            break
        fi
    done
    if [ "$skip" = true ]; then
        continue
    fi

    # 获取文件所在目录
    dir=$(dirname "$file")
    # 获取文件名（不含扩展名）
    filename=$(basename "$file" .go)

    # 创建mock目录
    mkdir -p "$dir/mock"

    echo "Processing $file"
    # 生成mock文件
    mockgen -source="$file" -destination="$dir/mock/${filename}_mock.go" -package=mock
done