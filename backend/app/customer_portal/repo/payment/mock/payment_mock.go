// Code generated by MockGen. DO NOT EDIT.
// Source: ./payment/payment.go
//
// Generated by this command:
//
//	mockgen -source=./payment/payment.go -destination=./payment/mock/payment_mock.go -package=mock
//

// Package mock is a generated GoMock package.
package mock

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockReadWriter is a mock of ReadWriter interface.
type MockReadWriter struct {
	ctrl     *gomock.Controller
	recorder *MockReadWriterMockRecorder
	isgomock struct{}
}

// MockReadWriterMockRecorder is the mock recorder for MockReadWriter.
type MockReadWriterMockRecorder struct {
	mock *MockReadWriter
}

// NewMockReadWriter creates a new mock instance.
func NewMockReadWriter(ctrl *gomock.Controller) *MockReadWriter {
	mock := &MockReadWriter{ctrl: ctrl}
	mock.recorder = &MockReadWriterMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockReadWriter) EXPECT() *MockReadWriterMockRecorder {
	return m.recorder
}

// CheckFeatureCodeIsEnableByCidPath mocks base method.
func (m *MockReadWriter) CheckFeatureCodeIsEnableByCidPath(ctx context.Context, companyID int64, code string) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckFeatureCodeIsEnableByCidPath", ctx, companyID, code)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckFeatureCodeIsEnableByCidPath indicates an expected call of CheckFeatureCodeIsEnableByCidPath.
func (mr *MockReadWriterMockRecorder) CheckFeatureCodeIsEnableByCidPath(ctx, companyID, code any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckFeatureCodeIsEnableByCidPath", reflect.TypeOf((*MockReadWriter)(nil).CheckFeatureCodeIsEnableByCidPath), ctx, companyID, code)
}
