package payment

import (
	"context"
	"fmt"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/http"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type ReadWriter interface {
	CheckFeatureCodeIsEnableByCidPath(ctx context.Context, companyID int64, code string) (bool, error)
}

type impl struct {
	cli http.Client
}

func New() ReadWriter {
	return &impl{
		cli: http.NewClientProxy("moego-service-payment"),
	}
}

const (
	checkFeatureCodeIsEnableByCidPath = "/service/payment/plan/checkFeatureCodeIsEnableByCid"
)

func (i *impl) CheckFeatureCodeIsEnableByCidPath(ctx context.Context, companyID int64, code string) (bool, error) {
	// send req
	url := fmt.Sprintf("%s?companyId=%d&code=%s", checkFeatureCodeIsEnableByCidPath, companyID, code)
	var body bool
	if err := i.cli.Get(ctx, url, &body); err != nil {
		log.ErrorContextf(ctx, "CheckFeatureCodeIsEnableByCidPath Get err, err:%v", err)

		return false, err
	}

	// parse resp
	log.InfoContextf(ctx, "checkFeatureCodeIsEnableByCidPath resp, companyID:%d, code:%s, resp body:%v",
		companyID, code, body)

	return body, nil
}
