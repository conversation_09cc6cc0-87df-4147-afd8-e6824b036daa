load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mock",
    srcs = ["online_booking_setting_mock.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/db/online_booking_setting/mock",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer_portal/repo/db/online_booking_setting",
        "@org_uber_go_mock//gomock",
    ],
)
