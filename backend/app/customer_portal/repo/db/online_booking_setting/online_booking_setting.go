package onlinebookingsetting

import (
	"context"
	"time"

	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/db"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// ReadWriter defines the interface for OnlineBookingSetting database operations.
type ReadWriter interface {
	Create(ctx context.Context, setting *OnlineBookingSetting) error
	List(ctx context.Context, datum *ListDatum) ([]*OnlineBookingSetting, error)
	Update(ctx context.Context, setting *OnlineBookingSetting) error
	Delete(ctx context.Context, id int64, staffID int64) error
}

type impl struct {
	db *gorm.DB
}

// <PERSON> creates a new instance of the OnlineBookingSetting ReadWriter.
func New() ReadWriter {
	return &impl{
		db: db.GetDB(), // 假设 db.GetDB() 提供 GORM DB 实例
	}
}

// C<PERSON> inserts a new OnlineBookingSetting record into the database.
func (i *impl) Create(ctx context.Context, setting *OnlineBookingSetting) error {
	if err := i.db.WithContext(ctx).Create(setting).Error; err != nil {
		log.ErrorContextf(ctx, "Create OnlineBookingSetting err, err:%+v", err)

		return err
	}

	return nil
}

// ListDatum defines the criteria for listing OnlineBookingSetting records.
type ListDatum struct {
	CompanyID  *int64
	BusinessID *int64
	// 可扩展更多筛选字段
}

// List retrieves OnlineBookingSetting records based on the provided criteria.
func (i *impl) List(ctx context.Context, datum *ListDatum) ([]*OnlineBookingSetting, error) {
	query := i.db.WithContext(ctx).Table("online_booking_setting").Where("deleted_at IS NULL")

	if datum.CompanyID != nil {
		query = query.Where("company_id = ?", *datum.CompanyID)
	}
	if datum.BusinessID != nil {
		query = query.Where("business_id = ?", *datum.BusinessID)
	}

	var res []*OnlineBookingSetting
	if err := query.Order("id desc").Find(&res).Error; err != nil {
		log.ErrorContextf(ctx, "List OnlineBookingSetting err, err:%+v", err)

		return nil, err
	}

	return res, nil
}

// Update updates an existing OnlineBookingSetting record.
func (i *impl) Update(ctx context.Context, setting *OnlineBookingSetting) error {
	if err := i.db.WithContext(ctx).Updates(setting).Error; err != nil {
		log.ErrorContextf(ctx, "Update OnlineBookingSetting err, setting:%+v, err:%+v", setting, err)

		return err
	}

	return nil
}

// Delete performs a soft delete on an OnlineBookingSetting record.
func (i *impl) Delete(ctx context.Context, id int64, staffID int64) error {
	if err := i.db.WithContext(ctx).Table("online_booking_setting").
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"deleted_by": staffID,
			"deleted_at": time.Now(),
		}).Error; err != nil {
		log.ErrorContextf(ctx, "Delete OnlineBookingSetting err, id:%d, staffID:%d, err:%v", id, staffID, err)

		return err
	}

	return nil
}
