load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "online_booking_setting",
    srcs = [
        "entity.go",
        "online_booking_setting.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/db/online_booking_setting",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer_portal/repo/db",
        "//backend/common/rpc/framework/log",
        "@io_gorm_gorm//:gorm",
    ],
)
