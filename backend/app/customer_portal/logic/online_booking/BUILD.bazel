load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "online_booking",
    srcs = [
        "entity.go",
        "logic.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer_portal/logic/online_booking",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer/utils",
        "//backend/app/customer_portal/constdef",
        "//backend/app/customer_portal/repo/db/online_booking_setting",
        "//backend/app/customer_portal/repo/payment",
        "//backend/app/customer_portal/repo/permission",
        "//backend/app/customer_portal/repo/staff",
        "//backend/app/customer_portal/utils",
        "//backend/common/rpc/framework/log",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_thoas_go_funk//:go-funk",
        "@org_golang_google_grpc//codes",
        "@org_golang_google_grpc//status",
    ],
)

go_test(
    name = "online_booking_test",
    srcs = ["logic_test.go"],
    embed = [":online_booking"],
    deps = [
        "//backend/app/customer/utils",
        "//backend/app/customer_portal/constdef",
        "//backend/app/customer_portal/repo/db/online_booking_setting",
        "//backend/app/customer_portal/repo/db/online_booking_setting/mock",
        "//backend/app/customer_portal/repo/payment/mock",
        "//backend/app/customer_portal/repo/permission/mock",
        "//backend/app/customer_portal/repo/staff/mock",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/organization/v1:organization",
        "@com_github_stretchr_testify//assert",
        "@org_uber_go_mock//gomock",
    ],
)
