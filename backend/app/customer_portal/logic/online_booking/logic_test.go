package onlinebooking

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	utils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/app/customer_portal/constdef"
	onlinebookingsetting "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/db/online_booking_setting"
	mockob "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/db/online_booking_setting/mock"
	mockpayment "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/payment/mock"
	mockpermission "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/permission/mock"
	mockstaff "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/staff/mock"
)

func TestLogic_SetOnlineBookingScript(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockObRepo := mockob.NewMockReadWriter(ctrl)
	mockStaffRepo := mockstaff.NewMockReadWriter(ctrl)
	mockPermissionRepo := mockpermission.NewMockReadWriter(ctrl)
	mockPaymentRepo := mockpayment.NewMockReadWriter(ctrl)

	logic := &Logic{
		obSettingRepo:     mockObRepo,
		staffService:      mockStaffRepo,
		permissionService: mockPermissionRepo,
		paymentService:    mockPaymentRepo,
	}

	css := "body { color: red; }"
	js := "console.log('hello');"
	validDatum := &SetOnlineBookingScriptDatum{
		CompanyID:  1,
		BusinessID: 2,
		StaffID:    3,
		CSS:        &css,
		JS:         &js,
	}

	// 权限相关mock返回值
	mockStaff := &organizationpb.StaffModel{}
	mockOwnerMap := map[int64]*organizationpb.OwnerStaffDef{
		validDatum.CompanyID: &organizationpb.OwnerStaffDef{},
	}
	noPermissionList := []string{}

	t.Run("invalid params - nil datum", func(t *testing.T) {
		err := logic.SetOnlineBookingScript(ctx, nil)
		assert.Error(t, err)
	})

	t.Run("invalid params - missing BusinessID", func(t *testing.T) {
		d := *validDatum
		d.BusinessID = 0
		err := logic.SetOnlineBookingScript(ctx, &d)
		assert.Error(t, err)
	})

	t.Run("invalid params - CSS and JS both nil", func(t *testing.T) {
		d := *validDatum
		d.CSS = nil
		d.JS = nil
		err := logic.SetOnlineBookingScript(ctx, &d)
		assert.Error(t, err)
	})

	t.Run("repo List error", func(t *testing.T) {
		// 权限相关mock
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(true, nil)
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(mockStaff, nil)
		mockPermissionRepo.EXPECT().GetNoPermissionList(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(noPermissionList, nil)

		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return(nil, errors.New("db error"))
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
	})

	t.Run("create new setting", func(t *testing.T) {
		// 权限相关mock
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(true, nil)
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(mockStaff, nil)
		mockPermissionRepo.EXPECT().GetNoPermissionList(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(noPermissionList, nil)

		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{}, nil)
		mockObRepo.EXPECT().Create(ctx, gomock.Any()).Return(nil)
		// mock staff service for sendReviewMsg
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(mockStaff, nil)
		mockStaffRepo.EXPECT().ListOwnerStaffInfoRequest(ctx, []int64{validDatum.CompanyID}).Return(mockOwnerMap, nil)

		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.NoError(t, err)
	})

	t.Run("create new setting error", func(t *testing.T) {
		// 权限相关mock
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(true, nil)
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(mockStaff, nil)
		mockPermissionRepo.EXPECT().GetNoPermissionList(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(noPermissionList, nil)

		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{}, nil)
		mockObRepo.EXPECT().Create(ctx, gomock.Any()).Return(errors.New("create error"))
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
	})

	t.Run("update existing setting", func(t *testing.T) {
		setting := &onlinebookingsetting.OnlineBookingSetting{
			ID:         123,
			CompanyID:  validDatum.CompanyID,
			BusinessID: validDatum.BusinessID,
			CSS:        utils.ToPointer(""),
			JS:         utils.ToPointer(""),
		}
		// 权限相关mock
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(true, nil)
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(mockStaff, nil)
		mockPermissionRepo.EXPECT().GetNoPermissionList(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(noPermissionList, nil)

		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{setting}, nil)
		mockObRepo.EXPECT().Update(ctx, gomock.Any()).Return(nil)
		// mock staff service for sendReviewMsg
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(mockStaff, nil)
		mockStaffRepo.EXPECT().ListOwnerStaffInfoRequest(ctx, []int64{validDatum.CompanyID}).Return(mockOwnerMap, nil)

		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.NoError(t, err)
	})

	t.Run("update existing setting error", func(t *testing.T) {
		setting := &onlinebookingsetting.OnlineBookingSetting{
			ID:         123,
			CompanyID:  validDatum.CompanyID,
			BusinessID: validDatum.BusinessID,
			CSS:        utils.ToPointer(""),
			JS:         utils.ToPointer(""),
		}
		// 权限相关mock
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(true, nil)
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(mockStaff, nil)
		mockPermissionRepo.EXPECT().GetNoPermissionList(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(noPermissionList, nil)

		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{setting}, nil)
		mockObRepo.EXPECT().Update(ctx, gomock.Any()).Return(errors.New("update error"))
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
	})

	t.Run("checkPermission: paymentService.CheckFeatureCodeIsEnableByCidPath error", func(t *testing.T) {
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(false, errors.New("pay error"))
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "pay error")
	})

	t.Run("checkPermission: paymentService.CheckFeatureCodeIsEnableByCidPath false", func(t *testing.T) {
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(false, nil)
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "Access denied")
	})

	t.Run("checkPermission: staffService.GetStaffDetail error", func(t *testing.T) {
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(true, nil)
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(nil, errors.New("staff error"))
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "staff error")
	})

	t.Run("checkPermission: staffService.GetStaffDetail nil", func(t *testing.T) {
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(true, nil)
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(nil, nil)
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "staff not found")
	})

	t.Run("checkPermission: staff is owner", func(t *testing.T) {
		ownerStaff := &organizationpb.StaffModel{}
		ownerStaff.EmployeeCategory = organizationpb.StaffEmployeeCategory_COMPANY_OWNER
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(true, nil)
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(ownerStaff, nil)
		// 走到这里就直接通过，不会再查 permission
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{}, nil)
		mockObRepo.EXPECT().Create(ctx, gomock.Any()).Return(nil)
		// sendReviewMsg
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(ownerStaff, nil)
		mockStaffRepo.EXPECT().ListOwnerStaffInfoRequest(ctx, []int64{validDatum.CompanyID}).Return(mockOwnerMap, nil)
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.NoError(t, err)
	})

	t.Run("checkPermission: permissionService.GetNoPermissionList error", func(t *testing.T) {
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(true, nil)
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(mockStaff, nil)
		mockPermissionRepo.EXPECT().GetNoPermissionList(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return(nil, errors.New("perm error"))
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "perm error")
	})

	t.Run("checkPermission: permissionService.GetNoPermissionList contains OBScriptPermissionName", func(t *testing.T) {
		mockPaymentRepo.EXPECT().CheckFeatureCodeIsEnableByCidPath(ctx, validDatum.CompanyID, gomock.Any()).Return(true, nil)
		mockStaffRepo.EXPECT().GetStaffDetail(ctx, validDatum.StaffID, validDatum.CompanyID, int64(0)).Return(mockStaff, nil)
		mockPermissionRepo.EXPECT().GetNoPermissionList(ctx, gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).Return([]string{constdef.OBScriptPermissionName}, nil)
		err := logic.SetOnlineBookingScript(ctx, validDatum)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "Access denied")
	})
}

func TestLogic_GetOnlineBookingScript(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	mockObRepo := mockob.NewMockReadWriter(ctrl)
	mockStaffRepo := mockstaff.NewMockReadWriter(ctrl)

	logic := &Logic{
		obSettingRepo: mockObRepo,
		staffService:  mockStaffRepo,
	}

	t.Run("invalid params - nil datum", func(t *testing.T) {
		res, err := logic.GetOnlineBookingScript(ctx, nil)
		assert.Error(t, err)
		assert.Nil(t, res)
	})

	t.Run("invalid params - missing BusinessID", func(t *testing.T) {
		res, err := logic.GetOnlineBookingScript(ctx, &GetOnlineBookingScriptDatum{})
		assert.Error(t, err)
		assert.Nil(t, res)
	})

	t.Run("repo List error", func(t *testing.T) {
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return(nil, errors.New("db error"))
		res, err := logic.GetOnlineBookingScript(ctx, &GetOnlineBookingScriptDatum{BusinessID: 2})
		assert.Error(t, err)
		assert.Nil(t, res)
	})

	t.Run("no settings found", func(t *testing.T) {
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{}, nil)
		res, err := logic.GetOnlineBookingScript(ctx, &GetOnlineBookingScriptDatum{BusinessID: 2})
		assert.NoError(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, "", res.CSS)
		assert.Equal(t, "", res.JS)
	})

	t.Run("settings found", func(t *testing.T) {
		setting := &onlinebookingsetting.OnlineBookingSetting{
			ID:         123,
			CompanyID:  1,
			BusinessID: 2,
			CSS:        utils.ToPointer("body { color: red; }"),
			JS:         utils.ToPointer("console.log('hello');"),
		}
		mockObRepo.EXPECT().List(ctx, gomock.Any()).Return([]*onlinebookingsetting.OnlineBookingSetting{setting}, nil)
		res, err := logic.GetOnlineBookingScript(ctx, &GetOnlineBookingScriptDatum{BusinessID: 2})
		assert.NoError(t, err)
		assert.NotNil(t, res)
		assert.Equal(t, utils.ToValue(setting.CSS), res.CSS)
		assert.Equal(t, utils.ToValue(setting.JS), res.JS)
	})
}
