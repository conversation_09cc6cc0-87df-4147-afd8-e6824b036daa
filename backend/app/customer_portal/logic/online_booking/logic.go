package onlinebooking

import (
	"context"
	"fmt"
	"strings"
	"time"

	funk "github.com/thoas/go-funk"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	utils "github.com/MoeGolibrary/moego/backend/app/customer/utils"
	"github.com/MoeGolibrary/moego/backend/app/customer_portal/constdef"
	onlinebookingsetting "github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/db/online_booking_setting"
	"github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/payment"
	"github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/permission"
	"github.com/MoeGolibrary/moego/backend/app/customer_portal/repo/staff"
	customerportalutils "github.com/MoeGolibrary/moego/backend/app/customer_portal/utils"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

type Logic struct {
	obSettingRepo     onlinebookingsetting.ReadWriter
	staffService      staff.ReadWriter
	permissionService permission.ReadWriter
	paymentService    payment.ReadWriter
}

func NewLogic() *Logic {
	return &Logic{
		obSettingRepo:     onlinebookingsetting.New(),
		staffService:      staff.New(),
		permissionService: permission.New(),
		paymentService:    payment.New(),
	}
}

func (l *Logic) SetOnlineBookingScript(ctx context.Context, datum *SetOnlineBookingScriptDatum) error {
	// check
	if datum == nil || datum.BusinessID == 0 ||
		datum.CompanyID == 0 || datum.StaffID == 0 ||
		(datum.CSS == nil && datum.JS == nil) {
		log.InfoContextf(ctx, "SetOnlineBookingScript params is invalid, datum:%+v", datum)

		return status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// check permission
	if err := l.checkPermission(ctx, datum.CompanyID, datum.StaffID); err != nil {
		return err
	}

	// get
	settings, err := l.obSettingRepo.List(ctx, &onlinebookingsetting.ListDatum{
		BusinessID: &datum.BusinessID,
	})
	if err != nil {
		return err
	}

	// create or update
	if len(settings) == 0 {
		// save db
		createDB := &onlinebookingsetting.OnlineBookingSetting{
			CompanyID:  datum.CompanyID,
			BusinessID: datum.BusinessID,
			CSS:        datum.CSS,
			JS:         datum.JS,
			CreatedBy:  datum.StaffID,
			UpdatedBy:  datum.StaffID,
			CreatedAt:  time.Now(),
			UpdatedAt:  time.Now(),
		}
		if createDB.CSS == nil {
			createDB.CSS = utils.ToPointer("")
		}
		if createDB.JS == nil {
			createDB.JS = utils.ToPointer("")
		}
		if err := l.obSettingRepo.Create(ctx, createDB); err != nil {
			return err
		}
	} else {
		// update db
		updateDB := &onlinebookingsetting.OnlineBookingSetting{
			ID:        settings[0].ID,
			UpdatedBy: datum.StaffID,
			UpdatedAt: time.Now(),
		}
		if datum.CSS != nil {
			updateDB.CSS = datum.CSS
		}
		if datum.JS != nil {
			updateDB.JS = datum.JS
		}
		if err := l.obSettingRepo.Update(ctx, updateDB); err != nil {
			return err
		}
	}

	// send review msg, optional
	l.sendReviewMsg(ctx, datum)

	return nil
}

func (l *Logic) sendReviewMsg(ctx context.Context, datum *SetOnlineBookingScriptDatum) {
	// load staff/ownerStaff data
	var (
		optStaff *organizationpb.StaffModel
		owner    *organizationpb.OwnerStaffDef
	)
	if datum.StaffID > 0 {
		optStaff, _ = l.staffService.GetStaffDetail(ctx, datum.StaffID, datum.CompanyID, 0)
	}
	if datum.CompanyID > 0 {
		ownerMap, _ := l.staffService.ListOwnerStaffInfoRequest(ctx, []int64{datum.CompanyID})
		owner = ownerMap[datum.CompanyID]
	}

	// build msg
	var msgBuilder strings.Builder
	msgBuilder.WriteString("【Set OB Script Review】\n")
	msgBuilder.WriteString(fmt.Sprintf("CompanyID: %d\n", datum.CompanyID))
	msgBuilder.WriteString(fmt.Sprintf("BusinessID: %d\n", datum.BusinessID))
	msgBuilder.WriteString(fmt.Sprintf("Edit Staff ID: %d\n", datum.StaffID))
	msgBuilder.WriteString(fmt.Sprintf("Edit Staff Email: %s\n", optStaff.GetProfileEmail()))
	msgBuilder.WriteString(fmt.Sprintf("Company Owner ID: %d\n",
		owner.GetActiveStaff().GetStaff().GetId()))
	msgBuilder.WriteString(fmt.Sprintf("Company Owner Email: %s\n",
		owner.GetActiveStaff().GetStaffEmailDef().GetEmail()))
	msgBuilder.WriteString(fmt.Sprintf("CSS: %s\n", utils.ToValue(datum.CSS)))
	msgBuilder.WriteString(fmt.Sprintf("JS: %s\n", utils.ToValue(datum.JS)))
	msgBuilder.WriteString(fmt.Sprintf("UTC Time: %s\n", time.Now().Format("2006-01-02 15:04:05")))

	// send to slack
	if err := customerportalutils.SlackPostMessage(ctx,
		customerportalutils.SlackReviewOBScriptChannelID,
		customerportalutils.SlackHarvieBotToken,
		msgBuilder.String()); err != nil {
		log.ErrorContextf(ctx, "SetOnlineBookingScript SlackPostMessage err, err:%v", err)
	}
}

func (l *Logic) GetOnlineBookingScript(ctx context.Context, datum *GetOnlineBookingScriptDatum) (*ScriptDatum, error) {
	// check
	if datum == nil || datum.BusinessID == 0 {
		log.InfoContextf(ctx, "GetOnlineBookingScript params is invalid, datum:%+v", datum)

		return nil, status.Errorf(codes.InvalidArgument, "params is invalid")
	}

	// list
	settings, err := l.obSettingRepo.List(ctx, &onlinebookingsetting.ListDatum{
		BusinessID: &datum.BusinessID,
	})
	if err != nil {
		return nil, err
	}
	if len(settings) == 0 {
		log.InfoContextf(ctx, "GetOnlineBookingScript settings is empty, businessID:%d", datum.BusinessID)

		return &ScriptDatum{}, nil
	}

	log.InfoContextf(ctx, "GetOnlineBookingScript settings, businessID:%d, setting:%+v", datum.BusinessID, settings[0])

	return &ScriptDatum{CSS: utils.ToValue(settings[0].CSS), JS: utils.ToValue(settings[0].JS)}, nil
}

func (l *Logic) checkPermission(ctx context.Context, companyID, staffID int64) error {
	// check company feature_code
	checkCodeRes, err := l.paymentService.CheckFeatureCodeIsEnableByCidPath(ctx, companyID,
		constdef.OBScriptFeatureCode)
	if err != nil {
		return err
	}
	if !checkCodeRes {
		log.InfoContextf(ctx, "checkPermission checkOBScriptFeatureCode fail, companyID:%d", companyID)

		return status.Errorf(codes.PermissionDenied, "Error: Access denied. Please contact your administrator.")
	}

	// check role permission
	staff, err := l.staffService.GetStaffDetail(ctx, staffID, companyID, 0)
	if err != nil {
		return err
	}
	if staff == nil {
		return status.Errorf(codes.InvalidArgument, "staff not found")
	}

	// owner has all permission
	if constdef.CompanyOwnerEmployCategoryMap[staff.GetEmployeeCategory()] {
		return nil
	}

	// get role permission
	noPermissionList, err := l.permissionService.GetNoPermissionList(ctx,
		staff.GetRoleId(), companyID, 0, []string{constdef.OBScriptPermissionName})
	if err != nil {
		return err
	}
	if funk.ContainsString(noPermissionList, constdef.OBScriptPermissionName) {
		log.InfoContextf(ctx, "checkPermission checkOBScriptPermission fail, companyID:%d, staffID:%d",
			companyID, staffID)

		return status.Errorf(codes.PermissionDenied, "Error: Access denied. Please contact your administrator.")
	}

	return nil
}
