load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "service",
    srcs = ["customer_portal_service.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/app/customer_portal/service",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/app/customer_portal/logic/online_booking",
        "//backend/proto/customer_portal/v1:customer_portal",
    ],
)
