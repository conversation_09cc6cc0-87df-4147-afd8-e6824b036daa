package service

import (
	"context"

	onlinebooking "github.com/MoeGolibrary/moego/backend/app/customer_portal/logic/online_booking"
	customerportalpb "github.com/MoeGolibrary/moego/backend/proto/customer_portal/v1"
)

type CustomerPortalService struct {
	oll *onlinebooking.Logic
	customerportalpb.UnimplementedCustomerPortalServiceServer
}

func NewCustomerPortalService() *CustomerPortalService {
	return &CustomerPortalService{
		oll: onlinebooking.NewLogic(),
	}
}

func (s *CustomerPortalService) SetOnlineBookingScript(ctx context.Context,
	req *customerportalpb.SetOnlineBookingScriptRequest) (
	*customerportalpb.SetOnlineBookingScriptResponse, error) {
	// TODO check crud
	if err := s.oll.SetOnlineBookingScript(ctx, &onlinebooking.SetOnlineBookingScriptDatum{
		CompanyID:  req.GetCompanyId(),
		BusinessID: req.GetBusinessId(),
		StaffID:    req.GetStaffId(),
		CSS:        req.Css,
		JS:         req.Js,
	}); err != nil {
		return nil, err
	}

	return &customerportalpb.SetOnlineBookingScriptResponse{}, nil
}

func (s *CustomerPortalService) GetOnlineBookingScript(ctx context.Context,
	req *customerportalpb.GetOnlineBookingScriptRequest) (
	*customerportalpb.GetOnlineBookingScriptResponse, error) {
	script, err := s.oll.GetOnlineBookingScript(ctx,
		&onlinebooking.GetOnlineBookingScriptDatum{BusinessID: req.GetBusinessId()})
	if err != nil {
		return nil, err
	}

	return &customerportalpb.GetOnlineBookingScriptResponse{Css: script.CSS, Js: script.JS}, nil
}
