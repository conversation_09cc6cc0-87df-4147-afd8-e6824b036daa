syntax = "proto3";

package backend.proto.authn.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/authn/v1;authnpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.authn.v1";

import "buf/validate/validate.proto";
import "google/protobuf/duration.proto";
import "backend/proto/authn/v1/mfa_management_service.proto";

// AuthnService 负责用户认证的所有方面。
// 其核心功能是 `Login` RPC，该接口支持多步骤流程以兼容多因素认证（MFA），从而增强系统安全性。
// 此外，它还提供了重新发送 MFA 验证码的机制。
service AuthnService {
  // (-- api-linter: core::0136::verb-noun=disabled
  //     aip.dev/not-precedent: We need to do this because login is a common naming. --)
  //
  // Login 用于处理用户登录。如果用户已启用 MFA，整个登录流程可能涉及多个步骤。
  //
  // MFA 多步登录流程：
  // 1.  **首次调用：** 客户端通过发送用户的 `email`、`password` 及其他设备信息来发起登录请求。
  // 2.  **MFA 质询：** 如果需要进行 MFA 验证，服务器将返回 `require_mfa = true` 和 `factor`（指明所用的MFA方式）。
  //     此时，响应中不会包含 `session_token`。客户端在收到此响应后，应调用 `SendLoginChallengeCode` 来发送验证码。
  // 3.  **二次调用：** 客户端必须再次发起 `Login` 请求，其中需包含原始的 `email`、`password` 以及 `SendLoginChallengeCode`
  //     返回的 `challenge_token` 和用户输入的 `challenge_code`。
  // 4.  **会话授予：** 在成功验证 MFA 质询后，服务器将返回一个 `session_token`。
  rpc Login(LoginRequest) returns (LoginResponse);

  // SendLoginChallengeCode 用于在 MFA 流程中发送验证码。
  // 当 `Login` 接口返回 `require_mfa=true` 时，客户端必须调用此接口来触发验证码的发送。
  // 用户也可以使用此接口来重新发送验证码。该接口会做限流处理。
  rpc SendLoginChallengeCode(SendLoginChallengeCodeRequest) returns (SendLoginChallengeCodeResponse);
}

// Login RPC 的请求消息。
message LoginRequest {
  // 用户的电子邮件地址。
  string email = 1 [(buf.validate.field).string.min_len = 1];
  // 用户的密码。
  string password = 2 [(buf.validate.field).string.min_len = 1];

  // 用于进行中的 MFA 质询的令牌。此字段为 MFA 登录第二步的必需参数。
  optional string challenge_token = 3 [(buf.validate.field).string.min_len = 1];
  // 用户收到的 MFA 验证码。此字段在 MFA 登录流程的第二步为必需参数。
  // 为了兼容未来可能出现的不同格式的验证码，此处不限制其长度或字符类型。
  optional string challenge_code = 4;

  // 客户端的 IP 地址，从请求头的 `x-forwarded-for` 解析。
  string ip = 5 [(buf.validate.field).string.ip = true];
  // 用户代理，从请求头的 `user-agent` 解析。
  string user_agent = 6 [(buf.validate.field).string.min_len = 1];
  // 来源链接，从请求头的 `referer` 解析。
  string referer_link = 7;
  // 来源会话 ID，从请求头的 `x-moe-session-id` 解析。
  int64 referer_session_id = 8 [(buf.validate.field).int64.gte = 0];
  // 客户端的设备 ID，从请求头的 `x-moe-device-id` 解析。
  string device_id = 9 [(buf.validate.field).string.min_len = 1];

  // 登录请求的来源。
  Source source = 10 [(buf.validate.field).enum = {
    defined_only: true;
    not_in: [0]
  }];

  // 登录请求的来源枚举。
  enum Source {
    // 未指定。
    SOURCE_UNSPECIFIED = 0;
    // Business (B web / B App)。
    BUSINESS = 1;
  }
}

// Login RPC 的响应消息。
message LoginResponse {
  // 登录成功后返回的会话令牌。
  optional string session_token = 1;

  // 会话令牌的最大有效期。
  optional google.protobuf.Duration session_max_age = 2;

  // 若为 `true`，则表示用户必须完成 MFA 质询。
  bool require_mfa = 5;

  // 如果需要 MFA，此字段会包含用于验证的认证方式信息。
  optional AuthenticationFactor factor = 7;
}

// SendLoginChallengeCode RPC 的请求消息。
message SendLoginChallengeCodeRequest {
  // 与登录尝试关联的用户电子邮件地址。
  string email = 1 [(buf.validate.field).string.min_len = 1];
}

// SendLoginChallengeCode RPC 的响应消息。
message SendLoginChallengeCodeResponse {
  // 新生成的质询令牌，用于后续的 `Login` 请求。
  string challenge_token = 1;
}

// ErrCode 定义错误码枚举
// 
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because 
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 997100; 
}