// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/authn/v1/mfa_management_service.proto

package authnpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 定义多因素认证（MFA）方法的类型。
type FactorType int32

const (
	// 未指定。
	FactorType_FACTOR_TYPE_UNSPECIFIED FactorType = 0
	// 基于手机号的认证（例如，短信）。
	FactorType_PHONE_NUMBER FactorType = 1
)

// Enum value maps for FactorType.
var (
	FactorType_name = map[int32]string{
		0: "FACTOR_TYPE_UNSPECIFIED",
		1: "PHONE_NUMBER",
	}
	FactorType_value = map[string]int32{
		"FACTOR_TYPE_UNSPECIFIED": 0,
		"PHONE_NUMBER":            1,
	}
)

func (x FactorType) Enum() *FactorType {
	p := new(FactorType)
	*p = x
	return p
}

func (x FactorType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (FactorType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_authn_v1_mfa_management_service_proto_enumTypes[0].Descriptor()
}

func (FactorType) Type() protoreflect.EnumType {
	return &file_backend_proto_authn_v1_mfa_management_service_proto_enumTypes[0]
}

func (x FactorType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use FactorType.Descriptor instead.
func (FactorType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP(), []int{0}
}

// 代表一个已注册的认证方式的信息。
type AuthenticationFactor struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 此认证方式的唯一标识符。
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// 认证方式的类型。
	Type FactorType `protobuf:"varint,2,opt,name=type,proto3,enum=backend.proto.authn.v1.FactorType" json:"type,omitempty"`
	// 特定类型的详细信息。
	//
	// Types that are valid to be assigned to Details:
	//
	//	*AuthenticationFactor_PhoneNumber_
	Details       isAuthenticationFactor_Details `protobuf_oneof:"details"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuthenticationFactor) Reset() {
	*x = AuthenticationFactor{}
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthenticationFactor) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticationFactor) ProtoMessage() {}

func (x *AuthenticationFactor) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticationFactor.ProtoReflect.Descriptor instead.
func (*AuthenticationFactor) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP(), []int{0}
}

func (x *AuthenticationFactor) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AuthenticationFactor) GetType() FactorType {
	if x != nil {
		return x.Type
	}
	return FactorType_FACTOR_TYPE_UNSPECIFIED
}

func (x *AuthenticationFactor) GetDetails() isAuthenticationFactor_Details {
	if x != nil {
		return x.Details
	}
	return nil
}

func (x *AuthenticationFactor) GetPhoneNumber() *AuthenticationFactor_PhoneNumber {
	if x != nil {
		if x, ok := x.Details.(*AuthenticationFactor_PhoneNumber_); ok {
			return x.PhoneNumber
		}
	}
	return nil
}

type isAuthenticationFactor_Details interface {
	isAuthenticationFactor_Details()
}

type AuthenticationFactor_PhoneNumber_ struct {
	// 当类型为 PHONE_NUMBER 时，此字段将被填充。
	PhoneNumber *AuthenticationFactor_PhoneNumber `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3,oneof"`
}

func (*AuthenticationFactor_PhoneNumber_) isAuthenticationFactor_Details() {}

// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: We don't need pagination for now. --)
//
// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: We don't need pagination for now. --)
//
// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: We don't use parent field. --)
//
// ListAuthenticationFactors RPC 的请求消息。
type ListAuthenticationFactorsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 需要列出认证方式的用户账户 ID。
	AccountId     int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAuthenticationFactorsRequest) Reset() {
	*x = ListAuthenticationFactorsRequest{}
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAuthenticationFactorsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAuthenticationFactorsRequest) ProtoMessage() {}

func (x *ListAuthenticationFactorsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAuthenticationFactorsRequest.ProtoReflect.Descriptor instead.
func (*ListAuthenticationFactorsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP(), []int{1}
}

func (x *ListAuthenticationFactorsRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

// (-- api-linter: core::0158::response-next-page-token-field=disabled
//
//	aip.dev/not-precedent: We don't need pagination for now. --)
//
// ListAuthenticationFactors RPC 的响应消息。
type ListAuthenticationFactorsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 为该用户注册的 MFA 认证方式列表。
	AuthenticationFactors []*AuthenticationFactor `protobuf:"bytes,1,rep,name=authentication_factors,json=authenticationFactors,proto3" json:"authentication_factors,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ListAuthenticationFactorsResponse) Reset() {
	*x = ListAuthenticationFactorsResponse{}
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAuthenticationFactorsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAuthenticationFactorsResponse) ProtoMessage() {}

func (x *ListAuthenticationFactorsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAuthenticationFactorsResponse.ProtoReflect.Descriptor instead.
func (*ListAuthenticationFactorsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListAuthenticationFactorsResponse) GetAuthenticationFactors() []*AuthenticationFactor {
	if x != nil {
		return x.AuthenticationFactors
	}
	return nil
}

// SendPhoneNumberVerificationCode RPC 的请求消息。
type SendPhoneNumberVerificationCodeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户的账户 ID。
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// 将接收验证码的手机号码，E.164 格式（例如，+***********）。
	E164Number string `protobuf:"bytes,2,opt,name=e164_number,json=e164Number,proto3" json:"e164_number,omitempty"`
	// 手机号的区域代码（例如，"US"）。
	RegionCode    string `protobuf:"bytes,3,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendPhoneNumberVerificationCodeRequest) Reset() {
	*x = SendPhoneNumberVerificationCodeRequest{}
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPhoneNumberVerificationCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPhoneNumberVerificationCodeRequest) ProtoMessage() {}

func (x *SendPhoneNumberVerificationCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPhoneNumberVerificationCodeRequest.ProtoReflect.Descriptor instead.
func (*SendPhoneNumberVerificationCodeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP(), []int{3}
}

func (x *SendPhoneNumberVerificationCodeRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *SendPhoneNumberVerificationCodeRequest) GetE164Number() string {
	if x != nil {
		return x.E164Number
	}
	return ""
}

func (x *SendPhoneNumberVerificationCodeRequest) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

// SendPhoneNumberVerificationCode RPC 的响应消息。
type SendPhoneNumberVerificationCodeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 代表此验证过程的临时一次性令牌。
	// 此令牌需要在 `AddPhoneNumberFactor` RPC 中使用。
	VerificationToken string `protobuf:"bytes,1,opt,name=verification_token,json=verificationToken,proto3" json:"verification_token,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *SendPhoneNumberVerificationCodeResponse) Reset() {
	*x = SendPhoneNumberVerificationCodeResponse{}
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendPhoneNumberVerificationCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendPhoneNumberVerificationCodeResponse) ProtoMessage() {}

func (x *SendPhoneNumberVerificationCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendPhoneNumberVerificationCodeResponse.ProtoReflect.Descriptor instead.
func (*SendPhoneNumberVerificationCodeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP(), []int{4}
}

func (x *SendPhoneNumberVerificationCodeResponse) GetVerificationToken() string {
	if x != nil {
		return x.VerificationToken
	}
	return ""
}

// AddPhoneNumberFactor RPC 的请求消息。
type AddPhoneNumberFactorRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 要关联新认证方式的用户账户 ID。
	AccountId int64 `protobuf:"varint,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// 手机号码的 E.164 格式。
	E164Number string `protobuf:"bytes,2,opt,name=e164_number,json=e164Number,proto3" json:"e164_number,omitempty"`
	// 手机号的区域代码。
	RegionCode string `protobuf:"bytes,3,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"`
	// 从 `SendPhoneNumberVerificationCode` 响应中获取的令牌。
	VerificationToken string `protobuf:"bytes,4,opt,name=verification_token,json=verificationToken,proto3" json:"verification_token,omitempty"`
	// 用户收到的验证码。
	// 为了兼容未来可能出现的不同格式的验证码，此处不限制其长度或字符类型。
	VerificationCode string `protobuf:"bytes,5,opt,name=verification_code,json=verificationCode,proto3" json:"verification_code,omitempty"`
	// 客户端的设备 ID。如果提供，该设备将在验证成功后被添加为受信任设备。
	DeviceId      *string `protobuf:"bytes,6,opt,name=device_id,json=deviceId,proto3,oneof" json:"device_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddPhoneNumberFactorRequest) Reset() {
	*x = AddPhoneNumberFactorRequest{}
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPhoneNumberFactorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPhoneNumberFactorRequest) ProtoMessage() {}

func (x *AddPhoneNumberFactorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPhoneNumberFactorRequest.ProtoReflect.Descriptor instead.
func (*AddPhoneNumberFactorRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP(), []int{5}
}

func (x *AddPhoneNumberFactorRequest) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *AddPhoneNumberFactorRequest) GetE164Number() string {
	if x != nil {
		return x.E164Number
	}
	return ""
}

func (x *AddPhoneNumberFactorRequest) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

func (x *AddPhoneNumberFactorRequest) GetVerificationToken() string {
	if x != nil {
		return x.VerificationToken
	}
	return ""
}

func (x *AddPhoneNumberFactorRequest) GetVerificationCode() string {
	if x != nil {
		return x.VerificationCode
	}
	return ""
}

func (x *AddPhoneNumberFactorRequest) GetDeviceId() string {
	if x != nil && x.DeviceId != nil {
		return *x.DeviceId
	}
	return ""
}

// AddPhoneNumberFactor RPC 的响应消息。
type AddPhoneNumberFactorResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 关于新创建的认证方式的信息。
	AuthenticationFactor *AuthenticationFactor `protobuf:"bytes,1,opt,name=authentication_factor,json=authenticationFactor,proto3" json:"authentication_factor,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *AddPhoneNumberFactorResponse) Reset() {
	*x = AddPhoneNumberFactorResponse{}
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddPhoneNumberFactorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddPhoneNumberFactorResponse) ProtoMessage() {}

func (x *AddPhoneNumberFactorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddPhoneNumberFactorResponse.ProtoReflect.Descriptor instead.
func (*AddPhoneNumberFactorResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP(), []int{6}
}

func (x *AddPhoneNumberFactorResponse) GetAuthenticationFactor() *AuthenticationFactor {
	if x != nil {
		return x.AuthenticationFactor
	}
	return nil
}

// DeleteAuthenticationFactor RPC 的请求消息。
type DeleteAuthenticationFactorRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 要删除的认证方式的唯一标识符。
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAuthenticationFactorRequest) Reset() {
	*x = DeleteAuthenticationFactorRequest{}
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAuthenticationFactorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAuthenticationFactorRequest) ProtoMessage() {}

func (x *DeleteAuthenticationFactorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAuthenticationFactorRequest.ProtoReflect.Descriptor instead.
func (*DeleteAuthenticationFactorRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteAuthenticationFactorRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// 针对 PHONE_NUMBER 类型的详细信息。
type AuthenticationFactor_PhoneNumber struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// E.164 格式的手机号 (例如, "+12223331234")。
	E164Number string `protobuf:"bytes,1,opt,name=e164_number,json=e164Number,proto3" json:"e164_number,omitempty"`
	// 手机号的区域代码 (例如, "US")。
	RegionCode    string `protobuf:"bytes,2,opt,name=region_code,json=regionCode,proto3" json:"region_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AuthenticationFactor_PhoneNumber) Reset() {
	*x = AuthenticationFactor_PhoneNumber{}
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AuthenticationFactor_PhoneNumber) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AuthenticationFactor_PhoneNumber) ProtoMessage() {}

func (x *AuthenticationFactor_PhoneNumber) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AuthenticationFactor_PhoneNumber.ProtoReflect.Descriptor instead.
func (*AuthenticationFactor_PhoneNumber) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *AuthenticationFactor_PhoneNumber) GetE164Number() string {
	if x != nil {
		return x.E164Number
	}
	return ""
}

func (x *AuthenticationFactor_PhoneNumber) GetRegionCode() string {
	if x != nil {
		return x.RegionCode
	}
	return ""
}

var File_backend_proto_authn_v1_mfa_management_service_proto protoreflect.FileDescriptor

const file_backend_proto_authn_v1_mfa_management_service_proto_rawDesc = "" +
	"\n" +
	"3backend/proto/authn/v1/mfa_management_service.proto\x12\x16backend.proto.authn.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\"\x99\x02\n" +
	"\x14AuthenticationFactor\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x126\n" +
	"\x04type\x18\x02 \x01(\x0e2\".backend.proto.authn.v1.FactorTypeR\x04type\x12]\n" +
	"\fphone_number\x18\x04 \x01(\v28.backend.proto.authn.v1.AuthenticationFactor.PhoneNumberH\x00R\vphoneNumber\x1aO\n" +
	"\vPhoneNumber\x12\x1f\n" +
	"\ve164_number\x18\x01 \x01(\tR\n" +
	"e164Number\x12\x1f\n" +
	"\vregion_code\x18\x02 \x01(\tR\n" +
	"regionCodeB\t\n" +
	"\adetails\"J\n" +
	" ListAuthenticationFactorsRequest\x12&\n" +
	"\n" +
	"account_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\taccountId\"\x88\x01\n" +
	"!ListAuthenticationFactorsResponse\x12c\n" +
	"\x16authentication_factors\x18\x01 \x03(\v2,.backend.proto.authn.v1.AuthenticationFactorR\x15authenticationFactors\"\x8c\x02\n" +
	"&SendPhoneNumberVerificationCodeRequest\x12&\n" +
	"\n" +
	"account_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\taccountId\x12\x8e\x01\n" +
	"\ve164_number\x18\x02 \x01(\tBm\xbaHj\xba\x01g\n" +
	"\x12e164_number_format\x12,Please input a phone number in E.164 format.\x1a#this.matches('^\\\\+[1-9]\\\\d{1,14}$')R\n" +
	"e164Number\x12)\n" +
	"\vregion_code\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x98\x01\x02R\n" +
	"regionCode\"X\n" +
	"'SendPhoneNumberVerificationCodeResponse\x12-\n" +
	"\x12verification_token\x18\x01 \x01(\tR\x11verificationToken\"\x9f\x03\n" +
	"\x1bAddPhoneNumberFactorRequest\x12&\n" +
	"\n" +
	"account_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\taccountId\x12\x8e\x01\n" +
	"\ve164_number\x18\x02 \x01(\tBm\xbaHj\xba\x01g\n" +
	"\x12e164_number_format\x12,Please input a phone number in E.164 format.\x1a#this.matches('^\\\\+[1-9]\\\\d{1,14}$')R\n" +
	"e164Number\x12)\n" +
	"\vregion_code\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x98\x01\x02R\n" +
	"regionCode\x126\n" +
	"\x12verification_token\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x11verificationToken\x12+\n" +
	"\x11verification_code\x18\x05 \x01(\tR\x10verificationCode\x12)\n" +
	"\tdevice_id\x18\x06 \x01(\tB\a\xbaH\x04r\x02\x10\x01H\x00R\bdeviceId\x88\x01\x01B\f\n" +
	"\n" +
	"_device_id\"\x81\x01\n" +
	"\x1cAddPhoneNumberFactorResponse\x12a\n" +
	"\x15authentication_factor\x18\x01 \x01(\v2,.backend.proto.authn.v1.AuthenticationFactorR\x14authenticationFactor\"<\n" +
	"!DeleteAuthenticationFactorRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id*;\n" +
	"\n" +
	"FactorType\x12\x1b\n" +
	"\x17FACTOR_TYPE_UNSPECIFIED\x10\x00\x12\x10\n" +
	"\fPHONE_NUMBER\x10\x012\xc3\x04\n" +
	"\x14MfaManagementService\x12\x90\x01\n" +
	"\x19ListAuthenticationFactors\x128.backend.proto.authn.v1.ListAuthenticationFactorsRequest\x1a9.backend.proto.authn.v1.ListAuthenticationFactorsResponse\x12\xa2\x01\n" +
	"\x1fSendPhoneNumberVerificationCode\x12>.backend.proto.authn.v1.SendPhoneNumberVerificationCodeRequest\x1a?.backend.proto.authn.v1.SendPhoneNumberVerificationCodeResponse\x12\x81\x01\n" +
	"\x14AddPhoneNumberFactor\x123.backend.proto.authn.v1.AddPhoneNumberFactorRequest\x1a4.backend.proto.authn.v1.AddPhoneNumberFactorResponse\x12o\n" +
	"\x1aDeleteAuthenticationFactor\x129.backend.proto.authn.v1.DeleteAuthenticationFactorRequest\x1a\x16.google.protobuf.EmptyBb\n" +
	" com.moego.backend.proto.authn.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/authn/v1;authnpbb\x06proto3"

var (
	file_backend_proto_authn_v1_mfa_management_service_proto_rawDescOnce sync.Once
	file_backend_proto_authn_v1_mfa_management_service_proto_rawDescData []byte
)

func file_backend_proto_authn_v1_mfa_management_service_proto_rawDescGZIP() []byte {
	file_backend_proto_authn_v1_mfa_management_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_authn_v1_mfa_management_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_authn_v1_mfa_management_service_proto_rawDesc), len(file_backend_proto_authn_v1_mfa_management_service_proto_rawDesc)))
	})
	return file_backend_proto_authn_v1_mfa_management_service_proto_rawDescData
}

var file_backend_proto_authn_v1_mfa_management_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_backend_proto_authn_v1_mfa_management_service_proto_goTypes = []any{
	(FactorType)(0),                                 // 0: backend.proto.authn.v1.FactorType
	(*AuthenticationFactor)(nil),                    // 1: backend.proto.authn.v1.AuthenticationFactor
	(*ListAuthenticationFactorsRequest)(nil),        // 2: backend.proto.authn.v1.ListAuthenticationFactorsRequest
	(*ListAuthenticationFactorsResponse)(nil),       // 3: backend.proto.authn.v1.ListAuthenticationFactorsResponse
	(*SendPhoneNumberVerificationCodeRequest)(nil),  // 4: backend.proto.authn.v1.SendPhoneNumberVerificationCodeRequest
	(*SendPhoneNumberVerificationCodeResponse)(nil), // 5: backend.proto.authn.v1.SendPhoneNumberVerificationCodeResponse
	(*AddPhoneNumberFactorRequest)(nil),             // 6: backend.proto.authn.v1.AddPhoneNumberFactorRequest
	(*AddPhoneNumberFactorResponse)(nil),            // 7: backend.proto.authn.v1.AddPhoneNumberFactorResponse
	(*DeleteAuthenticationFactorRequest)(nil),       // 8: backend.proto.authn.v1.DeleteAuthenticationFactorRequest
	(*AuthenticationFactor_PhoneNumber)(nil),        // 9: backend.proto.authn.v1.AuthenticationFactor.PhoneNumber
	(*emptypb.Empty)(nil),                           // 10: google.protobuf.Empty
}
var file_backend_proto_authn_v1_mfa_management_service_proto_depIdxs = []int32{
	0,  // 0: backend.proto.authn.v1.AuthenticationFactor.type:type_name -> backend.proto.authn.v1.FactorType
	9,  // 1: backend.proto.authn.v1.AuthenticationFactor.phone_number:type_name -> backend.proto.authn.v1.AuthenticationFactor.PhoneNumber
	1,  // 2: backend.proto.authn.v1.ListAuthenticationFactorsResponse.authentication_factors:type_name -> backend.proto.authn.v1.AuthenticationFactor
	1,  // 3: backend.proto.authn.v1.AddPhoneNumberFactorResponse.authentication_factor:type_name -> backend.proto.authn.v1.AuthenticationFactor
	2,  // 4: backend.proto.authn.v1.MfaManagementService.ListAuthenticationFactors:input_type -> backend.proto.authn.v1.ListAuthenticationFactorsRequest
	4,  // 5: backend.proto.authn.v1.MfaManagementService.SendPhoneNumberVerificationCode:input_type -> backend.proto.authn.v1.SendPhoneNumberVerificationCodeRequest
	6,  // 6: backend.proto.authn.v1.MfaManagementService.AddPhoneNumberFactor:input_type -> backend.proto.authn.v1.AddPhoneNumberFactorRequest
	8,  // 7: backend.proto.authn.v1.MfaManagementService.DeleteAuthenticationFactor:input_type -> backend.proto.authn.v1.DeleteAuthenticationFactorRequest
	3,  // 8: backend.proto.authn.v1.MfaManagementService.ListAuthenticationFactors:output_type -> backend.proto.authn.v1.ListAuthenticationFactorsResponse
	5,  // 9: backend.proto.authn.v1.MfaManagementService.SendPhoneNumberVerificationCode:output_type -> backend.proto.authn.v1.SendPhoneNumberVerificationCodeResponse
	7,  // 10: backend.proto.authn.v1.MfaManagementService.AddPhoneNumberFactor:output_type -> backend.proto.authn.v1.AddPhoneNumberFactorResponse
	10, // 11: backend.proto.authn.v1.MfaManagementService.DeleteAuthenticationFactor:output_type -> google.protobuf.Empty
	8,  // [8:12] is the sub-list for method output_type
	4,  // [4:8] is the sub-list for method input_type
	4,  // [4:4] is the sub-list for extension type_name
	4,  // [4:4] is the sub-list for extension extendee
	0,  // [0:4] is the sub-list for field type_name
}

func init() { file_backend_proto_authn_v1_mfa_management_service_proto_init() }
func file_backend_proto_authn_v1_mfa_management_service_proto_init() {
	if File_backend_proto_authn_v1_mfa_management_service_proto != nil {
		return
	}
	file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[0].OneofWrappers = []any{
		(*AuthenticationFactor_PhoneNumber_)(nil),
	}
	file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes[5].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_authn_v1_mfa_management_service_proto_rawDesc), len(file_backend_proto_authn_v1_mfa_management_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_authn_v1_mfa_management_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_authn_v1_mfa_management_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_authn_v1_mfa_management_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_authn_v1_mfa_management_service_proto_msgTypes,
	}.Build()
	File_backend_proto_authn_v1_mfa_management_service_proto = out.File
	file_backend_proto_authn_v1_mfa_management_service_proto_goTypes = nil
	file_backend_proto_authn_v1_mfa_management_service_proto_depIdxs = nil
}
