// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/authn/v1/authn_service.proto

package authnpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 997100
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		997100: "ERR_CODE_UNSPECIFIED",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":          0,
		"ERR_CODE_UNSPECIFIED": 997100,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_authn_v1_authn_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_authn_v1_authn_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP(), []int{0}
}

// 登录请求的来源枚举。
type LoginRequest_Source int32

const (
	// 未指定。
	LoginRequest_SOURCE_UNSPECIFIED LoginRequest_Source = 0
	// Business (B web / B App)。
	LoginRequest_BUSINESS LoginRequest_Source = 1
)

// Enum value maps for LoginRequest_Source.
var (
	LoginRequest_Source_name = map[int32]string{
		0: "SOURCE_UNSPECIFIED",
		1: "BUSINESS",
	}
	LoginRequest_Source_value = map[string]int32{
		"SOURCE_UNSPECIFIED": 0,
		"BUSINESS":           1,
	}
)

func (x LoginRequest_Source) Enum() *LoginRequest_Source {
	p := new(LoginRequest_Source)
	*p = x
	return p
}

func (x LoginRequest_Source) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoginRequest_Source) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_authn_v1_authn_service_proto_enumTypes[1].Descriptor()
}

func (LoginRequest_Source) Type() protoreflect.EnumType {
	return &file_backend_proto_authn_v1_authn_service_proto_enumTypes[1]
}

func (x LoginRequest_Source) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoginRequest_Source.Descriptor instead.
func (LoginRequest_Source) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP(), []int{0, 0}
}

// Login RPC 的请求消息。
type LoginRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户的电子邮件地址。
	Email string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	// 用户的密码。
	Password string `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	// 用于进行中的 MFA 质询的令牌。此字段为 MFA 登录第二步的必需参数。
	ChallengeToken *string `protobuf:"bytes,3,opt,name=challenge_token,json=challengeToken,proto3,oneof" json:"challenge_token,omitempty"`
	// 用户收到的 MFA 验证码。此字段在 MFA 登录流程的第二步为必需参数。
	// 为了兼容未来可能出现的不同格式的验证码，此处不限制其长度或字符类型。
	ChallengeCode *string `protobuf:"bytes,4,opt,name=challenge_code,json=challengeCode,proto3,oneof" json:"challenge_code,omitempty"`
	// 客户端的 IP 地址，从请求头的 `x-forwarded-for` 解析。
	Ip string `protobuf:"bytes,5,opt,name=ip,proto3" json:"ip,omitempty"`
	// 用户代理，从请求头的 `user-agent` 解析。
	UserAgent string `protobuf:"bytes,6,opt,name=user_agent,json=userAgent,proto3" json:"user_agent,omitempty"`
	// 来源链接，从请求头的 `referer` 解析。
	RefererLink string `protobuf:"bytes,7,opt,name=referer_link,json=refererLink,proto3" json:"referer_link,omitempty"`
	// 来源会话 ID，从请求头的 `x-moe-session-id` 解析。
	RefererSessionId int64 `protobuf:"varint,8,opt,name=referer_session_id,json=refererSessionId,proto3" json:"referer_session_id,omitempty"`
	// 客户端的设备 ID，从请求头的 `x-moe-device-id` 解析。
	DeviceId string `protobuf:"bytes,9,opt,name=device_id,json=deviceId,proto3" json:"device_id,omitempty"`
	// 登录请求的来源。
	Source        LoginRequest_Source `protobuf:"varint,10,opt,name=source,proto3,enum=backend.proto.authn.v1.LoginRequest_Source" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginRequest) Reset() {
	*x = LoginRequest{}
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginRequest) ProtoMessage() {}

func (x *LoginRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginRequest.ProtoReflect.Descriptor instead.
func (*LoginRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP(), []int{0}
}

func (x *LoginRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *LoginRequest) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *LoginRequest) GetChallengeToken() string {
	if x != nil && x.ChallengeToken != nil {
		return *x.ChallengeToken
	}
	return ""
}

func (x *LoginRequest) GetChallengeCode() string {
	if x != nil && x.ChallengeCode != nil {
		return *x.ChallengeCode
	}
	return ""
}

func (x *LoginRequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *LoginRequest) GetUserAgent() string {
	if x != nil {
		return x.UserAgent
	}
	return ""
}

func (x *LoginRequest) GetRefererLink() string {
	if x != nil {
		return x.RefererLink
	}
	return ""
}

func (x *LoginRequest) GetRefererSessionId() int64 {
	if x != nil {
		return x.RefererSessionId
	}
	return 0
}

func (x *LoginRequest) GetDeviceId() string {
	if x != nil {
		return x.DeviceId
	}
	return ""
}

func (x *LoginRequest) GetSource() LoginRequest_Source {
	if x != nil {
		return x.Source
	}
	return LoginRequest_SOURCE_UNSPECIFIED
}

// Login RPC 的响应消息。
type LoginResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 登录成功后返回的会话令牌。
	SessionToken *string `protobuf:"bytes,1,opt,name=session_token,json=sessionToken,proto3,oneof" json:"session_token,omitempty"`
	// 会话令牌的最大有效期。
	SessionMaxAge *durationpb.Duration `protobuf:"bytes,2,opt,name=session_max_age,json=sessionMaxAge,proto3,oneof" json:"session_max_age,omitempty"`
	// 若为 `true`，则表示用户必须完成 MFA 质询。
	RequireMfa bool `protobuf:"varint,5,opt,name=require_mfa,json=requireMfa,proto3" json:"require_mfa,omitempty"`
	// 如果需要 MFA，此字段会包含用于验证的认证方式信息。
	Factor        *AuthenticationFactor `protobuf:"bytes,7,opt,name=factor,proto3,oneof" json:"factor,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginResponse) Reset() {
	*x = LoginResponse{}
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResponse) ProtoMessage() {}

func (x *LoginResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResponse.ProtoReflect.Descriptor instead.
func (*LoginResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP(), []int{1}
}

func (x *LoginResponse) GetSessionToken() string {
	if x != nil && x.SessionToken != nil {
		return *x.SessionToken
	}
	return ""
}

func (x *LoginResponse) GetSessionMaxAge() *durationpb.Duration {
	if x != nil {
		return x.SessionMaxAge
	}
	return nil
}

func (x *LoginResponse) GetRequireMfa() bool {
	if x != nil {
		return x.RequireMfa
	}
	return false
}

func (x *LoginResponse) GetFactor() *AuthenticationFactor {
	if x != nil {
		return x.Factor
	}
	return nil
}

// SendLoginChallengeCode RPC 的请求消息。
type SendLoginChallengeCodeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 与登录尝试关联的用户电子邮件地址。
	Email         string `protobuf:"bytes,1,opt,name=email,proto3" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendLoginChallengeCodeRequest) Reset() {
	*x = SendLoginChallengeCodeRequest{}
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendLoginChallengeCodeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendLoginChallengeCodeRequest) ProtoMessage() {}

func (x *SendLoginChallengeCodeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendLoginChallengeCodeRequest.ProtoReflect.Descriptor instead.
func (*SendLoginChallengeCodeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP(), []int{2}
}

func (x *SendLoginChallengeCodeRequest) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

// SendLoginChallengeCode RPC 的响应消息。
type SendLoginChallengeCodeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 新生成的质询令牌，用于后续的 `Login` 请求。
	ChallengeToken string `protobuf:"bytes,1,opt,name=challenge_token,json=challengeToken,proto3" json:"challenge_token,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *SendLoginChallengeCodeResponse) Reset() {
	*x = SendLoginChallengeCodeResponse{}
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendLoginChallengeCodeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendLoginChallengeCodeResponse) ProtoMessage() {}

func (x *SendLoginChallengeCodeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_authn_v1_authn_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendLoginChallengeCodeResponse.ProtoReflect.Descriptor instead.
func (*SendLoginChallengeCodeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP(), []int{3}
}

func (x *SendLoginChallengeCodeResponse) GetChallengeToken() string {
	if x != nil {
		return x.ChallengeToken
	}
	return ""
}

var File_backend_proto_authn_v1_authn_service_proto protoreflect.FileDescriptor

const file_backend_proto_authn_v1_authn_service_proto_rawDesc = "" +
	"\n" +
	"*backend/proto/authn/v1/authn_service.proto\x12\x16backend.proto.authn.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1egoogle/protobuf/duration.proto\x1a3backend/proto/authn/v1/mfa_management_service.proto\"\x9e\x04\n" +
	"\fLoginRequest\x12\x1d\n" +
	"\x05email\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x05email\x12#\n" +
	"\bpassword\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bpassword\x125\n" +
	"\x0fchallenge_token\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x10\x01H\x00R\x0echallengeToken\x88\x01\x01\x12*\n" +
	"\x0echallenge_code\x18\x04 \x01(\tH\x01R\rchallengeCode\x88\x01\x01\x12\x17\n" +
	"\x02ip\x18\x05 \x01(\tB\a\xbaH\x04r\x02p\x01R\x02ip\x12&\n" +
	"\n" +
	"user_agent\x18\x06 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\tuserAgent\x12!\n" +
	"\freferer_link\x18\a \x01(\tR\vrefererLink\x125\n" +
	"\x12referer_session_id\x18\b \x01(\x03B\a\xbaH\x04\"\x02(\x00R\x10refererSessionId\x12$\n" +
	"\tdevice_id\x18\t \x01(\tB\a\xbaH\x04r\x02\x10\x01R\bdeviceId\x12O\n" +
	"\x06source\x18\n" +
	" \x01(\x0e2+.backend.proto.authn.v1.LoginRequest.SourceB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x06source\".\n" +
	"\x06Source\x12\x16\n" +
	"\x12SOURCE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bBUSINESS\x10\x01B\x12\n" +
	"\x10_challenge_tokenB\x11\n" +
	"\x0f_challenge_code\"\x9e\x02\n" +
	"\rLoginResponse\x12(\n" +
	"\rsession_token\x18\x01 \x01(\tH\x00R\fsessionToken\x88\x01\x01\x12F\n" +
	"\x0fsession_max_age\x18\x02 \x01(\v2\x19.google.protobuf.DurationH\x01R\rsessionMaxAge\x88\x01\x01\x12\x1f\n" +
	"\vrequire_mfa\x18\x05 \x01(\bR\n" +
	"requireMfa\x12I\n" +
	"\x06factor\x18\a \x01(\v2,.backend.proto.authn.v1.AuthenticationFactorH\x02R\x06factor\x88\x01\x01B\x10\n" +
	"\x0e_session_tokenB\x12\n" +
	"\x10_session_max_ageB\t\n" +
	"\a_factor\">\n" +
	"\x1dSendLoginChallengeCodeRequest\x12\x1d\n" +
	"\x05email\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x05email\"I\n" +
	"\x1eSendLoginChallengeCodeResponse\x12'\n" +
	"\x0fchallenge_token\x18\x01 \x01(\tR\x0echallengeToken*6\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10\xec\xed<2\xee\x01\n" +
	"\fAuthnService\x12T\n" +
	"\x05Login\x12$.backend.proto.authn.v1.LoginRequest\x1a%.backend.proto.authn.v1.LoginResponse\x12\x87\x01\n" +
	"\x16SendLoginChallengeCode\x125.backend.proto.authn.v1.SendLoginChallengeCodeRequest\x1a6.backend.proto.authn.v1.SendLoginChallengeCodeResponseBb\n" +
	" com.moego.backend.proto.authn.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/authn/v1;authnpbb\x06proto3"

var (
	file_backend_proto_authn_v1_authn_service_proto_rawDescOnce sync.Once
	file_backend_proto_authn_v1_authn_service_proto_rawDescData []byte
)

func file_backend_proto_authn_v1_authn_service_proto_rawDescGZIP() []byte {
	file_backend_proto_authn_v1_authn_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_authn_v1_authn_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_authn_v1_authn_service_proto_rawDesc), len(file_backend_proto_authn_v1_authn_service_proto_rawDesc)))
	})
	return file_backend_proto_authn_v1_authn_service_proto_rawDescData
}

var file_backend_proto_authn_v1_authn_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_backend_proto_authn_v1_authn_service_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_backend_proto_authn_v1_authn_service_proto_goTypes = []any{
	(ErrCode)(0),                           // 0: backend.proto.authn.v1.ErrCode
	(LoginRequest_Source)(0),               // 1: backend.proto.authn.v1.LoginRequest.Source
	(*LoginRequest)(nil),                   // 2: backend.proto.authn.v1.LoginRequest
	(*LoginResponse)(nil),                  // 3: backend.proto.authn.v1.LoginResponse
	(*SendLoginChallengeCodeRequest)(nil),  // 4: backend.proto.authn.v1.SendLoginChallengeCodeRequest
	(*SendLoginChallengeCodeResponse)(nil), // 5: backend.proto.authn.v1.SendLoginChallengeCodeResponse
	(*durationpb.Duration)(nil),            // 6: google.protobuf.Duration
	(*AuthenticationFactor)(nil),           // 7: backend.proto.authn.v1.AuthenticationFactor
}
var file_backend_proto_authn_v1_authn_service_proto_depIdxs = []int32{
	1, // 0: backend.proto.authn.v1.LoginRequest.source:type_name -> backend.proto.authn.v1.LoginRequest.Source
	6, // 1: backend.proto.authn.v1.LoginResponse.session_max_age:type_name -> google.protobuf.Duration
	7, // 2: backend.proto.authn.v1.LoginResponse.factor:type_name -> backend.proto.authn.v1.AuthenticationFactor
	2, // 3: backend.proto.authn.v1.AuthnService.Login:input_type -> backend.proto.authn.v1.LoginRequest
	4, // 4: backend.proto.authn.v1.AuthnService.SendLoginChallengeCode:input_type -> backend.proto.authn.v1.SendLoginChallengeCodeRequest
	3, // 5: backend.proto.authn.v1.AuthnService.Login:output_type -> backend.proto.authn.v1.LoginResponse
	5, // 6: backend.proto.authn.v1.AuthnService.SendLoginChallengeCode:output_type -> backend.proto.authn.v1.SendLoginChallengeCodeResponse
	5, // [5:7] is the sub-list for method output_type
	3, // [3:5] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_backend_proto_authn_v1_authn_service_proto_init() }
func file_backend_proto_authn_v1_authn_service_proto_init() {
	if File_backend_proto_authn_v1_authn_service_proto != nil {
		return
	}
	file_backend_proto_authn_v1_mfa_management_service_proto_init()
	file_backend_proto_authn_v1_authn_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_authn_v1_authn_service_proto_msgTypes[1].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_authn_v1_authn_service_proto_rawDesc), len(file_backend_proto_authn_v1_authn_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_authn_v1_authn_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_authn_v1_authn_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_authn_v1_authn_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_authn_v1_authn_service_proto_msgTypes,
	}.Build()
	File_backend_proto_authn_v1_authn_service_proto = out.File
	file_backend_proto_authn_v1_authn_service_proto_goTypes = nil
	file_backend_proto_authn_v1_authn_service_proto_depIdxs = nil
}
