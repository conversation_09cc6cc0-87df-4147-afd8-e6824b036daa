load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "authnpb_proto",
    srcs = [
        "authn_service.proto",
        "mfa_management_service.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

go_proto_library(
    name = "authnpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_proto",
        "@io_bazel_rules_go//proto:go_grpc_v2",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/authn/v1",
    proto = ":authnpb_proto",
    visibility = ["//visibility:public"],
    deps = ["@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library"],
)

go_library(
    name = "authn",
    embed = [":authnpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/authn/v1",
    visibility = ["//visibility:public"],
)
