// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/authn/v1/authn_service.proto

package authnpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AuthnService_Login_FullMethodName                  = "/backend.proto.authn.v1.AuthnService/Login"
	AuthnService_SendLoginChallengeCode_FullMethodName = "/backend.proto.authn.v1.AuthnService/SendLoginChallengeCode"
)

// AuthnServiceClient is the client API for AuthnService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// AuthnService 负责用户认证的所有方面。
// 其核心功能是 `Login` RPC，该接口支持多步骤流程以兼容多因素认证（MFA），从而增强系统安全性。
// 此外，它还提供了重新发送 MFA 验证码的机制。
type AuthnServiceClient interface {
	// (-- api-linter: core::0136::verb-noun=disabled
	//
	//	aip.dev/not-precedent: We need to do this because login is a common naming. --)
	//
	// Login 用于处理用户登录。如果用户已启用 MFA，整个登录流程可能涉及多个步骤。
	//
	// MFA 多步登录流程：
	//  1. **首次调用：** 客户端通过发送用户的 `email`、`password` 及其他设备信息来发起登录请求。
	//  2. **MFA 质询：** 如果需要进行 MFA 验证，服务器将返回 `require_mfa = true` 和 `factor`（指明所用的MFA方式）。
	//     此时，响应中不会包含 `session_token`。客户端在收到此响应后，应调用 `SendLoginChallengeCode` 来发送验证码。
	//  3. **二次调用：** 客户端必须再次发起 `Login` 请求，其中需包含原始的 `email`、`password` 以及 `SendLoginChallengeCode`
	//     返回的 `challenge_token` 和用户输入的 `challenge_code`。
	//  4. **会话授予：** 在成功验证 MFA 质询后，服务器将返回一个 `session_token`。
	Login(ctx context.Context, in *LoginRequest, opts ...grpc.CallOption) (*LoginResponse, error)
	// SendLoginChallengeCode 用于在 MFA 流程中发送验证码。
	// 当 `Login` 接口返回 `require_mfa=true` 时，客户端必须调用此接口来触发验证码的发送。
	// 用户也可以使用此接口来重新发送验证码。该接口会做限流处理。
	SendLoginChallengeCode(ctx context.Context, in *SendLoginChallengeCodeRequest, opts ...grpc.CallOption) (*SendLoginChallengeCodeResponse, error)
}

type authnServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthnServiceClient(cc grpc.ClientConnInterface) AuthnServiceClient {
	return &authnServiceClient{cc}
}

func (c *authnServiceClient) Login(ctx context.Context, in *LoginRequest, opts ...grpc.CallOption) (*LoginResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoginResponse)
	err := c.cc.Invoke(ctx, AuthnService_Login_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authnServiceClient) SendLoginChallengeCode(ctx context.Context, in *SendLoginChallengeCodeRequest, opts ...grpc.CallOption) (*SendLoginChallengeCodeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendLoginChallengeCodeResponse)
	err := c.cc.Invoke(ctx, AuthnService_SendLoginChallengeCode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthnServiceServer is the server API for AuthnService service.
// All implementations must embed UnimplementedAuthnServiceServer
// for forward compatibility.
//
// AuthnService 负责用户认证的所有方面。
// 其核心功能是 `Login` RPC，该接口支持多步骤流程以兼容多因素认证（MFA），从而增强系统安全性。
// 此外，它还提供了重新发送 MFA 验证码的机制。
type AuthnServiceServer interface {
	// (-- api-linter: core::0136::verb-noun=disabled
	//
	//	aip.dev/not-precedent: We need to do this because login is a common naming. --)
	//
	// Login 用于处理用户登录。如果用户已启用 MFA，整个登录流程可能涉及多个步骤。
	//
	// MFA 多步登录流程：
	//  1. **首次调用：** 客户端通过发送用户的 `email`、`password` 及其他设备信息来发起登录请求。
	//  2. **MFA 质询：** 如果需要进行 MFA 验证，服务器将返回 `require_mfa = true` 和 `factor`（指明所用的MFA方式）。
	//     此时，响应中不会包含 `session_token`。客户端在收到此响应后，应调用 `SendLoginChallengeCode` 来发送验证码。
	//  3. **二次调用：** 客户端必须再次发起 `Login` 请求，其中需包含原始的 `email`、`password` 以及 `SendLoginChallengeCode`
	//     返回的 `challenge_token` 和用户输入的 `challenge_code`。
	//  4. **会话授予：** 在成功验证 MFA 质询后，服务器将返回一个 `session_token`。
	Login(context.Context, *LoginRequest) (*LoginResponse, error)
	// SendLoginChallengeCode 用于在 MFA 流程中发送验证码。
	// 当 `Login` 接口返回 `require_mfa=true` 时，客户端必须调用此接口来触发验证码的发送。
	// 用户也可以使用此接口来重新发送验证码。该接口会做限流处理。
	SendLoginChallengeCode(context.Context, *SendLoginChallengeCodeRequest) (*SendLoginChallengeCodeResponse, error)
	mustEmbedUnimplementedAuthnServiceServer()
}

// UnimplementedAuthnServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAuthnServiceServer struct{}

func (UnimplementedAuthnServiceServer) Login(context.Context, *LoginRequest) (*LoginResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedAuthnServiceServer) SendLoginChallengeCode(context.Context, *SendLoginChallengeCodeRequest) (*SendLoginChallengeCodeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendLoginChallengeCode not implemented")
}
func (UnimplementedAuthnServiceServer) mustEmbedUnimplementedAuthnServiceServer() {}
func (UnimplementedAuthnServiceServer) testEmbeddedByValue()                      {}

// UnsafeAuthnServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthnServiceServer will
// result in compilation errors.
type UnsafeAuthnServiceServer interface {
	mustEmbedUnimplementedAuthnServiceServer()
}

func RegisterAuthnServiceServer(s grpc.ServiceRegistrar, srv AuthnServiceServer) {
	// If the following call pancis, it indicates UnimplementedAuthnServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AuthnService_ServiceDesc, srv)
}

func _AuthnService_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthnServiceServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthnService_Login_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthnServiceServer).Login(ctx, req.(*LoginRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AuthnService_SendLoginChallengeCode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendLoginChallengeCodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthnServiceServer).SendLoginChallengeCode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AuthnService_SendLoginChallengeCode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthnServiceServer).SendLoginChallengeCode(ctx, req.(*SendLoginChallengeCodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AuthnService_ServiceDesc is the grpc.ServiceDesc for AuthnService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AuthnService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.authn.v1.AuthnService",
	HandlerType: (*AuthnServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Login",
			Handler:    _AuthnService_Login_Handler,
		},
		{
			MethodName: "SendLoginChallengeCode",
			Handler:    _AuthnService_SendLoginChallengeCode_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/authn/v1/authn_service.proto",
}
