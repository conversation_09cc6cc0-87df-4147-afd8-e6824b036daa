// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::response-repeated-first-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::response-plural-first-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0142::time-field-type=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/aistudio/v1/aistudio.proto

package aistudio

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RequestAiStudioMcpClientRequest is a request to request aiStudio mcp client.
type RequestAiStudioMcpClientRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// gemini_key is gemini key.
	GeminiKey string `protobuf:"bytes,1,opt,name=gemini_key,json=geminiKey,proto3" json:"gemini_key,omitempty"`
	// prompt is prompt.
	Prompt string `protobuf:"bytes,2,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// mcps 是 mcp 列表，以逗号分隔。
	Mcps string `protobuf:"bytes,3,opt,name=mcps,proto3" json:"mcps,omitempty"`
	// envs 是 envs。
	Envs string `protobuf:"bytes,4,opt,name=envs,proto3" json:"envs,omitempty"`
	// model 是模型。
	Model         string `protobuf:"bytes,5,opt,name=model,proto3" json:"model,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestAiStudioMcpClientRequest) Reset() {
	*x = RequestAiStudioMcpClientRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestAiStudioMcpClientRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestAiStudioMcpClientRequest) ProtoMessage() {}

func (x *RequestAiStudioMcpClientRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestAiStudioMcpClientRequest.ProtoReflect.Descriptor instead.
func (*RequestAiStudioMcpClientRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{0}
}

func (x *RequestAiStudioMcpClientRequest) GetGeminiKey() string {
	if x != nil {
		return x.GeminiKey
	}
	return ""
}

func (x *RequestAiStudioMcpClientRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *RequestAiStudioMcpClientRequest) GetMcps() string {
	if x != nil {
		return x.Mcps
	}
	return ""
}

func (x *RequestAiStudioMcpClientRequest) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

func (x *RequestAiStudioMcpClientRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

// RequestAiStudioMcpClientResponse is a response to request aiStudio mcp client.
type RequestAiStudioMcpClientResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// status is status.
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// msg is msg.
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	// dialogues is dialogues.
	// data is response data.
	Data          *ResponseData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RequestAiStudioMcpClientResponse) Reset() {
	*x = RequestAiStudioMcpClientResponse{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RequestAiStudioMcpClientResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RequestAiStudioMcpClientResponse) ProtoMessage() {}

func (x *RequestAiStudioMcpClientResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RequestAiStudioMcpClientResponse.ProtoReflect.Descriptor instead.
func (*RequestAiStudioMcpClientResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{1}
}

func (x *RequestAiStudioMcpClientResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *RequestAiStudioMcpClientResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RequestAiStudioMcpClientResponse) GetData() *ResponseData {
	if x != nil {
		return x.Data
	}
	return nil
}

// ResponseData is response data.
type ResponseData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// dialogues is dialogues.
	// 对话列表
	Dialogues     []string `protobuf:"bytes,1,rep,name=dialogues,proto3" json:"dialogues,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResponseData) Reset() {
	*x = ResponseData{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResponseData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResponseData) ProtoMessage() {}

func (x *ResponseData) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResponseData.ProtoReflect.Descriptor instead.
func (*ResponseData) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{2}
}

func (x *ResponseData) GetDialogues() []string {
	if x != nil {
		return x.Dialogues
	}
	return nil
}

// CreateAiStudioTemplateRequest is a request to create ai studio template.
type CreateAiStudioTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// model 是模型。
	Model string `protobuf:"bytes,1,opt,name=model,proto3" json:"model,omitempty"`
	// prompt 是 prompt.
	Prompt string `protobuf:"bytes,2,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// mcps 是 mcp 列表，以逗号分隔。
	Mcps string `protobuf:"bytes,3,opt,name=mcps,proto3" json:"mcps,omitempty"`
	// envs 是 envs。
	Envs string `protobuf:"bytes,4,opt,name=envs,proto3" json:"envs,omitempty"`
	// name 是 模版的name。
	Name          string `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAiStudioTemplateRequest) Reset() {
	*x = CreateAiStudioTemplateRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAiStudioTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAiStudioTemplateRequest) ProtoMessage() {}

func (x *CreateAiStudioTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAiStudioTemplateRequest.ProtoReflect.Descriptor instead.
func (*CreateAiStudioTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{3}
}

func (x *CreateAiStudioTemplateRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *CreateAiStudioTemplateRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *CreateAiStudioTemplateRequest) GetMcps() string {
	if x != nil {
		return x.Mcps
	}
	return ""
}

func (x *CreateAiStudioTemplateRequest) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

func (x *CreateAiStudioTemplateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// GetAiStudioTemplateRequest is a request to get aiStudio template.
type GetAiStudioTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAiStudioTemplateRequest) Reset() {
	*x = GetAiStudioTemplateRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAiStudioTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAiStudioTemplateRequest) ProtoMessage() {}

func (x *GetAiStudioTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAiStudioTemplateRequest.ProtoReflect.Descriptor instead.
func (*GetAiStudioTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{4}
}

func (x *GetAiStudioTemplateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// AiStudioTemplate is a response to get aiStudio template.
type AiStudioTemplate struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// status is status.
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// msg is msg.
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	// data is response data.
	Data          *AiStudioTemplateData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AiStudioTemplate) Reset() {
	*x = AiStudioTemplate{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AiStudioTemplate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AiStudioTemplate) ProtoMessage() {}

func (x *AiStudioTemplate) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AiStudioTemplate.ProtoReflect.Descriptor instead.
func (*AiStudioTemplate) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{5}
}

func (x *AiStudioTemplate) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AiStudioTemplate) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AiStudioTemplate) GetData() *AiStudioTemplateData {
	if x != nil {
		return x.Data
	}
	return nil
}

// AiStudioTemplateData is a response to get aiStudio template.
type AiStudioTemplateData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// model 是模型。
	Model string `protobuf:"bytes,2,opt,name=model,proto3" json:"model,omitempty"`
	// prompt 是 prompt.
	Prompt string `protobuf:"bytes,3,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// mcps 是 mcp 列表，以逗号分隔。
	Mcps string `protobuf:"bytes,4,opt,name=mcps,proto3" json:"mcps,omitempty"`
	// env_keys 是 env_keys。
	EnvKeys string `protobuf:"bytes,5,opt,name=env_keys,json=envKeys,proto3" json:"env_keys,omitempty"`
	// name 是 template的名字。
	Name          string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AiStudioTemplateData) Reset() {
	*x = AiStudioTemplateData{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AiStudioTemplateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AiStudioTemplateData) ProtoMessage() {}

func (x *AiStudioTemplateData) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AiStudioTemplateData.ProtoReflect.Descriptor instead.
func (*AiStudioTemplateData) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{6}
}

func (x *AiStudioTemplateData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AiStudioTemplateData) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *AiStudioTemplateData) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *AiStudioTemplateData) GetMcps() string {
	if x != nil {
		return x.Mcps
	}
	return ""
}

func (x *AiStudioTemplateData) GetEnvKeys() string {
	if x != nil {
		return x.EnvKeys
	}
	return ""
}

func (x *AiStudioTemplateData) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// UpdateAiStudioTemplateRequest is a request to update aiStudio template.
type UpdateAiStudioTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// model 是模型。
	Model string `protobuf:"bytes,2,opt,name=model,proto3" json:"model,omitempty"`
	// prompt 是 prompt.
	Prompt string `protobuf:"bytes,3,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// mcps 是 mcp 列表，以逗号分隔
	Mcps string `protobuf:"bytes,4,opt,name=mcps,proto3" json:"mcps,omitempty"`
	// env_keys 是 env_keys
	EnvKeys string `protobuf:"bytes,5,opt,name=env_keys,json=envKeys,proto3" json:"env_keys,omitempty"`
	// name 是 name
	Name          string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAiStudioTemplateRequest) Reset() {
	*x = UpdateAiStudioTemplateRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAiStudioTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAiStudioTemplateRequest) ProtoMessage() {}

func (x *UpdateAiStudioTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAiStudioTemplateRequest.ProtoReflect.Descriptor instead.
func (*UpdateAiStudioTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateAiStudioTemplateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAiStudioTemplateRequest) GetModel() string {
	if x != nil {
		return x.Model
	}
	return ""
}

func (x *UpdateAiStudioTemplateRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *UpdateAiStudioTemplateRequest) GetMcps() string {
	if x != nil {
		return x.Mcps
	}
	return ""
}

func (x *UpdateAiStudioTemplateRequest) GetEnvKeys() string {
	if x != nil {
		return x.EnvKeys
	}
	return ""
}

func (x *UpdateAiStudioTemplateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// DeleteAiStudioTemplateRequest is a request to delete aiStudio template.
type DeleteAiStudioTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAiStudioTemplateRequest) Reset() {
	*x = DeleteAiStudioTemplateRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAiStudioTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAiStudioTemplateRequest) ProtoMessage() {}

func (x *DeleteAiStudioTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAiStudioTemplateRequest.ProtoReflect.Descriptor instead.
func (*DeleteAiStudioTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteAiStudioTemplateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ListAiStudioTemplateRequest is a request to list aiStudio templates.
type ListAiStudioTemplateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// per_page is the number of results to return per page.
	PerPage int32 `protobuf:"varint,1,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// page is the id to retrieve the next page of results.
	Page          int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTemplateRequest) Reset() {
	*x = ListAiStudioTemplateRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTemplateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTemplateRequest) ProtoMessage() {}

func (x *ListAiStudioTemplateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTemplateRequest.ProtoReflect.Descriptor instead.
func (*ListAiStudioTemplateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{9}
}

func (x *ListAiStudioTemplateRequest) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListAiStudioTemplateRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

// ListAiStudioTemplateData is a response to list aiStudio templates.
type ListAiStudioTemplateData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// aistudio_tasks is the list of aistudio_tasks.
	Items []*AiStudioTemplateData `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	// total is the total count to retrieve the next page of results.
	Total         int32 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTemplateData) Reset() {
	*x = ListAiStudioTemplateData{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTemplateData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTemplateData) ProtoMessage() {}

func (x *ListAiStudioTemplateData) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTemplateData.ProtoReflect.Descriptor instead.
func (*ListAiStudioTemplateData) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{10}
}

func (x *ListAiStudioTemplateData) GetItems() []*AiStudioTemplateData {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListAiStudioTemplateData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// ListAiStudioTemplateResponse is a response to list aiStudio templates.
type ListAiStudioTemplateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// status is status.
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// msg is msg.
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	// data is response data.
	Data          *ListAiStudioTemplateData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTemplateResponse) Reset() {
	*x = ListAiStudioTemplateResponse{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTemplateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTemplateResponse) ProtoMessage() {}

func (x *ListAiStudioTemplateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTemplateResponse.ProtoReflect.Descriptor instead.
func (*ListAiStudioTemplateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{11}
}

func (x *ListAiStudioTemplateResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListAiStudioTemplateResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListAiStudioTemplateResponse) GetData() *ListAiStudioTemplateData {
	if x != nil {
		return x.Data
	}
	return nil
}

// ListAiStudioTemplateRequest is a request to list aiStudio templates.
type ListAiStudioTemplateIDRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTemplateIDRequest) Reset() {
	*x = ListAiStudioTemplateIDRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTemplateIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTemplateIDRequest) ProtoMessage() {}

func (x *ListAiStudioTemplateIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTemplateIDRequest.ProtoReflect.Descriptor instead.
func (*ListAiStudioTemplateIDRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{12}
}

// AiStudioTemplateID is a response to list aiStudio templates.
type AiStudioTemplateID struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// label is label.
	Label string `protobuf:"bytes,1,opt,name=label,proto3" json:"label,omitempty"`
	// value is value.
	Value         string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AiStudioTemplateID) Reset() {
	*x = AiStudioTemplateID{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AiStudioTemplateID) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AiStudioTemplateID) ProtoMessage() {}

func (x *AiStudioTemplateID) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AiStudioTemplateID.ProtoReflect.Descriptor instead.
func (*AiStudioTemplateID) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{13}
}

func (x *AiStudioTemplateID) GetLabel() string {
	if x != nil {
		return x.Label
	}
	return ""
}

func (x *AiStudioTemplateID) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// ListAiStudioTemplateIDData is a response to list aiStudio templates.
type ListAiStudioTemplateIDData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// aistudio_tasks is the list of aistudio_tasks.
	Options       []*AiStudioTemplateID `protobuf:"bytes,1,rep,name=options,proto3" json:"options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTemplateIDData) Reset() {
	*x = ListAiStudioTemplateIDData{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTemplateIDData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTemplateIDData) ProtoMessage() {}

func (x *ListAiStudioTemplateIDData) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTemplateIDData.ProtoReflect.Descriptor instead.
func (*ListAiStudioTemplateIDData) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{14}
}

func (x *ListAiStudioTemplateIDData) GetOptions() []*AiStudioTemplateID {
	if x != nil {
		return x.Options
	}
	return nil
}

// ListAiStudioTemplateResponse is a response to list aiStudio templates.
type ListAiStudioTemplateIDResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// status is status.
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// msg is msg.
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	// data is response data.
	Data          *ListAiStudioTemplateIDData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTemplateIDResponse) Reset() {
	*x = ListAiStudioTemplateIDResponse{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTemplateIDResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTemplateIDResponse) ProtoMessage() {}

func (x *ListAiStudioTemplateIDResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTemplateIDResponse.ProtoReflect.Descriptor instead.
func (*ListAiStudioTemplateIDResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{15}
}

func (x *ListAiStudioTemplateIDResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListAiStudioTemplateIDResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListAiStudioTemplateIDResponse) GetData() *ListAiStudioTemplateIDData {
	if x != nil {
		return x.Data
	}
	return nil
}

// CreateAiStudioTaskRequest is a request to create ai studio task.
type CreateAiStudioTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// task is task_name.
	Task string `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	// template_id is template_id.
	TemplateId int64 `protobuf:"varint,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// envs is envs.
	Envs string `protobuf:"bytes,3,opt,name=envs,proto3" json:"envs,omitempty"`
	// ai_key is ai_key.
	AiKey string `protobuf:"bytes,4,opt,name=ai_key,json=aiKey,proto3" json:"ai_key,omitempty"`
	// im_channel is im_channel.
	ImChannel string `protobuf:"bytes,5,opt,name=im_channel,json=imChannel,proto3" json:"im_channel,omitempty"`
	// spec is spec
	Spec          string `protobuf:"bytes,6,opt,name=spec,proto3" json:"spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAiStudioTaskRequest) Reset() {
	*x = CreateAiStudioTaskRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAiStudioTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAiStudioTaskRequest) ProtoMessage() {}

func (x *CreateAiStudioTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAiStudioTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateAiStudioTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{16}
}

func (x *CreateAiStudioTaskRequest) GetTask() string {
	if x != nil {
		return x.Task
	}
	return ""
}

func (x *CreateAiStudioTaskRequest) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *CreateAiStudioTaskRequest) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

func (x *CreateAiStudioTaskRequest) GetAiKey() string {
	if x != nil {
		return x.AiKey
	}
	return ""
}

func (x *CreateAiStudioTaskRequest) GetImChannel() string {
	if x != nil {
		return x.ImChannel
	}
	return ""
}

func (x *CreateAiStudioTaskRequest) GetSpec() string {
	if x != nil {
		return x.Spec
	}
	return ""
}

// AiStudioTask is a response to get aiStudio task warp.
type AiStudioTask struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// status is status.
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// msg is msg.
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	// data is response data.
	Data          *AiStudioTaskData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AiStudioTask) Reset() {
	*x = AiStudioTask{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AiStudioTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AiStudioTask) ProtoMessage() {}

func (x *AiStudioTask) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AiStudioTask.ProtoReflect.Descriptor instead.
func (*AiStudioTask) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{17}
}

func (x *AiStudioTask) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *AiStudioTask) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *AiStudioTask) GetData() *AiStudioTaskData {
	if x != nil {
		return x.Data
	}
	return nil
}

// AiStudioTaskData is a response to get aiStudio task.
type AiStudioTaskData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// task is task_name.
	Task string `protobuf:"bytes,2,opt,name=task,proto3" json:"task,omitempty"`
	// template_id is template_id.
	TemplateId int64 `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// envs is envs.
	Envs string `protobuf:"bytes,4,opt,name=envs,proto3" json:"envs,omitempty"`
	// ai_key is ai_key.
	AiKey string `protobuf:"bytes,5,opt,name=ai_key,json=aiKey,proto3" json:"ai_key,omitempty"`
	// im_channel is im_channel.
	ImChannel string `protobuf:"bytes,6,opt,name=im_channel,json=imChannel,proto3" json:"im_channel,omitempty"`
	// spec is spec
	Spec          string `protobuf:"bytes,7,opt,name=spec,proto3" json:"spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AiStudioTaskData) Reset() {
	*x = AiStudioTaskData{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AiStudioTaskData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AiStudioTaskData) ProtoMessage() {}

func (x *AiStudioTaskData) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AiStudioTaskData.ProtoReflect.Descriptor instead.
func (*AiStudioTaskData) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{18}
}

func (x *AiStudioTaskData) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AiStudioTaskData) GetTask() string {
	if x != nil {
		return x.Task
	}
	return ""
}

func (x *AiStudioTaskData) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *AiStudioTaskData) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

func (x *AiStudioTaskData) GetAiKey() string {
	if x != nil {
		return x.AiKey
	}
	return ""
}

func (x *AiStudioTaskData) GetImChannel() string {
	if x != nil {
		return x.ImChannel
	}
	return ""
}

func (x *AiStudioTaskData) GetSpec() string {
	if x != nil {
		return x.Spec
	}
	return ""
}

// GetAiStudioTaskRequest is a request to get aiStudio task.
type GetAiStudioTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAiStudioTaskRequest) Reset() {
	*x = GetAiStudioTaskRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAiStudioTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAiStudioTaskRequest) ProtoMessage() {}

func (x *GetAiStudioTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAiStudioTaskRequest.ProtoReflect.Descriptor instead.
func (*GetAiStudioTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{19}
}

func (x *GetAiStudioTaskRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// RunAiStudioTaskRequest is a request to get aiStudio task.
type RunAiStudioTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name is name.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// envs is envs.
	Envs          string `protobuf:"bytes,3,opt,name=envs,proto3" json:"envs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RunAiStudioTaskRequest) Reset() {
	*x = RunAiStudioTaskRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RunAiStudioTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunAiStudioTaskRequest) ProtoMessage() {}

func (x *RunAiStudioTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunAiStudioTaskRequest.ProtoReflect.Descriptor instead.
func (*RunAiStudioTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{20}
}

func (x *RunAiStudioTaskRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *RunAiStudioTaskRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *RunAiStudioTaskRequest) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

// RunAiStudioTaskResponse is a response to get aiStudio task.
type RunAiStudioTaskResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// status is status.
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// msg is msg.
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	// data is response data.
	Data          []string `protobuf:"bytes,3,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RunAiStudioTaskResponse) Reset() {
	*x = RunAiStudioTaskResponse{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RunAiStudioTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunAiStudioTaskResponse) ProtoMessage() {}

func (x *RunAiStudioTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunAiStudioTaskResponse.ProtoReflect.Descriptor instead.
func (*RunAiStudioTaskResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{21}
}

func (x *RunAiStudioTaskResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *RunAiStudioTaskResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *RunAiStudioTaskResponse) GetData() []string {
	if x != nil {
		return x.Data
	}
	return nil
}

// UpdateAiStudioTaskRequest is a request to update aiStudio template.
type UpdateAiStudioTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// task is task_name.
	Task string `protobuf:"bytes,2,opt,name=task,proto3" json:"task,omitempty"`
	// template_id is template_id.
	TemplateId int64 `protobuf:"varint,3,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// envs is envs.
	Envs string `protobuf:"bytes,4,opt,name=envs,proto3" json:"envs,omitempty"`
	// ai_key is ai_key.
	AiKey string `protobuf:"bytes,5,opt,name=ai_key,json=aiKey,proto3" json:"ai_key,omitempty"`
	// im_channel is im_channel.
	ImChannel string `protobuf:"bytes,6,opt,name=im_channel,json=imChannel,proto3" json:"im_channel,omitempty"`
	// spec is spec
	Spec          string `protobuf:"bytes,7,opt,name=spec,proto3" json:"spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAiStudioTaskRequest) Reset() {
	*x = UpdateAiStudioTaskRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAiStudioTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAiStudioTaskRequest) ProtoMessage() {}

func (x *UpdateAiStudioTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAiStudioTaskRequest.ProtoReflect.Descriptor instead.
func (*UpdateAiStudioTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateAiStudioTaskRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAiStudioTaskRequest) GetTask() string {
	if x != nil {
		return x.Task
	}
	return ""
}

func (x *UpdateAiStudioTaskRequest) GetTemplateId() int64 {
	if x != nil {
		return x.TemplateId
	}
	return 0
}

func (x *UpdateAiStudioTaskRequest) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

func (x *UpdateAiStudioTaskRequest) GetAiKey() string {
	if x != nil {
		return x.AiKey
	}
	return ""
}

func (x *UpdateAiStudioTaskRequest) GetImChannel() string {
	if x != nil {
		return x.ImChannel
	}
	return ""
}

func (x *UpdateAiStudioTaskRequest) GetSpec() string {
	if x != nil {
		return x.Spec
	}
	return ""
}

// DeleteAiStudioTaskRequest is a request to delete aiStudio template.
type DeleteAiStudioTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAiStudioTaskRequest) Reset() {
	*x = DeleteAiStudioTaskRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAiStudioTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAiStudioTaskRequest) ProtoMessage() {}

func (x *DeleteAiStudioTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAiStudioTaskRequest.ProtoReflect.Descriptor instead.
func (*DeleteAiStudioTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteAiStudioTaskRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// ListAiStudioTaskRequest is a request to list aiStudio tasks.
type ListAiStudioTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// per_page is the number of results to return per page.
	PerPage int32 `protobuf:"varint,1,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// page is the current page.
	Page          int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTaskRequest) Reset() {
	*x = ListAiStudioTaskRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTaskRequest) ProtoMessage() {}

func (x *ListAiStudioTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTaskRequest.ProtoReflect.Descriptor instead.
func (*ListAiStudioTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{24}
}

func (x *ListAiStudioTaskRequest) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListAiStudioTaskRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

// ListAiStudioTaskData is a response to list aiStudio tasks.
type ListAiStudioTaskData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// aistudio_tasks is the list of aistudio_tasks.
	Items []*AiStudioTaskData `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	// total is the total count to retrieve the next page of results.
	Total         int32 `protobuf:"varint,4,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTaskData) Reset() {
	*x = ListAiStudioTaskData{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTaskData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTaskData) ProtoMessage() {}

func (x *ListAiStudioTaskData) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTaskData.ProtoReflect.Descriptor instead.
func (*ListAiStudioTaskData) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{25}
}

func (x *ListAiStudioTaskData) GetItems() []*AiStudioTaskData {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListAiStudioTaskData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// ListAiStudioTaskResponse is a response to list aiStudio tasks.
type ListAiStudioTaskResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// status is status.
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// msg is msg.
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	// data is response data.
	Data          *ListAiStudioTaskData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTaskResponse) Reset() {
	*x = ListAiStudioTaskResponse{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTaskResponse) ProtoMessage() {}

func (x *ListAiStudioTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTaskResponse.ProtoReflect.Descriptor instead.
func (*ListAiStudioTaskResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{26}
}

func (x *ListAiStudioTaskResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListAiStudioTaskResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListAiStudioTaskResponse) GetData() *ListAiStudioTaskData {
	if x != nil {
		return x.Data
	}
	return nil
}

// AiStudioTaskLog is a response to get aiStudio task log.
type AiStudioTaskLog struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// task_id is task_id.
	TaskId int64 `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// prompt is prompt.
	Prompt string `protobuf:"bytes,3,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// envs is envs.
	Envs string `protobuf:"bytes,4,opt,name=envs,proto3" json:"envs,omitempty"`
	// dialogues is dialogues.
	Dialogues []string `protobuf:"bytes,5,rep,name=dialogues,proto3" json:"dialogues,omitempty"`
	// create_time is create_time.
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AiStudioTaskLog) Reset() {
	*x = AiStudioTaskLog{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AiStudioTaskLog) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AiStudioTaskLog) ProtoMessage() {}

func (x *AiStudioTaskLog) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AiStudioTaskLog.ProtoReflect.Descriptor instead.
func (*AiStudioTaskLog) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{27}
}

func (x *AiStudioTaskLog) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AiStudioTaskLog) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *AiStudioTaskLog) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *AiStudioTaskLog) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

func (x *AiStudioTaskLog) GetDialogues() []string {
	if x != nil {
		return x.Dialogues
	}
	return nil
}

func (x *AiStudioTaskLog) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

// CreateAiStudioTaskLogRequest is a request to create aiStudio task log.
type CreateAiStudioTaskLogRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// task_id is task_id.
	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// prompt is prompt.
	Prompt string `protobuf:"bytes,2,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// envs is envs.
	Envs string `protobuf:"bytes,3,opt,name=envs,proto3" json:"envs,omitempty"`
	// dialogues is dialogues.
	Dialogues     []string `protobuf:"bytes,4,rep,name=dialogues,proto3" json:"dialogues,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAiStudioTaskLogRequest) Reset() {
	*x = CreateAiStudioTaskLogRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAiStudioTaskLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAiStudioTaskLogRequest) ProtoMessage() {}

func (x *CreateAiStudioTaskLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAiStudioTaskLogRequest.ProtoReflect.Descriptor instead.
func (*CreateAiStudioTaskLogRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{28}
}

func (x *CreateAiStudioTaskLogRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *CreateAiStudioTaskLogRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *CreateAiStudioTaskLogRequest) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

func (x *CreateAiStudioTaskLogRequest) GetDialogues() []string {
	if x != nil {
		return x.Dialogues
	}
	return nil
}

// GetAiStudioTaskLogRequest is a request to get aiStudio task log.
type GetAiStudioTaskLogRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAiStudioTaskLogRequest) Reset() {
	*x = GetAiStudioTaskLogRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAiStudioTaskLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAiStudioTaskLogRequest) ProtoMessage() {}

func (x *GetAiStudioTaskLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAiStudioTaskLogRequest.ProtoReflect.Descriptor instead.
func (*GetAiStudioTaskLogRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{29}
}

func (x *GetAiStudioTaskLogRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// UpdateAiStudioTaskLogRequest is a request to update aiStudio task log.
type UpdateAiStudioTaskLogRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id is id.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// task_id is task_id.
	TaskId int64 `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// prompt is prompt.
	Prompt string `protobuf:"bytes,3,opt,name=prompt,proto3" json:"prompt,omitempty"`
	// envs is envs.
	Envs string `protobuf:"bytes,4,opt,name=envs,proto3" json:"envs,omitempty"`
	// dialogues is dialogues.
	Dialogues     []string `protobuf:"bytes,5,rep,name=dialogues,proto3" json:"dialogues,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAiStudioTaskLogRequest) Reset() {
	*x = UpdateAiStudioTaskLogRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAiStudioTaskLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAiStudioTaskLogRequest) ProtoMessage() {}

func (x *UpdateAiStudioTaskLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAiStudioTaskLogRequest.ProtoReflect.Descriptor instead.
func (*UpdateAiStudioTaskLogRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{30}
}

func (x *UpdateAiStudioTaskLogRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateAiStudioTaskLogRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UpdateAiStudioTaskLogRequest) GetPrompt() string {
	if x != nil {
		return x.Prompt
	}
	return ""
}

func (x *UpdateAiStudioTaskLogRequest) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

func (x *UpdateAiStudioTaskLogRequest) GetDialogues() []string {
	if x != nil {
		return x.Dialogues
	}
	return nil
}

// ListAiStudioTaskLogRequest is a request to list aiStudio task logs.
type ListAiStudioTaskLogRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// per_page is the number of results to return per page.
	PerPage int32 `protobuf:"varint,1,opt,name=per_page,json=perPage,proto3" json:"per_page,omitempty"`
	// page is the current page.
	Page          int32 `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTaskLogRequest) Reset() {
	*x = ListAiStudioTaskLogRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTaskLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTaskLogRequest) ProtoMessage() {}

func (x *ListAiStudioTaskLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTaskLogRequest.ProtoReflect.Descriptor instead.
func (*ListAiStudioTaskLogRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{31}
}

func (x *ListAiStudioTaskLogRequest) GetPerPage() int32 {
	if x != nil {
		return x.PerPage
	}
	return 0
}

func (x *ListAiStudioTaskLogRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

// ListAiStudioTaskLogData is a response to list aiStudio tasks.
type ListAiStudioTaskLogData struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// aistudio_tasks is the list of aistudio_tasks.
	Items []*AiStudioTaskLog `protobuf:"bytes,1,rep,name=items,proto3" json:"items,omitempty"`
	// total is the total count to retrieve the next page of results.
	Total         int32 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTaskLogData) Reset() {
	*x = ListAiStudioTaskLogData{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTaskLogData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTaskLogData) ProtoMessage() {}

func (x *ListAiStudioTaskLogData) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTaskLogData.ProtoReflect.Descriptor instead.
func (*ListAiStudioTaskLogData) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{32}
}

func (x *ListAiStudioTaskLogData) GetItems() []*AiStudioTaskLog {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *ListAiStudioTaskLogData) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// ListAiStudioTaskLogResponse is a response to list aiStudio task logs.
type ListAiStudioTaskLogResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// status is status.
	Status int32 `protobuf:"varint,1,opt,name=status,proto3" json:"status,omitempty"`
	// msg is msg.
	Msg string `protobuf:"bytes,2,opt,name=msg,proto3" json:"msg,omitempty"`
	// data is response data.
	Data          *ListAiStudioTaskLogData `protobuf:"bytes,3,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAiStudioTaskLogResponse) Reset() {
	*x = ListAiStudioTaskLogResponse{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAiStudioTaskLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAiStudioTaskLogResponse) ProtoMessage() {}

func (x *ListAiStudioTaskLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAiStudioTaskLogResponse.ProtoReflect.Descriptor instead.
func (*ListAiStudioTaskLogResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{33}
}

func (x *ListAiStudioTaskLogResponse) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *ListAiStudioTaskLogResponse) GetMsg() string {
	if x != nil {
		return x.Msg
	}
	return ""
}

func (x *ListAiStudioTaskLogResponse) GetData() *ListAiStudioTaskLogData {
	if x != nil {
		return x.Data
	}
	return nil
}

// HandleURLVerificationRequest 相关消息
type HandleURLVerificationRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// type 字段，预期值为 "url_verification"
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// token Slack 提供的 token (已弃用，但可能存在)
	Token string `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	// challenge 字段，Slack 会原样返回
	Challenge     string `protobuf:"bytes,3,opt,name=challenge,proto3" json:"challenge,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleURLVerificationRequest) Reset() {
	*x = HandleURLVerificationRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleURLVerificationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleURLVerificationRequest) ProtoMessage() {}

func (x *HandleURLVerificationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleURLVerificationRequest.ProtoReflect.Descriptor instead.
func (*HandleURLVerificationRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{34}
}

func (x *HandleURLVerificationRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *HandleURLVerificationRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *HandleURLVerificationRequest) GetChallenge() string {
	if x != nil {
		return x.Challenge
	}
	return ""
}

// HandleURLVerificationResponse 相关消息
type HandleURLVerificationResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// challenge 这个 gRPC 响应包含 challenge 字符串
	// 注意：HTTP 转码器需要特殊处理，将这个字段的值作为 text/plain 响应体返回给 Slack
	Challenge     string `protobuf:"bytes,1,opt,name=challenge,proto3" json:"challenge,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleURLVerificationResponse) Reset() {
	*x = HandleURLVerificationResponse{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleURLVerificationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleURLVerificationResponse) ProtoMessage() {}

func (x *HandleURLVerificationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleURLVerificationResponse.ProtoReflect.Descriptor instead.
func (*HandleURLVerificationResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{35}
}

func (x *HandleURLVerificationResponse) GetChallenge() string {
	if x != nil {
		return x.Challenge
	}
	return ""
}

// HandleEventCallbackRequest Callback 的外层结构
type HandleEventCallbackRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// token Slack 提供的 token (已弃用，但可能存在)
	Token string `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	// team_id Slack 提供的 team_id
	TeamId string `protobuf:"bytes,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	// api_app_id Slack 提供的 api_app_id
	ApiAppId string `protobuf:"bytes,3,opt,name=api_app_id,json=apiAppId,proto3" json:"api_app_id,omitempty"`
	// event 关键字段：嵌套的 event 对象。
	// 由于 event 的具体结构根据 event.type 不同而变化，
	// 使用 google.protobuf.Struct 可以最方便地让转码器直接映射 JSON 对象。
	// 在 Go 服务端，你需要检查这个 Struct 内部的 "type" 字段来确定具体事件类型。
	Event *structpb.Struct `protobuf:"bytes,4,opt,name=event,proto3" json:"event,omitempty"`
	// type 字段，预期值为 "event_callback"
	Type string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`
	// event_id Slack 提供的 event_id
	EventId string `protobuf:"bytes,6,opt,name=event_id,json=eventId,proto3" json:"event_id,omitempty"`
	// event_time 事件发生的 Unix 时间戳 (秒)
	EventTime int64 `protobuf:"varint,7,opt,name=event_time,json=eventTime,proto3" json:"event_time,omitempty"`
	// authorizations 授权信息 (如果存在)
	Authorizations []*Authorization `protobuf:"bytes,8,rep,name=authorizations,proto3" json:"authorizations,omitempty"`
	// is_ext_shared_channel 是否为外部共享频道
	IsExtSharedChannel bool `protobuf:"varint,9,opt,name=is_ext_shared_channel,json=isExtSharedChannel,proto3" json:"is_ext_shared_channel,omitempty"`
	// context_team_id 可选字段
	ContextTeamId *string `protobuf:"bytes,10,opt,name=context_team_id,json=contextTeamId,proto3,oneof" json:"context_team_id,omitempty"`
	// context_enterprise_id 可选字段
	ContextEnterpriseId *string `protobuf:"bytes,11,opt,name=context_enterprise_id,json=contextEnterpriseId,proto3,oneof" json:"context_enterprise_id,omitempty"`
	// challenge 字段，Slack 会原样返回
	Challenge     *string `protobuf:"bytes,12,opt,name=challenge,proto3,oneof" json:"challenge,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleEventCallbackRequest) Reset() {
	*x = HandleEventCallbackRequest{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleEventCallbackRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleEventCallbackRequest) ProtoMessage() {}

func (x *HandleEventCallbackRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleEventCallbackRequest.ProtoReflect.Descriptor instead.
func (*HandleEventCallbackRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{36}
}

func (x *HandleEventCallbackRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *HandleEventCallbackRequest) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

func (x *HandleEventCallbackRequest) GetApiAppId() string {
	if x != nil {
		return x.ApiAppId
	}
	return ""
}

func (x *HandleEventCallbackRequest) GetEvent() *structpb.Struct {
	if x != nil {
		return x.Event
	}
	return nil
}

func (x *HandleEventCallbackRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *HandleEventCallbackRequest) GetEventId() string {
	if x != nil {
		return x.EventId
	}
	return ""
}

func (x *HandleEventCallbackRequest) GetEventTime() int64 {
	if x != nil {
		return x.EventTime
	}
	return 0
}

func (x *HandleEventCallbackRequest) GetAuthorizations() []*Authorization {
	if x != nil {
		return x.Authorizations
	}
	return nil
}

func (x *HandleEventCallbackRequest) GetIsExtSharedChannel() bool {
	if x != nil {
		return x.IsExtSharedChannel
	}
	return false
}

func (x *HandleEventCallbackRequest) GetContextTeamId() string {
	if x != nil && x.ContextTeamId != nil {
		return *x.ContextTeamId
	}
	return ""
}

func (x *HandleEventCallbackRequest) GetContextEnterpriseId() string {
	if x != nil && x.ContextEnterpriseId != nil {
		return *x.ContextEnterpriseId
	}
	return ""
}

func (x *HandleEventCallbackRequest) GetChallenge() string {
	if x != nil && x.Challenge != nil {
		return *x.Challenge
	}
	return ""
}

// Event Callback 中的授权信息结构
type Authorization struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// enterprise_id 可选字段
	EnterpriseId *string `protobuf:"bytes,1,opt,name=enterprise_id,json=enterpriseId,proto3,oneof" json:"enterprise_id,omitempty"`
	// team_id Slack 提供的 team_id
	TeamId string `protobuf:"bytes,2,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	// user_id Slack 提供的 user_id
	UserId string `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// is_bot 是否为 Bot 用户
	IsBot bool `protobuf:"varint,4,opt,name=is_bot,json=isBot,proto3" json:"is_bot,omitempty"`
	// is_enterprise_install 是否为企业版 Slack
	IsEnterpriseInstall bool `protobuf:"varint,5,opt,name=is_enterprise_install,json=isEnterpriseInstall,proto3" json:"is_enterprise_install,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Authorization) Reset() {
	*x = Authorization{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Authorization) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Authorization) ProtoMessage() {}

func (x *Authorization) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Authorization.ProtoReflect.Descriptor instead.
func (*Authorization) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{37}
}

func (x *Authorization) GetEnterpriseId() string {
	if x != nil && x.EnterpriseId != nil {
		return *x.EnterpriseId
	}
	return ""
}

func (x *Authorization) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

func (x *Authorization) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *Authorization) GetIsBot() bool {
	if x != nil {
		return x.IsBot
	}
	return false
}

func (x *Authorization) GetIsEnterpriseInstall() bool {
	if x != nil {
		return x.IsEnterpriseInstall
	}
	return false
}

// AppMentionEvent 是一个具体的事件类型，它继承自 EventCallbackRequest.event，并且定义了与 app_mention 相关的特定字段。
// --- 具体事件类型的 Protobuf 定义 (可选，主要用于 Go 代码内部) ---
// 虽然 EventCallbackRequest.event 使用了 Struct，
// 但你可以在 Go 代码中将这个 Struct 反序列化为你定义的具体事件 Proto 消息，
// 以获得类型安全。这里定义一个 app_mention 的例子。
type AppMentionEvent struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// type 字段，预期值为 "app_mention"
	Type string `protobuf:"bytes,1,opt,name=type,proto3" json:"type,omitempty"`
	// client_msg_id 可选字段
	ClientMsgId *string `protobuf:"bytes,2,opt,name=client_msg_id,json=clientMsgId,proto3,oneof" json:"client_msg_id,omitempty"`
	// text 包含 @提及 的消息文本
	Text string `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	// user 发送提及的用户的 ID
	User string `protobuf:"bytes,4,opt,name=user,proto3" json:"user,omitempty"`
	// ts 消息的时间戳字符串
	Ts string `protobuf:"bytes,5,opt,name=ts,proto3" json:"ts,omitempty"`
	// team 可选字段，根据需要添加:
	Team *string `protobuf:"bytes,6,opt,name=team,proto3,oneof" json:"team,omitempty"`
	// channel 频道 ID
	Channel string `protobuf:"bytes,7,opt,name=channel,proto3" json:"channel,omitempty"`
	// event_ts 事件的时间戳字符串
	EventTs string `protobuf:"bytes,8,opt,name=event_ts,json=eventTs,proto3" json:"event_ts,omitempty"`
	// thread_ts 可选字段，根据需要添加, 如果是在线程中的提及
	ThreadTs      *string `protobuf:"bytes,10,opt,name=thread_ts,json=threadTs,proto3,oneof" json:"thread_ts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AppMentionEvent) Reset() {
	*x = AppMentionEvent{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AppMentionEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AppMentionEvent) ProtoMessage() {}

func (x *AppMentionEvent) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AppMentionEvent.ProtoReflect.Descriptor instead.
func (*AppMentionEvent) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{38}
}

func (x *AppMentionEvent) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *AppMentionEvent) GetClientMsgId() string {
	if x != nil && x.ClientMsgId != nil {
		return *x.ClientMsgId
	}
	return ""
}

func (x *AppMentionEvent) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *AppMentionEvent) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *AppMentionEvent) GetTs() string {
	if x != nil {
		return x.Ts
	}
	return ""
}

func (x *AppMentionEvent) GetTeam() string {
	if x != nil && x.Team != nil {
		return *x.Team
	}
	return ""
}

func (x *AppMentionEvent) GetChannel() string {
	if x != nil {
		return x.Channel
	}
	return ""
}

func (x *AppMentionEvent) GetEventTs() string {
	if x != nil {
		return x.EventTs
	}
	return ""
}

func (x *AppMentionEvent) GetThreadTs() string {
	if x != nil && x.ThreadTs != nil {
		return *x.ThreadTs
	}
	return ""
}

// HandleEventCallbackResponse 相关消息
type HandleEventCallbackResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// challenge 这个 gRPC 响应包含 challenge 字符串
	// 注意：HTTP 转码器需要特殊处理，将这个字段的值作为 text/plain 响应体返回给 Slack
	Challenge     string `protobuf:"bytes,1,opt,name=challenge,proto3" json:"challenge,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandleEventCallbackResponse) Reset() {
	*x = HandleEventCallbackResponse{}
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandleEventCallbackResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandleEventCallbackResponse) ProtoMessage() {}

func (x *HandleEventCallbackResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandleEventCallbackResponse.ProtoReflect.Descriptor instead.
func (*HandleEventCallbackResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP(), []int{39}
}

func (x *HandleEventCallbackResponse) GetChallenge() string {
	if x != nil {
		return x.Challenge
	}
	return ""
}

var File_backend_proto_aistudio_v1_aistudio_proto protoreflect.FileDescriptor

const file_backend_proto_aistudio_v1_aistudio_proto_rawDesc = "" +
	"\n" +
	"(backend/proto/aistudio/v1/aistudio.proto\x12\x19backend.proto.aistudio.v1\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x96\x01\n" +
	"\x1fRequestAiStudioMcpClientRequest\x12\x1d\n" +
	"\n" +
	"gemini_key\x18\x01 \x01(\tR\tgeminiKey\x12\x16\n" +
	"\x06prompt\x18\x02 \x01(\tR\x06prompt\x12\x12\n" +
	"\x04mcps\x18\x03 \x01(\tR\x04mcps\x12\x12\n" +
	"\x04envs\x18\x04 \x01(\tR\x04envs\x12\x14\n" +
	"\x05model\x18\x05 \x01(\tR\x05model\"\x89\x01\n" +
	" RequestAiStudioMcpClientResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12;\n" +
	"\x04data\x18\x03 \x01(\v2'.backend.proto.aistudio.v1.ResponseDataR\x04data\",\n" +
	"\fResponseData\x12\x1c\n" +
	"\tdialogues\x18\x01 \x03(\tR\tdialogues\"\x89\x01\n" +
	"\x1dCreateAiStudioTemplateRequest\x12\x14\n" +
	"\x05model\x18\x01 \x01(\tR\x05model\x12\x16\n" +
	"\x06prompt\x18\x02 \x01(\tR\x06prompt\x12\x12\n" +
	"\x04mcps\x18\x03 \x01(\tR\x04mcps\x12\x12\n" +
	"\x04envs\x18\x04 \x01(\tR\x04envs\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\",\n" +
	"\x1aGetAiStudioTemplateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x81\x01\n" +
	"\x10AiStudioTemplate\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12C\n" +
	"\x04data\x18\x03 \x01(\v2/.backend.proto.aistudio.v1.AiStudioTemplateDataR\x04data\"\x97\x01\n" +
	"\x14AiStudioTemplateData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05model\x18\x02 \x01(\tR\x05model\x12\x16\n" +
	"\x06prompt\x18\x03 \x01(\tR\x06prompt\x12\x12\n" +
	"\x04mcps\x18\x04 \x01(\tR\x04mcps\x12\x19\n" +
	"\benv_keys\x18\x05 \x01(\tR\aenvKeys\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\"\xa0\x01\n" +
	"\x1dUpdateAiStudioTemplateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x14\n" +
	"\x05model\x18\x02 \x01(\tR\x05model\x12\x16\n" +
	"\x06prompt\x18\x03 \x01(\tR\x06prompt\x12\x12\n" +
	"\x04mcps\x18\x04 \x01(\tR\x04mcps\x12\x19\n" +
	"\benv_keys\x18\x05 \x01(\tR\aenvKeys\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\"/\n" +
	"\x1dDeleteAiStudioTemplateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"L\n" +
	"\x1bListAiStudioTemplateRequest\x12\x19\n" +
	"\bper_page\x18\x01 \x01(\x05R\aperPage\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\"w\n" +
	"\x18ListAiStudioTemplateData\x12E\n" +
	"\x05items\x18\x01 \x03(\v2/.backend.proto.aistudio.v1.AiStudioTemplateDataR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"\x91\x01\n" +
	"\x1cListAiStudioTemplateResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12G\n" +
	"\x04data\x18\x03 \x01(\v23.backend.proto.aistudio.v1.ListAiStudioTemplateDataR\x04data\"\x1f\n" +
	"\x1dListAiStudioTemplateIDRequest\"@\n" +
	"\x12AiStudioTemplateID\x12\x14\n" +
	"\x05label\x18\x01 \x01(\tR\x05label\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value\"e\n" +
	"\x1aListAiStudioTemplateIDData\x12G\n" +
	"\aoptions\x18\x01 \x03(\v2-.backend.proto.aistudio.v1.AiStudioTemplateIDR\aoptions\"\x95\x01\n" +
	"\x1eListAiStudioTemplateIDResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12I\n" +
	"\x04data\x18\x03 \x01(\v25.backend.proto.aistudio.v1.ListAiStudioTemplateIDDataR\x04data\"\xae\x01\n" +
	"\x19CreateAiStudioTaskRequest\x12\x12\n" +
	"\x04task\x18\x01 \x01(\tR\x04task\x12\x1f\n" +
	"\vtemplate_id\x18\x02 \x01(\x03R\n" +
	"templateId\x12\x12\n" +
	"\x04envs\x18\x03 \x01(\tR\x04envs\x12\x15\n" +
	"\x06ai_key\x18\x04 \x01(\tR\x05aiKey\x12\x1d\n" +
	"\n" +
	"im_channel\x18\x05 \x01(\tR\timChannel\x12\x12\n" +
	"\x04spec\x18\x06 \x01(\tR\x04spec\"y\n" +
	"\fAiStudioTask\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12?\n" +
	"\x04data\x18\x03 \x01(\v2+.backend.proto.aistudio.v1.AiStudioTaskDataR\x04data\"\xb5\x01\n" +
	"\x10AiStudioTaskData\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04task\x18\x02 \x01(\tR\x04task\x12\x1f\n" +
	"\vtemplate_id\x18\x03 \x01(\x03R\n" +
	"templateId\x12\x12\n" +
	"\x04envs\x18\x04 \x01(\tR\x04envs\x12\x15\n" +
	"\x06ai_key\x18\x05 \x01(\tR\x05aiKey\x12\x1d\n" +
	"\n" +
	"im_channel\x18\x06 \x01(\tR\timChannel\x12\x12\n" +
	"\x04spec\x18\a \x01(\tR\x04spec\"(\n" +
	"\x16GetAiStudioTaskRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"P\n" +
	"\x16RunAiStudioTaskRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04envs\x18\x03 \x01(\tR\x04envs\"W\n" +
	"\x17RunAiStudioTaskResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12\x12\n" +
	"\x04data\x18\x03 \x03(\tR\x04data\"\xbe\x01\n" +
	"\x19UpdateAiStudioTaskRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04task\x18\x02 \x01(\tR\x04task\x12\x1f\n" +
	"\vtemplate_id\x18\x03 \x01(\x03R\n" +
	"templateId\x12\x12\n" +
	"\x04envs\x18\x04 \x01(\tR\x04envs\x12\x15\n" +
	"\x06ai_key\x18\x05 \x01(\tR\x05aiKey\x12\x1d\n" +
	"\n" +
	"im_channel\x18\x06 \x01(\tR\timChannel\x12\x12\n" +
	"\x04spec\x18\a \x01(\tR\x04spec\"+\n" +
	"\x19DeleteAiStudioTaskRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"H\n" +
	"\x17ListAiStudioTaskRequest\x12\x19\n" +
	"\bper_page\x18\x01 \x01(\x05R\aperPage\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\"o\n" +
	"\x14ListAiStudioTaskData\x12A\n" +
	"\x05items\x18\x01 \x03(\v2+.backend.proto.aistudio.v1.AiStudioTaskDataR\x05items\x12\x14\n" +
	"\x05total\x18\x04 \x01(\x05R\x05total\"\x89\x01\n" +
	"\x18ListAiStudioTaskResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12C\n" +
	"\x04data\x18\x03 \x01(\v2/.backend.proto.aistudio.v1.ListAiStudioTaskDataR\x04data\"\xc1\x01\n" +
	"\x0fAiStudioTaskLog\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\atask_id\x18\x02 \x01(\x03R\x06taskId\x12\x16\n" +
	"\x06prompt\x18\x03 \x01(\tR\x06prompt\x12\x12\n" +
	"\x04envs\x18\x04 \x01(\tR\x04envs\x12\x1c\n" +
	"\tdialogues\x18\x05 \x03(\tR\tdialogues\x12;\n" +
	"\vcreate_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\"\x81\x01\n" +
	"\x1cCreateAiStudioTaskLogRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\x03R\x06taskId\x12\x16\n" +
	"\x06prompt\x18\x02 \x01(\tR\x06prompt\x12\x12\n" +
	"\x04envs\x18\x03 \x01(\tR\x04envs\x12\x1c\n" +
	"\tdialogues\x18\x04 \x03(\tR\tdialogues\"+\n" +
	"\x19GetAiStudioTaskLogRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x91\x01\n" +
	"\x1cUpdateAiStudioTaskLogRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\atask_id\x18\x02 \x01(\x03R\x06taskId\x12\x16\n" +
	"\x06prompt\x18\x03 \x01(\tR\x06prompt\x12\x12\n" +
	"\x04envs\x18\x04 \x01(\tR\x04envs\x12\x1c\n" +
	"\tdialogues\x18\x05 \x03(\tR\tdialogues\"K\n" +
	"\x1aListAiStudioTaskLogRequest\x12\x19\n" +
	"\bper_page\x18\x01 \x01(\x05R\aperPage\x12\x12\n" +
	"\x04page\x18\x02 \x01(\x05R\x04page\"q\n" +
	"\x17ListAiStudioTaskLogData\x12@\n" +
	"\x05items\x18\x01 \x03(\v2*.backend.proto.aistudio.v1.AiStudioTaskLogR\x05items\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"\x8f\x01\n" +
	"\x1bListAiStudioTaskLogResponse\x12\x16\n" +
	"\x06status\x18\x01 \x01(\x05R\x06status\x12\x10\n" +
	"\x03msg\x18\x02 \x01(\tR\x03msg\x12F\n" +
	"\x04data\x18\x03 \x01(\v22.backend.proto.aistudio.v1.ListAiStudioTaskLogDataR\x04data\"f\n" +
	"\x1cHandleURLVerificationRequest\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12\x14\n" +
	"\x05token\x18\x02 \x01(\tR\x05token\x12\x1c\n" +
	"\tchallenge\x18\x03 \x01(\tR\tchallenge\"=\n" +
	"\x1dHandleURLVerificationResponse\x12\x1c\n" +
	"\tchallenge\x18\x01 \x01(\tR\tchallenge\"\xb0\x04\n" +
	"\x1aHandleEventCallbackRequest\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x17\n" +
	"\ateam_id\x18\x02 \x01(\tR\x06teamId\x12\x1c\n" +
	"\n" +
	"api_app_id\x18\x03 \x01(\tR\bapiAppId\x12-\n" +
	"\x05event\x18\x04 \x01(\v2\x17.google.protobuf.StructR\x05event\x12\x12\n" +
	"\x04type\x18\x05 \x01(\tR\x04type\x12\x19\n" +
	"\bevent_id\x18\x06 \x01(\tR\aeventId\x12\x1d\n" +
	"\n" +
	"event_time\x18\a \x01(\x03R\teventTime\x12P\n" +
	"\x0eauthorizations\x18\b \x03(\v2(.backend.proto.aistudio.v1.AuthorizationR\x0eauthorizations\x121\n" +
	"\x15is_ext_shared_channel\x18\t \x01(\bR\x12isExtSharedChannel\x12+\n" +
	"\x0fcontext_team_id\x18\n" +
	" \x01(\tH\x00R\rcontextTeamId\x88\x01\x01\x127\n" +
	"\x15context_enterprise_id\x18\v \x01(\tH\x01R\x13contextEnterpriseId\x88\x01\x01\x12!\n" +
	"\tchallenge\x18\f \x01(\tH\x02R\tchallenge\x88\x01\x01B\x12\n" +
	"\x10_context_team_idB\x18\n" +
	"\x16_context_enterprise_idB\f\n" +
	"\n" +
	"_challenge\"\xc8\x01\n" +
	"\rAuthorization\x12(\n" +
	"\renterprise_id\x18\x01 \x01(\tH\x00R\fenterpriseId\x88\x01\x01\x12\x17\n" +
	"\ateam_id\x18\x02 \x01(\tR\x06teamId\x12\x17\n" +
	"\auser_id\x18\x03 \x01(\tR\x06userId\x12\x15\n" +
	"\x06is_bot\x18\x04 \x01(\bR\x05isBot\x122\n" +
	"\x15is_enterprise_install\x18\x05 \x01(\bR\x13isEnterpriseInstallB\x10\n" +
	"\x0e_enterprise_id\"\x9f\x02\n" +
	"\x0fAppMentionEvent\x12\x12\n" +
	"\x04type\x18\x01 \x01(\tR\x04type\x12'\n" +
	"\rclient_msg_id\x18\x02 \x01(\tH\x00R\vclientMsgId\x88\x01\x01\x12\x12\n" +
	"\x04text\x18\x03 \x01(\tR\x04text\x12\x12\n" +
	"\x04user\x18\x04 \x01(\tR\x04user\x12\x0e\n" +
	"\x02ts\x18\x05 \x01(\tR\x02ts\x12\x17\n" +
	"\x04team\x18\x06 \x01(\tH\x01R\x04team\x88\x01\x01\x12\x18\n" +
	"\achannel\x18\a \x01(\tR\achannel\x12\x19\n" +
	"\bevent_ts\x18\b \x01(\tR\aeventTs\x12 \n" +
	"\tthread_ts\x18\n" +
	" \x01(\tH\x02R\bthreadTs\x88\x01\x01B\x10\n" +
	"\x0e_client_msg_idB\a\n" +
	"\x05_teamB\f\n" +
	"\n" +
	"_thread_ts\";\n" +
	"\x1bHandleEventCallbackResponse\x12\x1c\n" +
	"\tchallenge\x18\x01 \x01(\tR\tchallenge2\xef\x11\n" +
	"\x0fAIStudioService\x12\x93\x01\n" +
	"\x18RequestAiStudioMcpClient\x12:.backend.proto.aistudio.v1.RequestAiStudioMcpClientRequest\x1a;.backend.proto.aistudio.v1.RequestAiStudioMcpClientResponse\x12\x7f\n" +
	"\x16CreateAiStudioTemplate\x128.backend.proto.aistudio.v1.CreateAiStudioTemplateRequest\x1a+.backend.proto.aistudio.v1.AiStudioTemplate\x12y\n" +
	"\x13GetAiStudioTemplate\x125.backend.proto.aistudio.v1.GetAiStudioTemplateRequest\x1a+.backend.proto.aistudio.v1.AiStudioTemplate\x12\x7f\n" +
	"\x16UpdateAiStudioTemplate\x128.backend.proto.aistudio.v1.UpdateAiStudioTemplateRequest\x1a+.backend.proto.aistudio.v1.AiStudioTemplate\x12j\n" +
	"\x16DeleteAiStudioTemplate\x128.backend.proto.aistudio.v1.DeleteAiStudioTemplateRequest\x1a\x16.google.protobuf.Empty\x12\x87\x01\n" +
	"\x14ListAiStudioTemplate\x126.backend.proto.aistudio.v1.ListAiStudioTemplateRequest\x1a7.backend.proto.aistudio.v1.ListAiStudioTemplateResponse\x12\x8d\x01\n" +
	"\x16ListAiStudioTemplateID\x128.backend.proto.aistudio.v1.ListAiStudioTemplateIDRequest\x1a9.backend.proto.aistudio.v1.ListAiStudioTemplateIDResponse\x12s\n" +
	"\x12CreateAiStudioTask\x124.backend.proto.aistudio.v1.CreateAiStudioTaskRequest\x1a'.backend.proto.aistudio.v1.AiStudioTask\x12m\n" +
	"\x0fGetAiStudioTask\x121.backend.proto.aistudio.v1.GetAiStudioTaskRequest\x1a'.backend.proto.aistudio.v1.AiStudioTask\x12x\n" +
	"\x0fRunAiStudioTask\x121.backend.proto.aistudio.v1.RunAiStudioTaskRequest\x1a2.backend.proto.aistudio.v1.RunAiStudioTaskResponse\x12s\n" +
	"\x12UpdateAiStudioTask\x124.backend.proto.aistudio.v1.UpdateAiStudioTaskRequest\x1a'.backend.proto.aistudio.v1.AiStudioTask\x12b\n" +
	"\x12DeleteAiStudioTask\x124.backend.proto.aistudio.v1.DeleteAiStudioTaskRequest\x1a\x16.google.protobuf.Empty\x12{\n" +
	"\x10ListAiStudioTask\x122.backend.proto.aistudio.v1.ListAiStudioTaskRequest\x1a3.backend.proto.aistudio.v1.ListAiStudioTaskResponse\x12|\n" +
	"\x15CreateAiStudioTaskLog\x127.backend.proto.aistudio.v1.CreateAiStudioTaskLogRequest\x1a*.backend.proto.aistudio.v1.AiStudioTaskLog\x12v\n" +
	"\x12GetAiStudioTaskLog\x124.backend.proto.aistudio.v1.GetAiStudioTaskLogRequest\x1a*.backend.proto.aistudio.v1.AiStudioTaskLog\x12\x84\x01\n" +
	"\x13ListAiStudioTaskLog\x125.backend.proto.aistudio.v1.ListAiStudioTaskLogRequest\x1a6.backend.proto.aistudio.v1.ListAiStudioTaskLogResponse\x12\x8a\x01\n" +
	"\x15HandleURLVerification\x127.backend.proto.aistudio.v1.HandleURLVerificationRequest\x1a8.backend.proto.aistudio.v1.HandleURLVerificationResponse\x12\x84\x01\n" +
	"\x13HandleEventCallback\x125.backend.proto.aistudio.v1.HandleEventCallbackRequest\x1a6.backend.proto.aistudio.v1.HandleEventCallbackResponseBi\n" +
	"#com.moego.backend.proto.aistudio.v1P\<EMAIL>/MoeGolibrary/moego/backend/proto/aistudio/v1;aistudiob\x06proto3"

var (
	file_backend_proto_aistudio_v1_aistudio_proto_rawDescOnce sync.Once
	file_backend_proto_aistudio_v1_aistudio_proto_rawDescData []byte
)

func file_backend_proto_aistudio_v1_aistudio_proto_rawDescGZIP() []byte {
	file_backend_proto_aistudio_v1_aistudio_proto_rawDescOnce.Do(func() {
		file_backend_proto_aistudio_v1_aistudio_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_aistudio_v1_aistudio_proto_rawDesc), len(file_backend_proto_aistudio_v1_aistudio_proto_rawDesc)))
	})
	return file_backend_proto_aistudio_v1_aistudio_proto_rawDescData
}

var file_backend_proto_aistudio_v1_aistudio_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_backend_proto_aistudio_v1_aistudio_proto_goTypes = []any{
	(*RequestAiStudioMcpClientRequest)(nil),  // 0: backend.proto.aistudio.v1.RequestAiStudioMcpClientRequest
	(*RequestAiStudioMcpClientResponse)(nil), // 1: backend.proto.aistudio.v1.RequestAiStudioMcpClientResponse
	(*ResponseData)(nil),                     // 2: backend.proto.aistudio.v1.ResponseData
	(*CreateAiStudioTemplateRequest)(nil),    // 3: backend.proto.aistudio.v1.CreateAiStudioTemplateRequest
	(*GetAiStudioTemplateRequest)(nil),       // 4: backend.proto.aistudio.v1.GetAiStudioTemplateRequest
	(*AiStudioTemplate)(nil),                 // 5: backend.proto.aistudio.v1.AiStudioTemplate
	(*AiStudioTemplateData)(nil),             // 6: backend.proto.aistudio.v1.AiStudioTemplateData
	(*UpdateAiStudioTemplateRequest)(nil),    // 7: backend.proto.aistudio.v1.UpdateAiStudioTemplateRequest
	(*DeleteAiStudioTemplateRequest)(nil),    // 8: backend.proto.aistudio.v1.DeleteAiStudioTemplateRequest
	(*ListAiStudioTemplateRequest)(nil),      // 9: backend.proto.aistudio.v1.ListAiStudioTemplateRequest
	(*ListAiStudioTemplateData)(nil),         // 10: backend.proto.aistudio.v1.ListAiStudioTemplateData
	(*ListAiStudioTemplateResponse)(nil),     // 11: backend.proto.aistudio.v1.ListAiStudioTemplateResponse
	(*ListAiStudioTemplateIDRequest)(nil),    // 12: backend.proto.aistudio.v1.ListAiStudioTemplateIDRequest
	(*AiStudioTemplateID)(nil),               // 13: backend.proto.aistudio.v1.AiStudioTemplateID
	(*ListAiStudioTemplateIDData)(nil),       // 14: backend.proto.aistudio.v1.ListAiStudioTemplateIDData
	(*ListAiStudioTemplateIDResponse)(nil),   // 15: backend.proto.aistudio.v1.ListAiStudioTemplateIDResponse
	(*CreateAiStudioTaskRequest)(nil),        // 16: backend.proto.aistudio.v1.CreateAiStudioTaskRequest
	(*AiStudioTask)(nil),                     // 17: backend.proto.aistudio.v1.AiStudioTask
	(*AiStudioTaskData)(nil),                 // 18: backend.proto.aistudio.v1.AiStudioTaskData
	(*GetAiStudioTaskRequest)(nil),           // 19: backend.proto.aistudio.v1.GetAiStudioTaskRequest
	(*RunAiStudioTaskRequest)(nil),           // 20: backend.proto.aistudio.v1.RunAiStudioTaskRequest
	(*RunAiStudioTaskResponse)(nil),          // 21: backend.proto.aistudio.v1.RunAiStudioTaskResponse
	(*UpdateAiStudioTaskRequest)(nil),        // 22: backend.proto.aistudio.v1.UpdateAiStudioTaskRequest
	(*DeleteAiStudioTaskRequest)(nil),        // 23: backend.proto.aistudio.v1.DeleteAiStudioTaskRequest
	(*ListAiStudioTaskRequest)(nil),          // 24: backend.proto.aistudio.v1.ListAiStudioTaskRequest
	(*ListAiStudioTaskData)(nil),             // 25: backend.proto.aistudio.v1.ListAiStudioTaskData
	(*ListAiStudioTaskResponse)(nil),         // 26: backend.proto.aistudio.v1.ListAiStudioTaskResponse
	(*AiStudioTaskLog)(nil),                  // 27: backend.proto.aistudio.v1.AiStudioTaskLog
	(*CreateAiStudioTaskLogRequest)(nil),     // 28: backend.proto.aistudio.v1.CreateAiStudioTaskLogRequest
	(*GetAiStudioTaskLogRequest)(nil),        // 29: backend.proto.aistudio.v1.GetAiStudioTaskLogRequest
	(*UpdateAiStudioTaskLogRequest)(nil),     // 30: backend.proto.aistudio.v1.UpdateAiStudioTaskLogRequest
	(*ListAiStudioTaskLogRequest)(nil),       // 31: backend.proto.aistudio.v1.ListAiStudioTaskLogRequest
	(*ListAiStudioTaskLogData)(nil),          // 32: backend.proto.aistudio.v1.ListAiStudioTaskLogData
	(*ListAiStudioTaskLogResponse)(nil),      // 33: backend.proto.aistudio.v1.ListAiStudioTaskLogResponse
	(*HandleURLVerificationRequest)(nil),     // 34: backend.proto.aistudio.v1.HandleURLVerificationRequest
	(*HandleURLVerificationResponse)(nil),    // 35: backend.proto.aistudio.v1.HandleURLVerificationResponse
	(*HandleEventCallbackRequest)(nil),       // 36: backend.proto.aistudio.v1.HandleEventCallbackRequest
	(*Authorization)(nil),                    // 37: backend.proto.aistudio.v1.Authorization
	(*AppMentionEvent)(nil),                  // 38: backend.proto.aistudio.v1.AppMentionEvent
	(*HandleEventCallbackResponse)(nil),      // 39: backend.proto.aistudio.v1.HandleEventCallbackResponse
	(*timestamppb.Timestamp)(nil),            // 40: google.protobuf.Timestamp
	(*structpb.Struct)(nil),                  // 41: google.protobuf.Struct
	(*emptypb.Empty)(nil),                    // 42: google.protobuf.Empty
}
var file_backend_proto_aistudio_v1_aistudio_proto_depIdxs = []int32{
	2,  // 0: backend.proto.aistudio.v1.RequestAiStudioMcpClientResponse.data:type_name -> backend.proto.aistudio.v1.ResponseData
	6,  // 1: backend.proto.aistudio.v1.AiStudioTemplate.data:type_name -> backend.proto.aistudio.v1.AiStudioTemplateData
	6,  // 2: backend.proto.aistudio.v1.ListAiStudioTemplateData.items:type_name -> backend.proto.aistudio.v1.AiStudioTemplateData
	10, // 3: backend.proto.aistudio.v1.ListAiStudioTemplateResponse.data:type_name -> backend.proto.aistudio.v1.ListAiStudioTemplateData
	13, // 4: backend.proto.aistudio.v1.ListAiStudioTemplateIDData.options:type_name -> backend.proto.aistudio.v1.AiStudioTemplateID
	14, // 5: backend.proto.aistudio.v1.ListAiStudioTemplateIDResponse.data:type_name -> backend.proto.aistudio.v1.ListAiStudioTemplateIDData
	18, // 6: backend.proto.aistudio.v1.AiStudioTask.data:type_name -> backend.proto.aistudio.v1.AiStudioTaskData
	18, // 7: backend.proto.aistudio.v1.ListAiStudioTaskData.items:type_name -> backend.proto.aistudio.v1.AiStudioTaskData
	25, // 8: backend.proto.aistudio.v1.ListAiStudioTaskResponse.data:type_name -> backend.proto.aistudio.v1.ListAiStudioTaskData
	40, // 9: backend.proto.aistudio.v1.AiStudioTaskLog.create_time:type_name -> google.protobuf.Timestamp
	27, // 10: backend.proto.aistudio.v1.ListAiStudioTaskLogData.items:type_name -> backend.proto.aistudio.v1.AiStudioTaskLog
	32, // 11: backend.proto.aistudio.v1.ListAiStudioTaskLogResponse.data:type_name -> backend.proto.aistudio.v1.ListAiStudioTaskLogData
	41, // 12: backend.proto.aistudio.v1.HandleEventCallbackRequest.event:type_name -> google.protobuf.Struct
	37, // 13: backend.proto.aistudio.v1.HandleEventCallbackRequest.authorizations:type_name -> backend.proto.aistudio.v1.Authorization
	0,  // 14: backend.proto.aistudio.v1.AIStudioService.RequestAiStudioMcpClient:input_type -> backend.proto.aistudio.v1.RequestAiStudioMcpClientRequest
	3,  // 15: backend.proto.aistudio.v1.AIStudioService.CreateAiStudioTemplate:input_type -> backend.proto.aistudio.v1.CreateAiStudioTemplateRequest
	4,  // 16: backend.proto.aistudio.v1.AIStudioService.GetAiStudioTemplate:input_type -> backend.proto.aistudio.v1.GetAiStudioTemplateRequest
	7,  // 17: backend.proto.aistudio.v1.AIStudioService.UpdateAiStudioTemplate:input_type -> backend.proto.aistudio.v1.UpdateAiStudioTemplateRequest
	8,  // 18: backend.proto.aistudio.v1.AIStudioService.DeleteAiStudioTemplate:input_type -> backend.proto.aistudio.v1.DeleteAiStudioTemplateRequest
	9,  // 19: backend.proto.aistudio.v1.AIStudioService.ListAiStudioTemplate:input_type -> backend.proto.aistudio.v1.ListAiStudioTemplateRequest
	12, // 20: backend.proto.aistudio.v1.AIStudioService.ListAiStudioTemplateID:input_type -> backend.proto.aistudio.v1.ListAiStudioTemplateIDRequest
	16, // 21: backend.proto.aistudio.v1.AIStudioService.CreateAiStudioTask:input_type -> backend.proto.aistudio.v1.CreateAiStudioTaskRequest
	19, // 22: backend.proto.aistudio.v1.AIStudioService.GetAiStudioTask:input_type -> backend.proto.aistudio.v1.GetAiStudioTaskRequest
	20, // 23: backend.proto.aistudio.v1.AIStudioService.RunAiStudioTask:input_type -> backend.proto.aistudio.v1.RunAiStudioTaskRequest
	22, // 24: backend.proto.aistudio.v1.AIStudioService.UpdateAiStudioTask:input_type -> backend.proto.aistudio.v1.UpdateAiStudioTaskRequest
	23, // 25: backend.proto.aistudio.v1.AIStudioService.DeleteAiStudioTask:input_type -> backend.proto.aistudio.v1.DeleteAiStudioTaskRequest
	24, // 26: backend.proto.aistudio.v1.AIStudioService.ListAiStudioTask:input_type -> backend.proto.aistudio.v1.ListAiStudioTaskRequest
	28, // 27: backend.proto.aistudio.v1.AIStudioService.CreateAiStudioTaskLog:input_type -> backend.proto.aistudio.v1.CreateAiStudioTaskLogRequest
	29, // 28: backend.proto.aistudio.v1.AIStudioService.GetAiStudioTaskLog:input_type -> backend.proto.aistudio.v1.GetAiStudioTaskLogRequest
	31, // 29: backend.proto.aistudio.v1.AIStudioService.ListAiStudioTaskLog:input_type -> backend.proto.aistudio.v1.ListAiStudioTaskLogRequest
	34, // 30: backend.proto.aistudio.v1.AIStudioService.HandleURLVerification:input_type -> backend.proto.aistudio.v1.HandleURLVerificationRequest
	36, // 31: backend.proto.aistudio.v1.AIStudioService.HandleEventCallback:input_type -> backend.proto.aistudio.v1.HandleEventCallbackRequest
	1,  // 32: backend.proto.aistudio.v1.AIStudioService.RequestAiStudioMcpClient:output_type -> backend.proto.aistudio.v1.RequestAiStudioMcpClientResponse
	5,  // 33: backend.proto.aistudio.v1.AIStudioService.CreateAiStudioTemplate:output_type -> backend.proto.aistudio.v1.AiStudioTemplate
	5,  // 34: backend.proto.aistudio.v1.AIStudioService.GetAiStudioTemplate:output_type -> backend.proto.aistudio.v1.AiStudioTemplate
	5,  // 35: backend.proto.aistudio.v1.AIStudioService.UpdateAiStudioTemplate:output_type -> backend.proto.aistudio.v1.AiStudioTemplate
	42, // 36: backend.proto.aistudio.v1.AIStudioService.DeleteAiStudioTemplate:output_type -> google.protobuf.Empty
	11, // 37: backend.proto.aistudio.v1.AIStudioService.ListAiStudioTemplate:output_type -> backend.proto.aistudio.v1.ListAiStudioTemplateResponse
	15, // 38: backend.proto.aistudio.v1.AIStudioService.ListAiStudioTemplateID:output_type -> backend.proto.aistudio.v1.ListAiStudioTemplateIDResponse
	17, // 39: backend.proto.aistudio.v1.AIStudioService.CreateAiStudioTask:output_type -> backend.proto.aistudio.v1.AiStudioTask
	17, // 40: backend.proto.aistudio.v1.AIStudioService.GetAiStudioTask:output_type -> backend.proto.aistudio.v1.AiStudioTask
	21, // 41: backend.proto.aistudio.v1.AIStudioService.RunAiStudioTask:output_type -> backend.proto.aistudio.v1.RunAiStudioTaskResponse
	17, // 42: backend.proto.aistudio.v1.AIStudioService.UpdateAiStudioTask:output_type -> backend.proto.aistudio.v1.AiStudioTask
	42, // 43: backend.proto.aistudio.v1.AIStudioService.DeleteAiStudioTask:output_type -> google.protobuf.Empty
	26, // 44: backend.proto.aistudio.v1.AIStudioService.ListAiStudioTask:output_type -> backend.proto.aistudio.v1.ListAiStudioTaskResponse
	27, // 45: backend.proto.aistudio.v1.AIStudioService.CreateAiStudioTaskLog:output_type -> backend.proto.aistudio.v1.AiStudioTaskLog
	27, // 46: backend.proto.aistudio.v1.AIStudioService.GetAiStudioTaskLog:output_type -> backend.proto.aistudio.v1.AiStudioTaskLog
	33, // 47: backend.proto.aistudio.v1.AIStudioService.ListAiStudioTaskLog:output_type -> backend.proto.aistudio.v1.ListAiStudioTaskLogResponse
	35, // 48: backend.proto.aistudio.v1.AIStudioService.HandleURLVerification:output_type -> backend.proto.aistudio.v1.HandleURLVerificationResponse
	39, // 49: backend.proto.aistudio.v1.AIStudioService.HandleEventCallback:output_type -> backend.proto.aistudio.v1.HandleEventCallbackResponse
	32, // [32:50] is the sub-list for method output_type
	14, // [14:32] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_backend_proto_aistudio_v1_aistudio_proto_init() }
func file_backend_proto_aistudio_v1_aistudio_proto_init() {
	if File_backend_proto_aistudio_v1_aistudio_proto != nil {
		return
	}
	file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[36].OneofWrappers = []any{}
	file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[37].OneofWrappers = []any{}
	file_backend_proto_aistudio_v1_aistudio_proto_msgTypes[38].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_aistudio_v1_aistudio_proto_rawDesc), len(file_backend_proto_aistudio_v1_aistudio_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_aistudio_v1_aistudio_proto_goTypes,
		DependencyIndexes: file_backend_proto_aistudio_v1_aistudio_proto_depIdxs,
		MessageInfos:      file_backend_proto_aistudio_v1_aistudio_proto_msgTypes,
	}.Build()
	File_backend_proto_aistudio_v1_aistudio_proto = out.File
	file_backend_proto_aistudio_v1_aistudio_proto_goTypes = nil
	file_backend_proto_aistudio_v1_aistudio_proto_depIdxs = nil
}
