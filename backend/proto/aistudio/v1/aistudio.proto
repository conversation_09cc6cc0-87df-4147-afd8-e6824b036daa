// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::response-repeated-first-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::response-plural-first-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0142::time-field-type=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
syntax = "proto3";

package backend.proto.aistudio.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1;aistudio";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.aistudio.v1";

import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";

// AIStudioService 定义了 AIStudio 服务的接口。
service AIStudioService {
    // RequestAiStudioMcpClient requests aiStudio mcp client.
    rpc RequestAiStudioMcpClient(RequestAiStudioMcpClientRequest) returns (RequestAiStudioMcpClientResponse);

    // CreateAiStudioTemplate 创建 AIStudio 模板。
    rpc CreateAiStudioTemplate(CreateAiStudioTemplateRequest) returns (AiStudioTemplate);
    // GetAiStudioTemplate 获取 AIStudio 模板。
    rpc GetAiStudioTemplate(GetAiStudioTemplateRequest) returns (AiStudioTemplate);
    // UpdateAiStudioTemplate 更新 AIStudio 模板。
    rpc UpdateAiStudioTemplate(UpdateAiStudioTemplateRequest) returns (AiStudioTemplate);
    // DeleteAiStudioTemplate 删除 AIStudio 模板。
    rpc DeleteAiStudioTemplate(DeleteAiStudioTemplateRequest) returns (google.protobuf.Empty);
    // ListAiStudioTemplate 获取 AIStudio 模板。
    rpc ListAiStudioTemplate(ListAiStudioTemplateRequest) returns (ListAiStudioTemplateResponse);

    // ListAiStudioTemplateID 获取 AIStudio 模板的下拉框ID。
    rpc ListAiStudioTemplateID(ListAiStudioTemplateIDRequest) returns (ListAiStudioTemplateIDResponse);

    // CreateAiStudioTask 创建 AIStudio 模板。
    rpc CreateAiStudioTask(CreateAiStudioTaskRequest) returns (AiStudioTask);
    // GetAiStudioTask 获取 AIStudio 模板。
    rpc GetAiStudioTask(GetAiStudioTaskRequest) returns (AiStudioTask);
    // RunAiStudioTask 运行 AIStudio 任务。
    rpc RunAiStudioTask(RunAiStudioTaskRequest) returns (RunAiStudioTaskResponse);
    // UpdateAiStudioTask 更新 AIStudio 模板。
    rpc UpdateAiStudioTask(UpdateAiStudioTaskRequest) returns (AiStudioTask);
    // DeleteAiStudioTask 删除 AIStudio 模板。
    rpc DeleteAiStudioTask(DeleteAiStudioTaskRequest) returns (google.protobuf.Empty);
    // ListAiStudioTask 获取 AIStudio 模板。
    rpc ListAiStudioTask(ListAiStudioTaskRequest) returns (ListAiStudioTaskResponse);
    

    // CreateAiStudioTaskLog 创建 AIStudio 日志。
    rpc CreateAiStudioTaskLog (CreateAiStudioTaskLogRequest) returns (AiStudioTaskLog);
    // GetAiStudioTaskLog 获取 AIStudio 日志。
    rpc GetAiStudioTaskLog (GetAiStudioTaskLogRequest) returns (AiStudioTaskLog);
    // ListAiStudioTaskLog 获取 AIStudio 日志。
    rpc ListAiStudioTaskLog (ListAiStudioTaskLogRequest) returns (ListAiStudioTaskLogResponse);


    // HandleURLVerification 下面是Slack的接口
    // 处理 Slack URL Verification challenge 请求
    // 你的 HTTP 转码器需要配置将 POST /your/slack/challenge/path 路由到这里
    // 并且配置将返回的 URLVerificationResponse.challenge 作为纯文本 HTTP 响应体
    rpc HandleURLVerification(HandleURLVerificationRequest) returns (HandleURLVerificationResponse);

    // HandleEventCallback 处理 Slack Event Callback 请求 (例如 app_mention)
    // 你的 HTTP 转码器需要配置将 POST /your/slack/events/path 路由到这里
    // 并且配置在 gRPC 调用成功后，总是返回 HTTP 200 OK
    rpc HandleEventCallback(HandleEventCallbackRequest) returns (HandleEventCallbackResponse);
}


// RequestAiStudioMcpClientRequest is a request to request aiStudio mcp client.
message RequestAiStudioMcpClientRequest {
    // gemini_key is gemini key.
    string gemini_key = 1;
    // prompt is prompt.
    string prompt = 2;
    // mcps 是 mcp 列表，以逗号分隔。
    string mcps = 3;
    // envs 是 envs。
    string envs = 4;
    // model 是模型。
    string model = 5;
}

// RequestAiStudioMcpClientResponse is a response to request aiStudio mcp client.
message RequestAiStudioMcpClientResponse {
    // status is status.
    int32 status = 1;
    // msg is msg.
    string msg = 2;
    // dialogues is dialogues.
    // data is response data.
    ResponseData data = 3;
}

// ResponseData is response data.
message ResponseData {
    // dialogues is dialogues.
    // 对话列表
    repeated string dialogues = 1;
}

// CreateAiStudioTemplateRequest is a request to create ai studio template.
message CreateAiStudioTemplateRequest {
    // model 是模型。
    string model = 1;
    // prompt 是 prompt.
    string prompt = 2;
    // mcps 是 mcp 列表，以逗号分隔。
    string mcps = 3;
    // envs 是 envs。
    string envs = 4;
    // name 是 模版的name。
    string name = 5;
}

// GetAiStudioTemplateRequest is a request to get aiStudio template.
message GetAiStudioTemplateRequest {
    // id is id.
    int64 id = 1;
}

// AiStudioTemplate is a response to get aiStudio template.
message AiStudioTemplate {
    // status is status.
    int32 status = 1;
    // msg is msg.
    string msg = 2;
    // data is response data.
    AiStudioTemplateData data = 3;
}

// AiStudioTemplateData is a response to get aiStudio template.
message AiStudioTemplateData {
    // id is id.
    int64 id = 1;
    // model 是模型。
    string model = 2;
    // prompt 是 prompt.
    string prompt = 3;
    // mcps 是 mcp 列表，以逗号分隔。
    string mcps = 4;
    // env_keys 是 env_keys。
    string env_keys = 5;
    // name 是 template的名字。
    string name = 6;
}

// UpdateAiStudioTemplateRequest is a request to update aiStudio template.
message UpdateAiStudioTemplateRequest {
    // id is id.
    int64 id = 1;
    // model 是模型。
    string model = 2;
    // prompt 是 prompt.
    string prompt = 3;
    // mcps 是 mcp 列表，以逗号分隔
    string mcps = 4;
    // env_keys 是 env_keys
    string env_keys = 5;
    // name 是 name
    string name = 6;
}

// DeleteAiStudioTemplateRequest is a request to delete aiStudio template.
message DeleteAiStudioTemplateRequest {
    // id is id.
    int64 id = 1;
}

// ListAiStudioTemplateRequest is a request to list aiStudio templates.
message ListAiStudioTemplateRequest {
    // per_page is the number of results to return per page.
    int32 per_page = 1;
    // page is the id to retrieve the next page of results.
    int32 page = 2;
}

// ListAiStudioTemplateData is a response to list aiStudio templates.
message ListAiStudioTemplateData {
    // aistudio_tasks is the list of aistudio_tasks.
    repeated AiStudioTemplateData items = 1;
    // total is the total count to retrieve the next page of results.
    int32 total = 2;
}

// ListAiStudioTemplateResponse is a response to list aiStudio templates.
message ListAiStudioTemplateResponse {
    // status is status.
    int32 status = 1;
    // msg is msg.
    string msg = 2;
    // data is response data.
    ListAiStudioTemplateData data = 3;
}

// ListAiStudioTemplateRequest is a request to list aiStudio templates.
message ListAiStudioTemplateIDRequest {
}

// AiStudioTemplateID is a response to list aiStudio templates.
message AiStudioTemplateID {
    // label is label.
    string label = 1;
    // value is value.
    string value = 2;
}

// ListAiStudioTemplateIDData is a response to list aiStudio templates.
message ListAiStudioTemplateIDData {
    // aistudio_tasks is the list of aistudio_tasks.
    repeated AiStudioTemplateID options = 1;
}

// ListAiStudioTemplateResponse is a response to list aiStudio templates.
message ListAiStudioTemplateIDResponse {
    // status is status.
    int32 status = 1;
    // msg is msg.
    string msg = 2;
    // data is response data.
    ListAiStudioTemplateIDData data = 3;
}

// CreateAiStudioTaskRequest is a request to create ai studio task.
message CreateAiStudioTaskRequest {
    // task is task_name.
    string task = 1;
    // template_id is template_id.
    int64 template_id = 2;
    // envs is envs.
    string envs = 3;
    // ai_key is ai_key.
    string ai_key = 4;
    // im_channel is im_channel.
    string im_channel= 5;
    // spec is spec
    string spec = 6;
}

// AiStudioTask is a response to get aiStudio task warp.
message AiStudioTask{
    // status is status.
    int32 status = 1;
    // msg is msg.
    string msg = 2;
    // data is response data.
    AiStudioTaskData data = 3;
}

// AiStudioTaskData is a response to get aiStudio task.
message AiStudioTaskData {
    // id is id.
    int64 id = 1;
    // task is task_name.
    string task = 2;
    // template_id is template_id.
    int64 template_id = 3;
    // envs is envs.
    string envs = 4;
    // ai_key is ai_key.
    string ai_key = 5;
    // im_channel is im_channel.
    string im_channel= 6;
    // spec is spec
    string spec = 7;
}

// GetAiStudioTaskRequest is a request to get aiStudio task.
message GetAiStudioTaskRequest {
    // id is id.
    int64 id = 1;
}

// RunAiStudioTaskRequest is a request to get aiStudio task.
message RunAiStudioTaskRequest {
    // id is id.
    int64 id = 1;
    // name is name.
    string name = 2;
    // envs is envs.
    string envs = 3;
}

// RunAiStudioTaskResponse is a response to get aiStudio task.
message RunAiStudioTaskResponse {
    // status is status.
    int32 status = 1;
    // msg is msg.
    string msg = 2;
    // data is response data.
    repeated string data = 3;
}

// UpdateAiStudioTaskRequest is a request to update aiStudio template.
message UpdateAiStudioTaskRequest {
    // id is id.
    int64 id = 1;
    // task is task_name.
    string task = 2;
    // template_id is template_id.
    int64 template_id = 3;
    // envs is envs.
    string envs = 4;
    // ai_key is ai_key.
    string ai_key = 5;
    // im_channel is im_channel.
    string im_channel= 6;
    // spec is spec
    string spec = 7;
}

// DeleteAiStudioTaskRequest is a request to delete aiStudio template.
message DeleteAiStudioTaskRequest {
    // id is id.
    int64 id = 1;
}

// ListAiStudioTaskRequest is a request to list aiStudio tasks.
message ListAiStudioTaskRequest {
    // per_page is the number of results to return per page.
    int32 per_page = 1;
    // page is the current page.
    int32 page = 2;
}

// ListAiStudioTaskData is a response to list aiStudio tasks.
message ListAiStudioTaskData {
    // aistudio_tasks is the list of aistudio_tasks.
    repeated AiStudioTaskData items = 1;
    // total is the total count to retrieve the next page of results.
    int32 total = 4;
}
// ListAiStudioTaskResponse is a response to list aiStudio tasks.
message ListAiStudioTaskResponse {
    // status is status.
    int32 status = 1;
    // msg is msg.
    string msg = 2;
    // data is response data.
    ListAiStudioTaskData data = 3;
    
}

// AiStudioTaskLog is a response to get aiStudio task log.
message AiStudioTaskLog {
    // id is id.
    int64 id = 1;
    // task_id is task_id.
    int64 task_id = 2;
    // prompt is prompt.
    string prompt = 3;
    // envs is envs.
    string envs = 4;
    // dialogues is dialogues.
    repeated string dialogues = 5;
    // create_time is create_time.
    google.protobuf.Timestamp create_time = 6;
  }
  
  // CreateAiStudioTaskLogRequest is a request to create aiStudio task log.
  message CreateAiStudioTaskLogRequest {
    // task_id is task_id.
    int64 task_id = 1;
    // prompt is prompt.
    string prompt = 2;
    // envs is envs.
    string envs = 3;
    // dialogues is dialogues.
    repeated string dialogues = 4;
  }
  
  // GetAiStudioTaskLogRequest is a request to get aiStudio task log.
  message GetAiStudioTaskLogRequest {
    // id is id.
    int64 id = 1;
  }
  
  // UpdateAiStudioTaskLogRequest is a request to update aiStudio task log.
  message UpdateAiStudioTaskLogRequest {
    // id is id.
    int64 id = 1;
    // task_id is task_id.
    int64 task_id = 2;
    // prompt is prompt.
    string prompt = 3;
    // envs is envs.
    string envs = 4;
    // dialogues is dialogues.
    repeated string dialogues = 5;
  } 
  
  // ListAiStudioTaskLogRequest is a request to list aiStudio task logs.
  message ListAiStudioTaskLogRequest {
    // per_page is the number of results to return per page.
    int32 per_page = 1;
    // page is the current page.
    int32 page = 2;
  }
  
// ListAiStudioTaskLogData is a response to list aiStudio tasks.
message ListAiStudioTaskLogData {
    // aistudio_tasks is the list of aistudio_tasks.
    repeated AiStudioTaskLog items = 1;
    // total is the total count to retrieve the next page of results.
    int32 total = 2;
}

// ListAiStudioTaskLogResponse is a response to list aiStudio task logs.
message ListAiStudioTaskLogResponse {
  // status is status.
  int32 status = 1;
  // msg is msg.
  string msg = 2;
  // data is response data.
  ListAiStudioTaskLogData data = 3;
}

// HandleURLVerificationRequest 相关消息
message HandleURLVerificationRequest {
  // type 字段，预期值为 "url_verification"
  string type = 1;      
  // token Slack 提供的 token (已弃用，但可能存在)
  string token = 2;     
  // challenge 字段，Slack 会原样返回
  string challenge = 3; 
}

// HandleURLVerificationResponse 相关消息
message HandleURLVerificationResponse {
  // challenge 这个 gRPC 响应包含 challenge 字符串
  // 注意：HTTP 转码器需要特殊处理，将这个字段的值作为 text/plain 响应体返回给 Slack
  string challenge = 1;
}


// HandleEventCallbackRequest Callback 的外层结构
message HandleEventCallbackRequest {
    // token Slack 提供的 token (已弃用，但可能存在)
    string token = 1;       
    // team_id Slack 提供的 team_id
    string team_id = 2;     
    // api_app_id Slack 提供的 api_app_id
    string api_app_id = 3;  

    // event 关键字段：嵌套的 event 对象。
    // 由于 event 的具体结构根据 event.type 不同而变化，
    // 使用 google.protobuf.Struct 可以最方便地让转码器直接映射 JSON 对象。
    // 在 Go 服务端，你需要检查这个 Struct 内部的 "type" 字段来确定具体事件类型。
    google.protobuf.Struct event = 4;

    // type 字段，预期值为 "event_callback"
    string type = 5;        
    // event_id Slack 提供的 event_id
    string event_id = 6;   
    // event_time 事件发生的 Unix 时间戳 (秒)
    int64 event_time = 7;   
    // authorizations 授权信息 (如果存在)
    repeated Authorization authorizations = 8;
    // is_ext_shared_channel 是否为外部共享频道
    bool is_ext_shared_channel = 9;
    // context_team_id 可选字段
    optional string context_team_id = 10;
    // context_enterprise_id 可选字段
    optional string context_enterprise_id = 11;

    // challenge 字段，Slack 会原样返回
    optional string challenge = 12;

    // ... 根据需要可以添加其他顶级字段，如 "challenge" (重发时可能出现) 等
}

// Event Callback 中的授权信息结构
message Authorization {
    // enterprise_id 可选字段
    optional string enterprise_id = 1; 
    // team_id Slack 提供的 team_id
    string team_id = 2;
    // user_id Slack 提供的 user_id
    string user_id = 3;                 
    // is_bot 是否为 Bot 用户
    bool is_bot = 4;
    // is_enterprise_install 是否为企业版 Slack
    bool is_enterprise_install = 5;
}




// AppMentionEvent 是一个具体的事件类型，它继承自 EventCallbackRequest.event，并且定义了与 app_mention 相关的特定字段。
// --- 具体事件类型的 Protobuf 定义 (可选，主要用于 Go 代码内部) ---
// 虽然 EventCallbackRequest.event 使用了 Struct，
// 但你可以在 Go 代码中将这个 Struct 反序列化为你定义的具体事件 Proto 消息，
// 以获得类型安全。这里定义一个 app_mention 的例子。
message AppMentionEvent {
    // type 字段，预期值为 "app_mention"
    string type = 1;         
    // client_msg_id 可选字段
    optional string client_msg_id = 2;
    // text 包含 @提及 的消息文本
    string text = 3;          
    // user 发送提及的用户的 ID
    string user = 4;         
    // ts 消息的时间戳字符串
    string ts = 5;            
    // team 可选字段，根据需要添加:
    optional string team = 6;  
    // channel 频道 ID
    string channel = 7;      
    // event_ts 事件的时间戳字符串
    string event_ts = 8;      

    // thread_ts 可选字段，根据需要添加, 如果是在线程中的提及
    optional string thread_ts = 10; 
}


// HandleEventCallbackResponse 相关消息
message HandleEventCallbackResponse {
  // challenge 这个 gRPC 响应包含 challenge 字符串
  // 注意：HTTP 转码器需要特殊处理，将这个字段的值作为 text/plain 响应体返回给 Slack
  string challenge = 1;
}