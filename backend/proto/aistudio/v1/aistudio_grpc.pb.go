// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::response-repeated-first-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0158::response-plural-first-field=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0142::time-field-type=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/aistudio/v1/aistudio.proto

package aistudio

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AIStudioService_RequestAiStudioMcpClient_FullMethodName = "/backend.proto.aistudio.v1.AIStudioService/RequestAiStudioMcpClient"
	AIStudioService_CreateAiStudioTemplate_FullMethodName   = "/backend.proto.aistudio.v1.AIStudioService/CreateAiStudioTemplate"
	AIStudioService_GetAiStudioTemplate_FullMethodName      = "/backend.proto.aistudio.v1.AIStudioService/GetAiStudioTemplate"
	AIStudioService_UpdateAiStudioTemplate_FullMethodName   = "/backend.proto.aistudio.v1.AIStudioService/UpdateAiStudioTemplate"
	AIStudioService_DeleteAiStudioTemplate_FullMethodName   = "/backend.proto.aistudio.v1.AIStudioService/DeleteAiStudioTemplate"
	AIStudioService_ListAiStudioTemplate_FullMethodName     = "/backend.proto.aistudio.v1.AIStudioService/ListAiStudioTemplate"
	AIStudioService_ListAiStudioTemplateID_FullMethodName   = "/backend.proto.aistudio.v1.AIStudioService/ListAiStudioTemplateID"
	AIStudioService_CreateAiStudioTask_FullMethodName       = "/backend.proto.aistudio.v1.AIStudioService/CreateAiStudioTask"
	AIStudioService_GetAiStudioTask_FullMethodName          = "/backend.proto.aistudio.v1.AIStudioService/GetAiStudioTask"
	AIStudioService_RunAiStudioTask_FullMethodName          = "/backend.proto.aistudio.v1.AIStudioService/RunAiStudioTask"
	AIStudioService_UpdateAiStudioTask_FullMethodName       = "/backend.proto.aistudio.v1.AIStudioService/UpdateAiStudioTask"
	AIStudioService_DeleteAiStudioTask_FullMethodName       = "/backend.proto.aistudio.v1.AIStudioService/DeleteAiStudioTask"
	AIStudioService_ListAiStudioTask_FullMethodName         = "/backend.proto.aistudio.v1.AIStudioService/ListAiStudioTask"
	AIStudioService_CreateAiStudioTaskLog_FullMethodName    = "/backend.proto.aistudio.v1.AIStudioService/CreateAiStudioTaskLog"
	AIStudioService_GetAiStudioTaskLog_FullMethodName       = "/backend.proto.aistudio.v1.AIStudioService/GetAiStudioTaskLog"
	AIStudioService_ListAiStudioTaskLog_FullMethodName      = "/backend.proto.aistudio.v1.AIStudioService/ListAiStudioTaskLog"
	AIStudioService_HandleURLVerification_FullMethodName    = "/backend.proto.aistudio.v1.AIStudioService/HandleURLVerification"
	AIStudioService_HandleEventCallback_FullMethodName      = "/backend.proto.aistudio.v1.AIStudioService/HandleEventCallback"
)

// AIStudioServiceClient is the client API for AIStudioService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// AIStudioService 定义了 AIStudio 服务的接口。
type AIStudioServiceClient interface {
	// RequestAiStudioMcpClient requests aiStudio mcp client.
	RequestAiStudioMcpClient(ctx context.Context, in *RequestAiStudioMcpClientRequest, opts ...grpc.CallOption) (*RequestAiStudioMcpClientResponse, error)
	// CreateAiStudioTemplate 创建 AIStudio 模板。
	CreateAiStudioTemplate(ctx context.Context, in *CreateAiStudioTemplateRequest, opts ...grpc.CallOption) (*AiStudioTemplate, error)
	// GetAiStudioTemplate 获取 AIStudio 模板。
	GetAiStudioTemplate(ctx context.Context, in *GetAiStudioTemplateRequest, opts ...grpc.CallOption) (*AiStudioTemplate, error)
	// UpdateAiStudioTemplate 更新 AIStudio 模板。
	UpdateAiStudioTemplate(ctx context.Context, in *UpdateAiStudioTemplateRequest, opts ...grpc.CallOption) (*AiStudioTemplate, error)
	// DeleteAiStudioTemplate 删除 AIStudio 模板。
	DeleteAiStudioTemplate(ctx context.Context, in *DeleteAiStudioTemplateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// ListAiStudioTemplate 获取 AIStudio 模板。
	ListAiStudioTemplate(ctx context.Context, in *ListAiStudioTemplateRequest, opts ...grpc.CallOption) (*ListAiStudioTemplateResponse, error)
	// ListAiStudioTemplateID 获取 AIStudio 模板的下拉框ID。
	ListAiStudioTemplateID(ctx context.Context, in *ListAiStudioTemplateIDRequest, opts ...grpc.CallOption) (*ListAiStudioTemplateIDResponse, error)
	// CreateAiStudioTask 创建 AIStudio 模板。
	CreateAiStudioTask(ctx context.Context, in *CreateAiStudioTaskRequest, opts ...grpc.CallOption) (*AiStudioTask, error)
	// GetAiStudioTask 获取 AIStudio 模板。
	GetAiStudioTask(ctx context.Context, in *GetAiStudioTaskRequest, opts ...grpc.CallOption) (*AiStudioTask, error)
	// RunAiStudioTask 运行 AIStudio 任务。
	RunAiStudioTask(ctx context.Context, in *RunAiStudioTaskRequest, opts ...grpc.CallOption) (*RunAiStudioTaskResponse, error)
	// UpdateAiStudioTask 更新 AIStudio 模板。
	UpdateAiStudioTask(ctx context.Context, in *UpdateAiStudioTaskRequest, opts ...grpc.CallOption) (*AiStudioTask, error)
	// DeleteAiStudioTask 删除 AIStudio 模板。
	DeleteAiStudioTask(ctx context.Context, in *DeleteAiStudioTaskRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// ListAiStudioTask 获取 AIStudio 模板。
	ListAiStudioTask(ctx context.Context, in *ListAiStudioTaskRequest, opts ...grpc.CallOption) (*ListAiStudioTaskResponse, error)
	// CreateAiStudioTaskLog 创建 AIStudio 日志。
	CreateAiStudioTaskLog(ctx context.Context, in *CreateAiStudioTaskLogRequest, opts ...grpc.CallOption) (*AiStudioTaskLog, error)
	// GetAiStudioTaskLog 获取 AIStudio 日志。
	GetAiStudioTaskLog(ctx context.Context, in *GetAiStudioTaskLogRequest, opts ...grpc.CallOption) (*AiStudioTaskLog, error)
	// ListAiStudioTaskLog 获取 AIStudio 日志。
	ListAiStudioTaskLog(ctx context.Context, in *ListAiStudioTaskLogRequest, opts ...grpc.CallOption) (*ListAiStudioTaskLogResponse, error)
	// HandleURLVerification 下面是Slack的接口
	// 处理 Slack URL Verification challenge 请求
	// 你的 HTTP 转码器需要配置将 POST /your/slack/challenge/path 路由到这里
	// 并且配置将返回的 URLVerificationResponse.challenge 作为纯文本 HTTP 响应体
	HandleURLVerification(ctx context.Context, in *HandleURLVerificationRequest, opts ...grpc.CallOption) (*HandleURLVerificationResponse, error)
	// HandleEventCallback 处理 Slack Event Callback 请求 (例如 app_mention)
	// 你的 HTTP 转码器需要配置将 POST /your/slack/events/path 路由到这里
	// 并且配置在 gRPC 调用成功后，总是返回 HTTP 200 OK
	HandleEventCallback(ctx context.Context, in *HandleEventCallbackRequest, opts ...grpc.CallOption) (*HandleEventCallbackResponse, error)
}

type aIStudioServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAIStudioServiceClient(cc grpc.ClientConnInterface) AIStudioServiceClient {
	return &aIStudioServiceClient{cc}
}

func (c *aIStudioServiceClient) RequestAiStudioMcpClient(ctx context.Context, in *RequestAiStudioMcpClientRequest, opts ...grpc.CallOption) (*RequestAiStudioMcpClientResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RequestAiStudioMcpClientResponse)
	err := c.cc.Invoke(ctx, AIStudioService_RequestAiStudioMcpClient_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) CreateAiStudioTemplate(ctx context.Context, in *CreateAiStudioTemplateRequest, opts ...grpc.CallOption) (*AiStudioTemplate, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AiStudioTemplate)
	err := c.cc.Invoke(ctx, AIStudioService_CreateAiStudioTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) GetAiStudioTemplate(ctx context.Context, in *GetAiStudioTemplateRequest, opts ...grpc.CallOption) (*AiStudioTemplate, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AiStudioTemplate)
	err := c.cc.Invoke(ctx, AIStudioService_GetAiStudioTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) UpdateAiStudioTemplate(ctx context.Context, in *UpdateAiStudioTemplateRequest, opts ...grpc.CallOption) (*AiStudioTemplate, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AiStudioTemplate)
	err := c.cc.Invoke(ctx, AIStudioService_UpdateAiStudioTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) DeleteAiStudioTemplate(ctx context.Context, in *DeleteAiStudioTemplateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AIStudioService_DeleteAiStudioTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) ListAiStudioTemplate(ctx context.Context, in *ListAiStudioTemplateRequest, opts ...grpc.CallOption) (*ListAiStudioTemplateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAiStudioTemplateResponse)
	err := c.cc.Invoke(ctx, AIStudioService_ListAiStudioTemplate_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) ListAiStudioTemplateID(ctx context.Context, in *ListAiStudioTemplateIDRequest, opts ...grpc.CallOption) (*ListAiStudioTemplateIDResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAiStudioTemplateIDResponse)
	err := c.cc.Invoke(ctx, AIStudioService_ListAiStudioTemplateID_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) CreateAiStudioTask(ctx context.Context, in *CreateAiStudioTaskRequest, opts ...grpc.CallOption) (*AiStudioTask, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AiStudioTask)
	err := c.cc.Invoke(ctx, AIStudioService_CreateAiStudioTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) GetAiStudioTask(ctx context.Context, in *GetAiStudioTaskRequest, opts ...grpc.CallOption) (*AiStudioTask, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AiStudioTask)
	err := c.cc.Invoke(ctx, AIStudioService_GetAiStudioTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) RunAiStudioTask(ctx context.Context, in *RunAiStudioTaskRequest, opts ...grpc.CallOption) (*RunAiStudioTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RunAiStudioTaskResponse)
	err := c.cc.Invoke(ctx, AIStudioService_RunAiStudioTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) UpdateAiStudioTask(ctx context.Context, in *UpdateAiStudioTaskRequest, opts ...grpc.CallOption) (*AiStudioTask, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AiStudioTask)
	err := c.cc.Invoke(ctx, AIStudioService_UpdateAiStudioTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) DeleteAiStudioTask(ctx context.Context, in *DeleteAiStudioTaskRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, AIStudioService_DeleteAiStudioTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) ListAiStudioTask(ctx context.Context, in *ListAiStudioTaskRequest, opts ...grpc.CallOption) (*ListAiStudioTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAiStudioTaskResponse)
	err := c.cc.Invoke(ctx, AIStudioService_ListAiStudioTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) CreateAiStudioTaskLog(ctx context.Context, in *CreateAiStudioTaskLogRequest, opts ...grpc.CallOption) (*AiStudioTaskLog, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AiStudioTaskLog)
	err := c.cc.Invoke(ctx, AIStudioService_CreateAiStudioTaskLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) GetAiStudioTaskLog(ctx context.Context, in *GetAiStudioTaskLogRequest, opts ...grpc.CallOption) (*AiStudioTaskLog, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AiStudioTaskLog)
	err := c.cc.Invoke(ctx, AIStudioService_GetAiStudioTaskLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) ListAiStudioTaskLog(ctx context.Context, in *ListAiStudioTaskLogRequest, opts ...grpc.CallOption) (*ListAiStudioTaskLogResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAiStudioTaskLogResponse)
	err := c.cc.Invoke(ctx, AIStudioService_ListAiStudioTaskLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) HandleURLVerification(ctx context.Context, in *HandleURLVerificationRequest, opts ...grpc.CallOption) (*HandleURLVerificationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleURLVerificationResponse)
	err := c.cc.Invoke(ctx, AIStudioService_HandleURLVerification_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aIStudioServiceClient) HandleEventCallback(ctx context.Context, in *HandleEventCallbackRequest, opts ...grpc.CallOption) (*HandleEventCallbackResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleEventCallbackResponse)
	err := c.cc.Invoke(ctx, AIStudioService_HandleEventCallback_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AIStudioServiceServer is the server API for AIStudioService service.
// All implementations must embed UnimplementedAIStudioServiceServer
// for forward compatibility.
//
// AIStudioService 定义了 AIStudio 服务的接口。
type AIStudioServiceServer interface {
	// RequestAiStudioMcpClient requests aiStudio mcp client.
	RequestAiStudioMcpClient(context.Context, *RequestAiStudioMcpClientRequest) (*RequestAiStudioMcpClientResponse, error)
	// CreateAiStudioTemplate 创建 AIStudio 模板。
	CreateAiStudioTemplate(context.Context, *CreateAiStudioTemplateRequest) (*AiStudioTemplate, error)
	// GetAiStudioTemplate 获取 AIStudio 模板。
	GetAiStudioTemplate(context.Context, *GetAiStudioTemplateRequest) (*AiStudioTemplate, error)
	// UpdateAiStudioTemplate 更新 AIStudio 模板。
	UpdateAiStudioTemplate(context.Context, *UpdateAiStudioTemplateRequest) (*AiStudioTemplate, error)
	// DeleteAiStudioTemplate 删除 AIStudio 模板。
	DeleteAiStudioTemplate(context.Context, *DeleteAiStudioTemplateRequest) (*emptypb.Empty, error)
	// ListAiStudioTemplate 获取 AIStudio 模板。
	ListAiStudioTemplate(context.Context, *ListAiStudioTemplateRequest) (*ListAiStudioTemplateResponse, error)
	// ListAiStudioTemplateID 获取 AIStudio 模板的下拉框ID。
	ListAiStudioTemplateID(context.Context, *ListAiStudioTemplateIDRequest) (*ListAiStudioTemplateIDResponse, error)
	// CreateAiStudioTask 创建 AIStudio 模板。
	CreateAiStudioTask(context.Context, *CreateAiStudioTaskRequest) (*AiStudioTask, error)
	// GetAiStudioTask 获取 AIStudio 模板。
	GetAiStudioTask(context.Context, *GetAiStudioTaskRequest) (*AiStudioTask, error)
	// RunAiStudioTask 运行 AIStudio 任务。
	RunAiStudioTask(context.Context, *RunAiStudioTaskRequest) (*RunAiStudioTaskResponse, error)
	// UpdateAiStudioTask 更新 AIStudio 模板。
	UpdateAiStudioTask(context.Context, *UpdateAiStudioTaskRequest) (*AiStudioTask, error)
	// DeleteAiStudioTask 删除 AIStudio 模板。
	DeleteAiStudioTask(context.Context, *DeleteAiStudioTaskRequest) (*emptypb.Empty, error)
	// ListAiStudioTask 获取 AIStudio 模板。
	ListAiStudioTask(context.Context, *ListAiStudioTaskRequest) (*ListAiStudioTaskResponse, error)
	// CreateAiStudioTaskLog 创建 AIStudio 日志。
	CreateAiStudioTaskLog(context.Context, *CreateAiStudioTaskLogRequest) (*AiStudioTaskLog, error)
	// GetAiStudioTaskLog 获取 AIStudio 日志。
	GetAiStudioTaskLog(context.Context, *GetAiStudioTaskLogRequest) (*AiStudioTaskLog, error)
	// ListAiStudioTaskLog 获取 AIStudio 日志。
	ListAiStudioTaskLog(context.Context, *ListAiStudioTaskLogRequest) (*ListAiStudioTaskLogResponse, error)
	// HandleURLVerification 下面是Slack的接口
	// 处理 Slack URL Verification challenge 请求
	// 你的 HTTP 转码器需要配置将 POST /your/slack/challenge/path 路由到这里
	// 并且配置将返回的 URLVerificationResponse.challenge 作为纯文本 HTTP 响应体
	HandleURLVerification(context.Context, *HandleURLVerificationRequest) (*HandleURLVerificationResponse, error)
	// HandleEventCallback 处理 Slack Event Callback 请求 (例如 app_mention)
	// 你的 HTTP 转码器需要配置将 POST /your/slack/events/path 路由到这里
	// 并且配置在 gRPC 调用成功后，总是返回 HTTP 200 OK
	HandleEventCallback(context.Context, *HandleEventCallbackRequest) (*HandleEventCallbackResponse, error)
	mustEmbedUnimplementedAIStudioServiceServer()
}

// UnimplementedAIStudioServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAIStudioServiceServer struct{}

func (UnimplementedAIStudioServiceServer) RequestAiStudioMcpClient(context.Context, *RequestAiStudioMcpClientRequest) (*RequestAiStudioMcpClientResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RequestAiStudioMcpClient not implemented")
}
func (UnimplementedAIStudioServiceServer) CreateAiStudioTemplate(context.Context, *CreateAiStudioTemplateRequest) (*AiStudioTemplate, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAiStudioTemplate not implemented")
}
func (UnimplementedAIStudioServiceServer) GetAiStudioTemplate(context.Context, *GetAiStudioTemplateRequest) (*AiStudioTemplate, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAiStudioTemplate not implemented")
}
func (UnimplementedAIStudioServiceServer) UpdateAiStudioTemplate(context.Context, *UpdateAiStudioTemplateRequest) (*AiStudioTemplate, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAiStudioTemplate not implemented")
}
func (UnimplementedAIStudioServiceServer) DeleteAiStudioTemplate(context.Context, *DeleteAiStudioTemplateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAiStudioTemplate not implemented")
}
func (UnimplementedAIStudioServiceServer) ListAiStudioTemplate(context.Context, *ListAiStudioTemplateRequest) (*ListAiStudioTemplateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAiStudioTemplate not implemented")
}
func (UnimplementedAIStudioServiceServer) ListAiStudioTemplateID(context.Context, *ListAiStudioTemplateIDRequest) (*ListAiStudioTemplateIDResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAiStudioTemplateID not implemented")
}
func (UnimplementedAIStudioServiceServer) CreateAiStudioTask(context.Context, *CreateAiStudioTaskRequest) (*AiStudioTask, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAiStudioTask not implemented")
}
func (UnimplementedAIStudioServiceServer) GetAiStudioTask(context.Context, *GetAiStudioTaskRequest) (*AiStudioTask, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAiStudioTask not implemented")
}
func (UnimplementedAIStudioServiceServer) RunAiStudioTask(context.Context, *RunAiStudioTaskRequest) (*RunAiStudioTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunAiStudioTask not implemented")
}
func (UnimplementedAIStudioServiceServer) UpdateAiStudioTask(context.Context, *UpdateAiStudioTaskRequest) (*AiStudioTask, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAiStudioTask not implemented")
}
func (UnimplementedAIStudioServiceServer) DeleteAiStudioTask(context.Context, *DeleteAiStudioTaskRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAiStudioTask not implemented")
}
func (UnimplementedAIStudioServiceServer) ListAiStudioTask(context.Context, *ListAiStudioTaskRequest) (*ListAiStudioTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAiStudioTask not implemented")
}
func (UnimplementedAIStudioServiceServer) CreateAiStudioTaskLog(context.Context, *CreateAiStudioTaskLogRequest) (*AiStudioTaskLog, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAiStudioTaskLog not implemented")
}
func (UnimplementedAIStudioServiceServer) GetAiStudioTaskLog(context.Context, *GetAiStudioTaskLogRequest) (*AiStudioTaskLog, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAiStudioTaskLog not implemented")
}
func (UnimplementedAIStudioServiceServer) ListAiStudioTaskLog(context.Context, *ListAiStudioTaskLogRequest) (*ListAiStudioTaskLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAiStudioTaskLog not implemented")
}
func (UnimplementedAIStudioServiceServer) HandleURLVerification(context.Context, *HandleURLVerificationRequest) (*HandleURLVerificationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleURLVerification not implemented")
}
func (UnimplementedAIStudioServiceServer) HandleEventCallback(context.Context, *HandleEventCallbackRequest) (*HandleEventCallbackResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleEventCallback not implemented")
}
func (UnimplementedAIStudioServiceServer) mustEmbedUnimplementedAIStudioServiceServer() {}
func (UnimplementedAIStudioServiceServer) testEmbeddedByValue()                         {}

// UnsafeAIStudioServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AIStudioServiceServer will
// result in compilation errors.
type UnsafeAIStudioServiceServer interface {
	mustEmbedUnimplementedAIStudioServiceServer()
}

func RegisterAIStudioServiceServer(s grpc.ServiceRegistrar, srv AIStudioServiceServer) {
	// If the following call pancis, it indicates UnimplementedAIStudioServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AIStudioService_ServiceDesc, srv)
}

func _AIStudioService_RequestAiStudioMcpClient_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RequestAiStudioMcpClientRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).RequestAiStudioMcpClient(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_RequestAiStudioMcpClient_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).RequestAiStudioMcpClient(ctx, req.(*RequestAiStudioMcpClientRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_CreateAiStudioTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAiStudioTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).CreateAiStudioTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_CreateAiStudioTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).CreateAiStudioTemplate(ctx, req.(*CreateAiStudioTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_GetAiStudioTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiStudioTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).GetAiStudioTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_GetAiStudioTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).GetAiStudioTemplate(ctx, req.(*GetAiStudioTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_UpdateAiStudioTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAiStudioTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).UpdateAiStudioTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_UpdateAiStudioTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).UpdateAiStudioTemplate(ctx, req.(*UpdateAiStudioTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_DeleteAiStudioTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAiStudioTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).DeleteAiStudioTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_DeleteAiStudioTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).DeleteAiStudioTemplate(ctx, req.(*DeleteAiStudioTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_ListAiStudioTemplate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAiStudioTemplateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).ListAiStudioTemplate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_ListAiStudioTemplate_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).ListAiStudioTemplate(ctx, req.(*ListAiStudioTemplateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_ListAiStudioTemplateID_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAiStudioTemplateIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).ListAiStudioTemplateID(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_ListAiStudioTemplateID_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).ListAiStudioTemplateID(ctx, req.(*ListAiStudioTemplateIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_CreateAiStudioTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAiStudioTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).CreateAiStudioTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_CreateAiStudioTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).CreateAiStudioTask(ctx, req.(*CreateAiStudioTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_GetAiStudioTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiStudioTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).GetAiStudioTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_GetAiStudioTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).GetAiStudioTask(ctx, req.(*GetAiStudioTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_RunAiStudioTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunAiStudioTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).RunAiStudioTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_RunAiStudioTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).RunAiStudioTask(ctx, req.(*RunAiStudioTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_UpdateAiStudioTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAiStudioTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).UpdateAiStudioTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_UpdateAiStudioTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).UpdateAiStudioTask(ctx, req.(*UpdateAiStudioTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_DeleteAiStudioTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAiStudioTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).DeleteAiStudioTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_DeleteAiStudioTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).DeleteAiStudioTask(ctx, req.(*DeleteAiStudioTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_ListAiStudioTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAiStudioTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).ListAiStudioTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_ListAiStudioTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).ListAiStudioTask(ctx, req.(*ListAiStudioTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_CreateAiStudioTaskLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAiStudioTaskLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).CreateAiStudioTaskLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_CreateAiStudioTaskLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).CreateAiStudioTaskLog(ctx, req.(*CreateAiStudioTaskLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_GetAiStudioTaskLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetAiStudioTaskLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).GetAiStudioTaskLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_GetAiStudioTaskLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).GetAiStudioTaskLog(ctx, req.(*GetAiStudioTaskLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_ListAiStudioTaskLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAiStudioTaskLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).ListAiStudioTaskLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_ListAiStudioTaskLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).ListAiStudioTaskLog(ctx, req.(*ListAiStudioTaskLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_HandleURLVerification_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleURLVerificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).HandleURLVerification(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_HandleURLVerification_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).HandleURLVerification(ctx, req.(*HandleURLVerificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AIStudioService_HandleEventCallback_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleEventCallbackRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AIStudioServiceServer).HandleEventCallback(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AIStudioService_HandleEventCallback_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AIStudioServiceServer).HandleEventCallback(ctx, req.(*HandleEventCallbackRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AIStudioService_ServiceDesc is the grpc.ServiceDesc for AIStudioService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AIStudioService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.aistudio.v1.AIStudioService",
	HandlerType: (*AIStudioServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RequestAiStudioMcpClient",
			Handler:    _AIStudioService_RequestAiStudioMcpClient_Handler,
		},
		{
			MethodName: "CreateAiStudioTemplate",
			Handler:    _AIStudioService_CreateAiStudioTemplate_Handler,
		},
		{
			MethodName: "GetAiStudioTemplate",
			Handler:    _AIStudioService_GetAiStudioTemplate_Handler,
		},
		{
			MethodName: "UpdateAiStudioTemplate",
			Handler:    _AIStudioService_UpdateAiStudioTemplate_Handler,
		},
		{
			MethodName: "DeleteAiStudioTemplate",
			Handler:    _AIStudioService_DeleteAiStudioTemplate_Handler,
		},
		{
			MethodName: "ListAiStudioTemplate",
			Handler:    _AIStudioService_ListAiStudioTemplate_Handler,
		},
		{
			MethodName: "ListAiStudioTemplateID",
			Handler:    _AIStudioService_ListAiStudioTemplateID_Handler,
		},
		{
			MethodName: "CreateAiStudioTask",
			Handler:    _AIStudioService_CreateAiStudioTask_Handler,
		},
		{
			MethodName: "GetAiStudioTask",
			Handler:    _AIStudioService_GetAiStudioTask_Handler,
		},
		{
			MethodName: "RunAiStudioTask",
			Handler:    _AIStudioService_RunAiStudioTask_Handler,
		},
		{
			MethodName: "UpdateAiStudioTask",
			Handler:    _AIStudioService_UpdateAiStudioTask_Handler,
		},
		{
			MethodName: "DeleteAiStudioTask",
			Handler:    _AIStudioService_DeleteAiStudioTask_Handler,
		},
		{
			MethodName: "ListAiStudioTask",
			Handler:    _AIStudioService_ListAiStudioTask_Handler,
		},
		{
			MethodName: "CreateAiStudioTaskLog",
			Handler:    _AIStudioService_CreateAiStudioTaskLog_Handler,
		},
		{
			MethodName: "GetAiStudioTaskLog",
			Handler:    _AIStudioService_GetAiStudioTaskLog_Handler,
		},
		{
			MethodName: "ListAiStudioTaskLog",
			Handler:    _AIStudioService_ListAiStudioTaskLog_Handler,
		},
		{
			MethodName: "HandleURLVerification",
			Handler:    _AIStudioService_HandleURLVerification_Handler,
		},
		{
			MethodName: "HandleEventCallback",
			Handler:    _AIStudioService_HandleEventCallback_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/aistudio/v1/aistudio.proto",
}
