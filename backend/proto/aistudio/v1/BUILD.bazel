load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "aistudio_proto",
    srcs = ["aistudio.proto"],
    visibility = ["//visibility:public"],
    deps = [
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
    ],
)

go_proto_library(
    name = "aistudio_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1",
    proto = ":aistudio_proto",
    visibility = ["//visibility:public"],
)

go_library(
    name = "aistudio",
    embed = [":aistudio_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/aistudio/v1",
    visibility = ["//visibility:public"],
)
