load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "commonpb_proto",
    srcs = ["pagination.proto"],
    visibility = ["//visibility:public"],
    deps = ["@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto"],
)

go_proto_library(
    name = "commonpb_go_proto",
    importpath = "github.com/MoeGolibrary/moego/backend/proto/common/v1",
    proto = ":commonpb_proto",
    visibility = ["//visibility:public"],
    deps = ["@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library"],
)

go_library(
    name = "common",
    embed = [":commonpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/common/v1",
    visibility = ["//visibility:public"],
)
