syntax = "proto3";

package backend.proto.search.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/search/v1;searchpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.search.v1";

import "google/protobuf/struct.proto";
import "buf/validate/validate.proto";
import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

// 批量文档操作请求
message BulkDocumentRequest {
    // 批量操作
    message BulkOperation {
        // 操作目标
        message BulkTarget {
            // 索引名称（必填）
            string index = 1 [
                (buf.validate.field).string = {
                    min_len: 1,
                    max_len: 100
                },
                (google.api.field_behavior) = REQUIRED
            ];
            // 文档ID（可选）
            optional string id = 2;
        }

        // 操作类型
        OperationType operation_type = 1 [
            (buf.validate.field).enum = {
                defined_only: true,
                not_in: [0] // 不允许未指定类型
            },
            (google.api.field_behavior) = REQUIRED
        ];

        // 目标信息, 指引到具体的索引
        BulkTarget target = 2 [
            (buf.validate.field) = {
                required: true
            },
            (google.api.field_behavior) = REQUIRED
        ];

        // 文档内容JSON格式字符串
        // 对于DELETE操作，此字段应为空
        // 对于其他操作，此字段必填
        // 
        // type User struct {
        //     Age   int    `json:"age"`
        //     Name  string `json:"name"`
        //     Wages int    `json:"wages"`
        // }
        // 从数据库读取到 user 后
        // 
        // st, err := structpb.NewStruct(user) 
        optional google.protobuf.Struct document = 3;
    }

    // 批量操作列表（至少需要一个操作）
    repeated BulkOperation operations = 1 [
        (buf.validate.field).repeated = {
            min_items: 1,
            max_items: 5000 // 最大5000
        },
        (google.api.field_behavior) = REQUIRED
    ];
}


// 批量操作响应
message BulkDocumentResponse {
    // 错误信息
    // 只在操作失败时出现（status >= 400）
    // 此时 shards 字段将不存在
    message Error {
        // 错误类型，例如：
        // - document_missing_exception: 文档不存在
        // - version_conflict_engine_exception: 版本冲突
        // - mapper_parsing_exception: 字段映射错误
        // - 更多错误类型请参考 OpenSearch 文档
        string error_type = 1;
        // 错误具体原因描述
        string error_reason = 2;
    }
    // 分片信息
    // 只在操作成功时出现（status < 400）
    // 此时 error 字段将不存在
    message Shards {
        // 总分片数
        int32 total = 1;
        // 成功分片数
        int32 successful = 2;
        // 失败分片数
        int32 failed = 3;
    }
    // 单个操作的结果
    message OperationResult {
        // 操作类型
        OperationType operation_type = 1;
        // 索引名称
        string index = 2;
        // 文档ID
        string document_id = 3;
        // 操作返回的状态码:
        // - 200: 成功（更新）
        // - 201: 成功（创建）
        // - 404: 文档不存在
        // - 409: 版本冲突
        int32 status = 4;
        // 操作结果描述:
        // - created: 文档被创建
        // - updated: 文档被更新
        // - deleted: 文档被删除
        // - noop: 文档未发生变化
        // - not_found: 文档不存在
        string result = 5;
        // 文档版本号
        // 预期每次更新都会增加
        int32 version = 6;  
        // 错误信息（如果有）
        // 只在操作失败时存在（status >= 400）
        optional Error error = 7;
        // 分片信息
        // 只在操作成功时存在（status < 400）
        Shards shards = 8;
    }

    // 批量操作中是否存在任何错误
    bool has_errors = 1;
    // 所有操作的结果列表
    repeated OperationResult results = 2;
    // 整个批量操作耗时（毫秒）
    int32 took = 3;
}

// 操作类型枚举
enum OperationType {
    // 未指定操作类型
    OPERATION_TYPE_UNSPECIFIED = 0;
    // 索引文档
    INDEX = 1;
    // 创建文档
    CREATE = 2;
    // 更新文档
    UPDATE = 3;
    // 删除文档
    DELETE = 4;
}
