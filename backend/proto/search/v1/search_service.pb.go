// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/search/v1/search_service.proto

package searchpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 服务自动生成的唯一错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 119900
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		119900: "ERR_CODE_UNSPECIFIED",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":          0,
		"ERR_CODE_UNSPECIFIED": 119900,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_search_v1_search_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_search_v1_search_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_service_proto_rawDescGZIP(), []int{0}
}

// 排序方向
type SearchDocumentRequest_Sort_Order int32

const (
	// 未指定排序方向
	SearchDocumentRequest_Sort_ORDER_UNSPECIFIED SearchDocumentRequest_Sort_Order = 0
	// 升序
	SearchDocumentRequest_Sort_ASC SearchDocumentRequest_Sort_Order = 1
	// 降序
	SearchDocumentRequest_Sort_DESC SearchDocumentRequest_Sort_Order = 2
)

// Enum value maps for SearchDocumentRequest_Sort_Order.
var (
	SearchDocumentRequest_Sort_Order_name = map[int32]string{
		0: "ORDER_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	SearchDocumentRequest_Sort_Order_value = map[string]int32{
		"ORDER_UNSPECIFIED": 0,
		"ASC":               1,
		"DESC":              2,
	}
)

func (x SearchDocumentRequest_Sort_Order) Enum() *SearchDocumentRequest_Sort_Order {
	p := new(SearchDocumentRequest_Sort_Order)
	*p = x
	return p
}

func (x SearchDocumentRequest_Sort_Order) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SearchDocumentRequest_Sort_Order) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_search_v1_search_service_proto_enumTypes[1].Descriptor()
}

func (SearchDocumentRequest_Sort_Order) Type() protoreflect.EnumType {
	return &file_backend_proto_search_v1_search_service_proto_enumTypes[1]
}

func (x SearchDocumentRequest_Sort_Order) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SearchDocumentRequest_Sort_Order.Descriptor instead.
func (SearchDocumentRequest_Sort_Order) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_service_proto_rawDescGZIP(), []int{0, 0, 0}
}

// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
//
// SearchDocumentRequest 定义搜索请求消息
type SearchDocumentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 搜索策略组合(使用 BoolStrategy 构建复杂查询条件)
	// 具体组合可以翻阅 Strategy 实现
	Strategy *Strategy `protobuf:"bytes,1,opt,name=strategy,proto3" json:"strategy,omitempty"`
	// index 索引
	Index string `protobuf:"bytes,2,opt,name=index,proto3" json:"index,omitempty"`
	// 每页大小, 必填
	// 请根据业务需求自定义分页大小, search service 不会设置默认值
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// (-- api-linter: core::0140::prepositions=disabled
	//
	//	aip.dev/not-precedent: 这里必须叫 search_after, 不能叫 offset --)
	//
	// 分页起点, 非必填
	// 可以作为分页查询的起始点, 用于获取分页数据, 返回结果为offset + 1, 如果为空则从第一页开始查询
	// 当填写 offset 时, 字段值必须和 sort 字段值一致, 比如:
	// sort = {field: "created_at", order: DESC}, 则 offset: "2024-03-14T10:00:00Z"
	// open search 会先按照 created_at 字段降序排序, 然后从 2024-03-14T10:00:00Z 开始查询, ** 注意: 不包含2024-03-14T10:00:00Z **
	//
	// 同理 sort = {field: "id", order: DESC} 则 offset: 4396
	// open search 会先按照 id 字段降序排序, 然后从 4396 开始查询, ** 注意: 不包含4396 **
	SearchAfter []*structpb.Value `protobuf:"bytes,4,rep,name=search_after,json=searchAfter,proto3" json:"search_after,omitempty"`
	// 排序字段
	// Search Service 不会赋予任何默认排序, open search 默认根据查询条件相关性排序
	// 上游需要根据业务需求自行排序
	//
	// 举例: 如果使用_id(open search的id) 排序, 则 sort = {field: "_id", order: ASC} (注意区分open search 的 _id 和 业务数据 的 id)
	// 如果使用创建时间排序, 则 sort = {field: "created_at", order: DESC}
	Sort          []*SearchDocumentRequest_Sort `protobuf:"bytes,5,rep,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchDocumentRequest) Reset() {
	*x = SearchDocumentRequest{}
	mi := &file_backend_proto_search_v1_search_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchDocumentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDocumentRequest) ProtoMessage() {}

func (x *SearchDocumentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDocumentRequest.ProtoReflect.Descriptor instead.
func (*SearchDocumentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_service_proto_rawDescGZIP(), []int{0}
}

func (x *SearchDocumentRequest) GetStrategy() *Strategy {
	if x != nil {
		return x.Strategy
	}
	return nil
}

func (x *SearchDocumentRequest) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *SearchDocumentRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchDocumentRequest) GetSearchAfter() []*structpb.Value {
	if x != nil {
		return x.SearchAfter
	}
	return nil
}

func (x *SearchDocumentRequest) GetSort() []*SearchDocumentRequest_Sort {
	if x != nil {
		return x.Sort
	}
	return nil
}

// SearchDocumentResponse 定义搜索响应
type SearchDocumentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 搜索命中项列表
	Hits []*SearchDocumentResponse_Hit `protobuf:"bytes,1,rep,name=hits,proto3" json:"hits,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 响应耗时（单位：毫秒），表示整个搜索请求的处理时间
	Took int32 `protobuf:"varint,3,opt,name=took,proto3" json:"took,omitempty"`
	// 是否超时
	TimedOut bool `protobuf:"varint,4,opt,name=timed_out,json=timedOut,proto3" json:"timed_out,omitempty"`
	// 搜索命中总数
	Total *SearchDocumentResponse_Total `protobuf:"bytes,5,opt,name=total,proto3" json:"total,omitempty"`
	// 最大相关性得分, 帮助判断整体匹配质量（比如 0 分可能表示无匹配）
	MaxScore      float64 `protobuf:"fixed64,6,opt,name=max_score,json=maxScore,proto3" json:"max_score,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchDocumentResponse) Reset() {
	*x = SearchDocumentResponse{}
	mi := &file_backend_proto_search_v1_search_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchDocumentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDocumentResponse) ProtoMessage() {}

func (x *SearchDocumentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDocumentResponse.ProtoReflect.Descriptor instead.
func (*SearchDocumentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_service_proto_rawDescGZIP(), []int{1}
}

func (x *SearchDocumentResponse) GetHits() []*SearchDocumentResponse_Hit {
	if x != nil {
		return x.Hits
	}
	return nil
}

func (x *SearchDocumentResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *SearchDocumentResponse) GetTook() int32 {
	if x != nil {
		return x.Took
	}
	return 0
}

func (x *SearchDocumentResponse) GetTimedOut() bool {
	if x != nil {
		return x.TimedOut
	}
	return false
}

func (x *SearchDocumentResponse) GetTotal() *SearchDocumentResponse_Total {
	if x != nil {
		return x.Total
	}
	return nil
}

func (x *SearchDocumentResponse) GetMaxScore() float64 {
	if x != nil {
		return x.MaxScore
	}
	return 0
}

// 排序字段
type SearchDocumentRequest_Sort struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// 排序方向, 必填, search service 不会设置任何默认值
	Order         SearchDocumentRequest_Sort_Order `protobuf:"varint,2,opt,name=order,proto3,enum=backend.proto.search.v1.SearchDocumentRequest_Sort_Order" json:"order,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchDocumentRequest_Sort) Reset() {
	*x = SearchDocumentRequest_Sort{}
	mi := &file_backend_proto_search_v1_search_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchDocumentRequest_Sort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDocumentRequest_Sort) ProtoMessage() {}

func (x *SearchDocumentRequest_Sort) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDocumentRequest_Sort.ProtoReflect.Descriptor instead.
func (*SearchDocumentRequest_Sort) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *SearchDocumentRequest_Sort) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *SearchDocumentRequest_Sort) GetOrder() SearchDocumentRequest_Sort_Order {
	if x != nil {
		return x.Order
	}
	return SearchDocumentRequest_Sort_ORDER_UNSPECIFIED
}

// Hit 搜索结果命中项
type SearchDocumentResponse_Hit struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 文档所属索引, 类似数据库表名
	Index string `protobuf:"bytes,1,opt,name=index,proto3" json:"index,omitempty"`
	// 文档 id
	Id string `protobuf:"bytes,2,opt,name=id,proto3" json:"id,omitempty"`
	// 相关性得分, 越高表示匹配程度越高, 可用于结果排序（但不同查询的分数不可直接比较）
	Score float64 `protobuf:"fixed64,3,opt,name=score,proto3" json:"score,omitempty"`
	// 原始文档数据
	Source *structpb.Struct `protobuf:"bytes,4,opt,name=source,proto3" json:"source,omitempty"`
	// 排序
	Sort          []*structpb.Value `protobuf:"bytes,5,rep,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchDocumentResponse_Hit) Reset() {
	*x = SearchDocumentResponse_Hit{}
	mi := &file_backend_proto_search_v1_search_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchDocumentResponse_Hit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDocumentResponse_Hit) ProtoMessage() {}

func (x *SearchDocumentResponse_Hit) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDocumentResponse_Hit.ProtoReflect.Descriptor instead.
func (*SearchDocumentResponse_Hit) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *SearchDocumentResponse_Hit) GetIndex() string {
	if x != nil {
		return x.Index
	}
	return ""
}

func (x *SearchDocumentResponse_Hit) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SearchDocumentResponse_Hit) GetScore() float64 {
	if x != nil {
		return x.Score
	}
	return 0
}

func (x *SearchDocumentResponse_Hit) GetSource() *structpb.Struct {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *SearchDocumentResponse_Hit) GetSort() []*structpb.Value {
	if x != nil {
		return x.Sort
	}
	return nil
}

// Total 定义总数信息
type SearchDocumentResponse_Total struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 分页时计算总页数的重要依据（但要注意 relation 的说明）
	Value int32 `protobuf:"varint,1,opt,name=value,proto3" json:"value,omitempty"`
	// 总数关系标识：
	// - "eq" 表示精确总数
	// - "gte" 表示最小总数（当总数过大时可能返回近似值）
	Relation      string `protobuf:"bytes,2,opt,name=relation,proto3" json:"relation,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchDocumentResponse_Total) Reset() {
	*x = SearchDocumentResponse_Total{}
	mi := &file_backend_proto_search_v1_search_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchDocumentResponse_Total) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchDocumentResponse_Total) ProtoMessage() {}

func (x *SearchDocumentResponse_Total) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchDocumentResponse_Total.ProtoReflect.Descriptor instead.
func (*SearchDocumentResponse_Total) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_service_proto_rawDescGZIP(), []int{1, 1}
}

func (x *SearchDocumentResponse_Total) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *SearchDocumentResponse_Total) GetRelation() string {
	if x != nil {
		return x.Relation
	}
	return ""
}

var File_backend_proto_search_v1_search_service_proto protoreflect.FileDescriptor

const file_backend_proto_search_v1_search_service_proto_rawDesc = "" +
	"\n" +
	",backend/proto/search/v1/search_service.proto\x12\x17backend.proto.search.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1cgoogle/api/annotations.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a-backend/proto/search/v1/search_strategy.proto\x1a&backend/proto/search/v1/document.proto\"\x80\x04\n" +
	"\x15SearchDocumentRequest\x12H\n" +
	"\bstrategy\x18\x01 \x01(\v2!.backend.proto.search.v1.StrategyB\t\xe0A\x02\xbaH\x03\xc8\x01\x01R\bstrategy\x12 \n" +
	"\x05index\x18\x02 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05index\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xe0A\x02\xbaH\x04\x1a\x02 \x00R\bpageSize\x12>\n" +
	"\fsearch_after\x18\x04 \x03(\v2\x16.google.protobuf.ValueB\x03\xe0A\x01R\vsearchAfter\x12T\n" +
	"\x04sort\x18\x05 \x03(\v23.backend.proto.search.v1.SearchDocumentRequest.SortB\v\xe0A\x02\xbaH\x05\x92\x01\x02\b\x01R\x04sort\x1a\xbb\x01\n" +
	"\x04Sort\x12 \n" +
	"\x05field\x18\x01 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05field\x12^\n" +
	"\x05order\x18\x02 \x01(\x0e29.backend.proto.search.v1.SearchDocumentRequest.Sort.OrderB\r\xe0A\x02\xbaH\a\x82\x01\x04\x18\x01\x18\x02R\x05order\"1\n" +
	"\x05Order\x12\x15\n" +
	"\x11ORDER_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02\"\x80\x04\n" +
	"\x16SearchDocumentResponse\x12G\n" +
	"\x04hits\x18\x01 \x03(\v23.backend.proto.search.v1.SearchDocumentResponse.HitR\x04hits\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\x12\n" +
	"\x04took\x18\x03 \x01(\x05R\x04took\x12\x1b\n" +
	"\ttimed_out\x18\x04 \x01(\bR\btimedOut\x12K\n" +
	"\x05total\x18\x05 \x01(\v25.backend.proto.search.v1.SearchDocumentResponse.TotalR\x05total\x12\x1b\n" +
	"\tmax_score\x18\x06 \x01(\x01R\bmaxScore\x1a\x9e\x01\n" +
	"\x03Hit\x12\x14\n" +
	"\x05index\x18\x01 \x01(\tR\x05index\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\tR\x02id\x12\x14\n" +
	"\x05score\x18\x03 \x01(\x01R\x05score\x12/\n" +
	"\x06source\x18\x04 \x01(\v2\x17.google.protobuf.StructR\x06source\x12*\n" +
	"\x04sort\x18\x05 \x03(\v2\x16.google.protobuf.ValueR\x04sort\x1a9\n" +
	"\x05Total\x12\x14\n" +
	"\x05value\x18\x01 \x01(\x05R\x05value\x12\x1a\n" +
	"\brelation\x18\x02 \x01(\tR\brelation*6\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10ܨ\a2\xef\x01\n" +
	"\rSearchService\x12q\n" +
	"\x0eSearchDocument\x12..backend.proto.search.v1.SearchDocumentRequest\x1a/.backend.proto.search.v1.SearchDocumentResponse\x12k\n" +
	"\fBulkDocument\x12,.backend.proto.search.v1.BulkDocumentRequest\x1a-.backend.proto.search.v1.BulkDocumentResponseBe\n" +
	"!com.moego.backend.proto.search.v1P\x01Z>github.com/MoeGolibrary/moego/backend/proto/search/v1;searchpbb\x06proto3"

var (
	file_backend_proto_search_v1_search_service_proto_rawDescOnce sync.Once
	file_backend_proto_search_v1_search_service_proto_rawDescData []byte
)

func file_backend_proto_search_v1_search_service_proto_rawDescGZIP() []byte {
	file_backend_proto_search_v1_search_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_search_v1_search_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_search_v1_search_service_proto_rawDesc), len(file_backend_proto_search_v1_search_service_proto_rawDesc)))
	})
	return file_backend_proto_search_v1_search_service_proto_rawDescData
}

var file_backend_proto_search_v1_search_service_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_backend_proto_search_v1_search_service_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_backend_proto_search_v1_search_service_proto_goTypes = []any{
	(ErrCode)(0),                          // 0: backend.proto.search.v1.ErrCode
	(SearchDocumentRequest_Sort_Order)(0), // 1: backend.proto.search.v1.SearchDocumentRequest.Sort.Order
	(*SearchDocumentRequest)(nil),         // 2: backend.proto.search.v1.SearchDocumentRequest
	(*SearchDocumentResponse)(nil),        // 3: backend.proto.search.v1.SearchDocumentResponse
	(*SearchDocumentRequest_Sort)(nil),    // 4: backend.proto.search.v1.SearchDocumentRequest.Sort
	(*SearchDocumentResponse_Hit)(nil),    // 5: backend.proto.search.v1.SearchDocumentResponse.Hit
	(*SearchDocumentResponse_Total)(nil),  // 6: backend.proto.search.v1.SearchDocumentResponse.Total
	(*Strategy)(nil),                      // 7: backend.proto.search.v1.Strategy
	(*structpb.Value)(nil),                // 8: google.protobuf.Value
	(*structpb.Struct)(nil),               // 9: google.protobuf.Struct
	(*BulkDocumentRequest)(nil),           // 10: backend.proto.search.v1.BulkDocumentRequest
	(*BulkDocumentResponse)(nil),          // 11: backend.proto.search.v1.BulkDocumentResponse
}
var file_backend_proto_search_v1_search_service_proto_depIdxs = []int32{
	7,  // 0: backend.proto.search.v1.SearchDocumentRequest.strategy:type_name -> backend.proto.search.v1.Strategy
	8,  // 1: backend.proto.search.v1.SearchDocumentRequest.search_after:type_name -> google.protobuf.Value
	4,  // 2: backend.proto.search.v1.SearchDocumentRequest.sort:type_name -> backend.proto.search.v1.SearchDocumentRequest.Sort
	5,  // 3: backend.proto.search.v1.SearchDocumentResponse.hits:type_name -> backend.proto.search.v1.SearchDocumentResponse.Hit
	6,  // 4: backend.proto.search.v1.SearchDocumentResponse.total:type_name -> backend.proto.search.v1.SearchDocumentResponse.Total
	1,  // 5: backend.proto.search.v1.SearchDocumentRequest.Sort.order:type_name -> backend.proto.search.v1.SearchDocumentRequest.Sort.Order
	9,  // 6: backend.proto.search.v1.SearchDocumentResponse.Hit.source:type_name -> google.protobuf.Struct
	8,  // 7: backend.proto.search.v1.SearchDocumentResponse.Hit.sort:type_name -> google.protobuf.Value
	2,  // 8: backend.proto.search.v1.SearchService.SearchDocument:input_type -> backend.proto.search.v1.SearchDocumentRequest
	10, // 9: backend.proto.search.v1.SearchService.BulkDocument:input_type -> backend.proto.search.v1.BulkDocumentRequest
	3,  // 10: backend.proto.search.v1.SearchService.SearchDocument:output_type -> backend.proto.search.v1.SearchDocumentResponse
	11, // 11: backend.proto.search.v1.SearchService.BulkDocument:output_type -> backend.proto.search.v1.BulkDocumentResponse
	10, // [10:12] is the sub-list for method output_type
	8,  // [8:10] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_backend_proto_search_v1_search_service_proto_init() }
func file_backend_proto_search_v1_search_service_proto_init() {
	if File_backend_proto_search_v1_search_service_proto != nil {
		return
	}
	file_backend_proto_search_v1_search_strategy_proto_init()
	file_backend_proto_search_v1_document_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_search_v1_search_service_proto_rawDesc), len(file_backend_proto_search_v1_search_service_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_search_v1_search_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_search_v1_search_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_search_v1_search_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_search_v1_search_service_proto_msgTypes,
	}.Build()
	File_backend_proto_search_v1_search_service_proto = out.File
	file_backend_proto_search_v1_search_service_proto_goTypes = nil
	file_backend_proto_search_v1_search_service_proto_depIdxs = nil
}
