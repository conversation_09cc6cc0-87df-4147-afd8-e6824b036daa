// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/search/v1/search_service.proto

package searchpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	SearchService_SearchDocument_FullMethodName = "/backend.proto.search.v1.SearchService/SearchDocument"
	SearchService_BulkDocument_FullMethodName   = "/backend.proto.search.v1.SearchService/BulkDocument"
)

// SearchServiceClient is the client API for SearchService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 搜索服务定义
type SearchServiceClient interface {
	// Search 执行搜索操作
	SearchDocument(ctx context.Context, in *SearchDocumentRequest, opts ...grpc.CallOption) (*SearchDocumentResponse, error)
	// Bulk Document, 批量操作, 可以是增删改document
	// 注意:
	// 1. 非原子性操作: bulk API 不是事务性的，不支持回滚
	// 2. 独立执行: 每个操作都是独立的，某个操作失败不会影响其他操作的执行
	// 3. 返回结果: 会返回每个操作的执行结果，包括成功和失败的详细信息
	BulkDocument(ctx context.Context, in *BulkDocumentRequest, opts ...grpc.CallOption) (*BulkDocumentResponse, error)
}

type searchServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSearchServiceClient(cc grpc.ClientConnInterface) SearchServiceClient {
	return &searchServiceClient{cc}
}

func (c *searchServiceClient) SearchDocument(ctx context.Context, in *SearchDocumentRequest, opts ...grpc.CallOption) (*SearchDocumentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchDocumentResponse)
	err := c.cc.Invoke(ctx, SearchService_SearchDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *searchServiceClient) BulkDocument(ctx context.Context, in *BulkDocumentRequest, opts ...grpc.CallOption) (*BulkDocumentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BulkDocumentResponse)
	err := c.cc.Invoke(ctx, SearchService_BulkDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SearchServiceServer is the server API for SearchService service.
// All implementations must embed UnimplementedSearchServiceServer
// for forward compatibility.
//
// 搜索服务定义
type SearchServiceServer interface {
	// Search 执行搜索操作
	SearchDocument(context.Context, *SearchDocumentRequest) (*SearchDocumentResponse, error)
	// Bulk Document, 批量操作, 可以是增删改document
	// 注意:
	// 1. 非原子性操作: bulk API 不是事务性的，不支持回滚
	// 2. 独立执行: 每个操作都是独立的，某个操作失败不会影响其他操作的执行
	// 3. 返回结果: 会返回每个操作的执行结果，包括成功和失败的详细信息
	BulkDocument(context.Context, *BulkDocumentRequest) (*BulkDocumentResponse, error)
	mustEmbedUnimplementedSearchServiceServer()
}

// UnimplementedSearchServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSearchServiceServer struct{}

func (UnimplementedSearchServiceServer) SearchDocument(context.Context, *SearchDocumentRequest) (*SearchDocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchDocument not implemented")
}
func (UnimplementedSearchServiceServer) BulkDocument(context.Context, *BulkDocumentRequest) (*BulkDocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BulkDocument not implemented")
}
func (UnimplementedSearchServiceServer) mustEmbedUnimplementedSearchServiceServer() {}
func (UnimplementedSearchServiceServer) testEmbeddedByValue()                       {}

// UnsafeSearchServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SearchServiceServer will
// result in compilation errors.
type UnsafeSearchServiceServer interface {
	mustEmbedUnimplementedSearchServiceServer()
}

func RegisterSearchServiceServer(s grpc.ServiceRegistrar, srv SearchServiceServer) {
	// If the following call pancis, it indicates UnimplementedSearchServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&SearchService_ServiceDesc, srv)
}

func _SearchService_SearchDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchDocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServiceServer).SearchDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SearchService_SearchDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServiceServer).SearchDocument(ctx, req.(*SearchDocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SearchService_BulkDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BulkDocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SearchServiceServer).BulkDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: SearchService_BulkDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SearchServiceServer).BulkDocument(ctx, req.(*BulkDocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SearchService_ServiceDesc is the grpc.ServiceDesc for SearchService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SearchService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.search.v1.SearchService",
	HandlerType: (*SearchServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SearchDocument",
			Handler:    _SearchService_SearchDocument_Handler,
		},
		{
			MethodName: "BulkDocument",
			Handler:    _SearchService_BulkDocument_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/search/v1/search_service.proto",
}
