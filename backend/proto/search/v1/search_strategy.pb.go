// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/search/v1/search_strategy.proto

package searchpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 需要添加的枚举
type MatchStrategy_MatchOperator int32

const (
	// 未指定匹配操作符
	MatchStrategy_MATCH_OPERATOR_UNSPECIFIED MatchStrategy_MatchOperator = 0
	// 必须匹配所有条件
	MatchStrategy_AND MatchStrategy_MatchOperator = 1
	// 至少匹配一个条件
	MatchStrategy_OR MatchStrategy_MatchOperator = 2
)

// Enum value maps for MatchStrategy_MatchOperator.
var (
	MatchStrategy_MatchOperator_name = map[int32]string{
		0: "MATCH_OPERATOR_UNSPECIFIED",
		1: "AND",
		2: "OR",
	}
	MatchStrategy_MatchOperator_value = map[string]int32{
		"MATCH_OPERATOR_UNSPECIFIED": 0,
		"AND":                        1,
		"OR":                         2,
	}
)

func (x MatchStrategy_MatchOperator) Enum() *MatchStrategy_MatchOperator {
	p := new(MatchStrategy_MatchOperator)
	*p = x
	return p
}

func (x MatchStrategy_MatchOperator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MatchStrategy_MatchOperator) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_search_v1_search_strategy_proto_enumTypes[0].Descriptor()
}

func (MatchStrategy_MatchOperator) Type() protoreflect.EnumType {
	return &file_backend_proto_search_v1_search_strategy_proto_enumTypes[0]
}

func (x MatchStrategy_MatchOperator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MatchStrategy_MatchOperator.Descriptor instead.
func (MatchStrategy_MatchOperator) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{1, 0}
}

// 多字段匹配类型枚举
type MultiMatchStrategy_MatchType int32

const (
	// 未指定匹配类型
	MultiMatchStrategy_MATCH_TYPE_UNSPECIFIED MultiMatchStrategy_MatchType = 0
	// 查找最匹配的字段 (默认)
	MultiMatchStrategy_BEST_FIELDS MultiMatchStrategy_MatchType = 1
	// 合并多个字段的匹配分数
	MultiMatchStrategy_MOST_FIELDS MultiMatchStrategy_MatchType = 2
	// 将所有字段视为一个大字段
	MultiMatchStrategy_CROSS_FIELDS MultiMatchStrategy_MatchType = 3
	// 在每个字段中作为短语匹配
	MultiMatchStrategy_PHRASE MultiMatchStrategy_MatchType = 4
	// 短语前缀匹配
	MultiMatchStrategy_PHRASE_PREFIX MultiMatchStrategy_MatchType = 5
)

// Enum value maps for MultiMatchStrategy_MatchType.
var (
	MultiMatchStrategy_MatchType_name = map[int32]string{
		0: "MATCH_TYPE_UNSPECIFIED",
		1: "BEST_FIELDS",
		2: "MOST_FIELDS",
		3: "CROSS_FIELDS",
		4: "PHRASE",
		5: "PHRASE_PREFIX",
	}
	MultiMatchStrategy_MatchType_value = map[string]int32{
		"MATCH_TYPE_UNSPECIFIED": 0,
		"BEST_FIELDS":            1,
		"MOST_FIELDS":            2,
		"CROSS_FIELDS":           3,
		"PHRASE":                 4,
		"PHRASE_PREFIX":          5,
	}
)

func (x MultiMatchStrategy_MatchType) Enum() *MultiMatchStrategy_MatchType {
	p := new(MultiMatchStrategy_MatchType)
	*p = x
	return p
}

func (x MultiMatchStrategy_MatchType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (MultiMatchStrategy_MatchType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_search_v1_search_strategy_proto_enumTypes[1].Descriptor()
}

func (MultiMatchStrategy_MatchType) Type() protoreflect.EnumType {
	return &file_backend_proto_search_v1_search_strategy_proto_enumTypes[1]
}

func (x MultiMatchStrategy_MatchType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use MultiMatchStrategy_MatchType.Descriptor instead.
func (MultiMatchStrategy_MatchType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{6, 0}
}

// 默认操作作
type QueryStringStrategy_DefaultOperator int32

const (
	// 未指定默认操作作
	QueryStringStrategy_DEFAULT_OPERATOR_UNSPECIFIED QueryStringStrategy_DefaultOperator = 0
	// 必须匹配所有条件
	QueryStringStrategy_AND QueryStringStrategy_DefaultOperator = 1
	// 至少匹配一个条件
	QueryStringStrategy_OR QueryStringStrategy_DefaultOperator = 2
)

// Enum value maps for QueryStringStrategy_DefaultOperator.
var (
	QueryStringStrategy_DefaultOperator_name = map[int32]string{
		0: "DEFAULT_OPERATOR_UNSPECIFIED",
		1: "AND",
		2: "OR",
	}
	QueryStringStrategy_DefaultOperator_value = map[string]int32{
		"DEFAULT_OPERATOR_UNSPECIFIED": 0,
		"AND":                          1,
		"OR":                           2,
	}
)

func (x QueryStringStrategy_DefaultOperator) Enum() *QueryStringStrategy_DefaultOperator {
	p := new(QueryStringStrategy_DefaultOperator)
	*p = x
	return p
}

func (x QueryStringStrategy_DefaultOperator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (QueryStringStrategy_DefaultOperator) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_search_v1_search_strategy_proto_enumTypes[2].Descriptor()
}

func (QueryStringStrategy_DefaultOperator) Type() protoreflect.EnumType {
	return &file_backend_proto_search_v1_search_strategy_proto_enumTypes[2]
}

func (x QueryStringStrategy_DefaultOperator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use QueryStringStrategy_DefaultOperator.Descriptor instead.
func (QueryStringStrategy_DefaultOperator) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{7, 0}
}

// 精确查询（完全匹配字段值）
// 适用场景：字段值完全等于指定值的场景
// 示例：查询 name 字段值为 "jett" 的文档
// TermStrategy { field: "name", value: "jett" }
// 对应open search的查询语句为:
//
//	{
//	    "query": {
//	        "term": {
//	            "name": "jett"
//	        }
//	    }
//	}
//
// 值得注意的是, open search的text模式会自动分词, 所以如果存在例如"jett-4396"这样的字段
// 需要强行使用keyword模式, 否则会查询不到结果
// 即使用: TermStrategy { field: "name.keyword", value: "jett-4396" }
// 对应open search的查询语句为:
//
//	{
//	    "query": {
//	        "term": {
//	            "name.keyword": "jett-4396"
//	        }
//	    }
//	}
type TermStrategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字段名
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// 字段值
	Value         *structpb.Value `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TermStrategy) Reset() {
	*x = TermStrategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TermStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TermStrategy) ProtoMessage() {}

func (x *TermStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TermStrategy.ProtoReflect.Descriptor instead.
func (*TermStrategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{0}
}

func (x *TermStrategy) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *TermStrategy) GetValue() *structpb.Value {
	if x != nil {
		return x.Value
	}
	return nil
}

// 全文搜索（支持分词查询）
// 适用场景：文本字段的模糊搜索
// 示例1：查询 title 字段包含 "手机" AND "5G" 的商品
// MatchStrategy { field: "title", query: "手机 5G", operator: AND }
// 对应open search的查询语句为:
//
//	{
//	    "query": {
//	        "match": {
//	            "title": {
//	                "query": "手机 5G",
//	                "operator": "AND"
//	            }
//	        }
//	    }
//	}
//
//	result: {
//	    "hits": {
//	        "hits": [
//	            {
//	                "title": "华为手机5G就是牛"
//	            }
//	        ]
//	    }
//	}
//
// 示例2：查询 content 字段包含 "人工智能" OR "AI" 的文章
// MatchStrategy { field: "content", query: "人工智能 AI", operator: OR }
// 对应open search的查询语句为:
//
//	{
//	    "query": {
//	        "match": {
//	            "content": {
//	                "query": "人工智能 AI",
//	                "operator": "OR"
//	            }
//	        }
//	    }
//	}
//
//	result: {
//	    "hits": {
//	        "hits": [
//	            {
//	                "content": "华为人工智能真牛"
//	            },
//	            {
//	                "content": "华为AI真牛"
//	            }
//	        ]
//	    }
//	}
type MatchStrategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字段名
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// 查询条件
	Query string `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	// 权重
	Boost float32 `protobuf:"fixed32,3,opt,name=boost,proto3" json:"boost,omitempty"`
	// 匹配方式
	Operator      MatchStrategy_MatchOperator `protobuf:"varint,4,opt,name=operator,proto3,enum=backend.proto.search.v1.MatchStrategy_MatchOperator" json:"operator,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MatchStrategy) Reset() {
	*x = MatchStrategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MatchStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchStrategy) ProtoMessage() {}

func (x *MatchStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchStrategy.ProtoReflect.Descriptor instead.
func (*MatchStrategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{1}
}

func (x *MatchStrategy) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *MatchStrategy) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *MatchStrategy) GetBoost() float32 {
	if x != nil {
		return x.Boost
	}
	return 0
}

func (x *MatchStrategy) GetOperator() MatchStrategy_MatchOperator {
	if x != nil {
		return x.Operator
	}
	return MatchStrategy_MATCH_OPERATOR_UNSPECIFIED
}

// 范围查询（支持数值/日期范围）
// 示例1：查询价格在 1000-2000 之间的商品
// RangeStrategy { field: "price", gte: 1000, lte: 2000 }
// 对应open search的查询语句为:
//
//	{
//	    "query": {
//	        "range": {
//	            "price": {
//	                "gte": 1000,
//	                "lte": 2000
//	            }
//	        }
//	    }
//	}
//
// 需要注意的是, 必须知道字段类型, 例如:
// 如果字段是日期类型, 则gte和lte需要传入时间戳, 例如: 1712985600000
// 如果字段是字符串类型的时间, 则gte和lte需要传入时间字符串, 例如: "2024-04-01 00:00:00"
// 如果字段是数值类型, 则gte和lte需要传入数值, 例如: 1000
type RangeStrategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字段名
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// 大于等于的值
	Gte *structpb.Value `protobuf:"bytes,2,opt,name=gte,proto3" json:"gte,omitempty"`
	// 小于等于的值
	Lte           *structpb.Value `protobuf:"bytes,3,opt,name=lte,proto3" json:"lte,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RangeStrategy) Reset() {
	*x = RangeStrategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RangeStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RangeStrategy) ProtoMessage() {}

func (x *RangeStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RangeStrategy.ProtoReflect.Descriptor instead.
func (*RangeStrategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{2}
}

func (x *RangeStrategy) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *RangeStrategy) GetGte() *structpb.Value {
	if x != nil {
		return x.Gte
	}
	return nil
}

func (x *RangeStrategy) GetLte() *structpb.Value {
	if x != nil {
		return x.Lte
	}
	return nil
}

// 通配符匹配（支持 * 和 ? 通配符）
// 适用场景：简单模式匹配（慎用，性能较低）
// 示例：查询以 "华为" 开头，以 "手机" 结尾的商品名称
// WildcardStrategy { field: "name", value: "华为手机*" }
// 对应open search的查询语句为:
//
//	{
//	    "query": {
//	        "wildcard": {
//	            "name": "华为手机*"
//	        }
//	    }
//	}
//
//	result: {
//	    "hits": {
//	        "hits": [
//	            {
//	                "name": "华为手机就是牛"
//	            }
//	        ]
//	    }
//	}
type WildcardStrategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字段名
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// 通配符表达式
	Value         string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *WildcardStrategy) Reset() {
	*x = WildcardStrategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WildcardStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WildcardStrategy) ProtoMessage() {}

func (x *WildcardStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WildcardStrategy.ProtoReflect.Descriptor instead.
func (*WildcardStrategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{3}
}

func (x *WildcardStrategy) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *WildcardStrategy) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

// 多值精确匹配（支持多个值的精确匹配）
// 适用场景：字段值为多个固定值的场景
// 示例：查询 name 字段值为 "华为" 或 "小米" 的商品
// TermsStrategy { field: "name", values: ["华为", "小米"] }
// 对应open search的查询语句为:
//
//	{
//	    "query": {
//	        "terms": {
//	            "name": ["华为", "小米"]
//	        }
//	    }
//	}
//
//	result: {
//	    "hits": {
//	        "hits": [
//	            {
//	                "name": "华为"
//	            },
//	            {
//	                "name": "小米"
//	            }
//	        ]
//	    }
//	}
//
// 需要注意的是, Terms 有着和Term 一样的问题, 如果避免分词, 请使用Terms{ filed: "name.keyword", values: ["华为-4396","小米-2200"]}
type TermsStrategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字段名
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// 字段值
	Values        []*structpb.Value `protobuf:"bytes,2,rep,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TermsStrategy) Reset() {
	*x = TermsStrategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TermsStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TermsStrategy) ProtoMessage() {}

func (x *TermsStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TermsStrategy.ProtoReflect.Descriptor instead.
func (*TermsStrategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{4}
}

func (x *TermsStrategy) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *TermsStrategy) GetValues() []*structpb.Value {
	if x != nil {
		return x.Values
	}
	return nil
}

// 布尔组合查询（支持复杂逻辑组合）
// 适用场景：
// 1. 需要组合多个查询条件的场景（AND/OR/NOT）
// 2. 需要控制相关性得分的场景
//
// 示例1：电商商品搜索（必须满足所有条件）
// 条件: category == 手机 && price >= 1000 && price <= 5000 && title contains "5G 旗舰" && status != 0
//
//	BoolStrategy {
//	    must: [
//	        { terms: { field: "category", values: ["手机"] } },  // 分类必须是手机
//	        { range: { field: "price", gte: 1000, lte: 5000 } }, // 价格在1000-5000
//	        { match: { field: "title", query: "5G 旗舰", operator: AND } } // 标题包含5G和旗舰
//	    ],
//	    must_not: [
//	        { term: { field: "status", value: 0 } }  // 排除下架商品
//	    ]
//	}
//
// 示例2：订单搜索（至少满足一个条件）
// 条件: order_no == "MOE202307011234" || user_name == "jett"
//
//	BoolStrategy {
//	    should: {
//	        strategies: [
//	            { term: { field: "order_no", value: "MOE202307011234" } },  // 按订单号查询
//	            { match: { field: "user_name", query: "jett" } }  // 按用户名模糊匹配
//	        ],
//	        minimum_match: 1  // 至少满足一个条件
//	    }
//	}
//
// 示例3：组合复杂查询（必须满足A且B，同时排除C，或满足D）
//
//	BoolStrategy {
//	    must: [A, B],
//	    must_not: [C],
//	    should: {
//	        strategies: [D],
//	        minimum_match: 1
//	    }
//	}
type BoolStrategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 必须满足的条件
	Must []*Strategy `protobuf:"bytes,1,rep,name=must,proto3" json:"must,omitempty"`
	// 必须不满足的条件
	MustNot []*Strategy `protobuf:"bytes,2,rep,name=must_not,json=mustNot,proto3" json:"must_not,omitempty"`
	// 过滤条件
	Filter []*Strategy `protobuf:"bytes,3,rep,name=filter,proto3" json:"filter,omitempty"`
	// 至少满足的条件
	Should        *BoolStrategy_BoolShouldStrategy `protobuf:"bytes,4,opt,name=should,proto3" json:"should,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BoolStrategy) Reset() {
	*x = BoolStrategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BoolStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolStrategy) ProtoMessage() {}

func (x *BoolStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolStrategy.ProtoReflect.Descriptor instead.
func (*BoolStrategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{5}
}

func (x *BoolStrategy) GetMust() []*Strategy {
	if x != nil {
		return x.Must
	}
	return nil
}

func (x *BoolStrategy) GetMustNot() []*Strategy {
	if x != nil {
		return x.MustNot
	}
	return nil
}

func (x *BoolStrategy) GetFilter() []*Strategy {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *BoolStrategy) GetShould() *BoolStrategy_BoolShouldStrategy {
	if x != nil {
		return x.Should
	}
	return nil
}

// 多字段匹配策略（支持在多个字段中搜索相同的文本）
// 适用场景：需要在多个字段中搜索相同内容，并可以为不同字段设置权重
// 示例：在名称、描述、标签等多个字段中搜索"手机"
//
//	MultiMatchStrategy {
//	    query: "手机",
//	    type: "most_fields",
//	    fields: ["name^3", "description^2", "tags"],
//	    fuzziness: "AUTO:3,6",
//	    prefix_length: 2,
//	    boost: 2
//	}
//
// 对应OpenSearch查询:
//
//	{
//	    "multi_match": {
//	        "query": "手机",
//	        "type": "most_fields",
//	        "fields": ["name^3", "description^2", "tags"],
//	        "fuzziness": "AUTO:3,6",
//	        "prefix_length": 2,
//	        "boost": 2
//	    }
//	}
type MultiMatchStrategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询文本
	Query string `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	// 匹配类型
	Type MultiMatchStrategy_MatchType `protobuf:"varint,2,opt,name=type,proto3,enum=backend.proto.search.v1.MultiMatchStrategy_MatchType" json:"type,omitempty"`
	// 要搜索的字段列表，可以包含权重，如 "name^3"
	Fields []string `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields,omitempty"`
	// 模糊匹配参数，如 "AUTO" 或 "AUTO:3,6" 或具体数字
	Fuzziness string `protobuf:"bytes,4,opt,name=fuzziness,proto3" json:"fuzziness,omitempty"`
	// 前缀长度（用于模糊匹配时保持前缀不变）
	PrefixLength int32 `protobuf:"varint,5,opt,name=prefix_length,json=prefixLength,proto3" json:"prefix_length,omitempty"`
	// 查询权重
	Boost         float32 `protobuf:"fixed32,6,opt,name=boost,proto3" json:"boost,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MultiMatchStrategy) Reset() {
	*x = MultiMatchStrategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MultiMatchStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MultiMatchStrategy) ProtoMessage() {}

func (x *MultiMatchStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MultiMatchStrategy.ProtoReflect.Descriptor instead.
func (*MultiMatchStrategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{6}
}

func (x *MultiMatchStrategy) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *MultiMatchStrategy) GetType() MultiMatchStrategy_MatchType {
	if x != nil {
		return x.Type
	}
	return MultiMatchStrategy_MATCH_TYPE_UNSPECIFIED
}

func (x *MultiMatchStrategy) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *MultiMatchStrategy) GetFuzziness() string {
	if x != nil {
		return x.Fuzziness
	}
	return ""
}

func (x *MultiMatchStrategy) GetPrefixLength() int32 {
	if x != nil {
		return x.PrefixLength
	}
	return 0
}

func (x *MultiMatchStrategy) GetBoost() float32 {
	if x != nil {
		return x.Boost
	}
	return 0
}

// 查询字符串策略（支持高级查询语法）
// 适用场景：需要使用复杂查询语法，如通配符、正则表达式、模糊搜索等
// 示例：搜索包含"手机"且价格在1000-5000之间的商品
//
//	QueryStringStrategy {
//	    query: "手机 AND price:[1000 TO 5000]",
//	    fields: ["name", "description"],
//	    analyze_wildcard: true,
//	    default_operator: "AND",
//	    boost: 1.5
//	}
//
// 对应OpenSearch查询:
//
//	{
//	    "query_string": {
//	        "query": "手机 AND price:[1000 TO 5000]",
//	        "fields": ["name", "description"],
//	        "analyze_wildcard": true,
//	        "default_operator": "AND",
//	        "boost": 1.5
//	    }
//	}
type QueryStringStrategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 查询字符串，支持高级语法
	Query string `protobuf:"bytes,1,opt,name=query,proto3" json:"query,omitempty"`
	// 要搜索的字段列表
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
	// 是否分析通配符
	AnalyzeWildcard bool `protobuf:"varint,3,opt,name=analyze_wildcard,json=analyzeWildcard,proto3" json:"analyze_wildcard,omitempty"`
	// 默认操作符
	DefaultOperator QueryStringStrategy_DefaultOperator `protobuf:"varint,4,opt,name=default_operator,json=defaultOperator,proto3,enum=backend.proto.search.v1.QueryStringStrategy_DefaultOperator" json:"default_operator,omitempty"`
	// 查询权重
	Boost         float32 `protobuf:"fixed32,5,opt,name=boost,proto3" json:"boost,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QueryStringStrategy) Reset() {
	*x = QueryStringStrategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QueryStringStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QueryStringStrategy) ProtoMessage() {}

func (x *QueryStringStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QueryStringStrategy.ProtoReflect.Descriptor instead.
func (*QueryStringStrategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{7}
}

func (x *QueryStringStrategy) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *QueryStringStrategy) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *QueryStringStrategy) GetAnalyzeWildcard() bool {
	if x != nil {
		return x.AnalyzeWildcard
	}
	return false
}

func (x *QueryStringStrategy) GetDefaultOperator() QueryStringStrategy_DefaultOperator {
	if x != nil {
		return x.DefaultOperator
	}
	return QueryStringStrategy_DEFAULT_OPERATOR_UNSPECIFIED
}

func (x *QueryStringStrategy) GetBoost() float32 {
	if x != nil {
		return x.Boost
	}
	return 0
}

// 短语匹配策略（精确匹配短语，考虑词序）
// 适用场景：需要精确匹配短语，并可以设置词之间的最大间隔
// 示例：搜索标题中包含"5G手机"的商品
//
//	MatchPhraseStrategy {
//	    field: "title",
//	    query: "5G手机",
//	    slop: 1,
//	    boost: 1.2
//	}
//
// 对应OpenSearch查询:
//
//	{
//	    "match_phrase": {
//	        "title": {
//	            "query": "5G手机",
//	            "slop": 1,
//	            "boost": 1.2
//	        }
//	    }
//	}
type MatchPhraseStrategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 字段名
	Field string `protobuf:"bytes,1,opt,name=field,proto3" json:"field,omitempty"`
	// 查询短语
	Query string `protobuf:"bytes,2,opt,name=query,proto3" json:"query,omitempty"`
	// 词之间允许的最大间隔
	Slop int32 `protobuf:"varint,3,opt,name=slop,proto3" json:"slop,omitempty"`
	// 查询权重
	Boost         float32 `protobuf:"fixed32,4,opt,name=boost,proto3" json:"boost,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MatchPhraseStrategy) Reset() {
	*x = MatchPhraseStrategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MatchPhraseStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MatchPhraseStrategy) ProtoMessage() {}

func (x *MatchPhraseStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MatchPhraseStrategy.ProtoReflect.Descriptor instead.
func (*MatchPhraseStrategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{8}
}

func (x *MatchPhraseStrategy) GetField() string {
	if x != nil {
		return x.Field
	}
	return ""
}

func (x *MatchPhraseStrategy) GetQuery() string {
	if x != nil {
		return x.Query
	}
	return ""
}

func (x *MatchPhraseStrategy) GetSlop() int32 {
	if x != nil {
		return x.Slop
	}
	return 0
}

func (x *MatchPhraseStrategy) GetBoost() float32 {
	if x != nil {
		return x.Boost
	}
	return 0
}

// 搜索策略
// 用于定义不同类型的搜索条件
type Strategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Strategy:
	//
	//	*Strategy_Term
	//	*Strategy_Match
	//	*Strategy_Range
	//	*Strategy_Wildcard
	//	*Strategy_Terms
	//	*Strategy_Bool
	//	*Strategy_MultiMatch
	//	*Strategy_MatchPhrase
	//	*Strategy_QueryString
	Strategy      isStrategy_Strategy `protobuf_oneof:"strategy"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Strategy) Reset() {
	*x = Strategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Strategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Strategy) ProtoMessage() {}

func (x *Strategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Strategy.ProtoReflect.Descriptor instead.
func (*Strategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{9}
}

func (x *Strategy) GetStrategy() isStrategy_Strategy {
	if x != nil {
		return x.Strategy
	}
	return nil
}

func (x *Strategy) GetTerm() *TermStrategy {
	if x != nil {
		if x, ok := x.Strategy.(*Strategy_Term); ok {
			return x.Term
		}
	}
	return nil
}

func (x *Strategy) GetMatch() *MatchStrategy {
	if x != nil {
		if x, ok := x.Strategy.(*Strategy_Match); ok {
			return x.Match
		}
	}
	return nil
}

func (x *Strategy) GetRange() *RangeStrategy {
	if x != nil {
		if x, ok := x.Strategy.(*Strategy_Range); ok {
			return x.Range
		}
	}
	return nil
}

func (x *Strategy) GetWildcard() *WildcardStrategy {
	if x != nil {
		if x, ok := x.Strategy.(*Strategy_Wildcard); ok {
			return x.Wildcard
		}
	}
	return nil
}

func (x *Strategy) GetTerms() *TermsStrategy {
	if x != nil {
		if x, ok := x.Strategy.(*Strategy_Terms); ok {
			return x.Terms
		}
	}
	return nil
}

func (x *Strategy) GetBool() *BoolStrategy {
	if x != nil {
		if x, ok := x.Strategy.(*Strategy_Bool); ok {
			return x.Bool
		}
	}
	return nil
}

func (x *Strategy) GetMultiMatch() *MultiMatchStrategy {
	if x != nil {
		if x, ok := x.Strategy.(*Strategy_MultiMatch); ok {
			return x.MultiMatch
		}
	}
	return nil
}

func (x *Strategy) GetMatchPhrase() *MatchPhraseStrategy {
	if x != nil {
		if x, ok := x.Strategy.(*Strategy_MatchPhrase); ok {
			return x.MatchPhrase
		}
	}
	return nil
}

func (x *Strategy) GetQueryString() *QueryStringStrategy {
	if x != nil {
		if x, ok := x.Strategy.(*Strategy_QueryString); ok {
			return x.QueryString
		}
	}
	return nil
}

type isStrategy_Strategy interface {
	isStrategy_Strategy()
}

type Strategy_Term struct {
	// 精确搜索
	Term *TermStrategy `protobuf:"bytes,1,opt,name=term,proto3,oneof"`
}

type Strategy_Match struct {
	// 模糊匹配
	Match *MatchStrategy `protobuf:"bytes,2,opt,name=match,proto3,oneof"`
}

type Strategy_Range struct {
	// 范围查询
	Range *RangeStrategy `protobuf:"bytes,3,opt,name=range,proto3,oneof"`
}

type Strategy_Wildcard struct {
	// 通配符匹配
	Wildcard *WildcardStrategy `protobuf:"bytes,4,opt,name=wildcard,proto3,oneof"`
}

type Strategy_Terms struct {
	// 多值精确匹配
	Terms *TermsStrategy `protobuf:"bytes,5,opt,name=terms,proto3,oneof"`
}

type Strategy_Bool struct {
	// 多条件匹配
	Bool *BoolStrategy `protobuf:"bytes,6,opt,name=bool,proto3,oneof"`
}

type Strategy_MultiMatch struct {
	// 以下为扩展字段, 用于支持更多搜索策略
	// multi match
	MultiMatch *MultiMatchStrategy `protobuf:"bytes,7,opt,name=multi_match,json=multiMatch,proto3,oneof"`
}

type Strategy_MatchPhrase struct {
	// match phrase
	MatchPhrase *MatchPhraseStrategy `protobuf:"bytes,8,opt,name=match_phrase,json=matchPhrase,proto3,oneof"`
}

type Strategy_QueryString struct {
	// query string
	QueryString *QueryStringStrategy `protobuf:"bytes,9,opt,name=query_string,json=queryString,proto3,oneof"`
}

func (*Strategy_Term) isStrategy_Strategy() {}

func (*Strategy_Match) isStrategy_Strategy() {}

func (*Strategy_Range) isStrategy_Strategy() {}

func (*Strategy_Wildcard) isStrategy_Strategy() {}

func (*Strategy_Terms) isStrategy_Strategy() {}

func (*Strategy_Bool) isStrategy_Strategy() {}

func (*Strategy_MultiMatch) isStrategy_Strategy() {}

func (*Strategy_MatchPhrase) isStrategy_Strategy() {}

func (*Strategy_QueryString) isStrategy_Strategy() {}

// 用于指定至少满足条件的策略
type BoolStrategy_BoolShouldStrategy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 需要满足的条件
	Strategies []*Strategy `protobuf:"bytes,1,rep,name=strategies,proto3" json:"strategies,omitempty"`
	// 至少满足的条件数量
	MinimumMatch  int32 `protobuf:"varint,2,opt,name=minimum_match,json=minimumMatch,proto3" json:"minimum_match,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BoolStrategy_BoolShouldStrategy) Reset() {
	*x = BoolStrategy_BoolShouldStrategy{}
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BoolStrategy_BoolShouldStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BoolStrategy_BoolShouldStrategy) ProtoMessage() {}

func (x *BoolStrategy_BoolShouldStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_search_v1_search_strategy_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BoolStrategy_BoolShouldStrategy.ProtoReflect.Descriptor instead.
func (*BoolStrategy_BoolShouldStrategy) Descriptor() ([]byte, []int) {
	return file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP(), []int{5, 0}
}

func (x *BoolStrategy_BoolShouldStrategy) GetStrategies() []*Strategy {
	if x != nil {
		return x.Strategies
	}
	return nil
}

func (x *BoolStrategy_BoolShouldStrategy) GetMinimumMatch() int32 {
	if x != nil {
		return x.MinimumMatch
	}
	return 0
}

var File_backend_proto_search_v1_search_strategy_proto protoreflect.FileDescriptor

const file_backend_proto_search_v1_search_strategy_proto_rawDesc = "" +
	"\n" +
	"-backend/proto/search/v1/search_strategy.proto\x12\x17backend.proto.search.v1\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/api/field_behavior.proto\"i\n" +
	"\fTermStrategy\x12 \n" +
	"\x05field\x18\x01 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05field\x127\n" +
	"\x05value\x18\x02 \x01(\v2\x16.google.protobuf.ValueB\t\xe0A\x02\xbaH\x03\xc8\x01\x01R\x05value\"\x87\x02\n" +
	"\rMatchStrategy\x12 \n" +
	"\x05field\x18\x01 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05field\x12 \n" +
	"\x05query\x18\x02 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05query\x12\x19\n" +
	"\x05boost\x18\x03 \x01(\x02B\x03\xe0A\x01R\x05boost\x12U\n" +
	"\boperator\x18\x04 \x01(\x0e24.backend.proto.search.v1.MatchStrategy.MatchOperatorB\x03\xe0A\x02R\boperator\"@\n" +
	"\rMatchOperator\x12\x1e\n" +
	"\x1aMATCH_OPERATOR_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03AND\x10\x01\x12\x06\n" +
	"\x02OR\x10\x02\"\x8f\x01\n" +
	"\rRangeStrategy\x12 \n" +
	"\x05field\x18\x01 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05field\x12-\n" +
	"\x03gte\x18\x02 \x01(\v2\x16.google.protobuf.ValueB\x03\xe0A\x01R\x03gte\x12-\n" +
	"\x03lte\x18\x03 \x01(\v2\x16.google.protobuf.ValueB\x03\xe0A\x01R\x03lte\"V\n" +
	"\x10WildcardStrategy\x12 \n" +
	"\x05field\x18\x01 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05field\x12 \n" +
	"\x05value\x18\x02 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05value\"n\n" +
	"\rTermsStrategy\x12 \n" +
	"\x05field\x18\x01 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05field\x12;\n" +
	"\x06values\x18\x02 \x03(\v2\x16.google.protobuf.ValueB\v\xe0A\x02\xbaH\x05\x92\x01\x02\b\x01R\x06values\"\xad\x03\n" +
	"\fBoolStrategy\x12:\n" +
	"\x04must\x18\x01 \x03(\v2!.backend.proto.search.v1.StrategyB\x03\xe0A\x01R\x04must\x12A\n" +
	"\bmust_not\x18\x02 \x03(\v2!.backend.proto.search.v1.StrategyB\x03\xe0A\x01R\amustNot\x12>\n" +
	"\x06filter\x18\x03 \x03(\v2!.backend.proto.search.v1.StrategyB\x03\xe0A\x01R\x06filter\x12U\n" +
	"\x06should\x18\x04 \x01(\v28.backend.proto.search.v1.BoolStrategy.BoolShouldStrategyB\x03\xe0A\x01R\x06should\x1a\x86\x01\n" +
	"\x12BoolShouldStrategy\x12F\n" +
	"\n" +
	"strategies\x18\x01 \x03(\v2!.backend.proto.search.v1.StrategyB\x03\xe0A\x02R\n" +
	"strategies\x12(\n" +
	"\rminimum_match\x18\x02 \x01(\x05B\x03\xe0A\x02R\fminimumMatch\"\x8f\x03\n" +
	"\x12MultiMatchStrategy\x12 \n" +
	"\x05query\x18\x01 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05query\x12N\n" +
	"\x04type\x18\x02 \x01(\x0e25.backend.proto.search.v1.MultiMatchStrategy.MatchTypeB\x03\xe0A\x02R\x04type\x12#\n" +
	"\x06fields\x18\x03 \x03(\tB\v\xe0A\x02\xbaH\x05\x92\x01\x02\b\x01R\x06fields\x12!\n" +
	"\tfuzziness\x18\x04 \x01(\tB\x03\xe0A\x01R\tfuzziness\x12(\n" +
	"\rprefix_length\x18\x05 \x01(\x05B\x03\xe0A\x01R\fprefixLength\x12\x19\n" +
	"\x05boost\x18\x06 \x01(\x02B\x03\xe0A\x01R\x05boost\"z\n" +
	"\tMatchType\x12\x1a\n" +
	"\x16MATCH_TYPE_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vBEST_FIELDS\x10\x01\x12\x0f\n" +
	"\vMOST_FIELDS\x10\x02\x12\x10\n" +
	"\fCROSS_FIELDS\x10\x03\x12\n" +
	"\n" +
	"\x06PHRASE\x10\x04\x12\x11\n" +
	"\rPHRASE_PREFIX\x10\x05\"\xd3\x02\n" +
	"\x13QueryStringStrategy\x12 \n" +
	"\x05query\x18\x01 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05query\x12\x1b\n" +
	"\x06fields\x18\x02 \x03(\tB\x03\xe0A\x01R\x06fields\x12.\n" +
	"\x10analyze_wildcard\x18\x03 \x01(\bB\x03\xe0A\x01R\x0fanalyzeWildcard\x12l\n" +
	"\x10default_operator\x18\x04 \x01(\x0e2<.backend.proto.search.v1.QueryStringStrategy.DefaultOperatorB\x03\xe0A\x01R\x0fdefaultOperator\x12\x19\n" +
	"\x05boost\x18\x05 \x01(\x02B\x03\xe0A\x01R\x05boost\"D\n" +
	"\x0fDefaultOperator\x12 \n" +
	"\x1cDEFAULT_OPERATOR_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03AND\x10\x01\x12\x06\n" +
	"\x02OR\x10\x02\"\x8d\x01\n" +
	"\x13MatchPhraseStrategy\x12 \n" +
	"\x05field\x18\x01 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05field\x12 \n" +
	"\x05query\x18\x02 \x01(\tB\n" +
	"\xe0A\x02\xbaH\x04r\x02\x10\x01R\x05query\x12\x17\n" +
	"\x04slop\x18\x03 \x01(\x05B\x03\xe0A\x01R\x04slop\x12\x19\n" +
	"\x05boost\x18\x04 \x01(\x02B\x03\xe0A\x01R\x05boost\"\x8f\x05\n" +
	"\bStrategy\x12;\n" +
	"\x04term\x18\x01 \x01(\v2%.backend.proto.search.v1.TermStrategyH\x00R\x04term\x12>\n" +
	"\x05match\x18\x02 \x01(\v2&.backend.proto.search.v1.MatchStrategyH\x00R\x05match\x12>\n" +
	"\x05range\x18\x03 \x01(\v2&.backend.proto.search.v1.RangeStrategyH\x00R\x05range\x12G\n" +
	"\bwildcard\x18\x04 \x01(\v2).backend.proto.search.v1.WildcardStrategyH\x00R\bwildcard\x12>\n" +
	"\x05terms\x18\x05 \x01(\v2&.backend.proto.search.v1.TermsStrategyH\x00R\x05terms\x12;\n" +
	"\x04bool\x18\x06 \x01(\v2%.backend.proto.search.v1.BoolStrategyH\x00R\x04bool\x12N\n" +
	"\vmulti_match\x18\a \x01(\v2+.backend.proto.search.v1.MultiMatchStrategyH\x00R\n" +
	"multiMatch\x12Q\n" +
	"\fmatch_phrase\x18\b \x01(\v2,.backend.proto.search.v1.MatchPhraseStrategyH\x00R\vmatchPhrase\x12Q\n" +
	"\fquery_string\x18\t \x01(\v2,.backend.proto.search.v1.QueryStringStrategyH\x00R\vqueryStringB\n" +
	"\n" +
	"\bstrategyBe\n" +
	"!com.moego.backend.proto.search.v1P\x01Z>github.com/MoeGolibrary/moego/backend/proto/search/v1;searchpbb\x06proto3"

var (
	file_backend_proto_search_v1_search_strategy_proto_rawDescOnce sync.Once
	file_backend_proto_search_v1_search_strategy_proto_rawDescData []byte
)

func file_backend_proto_search_v1_search_strategy_proto_rawDescGZIP() []byte {
	file_backend_proto_search_v1_search_strategy_proto_rawDescOnce.Do(func() {
		file_backend_proto_search_v1_search_strategy_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_search_v1_search_strategy_proto_rawDesc), len(file_backend_proto_search_v1_search_strategy_proto_rawDesc)))
	})
	return file_backend_proto_search_v1_search_strategy_proto_rawDescData
}

var file_backend_proto_search_v1_search_strategy_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_backend_proto_search_v1_search_strategy_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_backend_proto_search_v1_search_strategy_proto_goTypes = []any{
	(MatchStrategy_MatchOperator)(0),         // 0: backend.proto.search.v1.MatchStrategy.MatchOperator
	(MultiMatchStrategy_MatchType)(0),        // 1: backend.proto.search.v1.MultiMatchStrategy.MatchType
	(QueryStringStrategy_DefaultOperator)(0), // 2: backend.proto.search.v1.QueryStringStrategy.DefaultOperator
	(*TermStrategy)(nil),                     // 3: backend.proto.search.v1.TermStrategy
	(*MatchStrategy)(nil),                    // 4: backend.proto.search.v1.MatchStrategy
	(*RangeStrategy)(nil),                    // 5: backend.proto.search.v1.RangeStrategy
	(*WildcardStrategy)(nil),                 // 6: backend.proto.search.v1.WildcardStrategy
	(*TermsStrategy)(nil),                    // 7: backend.proto.search.v1.TermsStrategy
	(*BoolStrategy)(nil),                     // 8: backend.proto.search.v1.BoolStrategy
	(*MultiMatchStrategy)(nil),               // 9: backend.proto.search.v1.MultiMatchStrategy
	(*QueryStringStrategy)(nil),              // 10: backend.proto.search.v1.QueryStringStrategy
	(*MatchPhraseStrategy)(nil),              // 11: backend.proto.search.v1.MatchPhraseStrategy
	(*Strategy)(nil),                         // 12: backend.proto.search.v1.Strategy
	(*BoolStrategy_BoolShouldStrategy)(nil),  // 13: backend.proto.search.v1.BoolStrategy.BoolShouldStrategy
	(*structpb.Value)(nil),                   // 14: google.protobuf.Value
}
var file_backend_proto_search_v1_search_strategy_proto_depIdxs = []int32{
	14, // 0: backend.proto.search.v1.TermStrategy.value:type_name -> google.protobuf.Value
	0,  // 1: backend.proto.search.v1.MatchStrategy.operator:type_name -> backend.proto.search.v1.MatchStrategy.MatchOperator
	14, // 2: backend.proto.search.v1.RangeStrategy.gte:type_name -> google.protobuf.Value
	14, // 3: backend.proto.search.v1.RangeStrategy.lte:type_name -> google.protobuf.Value
	14, // 4: backend.proto.search.v1.TermsStrategy.values:type_name -> google.protobuf.Value
	12, // 5: backend.proto.search.v1.BoolStrategy.must:type_name -> backend.proto.search.v1.Strategy
	12, // 6: backend.proto.search.v1.BoolStrategy.must_not:type_name -> backend.proto.search.v1.Strategy
	12, // 7: backend.proto.search.v1.BoolStrategy.filter:type_name -> backend.proto.search.v1.Strategy
	13, // 8: backend.proto.search.v1.BoolStrategy.should:type_name -> backend.proto.search.v1.BoolStrategy.BoolShouldStrategy
	1,  // 9: backend.proto.search.v1.MultiMatchStrategy.type:type_name -> backend.proto.search.v1.MultiMatchStrategy.MatchType
	2,  // 10: backend.proto.search.v1.QueryStringStrategy.default_operator:type_name -> backend.proto.search.v1.QueryStringStrategy.DefaultOperator
	3,  // 11: backend.proto.search.v1.Strategy.term:type_name -> backend.proto.search.v1.TermStrategy
	4,  // 12: backend.proto.search.v1.Strategy.match:type_name -> backend.proto.search.v1.MatchStrategy
	5,  // 13: backend.proto.search.v1.Strategy.range:type_name -> backend.proto.search.v1.RangeStrategy
	6,  // 14: backend.proto.search.v1.Strategy.wildcard:type_name -> backend.proto.search.v1.WildcardStrategy
	7,  // 15: backend.proto.search.v1.Strategy.terms:type_name -> backend.proto.search.v1.TermsStrategy
	8,  // 16: backend.proto.search.v1.Strategy.bool:type_name -> backend.proto.search.v1.BoolStrategy
	9,  // 17: backend.proto.search.v1.Strategy.multi_match:type_name -> backend.proto.search.v1.MultiMatchStrategy
	11, // 18: backend.proto.search.v1.Strategy.match_phrase:type_name -> backend.proto.search.v1.MatchPhraseStrategy
	10, // 19: backend.proto.search.v1.Strategy.query_string:type_name -> backend.proto.search.v1.QueryStringStrategy
	12, // 20: backend.proto.search.v1.BoolStrategy.BoolShouldStrategy.strategies:type_name -> backend.proto.search.v1.Strategy
	21, // [21:21] is the sub-list for method output_type
	21, // [21:21] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_backend_proto_search_v1_search_strategy_proto_init() }
func file_backend_proto_search_v1_search_strategy_proto_init() {
	if File_backend_proto_search_v1_search_strategy_proto != nil {
		return
	}
	file_backend_proto_search_v1_search_strategy_proto_msgTypes[9].OneofWrappers = []any{
		(*Strategy_Term)(nil),
		(*Strategy_Match)(nil),
		(*Strategy_Range)(nil),
		(*Strategy_Wildcard)(nil),
		(*Strategy_Terms)(nil),
		(*Strategy_Bool)(nil),
		(*Strategy_MultiMatch)(nil),
		(*Strategy_MatchPhrase)(nil),
		(*Strategy_QueryString)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_search_v1_search_strategy_proto_rawDesc), len(file_backend_proto_search_v1_search_strategy_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_search_v1_search_strategy_proto_goTypes,
		DependencyIndexes: file_backend_proto_search_v1_search_strategy_proto_depIdxs,
		EnumInfos:         file_backend_proto_search_v1_search_strategy_proto_enumTypes,
		MessageInfos:      file_backend_proto_search_v1_search_strategy_proto_msgTypes,
	}.Build()
	File_backend_proto_search_v1_search_strategy_proto = out.File
	file_backend_proto_search_v1_search_strategy_proto_goTypes = nil
	file_backend_proto_search_v1_search_strategy_proto_depIdxs = nil
}
