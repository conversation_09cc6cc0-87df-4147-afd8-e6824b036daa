// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/sales/v1/moego_pay_contract_service.proto

package salespb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MoegoPayContractService_CreateMoegoPayContract_FullMethodName = "/backend.proto.sales.v1.MoegoPayContractService/CreateMoegoPayContract"
	MoegoPayContractService_GetMoegoPayContract_FullMethodName    = "/backend.proto.sales.v1.MoegoPayContractService/GetMoegoPayContract"
	MoegoPayContractService_ListMoegoPayContracts_FullMethodName  = "/backend.proto.sales.v1.MoegoPayContractService/ListMoegoPayContracts"
	MoegoPayContractService_CountMoegoPayContracts_FullMethodName = "/backend.proto.sales.v1.MoegoPayContractService/CountMoegoPayContracts"
	MoegoPayContractService_SignMoegoPayContract_FullMethodName   = "/backend.proto.sales.v1.MoegoPayContractService/SignMoegoPayContract"
	MoegoPayContractService_DeleteMoegoPayContract_FullMethodName = "/backend.proto.sales.v1.MoegoPayContractService/DeleteMoegoPayContract"
)

// MoegoPayContractServiceClient is the client API for MoegoPayContractService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// MoegoPayContractService
type MoegoPayContractServiceClient interface {
	// CreateMoegoPayContract
	CreateMoegoPayContract(ctx context.Context, in *CreateMoegoPayContractRequest, opts ...grpc.CallOption) (*MoegoPayContract, error)
	// GetMoegoPayContract
	GetMoegoPayContract(ctx context.Context, in *GetMoegoPayContractRequest, opts ...grpc.CallOption) (*MoegoPayContract, error)
	// ListMoegoPayContracts
	ListMoegoPayContracts(ctx context.Context, in *ListMoegoPayContractsRequest, opts ...grpc.CallOption) (*ListMoegoPayContractsResponse, error)
	// CountMoegoPayContracts
	CountMoegoPayContracts(ctx context.Context, in *CountMoegoPayContractsRequest, opts ...grpc.CallOption) (*CountMoegoPayContractsResponse, error)
	// SignMoegoPayContract
	SignMoegoPayContract(ctx context.Context, in *SignMoegoPayContractRequest, opts ...grpc.CallOption) (*SignMoegoPayContractResponse, error)
	// DeleteMoegoPayContract
	DeleteMoegoPayContract(ctx context.Context, in *DeleteMoegoPayContractRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type moegoPayContractServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMoegoPayContractServiceClient(cc grpc.ClientConnInterface) MoegoPayContractServiceClient {
	return &moegoPayContractServiceClient{cc}
}

func (c *moegoPayContractServiceClient) CreateMoegoPayContract(ctx context.Context, in *CreateMoegoPayContractRequest, opts ...grpc.CallOption) (*MoegoPayContract, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MoegoPayContract)
	err := c.cc.Invoke(ctx, MoegoPayContractService_CreateMoegoPayContract_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayContractServiceClient) GetMoegoPayContract(ctx context.Context, in *GetMoegoPayContractRequest, opts ...grpc.CallOption) (*MoegoPayContract, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MoegoPayContract)
	err := c.cc.Invoke(ctx, MoegoPayContractService_GetMoegoPayContract_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayContractServiceClient) ListMoegoPayContracts(ctx context.Context, in *ListMoegoPayContractsRequest, opts ...grpc.CallOption) (*ListMoegoPayContractsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListMoegoPayContractsResponse)
	err := c.cc.Invoke(ctx, MoegoPayContractService_ListMoegoPayContracts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayContractServiceClient) CountMoegoPayContracts(ctx context.Context, in *CountMoegoPayContractsRequest, opts ...grpc.CallOption) (*CountMoegoPayContractsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountMoegoPayContractsResponse)
	err := c.cc.Invoke(ctx, MoegoPayContractService_CountMoegoPayContracts_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayContractServiceClient) SignMoegoPayContract(ctx context.Context, in *SignMoegoPayContractRequest, opts ...grpc.CallOption) (*SignMoegoPayContractResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SignMoegoPayContractResponse)
	err := c.cc.Invoke(ctx, MoegoPayContractService_SignMoegoPayContract_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *moegoPayContractServiceClient) DeleteMoegoPayContract(ctx context.Context, in *DeleteMoegoPayContractRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MoegoPayContractService_DeleteMoegoPayContract_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MoegoPayContractServiceServer is the server API for MoegoPayContractService service.
// All implementations must embed UnimplementedMoegoPayContractServiceServer
// for forward compatibility.
//
// MoegoPayContractService
type MoegoPayContractServiceServer interface {
	// CreateMoegoPayContract
	CreateMoegoPayContract(context.Context, *CreateMoegoPayContractRequest) (*MoegoPayContract, error)
	// GetMoegoPayContract
	GetMoegoPayContract(context.Context, *GetMoegoPayContractRequest) (*MoegoPayContract, error)
	// ListMoegoPayContracts
	ListMoegoPayContracts(context.Context, *ListMoegoPayContractsRequest) (*ListMoegoPayContractsResponse, error)
	// CountMoegoPayContracts
	CountMoegoPayContracts(context.Context, *CountMoegoPayContractsRequest) (*CountMoegoPayContractsResponse, error)
	// SignMoegoPayContract
	SignMoegoPayContract(context.Context, *SignMoegoPayContractRequest) (*SignMoegoPayContractResponse, error)
	// DeleteMoegoPayContract
	DeleteMoegoPayContract(context.Context, *DeleteMoegoPayContractRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedMoegoPayContractServiceServer()
}

// UnimplementedMoegoPayContractServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMoegoPayContractServiceServer struct{}

func (UnimplementedMoegoPayContractServiceServer) CreateMoegoPayContract(context.Context, *CreateMoegoPayContractRequest) (*MoegoPayContract, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateMoegoPayContract not implemented")
}
func (UnimplementedMoegoPayContractServiceServer) GetMoegoPayContract(context.Context, *GetMoegoPayContractRequest) (*MoegoPayContract, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMoegoPayContract not implemented")
}
func (UnimplementedMoegoPayContractServiceServer) ListMoegoPayContracts(context.Context, *ListMoegoPayContractsRequest) (*ListMoegoPayContractsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMoegoPayContracts not implemented")
}
func (UnimplementedMoegoPayContractServiceServer) CountMoegoPayContracts(context.Context, *CountMoegoPayContractsRequest) (*CountMoegoPayContractsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountMoegoPayContracts not implemented")
}
func (UnimplementedMoegoPayContractServiceServer) SignMoegoPayContract(context.Context, *SignMoegoPayContractRequest) (*SignMoegoPayContractResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SignMoegoPayContract not implemented")
}
func (UnimplementedMoegoPayContractServiceServer) DeleteMoegoPayContract(context.Context, *DeleteMoegoPayContractRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteMoegoPayContract not implemented")
}
func (UnimplementedMoegoPayContractServiceServer) mustEmbedUnimplementedMoegoPayContractServiceServer() {
}
func (UnimplementedMoegoPayContractServiceServer) testEmbeddedByValue() {}

// UnsafeMoegoPayContractServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MoegoPayContractServiceServer will
// result in compilation errors.
type UnsafeMoegoPayContractServiceServer interface {
	mustEmbedUnimplementedMoegoPayContractServiceServer()
}

func RegisterMoegoPayContractServiceServer(s grpc.ServiceRegistrar, srv MoegoPayContractServiceServer) {
	// If the following call pancis, it indicates UnimplementedMoegoPayContractServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MoegoPayContractService_ServiceDesc, srv)
}

func _MoegoPayContractService_CreateMoegoPayContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateMoegoPayContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayContractServiceServer).CreateMoegoPayContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayContractService_CreateMoegoPayContract_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayContractServiceServer).CreateMoegoPayContract(ctx, req.(*CreateMoegoPayContractRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayContractService_GetMoegoPayContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMoegoPayContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayContractServiceServer).GetMoegoPayContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayContractService_GetMoegoPayContract_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayContractServiceServer).GetMoegoPayContract(ctx, req.(*GetMoegoPayContractRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayContractService_ListMoegoPayContracts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMoegoPayContractsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayContractServiceServer).ListMoegoPayContracts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayContractService_ListMoegoPayContracts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayContractServiceServer).ListMoegoPayContracts(ctx, req.(*ListMoegoPayContractsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayContractService_CountMoegoPayContracts_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountMoegoPayContractsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayContractServiceServer).CountMoegoPayContracts(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayContractService_CountMoegoPayContracts_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayContractServiceServer).CountMoegoPayContracts(ctx, req.(*CountMoegoPayContractsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayContractService_SignMoegoPayContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SignMoegoPayContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayContractServiceServer).SignMoegoPayContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayContractService_SignMoegoPayContract_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayContractServiceServer).SignMoegoPayContract(ctx, req.(*SignMoegoPayContractRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MoegoPayContractService_DeleteMoegoPayContract_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteMoegoPayContractRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MoegoPayContractServiceServer).DeleteMoegoPayContract(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MoegoPayContractService_DeleteMoegoPayContract_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MoegoPayContractServiceServer).DeleteMoegoPayContract(ctx, req.(*DeleteMoegoPayContractRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MoegoPayContractService_ServiceDesc is the grpc.ServiceDesc for MoegoPayContractService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MoegoPayContractService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.sales.v1.MoegoPayContractService",
	HandlerType: (*MoegoPayContractServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateMoegoPayContract",
			Handler:    _MoegoPayContractService_CreateMoegoPayContract_Handler,
		},
		{
			MethodName: "GetMoegoPayContract",
			Handler:    _MoegoPayContractService_GetMoegoPayContract_Handler,
		},
		{
			MethodName: "ListMoegoPayContracts",
			Handler:    _MoegoPayContractService_ListMoegoPayContracts_Handler,
		},
		{
			MethodName: "CountMoegoPayContracts",
			Handler:    _MoegoPayContractService_CountMoegoPayContracts_Handler,
		},
		{
			MethodName: "SignMoegoPayContract",
			Handler:    _MoegoPayContractService_SignMoegoPayContract_Handler,
		},
		{
			MethodName: "DeleteMoegoPayContract",
			Handler:    _MoegoPayContractService_DeleteMoegoPayContract_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/sales/v1/moego_pay_contract_service.proto",
}
