// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/sales/v1/annual_contract_service.proto

package salespb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: We need to do this because 没必要嵌套一层，不好用. --)
//
// CreateAnnualContractRequest
type CreateAnnualContractRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// creator
	Creator string `protobuf:"bytes,1,opt,name=creator,proto3" json:"creator,omitempty"`
	// company id
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// subscription plan
	SubscriptionPlan SubscriptionPlan `protobuf:"varint,3,opt,name=subscription_plan,json=subscriptionPlan,proto3,enum=backend.proto.sales.v1.SubscriptionPlan" json:"subscription_plan,omitempty"`
	// subscription term months
	// e.g., 12 means 12 months (1 year)
	SubscriptionTermMonths int32 `protobuf:"varint,4,opt,name=subscription_term_months,json=subscriptionTermMonths,proto3" json:"subscription_term_months,omitempty"`
	// discount percentage of subscription
	// e.g., "10" means 10%
	DiscountPercentage string `protobuf:"bytes,5,opt,name=discount_percentage,json=discountPercentage,proto3" json:"discount_percentage,omitempty"`
	// number of boarding & daycare locations
	BdLocationCount int32 `protobuf:"varint,6,opt,name=bd_location_count,json=bdLocationCount,proto3" json:"bd_location_count,omitempty"`
	// number of grooming locations
	GroomingLocationCount int32 `protobuf:"varint,7,opt,name=grooming_location_count,json=groomingLocationCount,proto3" json:"grooming_location_count,omitempty"`
	// number of grooming vans
	GroomingVanCount int32 `protobuf:"varint,8,opt,name=grooming_van_count,json=groomingVanCount,proto3" json:"grooming_van_count,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CreateAnnualContractRequest) Reset() {
	*x = CreateAnnualContractRequest{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAnnualContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAnnualContractRequest) ProtoMessage() {}

func (x *CreateAnnualContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAnnualContractRequest.ProtoReflect.Descriptor instead.
func (*CreateAnnualContractRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateAnnualContractRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *CreateAnnualContractRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateAnnualContractRequest) GetSubscriptionPlan() SubscriptionPlan {
	if x != nil {
		return x.SubscriptionPlan
	}
	return SubscriptionPlan_SUBSCRIPTION_PLAN_UNSPECIFIED
}

func (x *CreateAnnualContractRequest) GetSubscriptionTermMonths() int32 {
	if x != nil {
		return x.SubscriptionTermMonths
	}
	return 0
}

func (x *CreateAnnualContractRequest) GetDiscountPercentage() string {
	if x != nil {
		return x.DiscountPercentage
	}
	return ""
}

func (x *CreateAnnualContractRequest) GetBdLocationCount() int32 {
	if x != nil {
		return x.BdLocationCount
	}
	return 0
}

func (x *CreateAnnualContractRequest) GetGroomingLocationCount() int32 {
	if x != nil {
		return x.GroomingLocationCount
	}
	return 0
}

func (x *CreateAnnualContractRequest) GetGroomingVanCount() int32 {
	if x != nil {
		return x.GroomingVanCount
	}
	return 0
}

// GetAnnualContractRequest
type GetAnnualContractRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAnnualContractRequest) Reset() {
	*x = GetAnnualContractRequest{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAnnualContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAnnualContractRequest) ProtoMessage() {}

func (x *GetAnnualContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAnnualContractRequest.ProtoReflect.Descriptor instead.
func (*GetAnnualContractRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetAnnualContractRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: We need to do this because we don't use parent. --)
//
// ListAnnualContractsRequest
type ListAnnualContractsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 每页返回条数，最大不超过 100，默认建议为 20
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 上一页返回的分页 token（用于获取下一页）
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// filters
	Filters       *AnnualContractQueryFilters `protobuf:"bytes,3,opt,name=filters,proto3" json:"filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAnnualContractsRequest) Reset() {
	*x = ListAnnualContractsRequest{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAnnualContractsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnnualContractsRequest) ProtoMessage() {}

func (x *ListAnnualContractsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnnualContractsRequest.ProtoReflect.Descriptor instead.
func (*ListAnnualContractsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListAnnualContractsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListAnnualContractsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListAnnualContractsRequest) GetFilters() *AnnualContractQueryFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

// ListAnnualContractsResponse
type ListAnnualContractsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 合同列表
	AnnualContracts []*AnnualContract `protobuf:"bytes,1,rep,name=annual_contracts,json=annualContracts,proto3" json:"annual_contracts,omitempty"`
	// 下一页的分页 token，如果为空表示无更多结果
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAnnualContractsResponse) Reset() {
	*x = ListAnnualContractsResponse{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAnnualContractsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAnnualContractsResponse) ProtoMessage() {}

func (x *ListAnnualContractsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAnnualContractsResponse.ProtoReflect.Descriptor instead.
func (*ListAnnualContractsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListAnnualContractsResponse) GetAnnualContracts() []*AnnualContract {
	if x != nil {
		return x.AnnualContracts
	}
	return nil
}

func (x *ListAnnualContractsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// CountAnnualContractsRequest
type CountAnnualContractsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filters
	Filters       *AnnualContractQueryFilters `protobuf:"bytes,1,opt,name=filters,proto3" json:"filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountAnnualContractsRequest) Reset() {
	*x = CountAnnualContractsRequest{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountAnnualContractsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountAnnualContractsRequest) ProtoMessage() {}

func (x *CountAnnualContractsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountAnnualContractsRequest.ProtoReflect.Descriptor instead.
func (*CountAnnualContractsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{4}
}

func (x *CountAnnualContractsRequest) GetFilters() *AnnualContractQueryFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

// CountAnnualContractsResponse
type CountAnnualContractsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// count
	Count         int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountAnnualContractsResponse) Reset() {
	*x = CountAnnualContractsResponse{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountAnnualContractsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountAnnualContractsResponse) ProtoMessage() {}

func (x *CountAnnualContractsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountAnnualContractsResponse.ProtoReflect.Descriptor instead.
func (*CountAnnualContractsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{5}
}

func (x *CountAnnualContractsResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// SignAnnualContractRequest
type SignAnnualContractRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// signature uri (url)
	SignatureUri  string `protobuf:"bytes,2,opt,name=signature_uri,json=signatureUri,proto3" json:"signature_uri,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignAnnualContractRequest) Reset() {
	*x = SignAnnualContractRequest{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignAnnualContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignAnnualContractRequest) ProtoMessage() {}

func (x *SignAnnualContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignAnnualContractRequest.ProtoReflect.Descriptor instead.
func (*SignAnnualContractRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{6}
}

func (x *SignAnnualContractRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SignAnnualContractRequest) GetSignatureUri() string {
	if x != nil {
		return x.SignatureUri
	}
	return ""
}

// SignAnnualContractResponse
type SignAnnualContractResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// contract
	Contract      *AnnualContract `protobuf:"bytes,1,opt,name=contract,proto3" json:"contract,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignAnnualContractResponse) Reset() {
	*x = SignAnnualContractResponse{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignAnnualContractResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignAnnualContractResponse) ProtoMessage() {}

func (x *SignAnnualContractResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignAnnualContractResponse.ProtoReflect.Descriptor instead.
func (*SignAnnualContractResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{7}
}

func (x *SignAnnualContractResponse) GetContract() *AnnualContract {
	if x != nil {
		return x.Contract
	}
	return nil
}

// DeleteAnnualContractRequest
type DeleteAnnualContractRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAnnualContractRequest) Reset() {
	*x = DeleteAnnualContractRequest{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAnnualContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAnnualContractRequest) ProtoMessage() {}

func (x *DeleteAnnualContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAnnualContractRequest.ProtoReflect.Descriptor instead.
func (*DeleteAnnualContractRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteAnnualContractRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// AnnualContract
type AnnualContract struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// template id
	TemplateId string `protobuf:"bytes,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// metadata
	Metadata *AnnualContract_Metadata `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// params
	Parameters *AnnualContract_Parameters `protobuf:"bytes,4,opt,name=parameters,proto3" json:"parameters,omitempty"`
	// content
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	// creator
	Creator string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// sign time
	SignTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=sign_time,json=signTime,proto3,oneof" json:"sign_time,omitempty"`
	// signature uri (url)
	SignatureUri  *string `protobuf:"bytes,10,opt,name=signature_uri,json=signatureUri,proto3,oneof" json:"signature_uri,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnnualContract) Reset() {
	*x = AnnualContract{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnualContract) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualContract) ProtoMessage() {}

func (x *AnnualContract) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualContract.ProtoReflect.Descriptor instead.
func (*AnnualContract) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{9}
}

func (x *AnnualContract) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AnnualContract) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *AnnualContract) GetMetadata() *AnnualContract_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *AnnualContract) GetParameters() *AnnualContract_Parameters {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *AnnualContract) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *AnnualContract) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *AnnualContract) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AnnualContract) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *AnnualContract) GetSignTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SignTime
	}
	return nil
}

func (x *AnnualContract) GetSignatureUri() string {
	if x != nil && x.SignatureUri != nil {
		return *x.SignatureUri
	}
	return ""
}

// Query filters for AnnualContract
type AnnualContractQueryFilters struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company_id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// account_id
	AccountId *int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// owner email (prefix like)
	OwnerEmail *string `protobuf:"bytes,3,opt,name=owner_email,json=ownerEmail,proto3,oneof" json:"owner_email,omitempty"`
	// creator (prefix like)
	Creator *string `protobuf:"bytes,4,opt,name=creator,proto3,oneof" json:"creator,omitempty"`
	// if the contract is signed
	Signed        *bool `protobuf:"varint,5,opt,name=signed,proto3,oneof" json:"signed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnnualContractQueryFilters) Reset() {
	*x = AnnualContractQueryFilters{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnualContractQueryFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualContractQueryFilters) ProtoMessage() {}

func (x *AnnualContractQueryFilters) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualContractQueryFilters.ProtoReflect.Descriptor instead.
func (*AnnualContractQueryFilters) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{10}
}

func (x *AnnualContractQueryFilters) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *AnnualContractQueryFilters) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *AnnualContractQueryFilters) GetOwnerEmail() string {
	if x != nil && x.OwnerEmail != nil {
		return *x.OwnerEmail
	}
	return ""
}

func (x *AnnualContractQueryFilters) GetCreator() string {
	if x != nil && x.Creator != nil {
		return *x.Creator
	}
	return ""
}

func (x *AnnualContractQueryFilters) GetSigned() bool {
	if x != nil && x.Signed != nil {
		return *x.Signed
	}
	return false
}

// metadata
type AnnualContract_Metadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// account id
	AccountId int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// subscription plan
	SubscriptionPlan SubscriptionPlan `protobuf:"varint,4,opt,name=subscription_plan,json=subscriptionPlan,proto3,enum=backend.proto.sales.v1.SubscriptionPlan" json:"subscription_plan,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *AnnualContract_Metadata) Reset() {
	*x = AnnualContract_Metadata{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnualContract_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualContract_Metadata) ProtoMessage() {}

func (x *AnnualContract_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualContract_Metadata.ProtoReflect.Descriptor instead.
func (*AnnualContract_Metadata) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *AnnualContract_Metadata) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *AnnualContract_Metadata) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

func (x *AnnualContract_Metadata) GetSubscriptionPlan() SubscriptionPlan {
	if x != nil {
		return x.SubscriptionPlan
	}
	return SubscriptionPlan_SUBSCRIPTION_PLAN_UNSPECIFIED
}

// parameters for rendering
type AnnualContract_Parameters struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
	//
	// company name
	CompanyName string `protobuf:"bytes,1,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
	//
	// owner name
	OwnerName string `protobuf:"bytes,2,opt,name=owner_name,json=ownerName,proto3" json:"owner_name,omitempty"`
	// owner email
	OwnerEmail string `protobuf:"bytes,3,opt,name=owner_email,json=ownerEmail,proto3" json:"owner_email,omitempty"`
	// address
	Address string `protobuf:"bytes,4,opt,name=address,proto3" json:"address,omitempty"`
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
	//
	// subscription plan name
	SubscriptionPlanName string `protobuf:"bytes,5,opt,name=subscription_plan_name,json=subscriptionPlanName,proto3" json:"subscription_plan_name,omitempty"`
	// subscription term months
	SubscriptionTermMonths int32 `protobuf:"varint,6,opt,name=subscription_term_months,json=subscriptionTermMonths,proto3" json:"subscription_term_months,omitempty"`
	// discount percentage
	DiscountPercentage string `protobuf:"bytes,7,opt,name=discount_percentage,json=discountPercentage,proto3" json:"discount_percentage,omitempty"`
	// total amount
	TotalAmount string `protobuf:"bytes,8,opt,name=total_amount,json=totalAmount,proto3" json:"total_amount,omitempty"`
	// grooming location
	GroomingLocation *AnnualContract_ProductLineItem `protobuf:"bytes,9,opt,name=grooming_location,json=groomingLocation,proto3,oneof" json:"grooming_location,omitempty"`
	// boarding & daycare location
	BdLocation *AnnualContract_ProductLineItem `protobuf:"bytes,10,opt,name=bd_location,json=bdLocation,proto3,oneof" json:"bd_location,omitempty"`
	// grooming van
	GroomingVan   *AnnualContract_ProductLineItem `protobuf:"bytes,11,opt,name=grooming_van,json=groomingVan,proto3,oneof" json:"grooming_van,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnnualContract_Parameters) Reset() {
	*x = AnnualContract_Parameters{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnualContract_Parameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualContract_Parameters) ProtoMessage() {}

func (x *AnnualContract_Parameters) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualContract_Parameters.ProtoReflect.Descriptor instead.
func (*AnnualContract_Parameters) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{9, 1}
}

func (x *AnnualContract_Parameters) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *AnnualContract_Parameters) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *AnnualContract_Parameters) GetOwnerEmail() string {
	if x != nil {
		return x.OwnerEmail
	}
	return ""
}

func (x *AnnualContract_Parameters) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *AnnualContract_Parameters) GetSubscriptionPlanName() string {
	if x != nil {
		return x.SubscriptionPlanName
	}
	return ""
}

func (x *AnnualContract_Parameters) GetSubscriptionTermMonths() int32 {
	if x != nil {
		return x.SubscriptionTermMonths
	}
	return 0
}

func (x *AnnualContract_Parameters) GetDiscountPercentage() string {
	if x != nil {
		return x.DiscountPercentage
	}
	return ""
}

func (x *AnnualContract_Parameters) GetTotalAmount() string {
	if x != nil {
		return x.TotalAmount
	}
	return ""
}

func (x *AnnualContract_Parameters) GetGroomingLocation() *AnnualContract_ProductLineItem {
	if x != nil {
		return x.GroomingLocation
	}
	return nil
}

func (x *AnnualContract_Parameters) GetBdLocation() *AnnualContract_ProductLineItem {
	if x != nil {
		return x.BdLocation
	}
	return nil
}

func (x *AnnualContract_Parameters) GetGroomingVan() *AnnualContract_ProductLineItem {
	if x != nil {
		return x.GroomingVan
	}
	return nil
}

// product line item
type AnnualContract_ProductLineItem struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The number of units.
	Quantity int32 `protobuf:"varint,1,opt,name=quantity,proto3" json:"quantity,omitempty"`
	// The price per unit before any discounts.
	UnitPrice string `protobuf:"bytes,2,opt,name=unit_price,json=unitPrice,proto3" json:"unit_price,omitempty"`
	// The final price per unit after discounts have been applied.
	DiscountedUnitPrice string `protobuf:"bytes,3,opt,name=discounted_unit_price,json=discountedUnitPrice,proto3" json:"discounted_unit_price,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *AnnualContract_ProductLineItem) Reset() {
	*x = AnnualContract_ProductLineItem{}
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnualContract_ProductLineItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnualContract_ProductLineItem) ProtoMessage() {}

func (x *AnnualContract_ProductLineItem) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnualContract_ProductLineItem.ProtoReflect.Descriptor instead.
func (*AnnualContract_ProductLineItem) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP(), []int{9, 2}
}

func (x *AnnualContract_ProductLineItem) GetQuantity() int32 {
	if x != nil {
		return x.Quantity
	}
	return 0
}

func (x *AnnualContract_ProductLineItem) GetUnitPrice() string {
	if x != nil {
		return x.UnitPrice
	}
	return ""
}

func (x *AnnualContract_ProductLineItem) GetDiscountedUnitPrice() string {
	if x != nil {
		return x.DiscountedUnitPrice
	}
	return ""
}

var File_backend_proto_sales_v1_annual_contract_service_proto protoreflect.FileDescriptor

const file_backend_proto_sales_v1_annual_contract_service_proto_rawDesc = "" +
	"\n" +
	"4backend/proto/sales/v1/annual_contract_service.proto\x12\x16backend.proto.sales.v1\x1a(backend/proto/sales/v1/sales_enums.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\"\x8c\x04\n" +
	"\x1bCreateAnnualContractRequest\x12!\n" +
	"\acreator\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\acreator\x12&\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12a\n" +
	"\x11subscription_plan\x18\x03 \x01(\x0e2(.backend.proto.sales.v1.SubscriptionPlanB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x10subscriptionPlan\x12A\n" +
	"\x18subscription_term_months\x18\x04 \x01(\x05B\a\xbaH\x04\x1a\x02 \x00R\x16subscriptionTermMonths\x12O\n" +
	"\x13discount_percentage\x18\x05 \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\x12discountPercentage\x123\n" +
	"\x11bd_location_count\x18\x06 \x01(\x05B\a\xbaH\x04\x1a\x02(\x00R\x0fbdLocationCount\x12?\n" +
	"\x17grooming_location_count\x18\a \x01(\x05B\a\xbaH\x04\x1a\x02(\x00R\x15groomingLocationCount\x125\n" +
	"\x12grooming_van_count\x18\b \x01(\x05B\a\xbaH\x04\x1a\x02(\x00R\x10groomingVanCount\"3\n" +
	"\x18GetAnnualContractRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id\"\xb1\x01\n" +
	"\x1aListAnnualContractsRequest\x12&\n" +
	"\tpage_size\x18\x01 \x01(\x05B\t\xbaH\x06\x1a\x04\x18d \x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x02 \x01(\tR\tpageToken\x12L\n" +
	"\afilters\x18\x03 \x01(\v22.backend.proto.sales.v1.AnnualContractQueryFiltersR\afilters\"\x98\x01\n" +
	"\x1bListAnnualContractsResponse\x12Q\n" +
	"\x10annual_contracts\x18\x01 \x03(\v2&.backend.proto.sales.v1.AnnualContractR\x0fannualContracts\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\"k\n" +
	"\x1bCountAnnualContractsRequest\x12L\n" +
	"\afilters\x18\x01 \x01(\v22.backend.proto.sales.v1.AnnualContractQueryFiltersR\afilters\"4\n" +
	"\x1cCountAnnualContractsResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\"c\n" +
	"\x19SignAnnualContractRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id\x12-\n" +
	"\rsignature_uri\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x88\x01\x01R\fsignatureUri\"`\n" +
	"\x1aSignAnnualContractResponse\x12B\n" +
	"\bcontract\x18\x01 \x01(\v2&.backend.proto.sales.v1.AnnualContractR\bcontract\"6\n" +
	"\x1bDeleteAnnualContractRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id\"\xeb\v\n" +
	"\x0eAnnualContract\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1f\n" +
	"\vtemplate_id\x18\x02 \x01(\tR\n" +
	"templateId\x12K\n" +
	"\bmetadata\x18\x03 \x01(\v2/.backend.proto.sales.v1.AnnualContract.MetadataR\bmetadata\x12Q\n" +
	"\n" +
	"parameters\x18\x04 \x01(\v21.backend.proto.sales.v1.AnnualContract.ParametersR\n" +
	"parameters\x12\x18\n" +
	"\acontent\x18\x05 \x01(\tR\acontent\x12\x18\n" +
	"\acreator\x18\x06 \x01(\tR\acreator\x12;\n" +
	"\vcreate_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12<\n" +
	"\tsign_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\x00R\bsignTime\x88\x01\x01\x12(\n" +
	"\rsignature_uri\x18\n" +
	" \x01(\tH\x01R\fsignatureUri\x88\x01\x01\x1a\x9f\x01\n" +
	"\bMetadata\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1d\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03R\taccountId\x12U\n" +
	"\x11subscription_plan\x18\x04 \x01(\x0e2(.backend.proto.sales.v1.SubscriptionPlanR\x10subscriptionPlan\x1a\xac\x05\n" +
	"\n" +
	"Parameters\x12!\n" +
	"\fcompany_name\x18\x01 \x01(\tR\vcompanyName\x12\x1d\n" +
	"\n" +
	"owner_name\x18\x02 \x01(\tR\townerName\x12\x1f\n" +
	"\vowner_email\x18\x03 \x01(\tR\n" +
	"ownerEmail\x12\x18\n" +
	"\aaddress\x18\x04 \x01(\tR\aaddress\x124\n" +
	"\x16subscription_plan_name\x18\x05 \x01(\tR\x14subscriptionPlanName\x128\n" +
	"\x18subscription_term_months\x18\x06 \x01(\x05R\x16subscriptionTermMonths\x12/\n" +
	"\x13discount_percentage\x18\a \x01(\tR\x12discountPercentage\x12!\n" +
	"\ftotal_amount\x18\b \x01(\tR\vtotalAmount\x12h\n" +
	"\x11grooming_location\x18\t \x01(\v26.backend.proto.sales.v1.AnnualContract.ProductLineItemH\x00R\x10groomingLocation\x88\x01\x01\x12\\\n" +
	"\vbd_location\x18\n" +
	" \x01(\v26.backend.proto.sales.v1.AnnualContract.ProductLineItemH\x01R\n" +
	"bdLocation\x88\x01\x01\x12^\n" +
	"\fgrooming_van\x18\v \x01(\v26.backend.proto.sales.v1.AnnualContract.ProductLineItemH\x02R\vgroomingVan\x88\x01\x01B\x14\n" +
	"\x12_grooming_locationB\x0e\n" +
	"\f_bd_locationB\x0f\n" +
	"\r_grooming_van\x1a\x80\x01\n" +
	"\x0fProductLineItem\x12\x1a\n" +
	"\bquantity\x18\x01 \x01(\x05R\bquantity\x12\x1d\n" +
	"\n" +
	"unit_price\x18\x02 \x01(\tR\tunitPrice\x122\n" +
	"\x15discounted_unit_price\x18\x03 \x01(\tR\x13discountedUnitPriceB\f\n" +
	"\n" +
	"_sign_timeB\x10\n" +
	"\x0e_signature_uri\"\x8b\x02\n" +
	"\x1aAnnualContractQueryFilters\x12\"\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03H\x00R\tcompanyId\x88\x01\x01\x12\"\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03H\x01R\taccountId\x88\x01\x01\x12$\n" +
	"\vowner_email\x18\x03 \x01(\tH\x02R\n" +
	"ownerEmail\x88\x01\x01\x12\x1d\n" +
	"\acreator\x18\x04 \x01(\tH\x03R\acreator\x88\x01\x01\x12\x1b\n" +
	"\x06signed\x18\x05 \x01(\bH\x04R\x06signed\x88\x01\x01B\r\n" +
	"\v_company_idB\r\n" +
	"\v_account_idB\x0e\n" +
	"\f_owner_emailB\n" +
	"\n" +
	"\b_creatorB\t\n" +
	"\a_signed2\xe1\x05\n" +
	"\x15AnnualContractService\x12s\n" +
	"\x14CreateAnnualContract\x123.backend.proto.sales.v1.CreateAnnualContractRequest\x1a&.backend.proto.sales.v1.AnnualContract\x12m\n" +
	"\x11GetAnnualContract\x120.backend.proto.sales.v1.GetAnnualContractRequest\x1a&.backend.proto.sales.v1.AnnualContract\x12~\n" +
	"\x13ListAnnualContracts\x122.backend.proto.sales.v1.ListAnnualContractsRequest\x1a3.backend.proto.sales.v1.ListAnnualContractsResponse\x12\x81\x01\n" +
	"\x14CountAnnualContracts\x123.backend.proto.sales.v1.CountAnnualContractsRequest\x1a4.backend.proto.sales.v1.CountAnnualContractsResponse\x12{\n" +
	"\x12SignAnnualContract\x121.backend.proto.sales.v1.SignAnnualContractRequest\x1a2.backend.proto.sales.v1.SignAnnualContractResponse\x12c\n" +
	"\x14DeleteAnnualContract\x123.backend.proto.sales.v1.DeleteAnnualContractRequest\x1a\x16.google.protobuf.EmptyBb\n" +
	" com.moego.backend.proto.sales.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespbb\x06proto3"

var (
	file_backend_proto_sales_v1_annual_contract_service_proto_rawDescOnce sync.Once
	file_backend_proto_sales_v1_annual_contract_service_proto_rawDescData []byte
)

func file_backend_proto_sales_v1_annual_contract_service_proto_rawDescGZIP() []byte {
	file_backend_proto_sales_v1_annual_contract_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_sales_v1_annual_contract_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_annual_contract_service_proto_rawDesc), len(file_backend_proto_sales_v1_annual_contract_service_proto_rawDesc)))
	})
	return file_backend_proto_sales_v1_annual_contract_service_proto_rawDescData
}

var file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes = make([]protoimpl.MessageInfo, 14)
var file_backend_proto_sales_v1_annual_contract_service_proto_goTypes = []any{
	(*CreateAnnualContractRequest)(nil),    // 0: backend.proto.sales.v1.CreateAnnualContractRequest
	(*GetAnnualContractRequest)(nil),       // 1: backend.proto.sales.v1.GetAnnualContractRequest
	(*ListAnnualContractsRequest)(nil),     // 2: backend.proto.sales.v1.ListAnnualContractsRequest
	(*ListAnnualContractsResponse)(nil),    // 3: backend.proto.sales.v1.ListAnnualContractsResponse
	(*CountAnnualContractsRequest)(nil),    // 4: backend.proto.sales.v1.CountAnnualContractsRequest
	(*CountAnnualContractsResponse)(nil),   // 5: backend.proto.sales.v1.CountAnnualContractsResponse
	(*SignAnnualContractRequest)(nil),      // 6: backend.proto.sales.v1.SignAnnualContractRequest
	(*SignAnnualContractResponse)(nil),     // 7: backend.proto.sales.v1.SignAnnualContractResponse
	(*DeleteAnnualContractRequest)(nil),    // 8: backend.proto.sales.v1.DeleteAnnualContractRequest
	(*AnnualContract)(nil),                 // 9: backend.proto.sales.v1.AnnualContract
	(*AnnualContractQueryFilters)(nil),     // 10: backend.proto.sales.v1.AnnualContractQueryFilters
	(*AnnualContract_Metadata)(nil),        // 11: backend.proto.sales.v1.AnnualContract.Metadata
	(*AnnualContract_Parameters)(nil),      // 12: backend.proto.sales.v1.AnnualContract.Parameters
	(*AnnualContract_ProductLineItem)(nil), // 13: backend.proto.sales.v1.AnnualContract.ProductLineItem
	(SubscriptionPlan)(0),                  // 14: backend.proto.sales.v1.SubscriptionPlan
	(*timestamppb.Timestamp)(nil),          // 15: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                  // 16: google.protobuf.Empty
}
var file_backend_proto_sales_v1_annual_contract_service_proto_depIdxs = []int32{
	14, // 0: backend.proto.sales.v1.CreateAnnualContractRequest.subscription_plan:type_name -> backend.proto.sales.v1.SubscriptionPlan
	10, // 1: backend.proto.sales.v1.ListAnnualContractsRequest.filters:type_name -> backend.proto.sales.v1.AnnualContractQueryFilters
	9,  // 2: backend.proto.sales.v1.ListAnnualContractsResponse.annual_contracts:type_name -> backend.proto.sales.v1.AnnualContract
	10, // 3: backend.proto.sales.v1.CountAnnualContractsRequest.filters:type_name -> backend.proto.sales.v1.AnnualContractQueryFilters
	9,  // 4: backend.proto.sales.v1.SignAnnualContractResponse.contract:type_name -> backend.proto.sales.v1.AnnualContract
	11, // 5: backend.proto.sales.v1.AnnualContract.metadata:type_name -> backend.proto.sales.v1.AnnualContract.Metadata
	12, // 6: backend.proto.sales.v1.AnnualContract.parameters:type_name -> backend.proto.sales.v1.AnnualContract.Parameters
	15, // 7: backend.proto.sales.v1.AnnualContract.create_time:type_name -> google.protobuf.Timestamp
	15, // 8: backend.proto.sales.v1.AnnualContract.update_time:type_name -> google.protobuf.Timestamp
	15, // 9: backend.proto.sales.v1.AnnualContract.sign_time:type_name -> google.protobuf.Timestamp
	14, // 10: backend.proto.sales.v1.AnnualContract.Metadata.subscription_plan:type_name -> backend.proto.sales.v1.SubscriptionPlan
	13, // 11: backend.proto.sales.v1.AnnualContract.Parameters.grooming_location:type_name -> backend.proto.sales.v1.AnnualContract.ProductLineItem
	13, // 12: backend.proto.sales.v1.AnnualContract.Parameters.bd_location:type_name -> backend.proto.sales.v1.AnnualContract.ProductLineItem
	13, // 13: backend.proto.sales.v1.AnnualContract.Parameters.grooming_van:type_name -> backend.proto.sales.v1.AnnualContract.ProductLineItem
	0,  // 14: backend.proto.sales.v1.AnnualContractService.CreateAnnualContract:input_type -> backend.proto.sales.v1.CreateAnnualContractRequest
	1,  // 15: backend.proto.sales.v1.AnnualContractService.GetAnnualContract:input_type -> backend.proto.sales.v1.GetAnnualContractRequest
	2,  // 16: backend.proto.sales.v1.AnnualContractService.ListAnnualContracts:input_type -> backend.proto.sales.v1.ListAnnualContractsRequest
	4,  // 17: backend.proto.sales.v1.AnnualContractService.CountAnnualContracts:input_type -> backend.proto.sales.v1.CountAnnualContractsRequest
	6,  // 18: backend.proto.sales.v1.AnnualContractService.SignAnnualContract:input_type -> backend.proto.sales.v1.SignAnnualContractRequest
	8,  // 19: backend.proto.sales.v1.AnnualContractService.DeleteAnnualContract:input_type -> backend.proto.sales.v1.DeleteAnnualContractRequest
	9,  // 20: backend.proto.sales.v1.AnnualContractService.CreateAnnualContract:output_type -> backend.proto.sales.v1.AnnualContract
	9,  // 21: backend.proto.sales.v1.AnnualContractService.GetAnnualContract:output_type -> backend.proto.sales.v1.AnnualContract
	3,  // 22: backend.proto.sales.v1.AnnualContractService.ListAnnualContracts:output_type -> backend.proto.sales.v1.ListAnnualContractsResponse
	5,  // 23: backend.proto.sales.v1.AnnualContractService.CountAnnualContracts:output_type -> backend.proto.sales.v1.CountAnnualContractsResponse
	7,  // 24: backend.proto.sales.v1.AnnualContractService.SignAnnualContract:output_type -> backend.proto.sales.v1.SignAnnualContractResponse
	16, // 25: backend.proto.sales.v1.AnnualContractService.DeleteAnnualContract:output_type -> google.protobuf.Empty
	20, // [20:26] is the sub-list for method output_type
	14, // [14:20] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_backend_proto_sales_v1_annual_contract_service_proto_init() }
func file_backend_proto_sales_v1_annual_contract_service_proto_init() {
	if File_backend_proto_sales_v1_annual_contract_service_proto != nil {
		return
	}
	file_backend_proto_sales_v1_sales_enums_proto_init()
	file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[9].OneofWrappers = []any{}
	file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes[12].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_annual_contract_service_proto_rawDesc), len(file_backend_proto_sales_v1_annual_contract_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   14,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_sales_v1_annual_contract_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_sales_v1_annual_contract_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_sales_v1_annual_contract_service_proto_msgTypes,
	}.Build()
	File_backend_proto_sales_v1_annual_contract_service_proto = out.File
	file_backend_proto_sales_v1_annual_contract_service_proto_goTypes = nil
	file_backend_proto_sales_v1_annual_contract_service_proto_depIdxs = nil
}
