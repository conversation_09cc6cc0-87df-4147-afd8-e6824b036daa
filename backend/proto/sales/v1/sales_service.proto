syntax = "proto3";

package backend.proto.sales.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.sales.v1";

import "buf/validate/validate.proto";
import "google/type/decimal.proto";

// SalesService
service SalesService {
  // SyncOpportunity updates an opportunity.
  rpc SyncOpportunity(SyncOpportunityRequest) returns (SyncOpportunityResponse);
  // CheckOpportunityExists checks if an opportunity exists.
  rpc CheckOpportunityExists(CheckOpportunityExistsRequest) returns (CheckOpportunityExistsResponse);
}

// SyncOpportunityRequest
message SyncOpportunityRequest {
  // opportunity id
  string opportunity_id = 1 [(buf.validate.field).string.min_len = 1];
  // email
  optional string email = 2 [(buf.validate.field).string.min_len = 1];
  // tier
  optional string tier = 3 [(buf.validate.field).string.min_len = 1];
  // terminal percentage
  optional google.type.Decimal terminal_percentage = 4;
  // terminal fixed
  optional google.type.Decimal terminal_fixed = 5;
  // non terminal percentage
  optional google.type.Decimal non_terminal_percentage = 6;
  // non terminal fixed
  optional google.type.Decimal non_terminal_fixed = 7;
  // min_volume
  optional google.type.Decimal min_volume = 8;
  // spif
  optional google.type.Decimal spif = 9;
}

// SyncOpportunityResponse
message SyncOpportunityResponse {}

// CheckOpportunityExistsRequest
message CheckOpportunityExistsRequest {
  // opportunity id
  string opportunity_id = 1 [(buf.validate.field).string.min_len = 1];
}

// CheckOpportunityExistsResponse
message CheckOpportunityExistsResponse {
  // exists
  bool exists = 1;
}

// ErrCode 定义错误码枚举
// 
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because 
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 993900; 
}
