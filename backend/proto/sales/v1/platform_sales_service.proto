syntax = "proto3";

package backend.proto.sales.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.sales.v1";

import "buf/validate/validate.proto";

// PlatformSalesService
service PlatformSalesService {
  // SyncPlatformSales
  rpc SyncPlatformSales(SyncPlatformSalesRequest) returns (SyncPlatformSalesResponse);

}

// SyncPlatformSalesRequest
message SyncPlatformSalesRequest {
  // sales code
  string sales_code = 1 [(buf.validate.field).string.min_len = 1];

  // term type, e.g. monthly, annual
  string term_type = 2;

  // subscription items
  // location count
  int32 location_count = 3;
  // van count
  int32 van_count = 4;

  // hardware items
  // bbpos count
  int32 bbpos_count = 5;
  // reader m2 count
  int32 reader_m2_count = 6;

}

// SyncPlatformSalesResponse
message SyncPlatformSalesResponse {}
