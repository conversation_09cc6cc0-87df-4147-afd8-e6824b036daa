// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/sales/v1/moego_pay_contract_service.proto

package salespb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: We need to do this because 没必要嵌套一层，不好用. --)
//
// CreateMoegoPayContractRequest
type CreateMoegoPayContractRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
	TerminalPercentage string `protobuf:"bytes,2,opt,name=terminal_percentage,json=terminalPercentage,proto3" json:"terminal_percentage,omitempty"`
	// 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
	TerminalFixed string `protobuf:"bytes,3,opt,name=terminal_fixed,json=terminalFixed,proto3" json:"terminal_fixed,omitempty"`
	// 非终端收款手续费百分比，数字格式
	NonTerminalPercentage string `protobuf:"bytes,4,opt,name=non_terminal_percentage,json=nonTerminalPercentage,proto3" json:"non_terminal_percentage,omitempty"`
	// 非终端收款固定手续费，单位为 USD，数字格式
	NonTerminalFixed string `protobuf:"bytes,5,opt,name=non_terminal_fixed,json=nonTerminalFixed,proto3" json:"non_terminal_fixed,omitempty"`
	// 每月最低交易额要求，单位为 USD，数字格式
	MinVolume string `protobuf:"bytes,6,opt,name=min_volume,json=minVolume,proto3" json:"min_volume,omitempty"`
	// creator
	Creator       string `protobuf:"bytes,7,opt,name=creator,proto3" json:"creator,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateMoegoPayContractRequest) Reset() {
	*x = CreateMoegoPayContractRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateMoegoPayContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateMoegoPayContractRequest) ProtoMessage() {}

func (x *CreateMoegoPayContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateMoegoPayContractRequest.ProtoReflect.Descriptor instead.
func (*CreateMoegoPayContractRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateMoegoPayContractRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateMoegoPayContractRequest) GetTerminalPercentage() string {
	if x != nil {
		return x.TerminalPercentage
	}
	return ""
}

func (x *CreateMoegoPayContractRequest) GetTerminalFixed() string {
	if x != nil {
		return x.TerminalFixed
	}
	return ""
}

func (x *CreateMoegoPayContractRequest) GetNonTerminalPercentage() string {
	if x != nil {
		return x.NonTerminalPercentage
	}
	return ""
}

func (x *CreateMoegoPayContractRequest) GetNonTerminalFixed() string {
	if x != nil {
		return x.NonTerminalFixed
	}
	return ""
}

func (x *CreateMoegoPayContractRequest) GetMinVolume() string {
	if x != nil {
		return x.MinVolume
	}
	return ""
}

func (x *CreateMoegoPayContractRequest) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

// GetMoegoPayContractRequest
type GetMoegoPayContractRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMoegoPayContractRequest) Reset() {
	*x = GetMoegoPayContractRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMoegoPayContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMoegoPayContractRequest) ProtoMessage() {}

func (x *GetMoegoPayContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMoegoPayContractRequest.ProtoReflect.Descriptor instead.
func (*GetMoegoPayContractRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{1}
}

func (x *GetMoegoPayContractRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// (-- api-linter: core::0132::request-parent-required=disabled
//
//	aip.dev/not-precedent: We need to do this because we don't use parent. --)
//
// ListMoegoPayContractsRequest
type ListMoegoPayContractsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 每页返回条数，最大不超过 100，默认建议为 20
	PageSize int32 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 上一页返回的分页 token（用于获取下一页）
	PageToken string `protobuf:"bytes,2,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// filters
	Filters       *MoegoPayContractQueryFilters `protobuf:"bytes,3,opt,name=filters,proto3" json:"filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMoegoPayContractsRequest) Reset() {
	*x = ListMoegoPayContractsRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMoegoPayContractsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMoegoPayContractsRequest) ProtoMessage() {}

func (x *ListMoegoPayContractsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMoegoPayContractsRequest.ProtoReflect.Descriptor instead.
func (*ListMoegoPayContractsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListMoegoPayContractsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListMoegoPayContractsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListMoegoPayContractsRequest) GetFilters() *MoegoPayContractQueryFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

// ListMoegoPayContractsResponse
type ListMoegoPayContractsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 合同列表
	MoegoPayContracts []*MoegoPayContract `protobuf:"bytes,1,rep,name=moego_pay_contracts,json=moegoPayContracts,proto3" json:"moego_pay_contracts,omitempty"`
	// 下一页的分页 token，如果为空表示无更多结果
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMoegoPayContractsResponse) Reset() {
	*x = ListMoegoPayContractsResponse{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMoegoPayContractsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMoegoPayContractsResponse) ProtoMessage() {}

func (x *ListMoegoPayContractsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMoegoPayContractsResponse.ProtoReflect.Descriptor instead.
func (*ListMoegoPayContractsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListMoegoPayContractsResponse) GetMoegoPayContracts() []*MoegoPayContract {
	if x != nil {
		return x.MoegoPayContracts
	}
	return nil
}

func (x *ListMoegoPayContractsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// CountMoegoPayContractsRequest
type CountMoegoPayContractsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filters
	Filters       *MoegoPayContractQueryFilters `protobuf:"bytes,1,opt,name=filters,proto3" json:"filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountMoegoPayContractsRequest) Reset() {
	*x = CountMoegoPayContractsRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountMoegoPayContractsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountMoegoPayContractsRequest) ProtoMessage() {}

func (x *CountMoegoPayContractsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountMoegoPayContractsRequest.ProtoReflect.Descriptor instead.
func (*CountMoegoPayContractsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{4}
}

func (x *CountMoegoPayContractsRequest) GetFilters() *MoegoPayContractQueryFilters {
	if x != nil {
		return x.Filters
	}
	return nil
}

// CountMoegoPayContractsResponse
type CountMoegoPayContractsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// count
	Count         int64 `protobuf:"varint,1,opt,name=count,proto3" json:"count,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CountMoegoPayContractsResponse) Reset() {
	*x = CountMoegoPayContractsResponse{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CountMoegoPayContractsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CountMoegoPayContractsResponse) ProtoMessage() {}

func (x *CountMoegoPayContractsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CountMoegoPayContractsResponse.ProtoReflect.Descriptor instead.
func (*CountMoegoPayContractsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{5}
}

func (x *CountMoegoPayContractsResponse) GetCount() int64 {
	if x != nil {
		return x.Count
	}
	return 0
}

// SignMoegoPayContractRequest
type SignMoegoPayContractRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// signature uri (url)
	SignatureUri  string `protobuf:"bytes,2,opt,name=signature_uri,json=signatureUri,proto3" json:"signature_uri,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignMoegoPayContractRequest) Reset() {
	*x = SignMoegoPayContractRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignMoegoPayContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignMoegoPayContractRequest) ProtoMessage() {}

func (x *SignMoegoPayContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignMoegoPayContractRequest.ProtoReflect.Descriptor instead.
func (*SignMoegoPayContractRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{6}
}

func (x *SignMoegoPayContractRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SignMoegoPayContractRequest) GetSignatureUri() string {
	if x != nil {
		return x.SignatureUri
	}
	return ""
}

// SignMoegoPayContractResponse
type SignMoegoPayContractResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// contract
	Contract      *MoegoPayContract `protobuf:"bytes,1,opt,name=contract,proto3" json:"contract,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SignMoegoPayContractResponse) Reset() {
	*x = SignMoegoPayContractResponse{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SignMoegoPayContractResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SignMoegoPayContractResponse) ProtoMessage() {}

func (x *SignMoegoPayContractResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SignMoegoPayContractResponse.ProtoReflect.Descriptor instead.
func (*SignMoegoPayContractResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{7}
}

func (x *SignMoegoPayContractResponse) GetContract() *MoegoPayContract {
	if x != nil {
		return x.Contract
	}
	return nil
}

// DeleteMoegoPayContractRequest
type DeleteMoegoPayContractRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id            string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteMoegoPayContractRequest) Reset() {
	*x = DeleteMoegoPayContractRequest{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteMoegoPayContractRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteMoegoPayContractRequest) ProtoMessage() {}

func (x *DeleteMoegoPayContractRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteMoegoPayContractRequest.ProtoReflect.Descriptor instead.
func (*DeleteMoegoPayContractRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteMoegoPayContractRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// MoegoPayContract
type MoegoPayContract struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// template id
	TemplateId string `protobuf:"bytes,2,opt,name=template_id,json=templateId,proto3" json:"template_id,omitempty"`
	// metadata
	Metadata *MoegoPayContract_Metadata `protobuf:"bytes,3,opt,name=metadata,proto3" json:"metadata,omitempty"`
	// params
	Parameters *MoegoPayContract_Parameters `protobuf:"bytes,4,opt,name=parameters,proto3" json:"parameters,omitempty"`
	// content
	Content string `protobuf:"bytes,5,opt,name=content,proto3" json:"content,omitempty"`
	// creator
	Creator string `protobuf:"bytes,6,opt,name=creator,proto3" json:"creator,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// sign time
	SignTime *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=sign_time,json=signTime,proto3,oneof" json:"sign_time,omitempty"`
	// signature uri (url)
	SignatureUri  *string `protobuf:"bytes,10,opt,name=signature_uri,json=signatureUri,proto3,oneof" json:"signature_uri,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MoegoPayContract) Reset() {
	*x = MoegoPayContract{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoegoPayContract) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoegoPayContract) ProtoMessage() {}

func (x *MoegoPayContract) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoegoPayContract.ProtoReflect.Descriptor instead.
func (*MoegoPayContract) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{9}
}

func (x *MoegoPayContract) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MoegoPayContract) GetTemplateId() string {
	if x != nil {
		return x.TemplateId
	}
	return ""
}

func (x *MoegoPayContract) GetMetadata() *MoegoPayContract_Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *MoegoPayContract) GetParameters() *MoegoPayContract_Parameters {
	if x != nil {
		return x.Parameters
	}
	return nil
}

func (x *MoegoPayContract) GetContent() string {
	if x != nil {
		return x.Content
	}
	return ""
}

func (x *MoegoPayContract) GetCreator() string {
	if x != nil {
		return x.Creator
	}
	return ""
}

func (x *MoegoPayContract) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *MoegoPayContract) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *MoegoPayContract) GetSignTime() *timestamppb.Timestamp {
	if x != nil {
		return x.SignTime
	}
	return nil
}

func (x *MoegoPayContract) GetSignatureUri() string {
	if x != nil && x.SignatureUri != nil {
		return *x.SignatureUri
	}
	return ""
}

// Query filters for MoegoPayContract
type MoegoPayContractQueryFilters struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company_id
	CompanyId *int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// account_id
	AccountId *int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3,oneof" json:"account_id,omitempty"`
	// owner email (prefix like)
	OwnerEmail *string `protobuf:"bytes,3,opt,name=owner_email,json=ownerEmail,proto3,oneof" json:"owner_email,omitempty"`
	// creator (prefix like)
	Creator *string `protobuf:"bytes,4,opt,name=creator,proto3,oneof" json:"creator,omitempty"`
	// if the contract is signed
	Signed        *bool `protobuf:"varint,5,opt,name=signed,proto3,oneof" json:"signed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MoegoPayContractQueryFilters) Reset() {
	*x = MoegoPayContractQueryFilters{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoegoPayContractQueryFilters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoegoPayContractQueryFilters) ProtoMessage() {}

func (x *MoegoPayContractQueryFilters) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoegoPayContractQueryFilters.ProtoReflect.Descriptor instead.
func (*MoegoPayContractQueryFilters) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{10}
}

func (x *MoegoPayContractQueryFilters) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *MoegoPayContractQueryFilters) GetAccountId() int64 {
	if x != nil && x.AccountId != nil {
		return *x.AccountId
	}
	return 0
}

func (x *MoegoPayContractQueryFilters) GetOwnerEmail() string {
	if x != nil && x.OwnerEmail != nil {
		return *x.OwnerEmail
	}
	return ""
}

func (x *MoegoPayContractQueryFilters) GetCreator() string {
	if x != nil && x.Creator != nil {
		return *x.Creator
	}
	return ""
}

func (x *MoegoPayContractQueryFilters) GetSigned() bool {
	if x != nil && x.Signed != nil {
		return *x.Signed
	}
	return false
}

// metadata
type MoegoPayContract_Metadata struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company id
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// account id
	AccountId     int64 `protobuf:"varint,2,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MoegoPayContract_Metadata) Reset() {
	*x = MoegoPayContract_Metadata{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoegoPayContract_Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoegoPayContract_Metadata) ProtoMessage() {}

func (x *MoegoPayContract_Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoegoPayContract_Metadata.ProtoReflect.Descriptor instead.
func (*MoegoPayContract_Metadata) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *MoegoPayContract_Metadata) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *MoegoPayContract_Metadata) GetAccountId() int64 {
	if x != nil {
		return x.AccountId
	}
	return 0
}

// parameters for rendering
type MoegoPayContract_Parameters struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
	//
	// company name
	CompanyName string `protobuf:"bytes,1,opt,name=company_name,json=companyName,proto3" json:"company_name,omitempty"`
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: We need to do this because we need to avoid ambiguity. --)
	//
	// owner name
	OwnerName string `protobuf:"bytes,2,opt,name=owner_name,json=ownerName,proto3" json:"owner_name,omitempty"`
	// owner email
	OwnerEmail string `protobuf:"bytes,3,opt,name=owner_email,json=ownerEmail,proto3" json:"owner_email,omitempty"`
	// 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
	TerminalPercentage string `protobuf:"bytes,4,opt,name=terminal_percentage,json=terminalPercentage,proto3" json:"terminal_percentage,omitempty"`
	// 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
	TerminalFixed string `protobuf:"bytes,5,opt,name=terminal_fixed,json=terminalFixed,proto3" json:"terminal_fixed,omitempty"`
	// 非终端收款手续费百分比，数字格式
	NonTerminalPercentage string `protobuf:"bytes,6,opt,name=non_terminal_percentage,json=nonTerminalPercentage,proto3" json:"non_terminal_percentage,omitempty"`
	// 非终端收款固定手续费，单位为 USD，数字格式
	NonTerminalFixed string `protobuf:"bytes,7,opt,name=non_terminal_fixed,json=nonTerminalFixed,proto3" json:"non_terminal_fixed,omitempty"`
	// 每月最低交易额要求，单位为 USD，数字格式
	MinVolume     string `protobuf:"bytes,8,opt,name=min_volume,json=minVolume,proto3" json:"min_volume,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MoegoPayContract_Parameters) Reset() {
	*x = MoegoPayContract_Parameters{}
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MoegoPayContract_Parameters) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MoegoPayContract_Parameters) ProtoMessage() {}

func (x *MoegoPayContract_Parameters) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MoegoPayContract_Parameters.ProtoReflect.Descriptor instead.
func (*MoegoPayContract_Parameters) Descriptor() ([]byte, []int) {
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP(), []int{9, 1}
}

func (x *MoegoPayContract_Parameters) GetCompanyName() string {
	if x != nil {
		return x.CompanyName
	}
	return ""
}

func (x *MoegoPayContract_Parameters) GetOwnerName() string {
	if x != nil {
		return x.OwnerName
	}
	return ""
}

func (x *MoegoPayContract_Parameters) GetOwnerEmail() string {
	if x != nil {
		return x.OwnerEmail
	}
	return ""
}

func (x *MoegoPayContract_Parameters) GetTerminalPercentage() string {
	if x != nil {
		return x.TerminalPercentage
	}
	return ""
}

func (x *MoegoPayContract_Parameters) GetTerminalFixed() string {
	if x != nil {
		return x.TerminalFixed
	}
	return ""
}

func (x *MoegoPayContract_Parameters) GetNonTerminalPercentage() string {
	if x != nil {
		return x.NonTerminalPercentage
	}
	return ""
}

func (x *MoegoPayContract_Parameters) GetNonTerminalFixed() string {
	if x != nil {
		return x.NonTerminalFixed
	}
	return ""
}

func (x *MoegoPayContract_Parameters) GetMinVolume() string {
	if x != nil {
		return x.MinVolume
	}
	return ""
}

var File_backend_proto_sales_v1_moego_pay_contract_service_proto protoreflect.FileDescriptor

const file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDesc = "" +
	"\n" +
	"7backend/proto/sales/v1/moego_pay_contract_service.proto\x12\x16backend.proto.sales.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bgoogle/protobuf/empty.proto\"\xe7\x03\n" +
	"\x1dCreateMoegoPayContractRequest\x12&\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12O\n" +
	"\x13terminal_percentage\x18\x02 \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\x12terminalPercentage\x12E\n" +
	"\x0eterminal_fixed\x18\x03 \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\rterminalFixed\x12V\n" +
	"\x17non_terminal_percentage\x18\x04 \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\x15nonTerminalPercentage\x12L\n" +
	"\x12non_terminal_fixed\x18\x05 \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\x10nonTerminalFixed\x12=\n" +
	"\n" +
	"min_volume\x18\x06 \x01(\tB\x1e\xbaH\x1br\x192\x17^[0-9]+(\\.[0-9]{1,2})?$R\tminVolume\x12!\n" +
	"\acreator\x18\a \x01(\tB\a\xbaH\x04r\x02\x10\x01R\acreator\"5\n" +
	"\x1aGetMoegoPayContractRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id\"\xb5\x01\n" +
	"\x1cListMoegoPayContractsRequest\x12&\n" +
	"\tpage_size\x18\x01 \x01(\x05B\t\xbaH\x06\x1a\x04\x18d \x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x02 \x01(\tR\tpageToken\x12N\n" +
	"\afilters\x18\x03 \x01(\v24.backend.proto.sales.v1.MoegoPayContractQueryFiltersR\afilters\"\xa1\x01\n" +
	"\x1dListMoegoPayContractsResponse\x12X\n" +
	"\x13moego_pay_contracts\x18\x01 \x03(\v2(.backend.proto.sales.v1.MoegoPayContractR\x11moegoPayContracts\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\"o\n" +
	"\x1dCountMoegoPayContractsRequest\x12N\n" +
	"\afilters\x18\x01 \x01(\v24.backend.proto.sales.v1.MoegoPayContractQueryFiltersR\afilters\"6\n" +
	"\x1eCountMoegoPayContractsResponse\x12\x14\n" +
	"\x05count\x18\x01 \x01(\x03R\x05count\"e\n" +
	"\x1bSignMoegoPayContractRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id\x12-\n" +
	"\rsignature_uri\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x88\x01\x01R\fsignatureUri\"d\n" +
	"\x1cSignMoegoPayContractResponse\x12D\n" +
	"\bcontract\x18\x01 \x01(\v2(.backend.proto.sales.v1.MoegoPayContractR\bcontract\"8\n" +
	"\x1dDeleteMoegoPayContractRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x10\x01R\x02id\"\xb6\a\n" +
	"\x10MoegoPayContract\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x1f\n" +
	"\vtemplate_id\x18\x02 \x01(\tR\n" +
	"templateId\x12M\n" +
	"\bmetadata\x18\x03 \x01(\v21.backend.proto.sales.v1.MoegoPayContract.MetadataR\bmetadata\x12S\n" +
	"\n" +
	"parameters\x18\x04 \x01(\v23.backend.proto.sales.v1.MoegoPayContract.ParametersR\n" +
	"parameters\x12\x18\n" +
	"\acontent\x18\x05 \x01(\tR\acontent\x12\x18\n" +
	"\acreator\x18\x06 \x01(\tR\acreator\x12;\n" +
	"\vcreate_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12<\n" +
	"\tsign_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\x00R\bsignTime\x88\x01\x01\x12(\n" +
	"\rsignature_uri\x18\n" +
	" \x01(\tH\x01R\fsignatureUri\x88\x01\x01\x1aH\n" +
	"\bMetadata\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1d\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03R\taccountId\x1a\xcc\x02\n" +
	"\n" +
	"Parameters\x12!\n" +
	"\fcompany_name\x18\x01 \x01(\tR\vcompanyName\x12\x1d\n" +
	"\n" +
	"owner_name\x18\x02 \x01(\tR\townerName\x12\x1f\n" +
	"\vowner_email\x18\x03 \x01(\tR\n" +
	"ownerEmail\x12/\n" +
	"\x13terminal_percentage\x18\x04 \x01(\tR\x12terminalPercentage\x12%\n" +
	"\x0eterminal_fixed\x18\x05 \x01(\tR\rterminalFixed\x126\n" +
	"\x17non_terminal_percentage\x18\x06 \x01(\tR\x15nonTerminalPercentage\x12,\n" +
	"\x12non_terminal_fixed\x18\a \x01(\tR\x10nonTerminalFixed\x12\x1d\n" +
	"\n" +
	"min_volume\x18\b \x01(\tR\tminVolumeB\f\n" +
	"\n" +
	"_sign_timeB\x10\n" +
	"\x0e_signature_uri\"\x8d\x02\n" +
	"\x1cMoegoPayContractQueryFilters\x12\"\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03H\x00R\tcompanyId\x88\x01\x01\x12\"\n" +
	"\n" +
	"account_id\x18\x02 \x01(\x03H\x01R\taccountId\x88\x01\x01\x12$\n" +
	"\vowner_email\x18\x03 \x01(\tH\x02R\n" +
	"ownerEmail\x88\x01\x01\x12\x1d\n" +
	"\acreator\x18\x04 \x01(\tH\x03R\acreator\x88\x01\x01\x12\x1b\n" +
	"\x06signed\x18\x05 \x01(\bH\x04R\x06signed\x88\x01\x01B\r\n" +
	"\v_company_idB\r\n" +
	"\v_account_idB\x0e\n" +
	"\f_owner_emailB\n" +
	"\n" +
	"\b_creatorB\t\n" +
	"\a_signed2\x87\x06\n" +
	"\x17MoegoPayContractService\x12y\n" +
	"\x16CreateMoegoPayContract\x125.backend.proto.sales.v1.CreateMoegoPayContractRequest\x1a(.backend.proto.sales.v1.MoegoPayContract\x12s\n" +
	"\x13GetMoegoPayContract\x122.backend.proto.sales.v1.GetMoegoPayContractRequest\x1a(.backend.proto.sales.v1.MoegoPayContract\x12\x84\x01\n" +
	"\x15ListMoegoPayContracts\x124.backend.proto.sales.v1.ListMoegoPayContractsRequest\x1a5.backend.proto.sales.v1.ListMoegoPayContractsResponse\x12\x87\x01\n" +
	"\x16CountMoegoPayContracts\x125.backend.proto.sales.v1.CountMoegoPayContractsRequest\x1a6.backend.proto.sales.v1.CountMoegoPayContractsResponse\x12\x81\x01\n" +
	"\x14SignMoegoPayContract\x123.backend.proto.sales.v1.SignMoegoPayContractRequest\x1a4.backend.proto.sales.v1.SignMoegoPayContractResponse\x12g\n" +
	"\x16DeleteMoegoPayContract\x125.backend.proto.sales.v1.DeleteMoegoPayContractRequest\x1a\x16.google.protobuf.EmptyBb\n" +
	" com.moego.backend.proto.sales.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespbb\x06proto3"

var (
	file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescOnce sync.Once
	file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescData []byte
)

func file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescGZIP() []byte {
	file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDesc), len(file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDesc)))
	})
	return file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDescData
}

var file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes = make([]protoimpl.MessageInfo, 13)
var file_backend_proto_sales_v1_moego_pay_contract_service_proto_goTypes = []any{
	(*CreateMoegoPayContractRequest)(nil),  // 0: backend.proto.sales.v1.CreateMoegoPayContractRequest
	(*GetMoegoPayContractRequest)(nil),     // 1: backend.proto.sales.v1.GetMoegoPayContractRequest
	(*ListMoegoPayContractsRequest)(nil),   // 2: backend.proto.sales.v1.ListMoegoPayContractsRequest
	(*ListMoegoPayContractsResponse)(nil),  // 3: backend.proto.sales.v1.ListMoegoPayContractsResponse
	(*CountMoegoPayContractsRequest)(nil),  // 4: backend.proto.sales.v1.CountMoegoPayContractsRequest
	(*CountMoegoPayContractsResponse)(nil), // 5: backend.proto.sales.v1.CountMoegoPayContractsResponse
	(*SignMoegoPayContractRequest)(nil),    // 6: backend.proto.sales.v1.SignMoegoPayContractRequest
	(*SignMoegoPayContractResponse)(nil),   // 7: backend.proto.sales.v1.SignMoegoPayContractResponse
	(*DeleteMoegoPayContractRequest)(nil),  // 8: backend.proto.sales.v1.DeleteMoegoPayContractRequest
	(*MoegoPayContract)(nil),               // 9: backend.proto.sales.v1.MoegoPayContract
	(*MoegoPayContractQueryFilters)(nil),   // 10: backend.proto.sales.v1.MoegoPayContractQueryFilters
	(*MoegoPayContract_Metadata)(nil),      // 11: backend.proto.sales.v1.MoegoPayContract.Metadata
	(*MoegoPayContract_Parameters)(nil),    // 12: backend.proto.sales.v1.MoegoPayContract.Parameters
	(*timestamppb.Timestamp)(nil),          // 13: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                  // 14: google.protobuf.Empty
}
var file_backend_proto_sales_v1_moego_pay_contract_service_proto_depIdxs = []int32{
	10, // 0: backend.proto.sales.v1.ListMoegoPayContractsRequest.filters:type_name -> backend.proto.sales.v1.MoegoPayContractQueryFilters
	9,  // 1: backend.proto.sales.v1.ListMoegoPayContractsResponse.moego_pay_contracts:type_name -> backend.proto.sales.v1.MoegoPayContract
	10, // 2: backend.proto.sales.v1.CountMoegoPayContractsRequest.filters:type_name -> backend.proto.sales.v1.MoegoPayContractQueryFilters
	9,  // 3: backend.proto.sales.v1.SignMoegoPayContractResponse.contract:type_name -> backend.proto.sales.v1.MoegoPayContract
	11, // 4: backend.proto.sales.v1.MoegoPayContract.metadata:type_name -> backend.proto.sales.v1.MoegoPayContract.Metadata
	12, // 5: backend.proto.sales.v1.MoegoPayContract.parameters:type_name -> backend.proto.sales.v1.MoegoPayContract.Parameters
	13, // 6: backend.proto.sales.v1.MoegoPayContract.create_time:type_name -> google.protobuf.Timestamp
	13, // 7: backend.proto.sales.v1.MoegoPayContract.update_time:type_name -> google.protobuf.Timestamp
	13, // 8: backend.proto.sales.v1.MoegoPayContract.sign_time:type_name -> google.protobuf.Timestamp
	0,  // 9: backend.proto.sales.v1.MoegoPayContractService.CreateMoegoPayContract:input_type -> backend.proto.sales.v1.CreateMoegoPayContractRequest
	1,  // 10: backend.proto.sales.v1.MoegoPayContractService.GetMoegoPayContract:input_type -> backend.proto.sales.v1.GetMoegoPayContractRequest
	2,  // 11: backend.proto.sales.v1.MoegoPayContractService.ListMoegoPayContracts:input_type -> backend.proto.sales.v1.ListMoegoPayContractsRequest
	4,  // 12: backend.proto.sales.v1.MoegoPayContractService.CountMoegoPayContracts:input_type -> backend.proto.sales.v1.CountMoegoPayContractsRequest
	6,  // 13: backend.proto.sales.v1.MoegoPayContractService.SignMoegoPayContract:input_type -> backend.proto.sales.v1.SignMoegoPayContractRequest
	8,  // 14: backend.proto.sales.v1.MoegoPayContractService.DeleteMoegoPayContract:input_type -> backend.proto.sales.v1.DeleteMoegoPayContractRequest
	9,  // 15: backend.proto.sales.v1.MoegoPayContractService.CreateMoegoPayContract:output_type -> backend.proto.sales.v1.MoegoPayContract
	9,  // 16: backend.proto.sales.v1.MoegoPayContractService.GetMoegoPayContract:output_type -> backend.proto.sales.v1.MoegoPayContract
	3,  // 17: backend.proto.sales.v1.MoegoPayContractService.ListMoegoPayContracts:output_type -> backend.proto.sales.v1.ListMoegoPayContractsResponse
	5,  // 18: backend.proto.sales.v1.MoegoPayContractService.CountMoegoPayContracts:output_type -> backend.proto.sales.v1.CountMoegoPayContractsResponse
	7,  // 19: backend.proto.sales.v1.MoegoPayContractService.SignMoegoPayContract:output_type -> backend.proto.sales.v1.SignMoegoPayContractResponse
	14, // 20: backend.proto.sales.v1.MoegoPayContractService.DeleteMoegoPayContract:output_type -> google.protobuf.Empty
	15, // [15:21] is the sub-list for method output_type
	9,  // [9:15] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_backend_proto_sales_v1_moego_pay_contract_service_proto_init() }
func file_backend_proto_sales_v1_moego_pay_contract_service_proto_init() {
	if File_backend_proto_sales_v1_moego_pay_contract_service_proto != nil {
		return
	}
	file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[9].OneofWrappers = []any{}
	file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes[10].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDesc), len(file_backend_proto_sales_v1_moego_pay_contract_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   13,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_sales_v1_moego_pay_contract_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_sales_v1_moego_pay_contract_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_sales_v1_moego_pay_contract_service_proto_msgTypes,
	}.Build()
	File_backend_proto_sales_v1_moego_pay_contract_service_proto = out.File
	file_backend_proto_sales_v1_moego_pay_contract_service_proto_goTypes = nil
	file_backend_proto_sales_v1_moego_pay_contract_service_proto_depIdxs = nil
}
