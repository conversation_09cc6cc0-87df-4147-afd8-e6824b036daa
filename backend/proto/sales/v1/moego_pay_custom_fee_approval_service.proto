syntax = "proto3";

package backend.proto.sales.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/sales/v1;salespb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.sales.v1";

import "buf/validate/validate.proto";
import "google/protobuf/timestamp.proto";

// MoegoPayCustomFeeApprovalService
service MoegoPayCustomFeeApprovalService {
    // CreateMoegoPayCustomFeeApproval
    rpc CreateMoegoPayCustomFeeApproval(CreateMoegoPayCustomFeeApprovalRequest) returns (MoegoPayCustomFeeApproval);
    // GetMoegoPayCustomFeeApproval
    rpc GetMoegoPayCustomFeeApproval(GetMoegoPayCustomFeeApprovalRequest) returns (MoegoPayCustomFeeApproval);
    // ListMoegoPayCustomFeeApprovals
    rpc ListMoegoPayCustomFeeApprovals(ListMoegoPayCustomFeeApprovalsRequest) returns (ListMoegoPayCustomFeeApprovalsResponse);
    // CountMoegoPayCustomFeeApprovals
    rpc CountMoegoPayCustomFeeApprovals(CountMoegoPayCustomFeeApprovalsRequest) returns (CountMoegoPayCustomFeeApprovalsResponse);
    // ApproveMoegoPayCustomFeeApproval
    rpc ApproveMoegoPayCustomFeeApproval(ApproveMoegoPayCustomFeeApprovalRequest) returns (ApproveMoegoPayCustomFeeApprovalResponse);
    // RejectMoegoPayCustomFeeApproval
    rpc RejectMoegoPayCustomFeeApproval(RejectMoegoPayCustomFeeApprovalRequest) returns (RejectMoegoPayCustomFeeApprovalResponse);
}

// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: We need to do this because 没必要嵌套一层，不好用. --)
// CreateMoegoPayCustomFeeApprovalRequest
message CreateMoegoPayCustomFeeApprovalRequest {
    // creator
    string creator = 1 [(buf.validate.field).string.min_len = 1];
    // company id
    int64 company_id = 2 [(buf.validate.field).int64.gt = 0];
    // account id
    int64 account_id = 3 [(buf.validate.field).int64.gt = 0];
    // owner email
    string owner_email = 4 [(buf.validate.field).string.min_len = 1];
    // 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
    string terminal_percentage = 5 [(buf.validate.field).string.pattern = "^[0-9]+(\\.[0-9]{1,2})?$"];
    // 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
    string terminal_fixed = 6 [(buf.validate.field).string.pattern = "^[0-9]+(\\.[0-9]{1,2})?$"];
    // 非终端收款手续费百分比，数字格式
    string non_terminal_percentage = 7 [(buf.validate.field).string.pattern = "^[0-9]+(\\.[0-9]{1,2})?$"];
    // 非终端收款固定手续费，单位为 USD，数字格式
    string non_terminal_fixed = 8 [(buf.validate.field).string.pattern = "^[0-9]+(\\.[0-9]{1,2})?$"];
    // 每月最低交易额要求，单位为 USD，数字格式
    string min_volume = 9 [(buf.validate.field).string.pattern = "^[0-9]+(\\.[0-9]{1,2})?$"];
    // spif
    optional string spif = 10;
    // opportunity id
    optional string opportunity_id = 11;
    // contract id
    optional string contract_id = 12;
}

// GetMoegoPayCustomFeeApprovalRequest
message GetMoegoPayCustomFeeApprovalRequest {
    // id
    string id = 1 [(buf.validate.field).string.min_len = 1];
}

// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: We need to do this because this model does not have a parent. --)
// ListMoegoPayCustomFeeApprovalsRequest
message ListMoegoPayCustomFeeApprovalsRequest {
    // page size
    int32 page_size = 1 [(buf.validate.field).int32.gt = 0];
    // page token
    string page_token = 2;
    // query filters
    MoegoPayCustomFeeApprovalQueryFilters filters = 3;
}

// ListMoegoPayCustomFeeApprovalsResponse
message ListMoegoPayCustomFeeApprovalsResponse {
    // approval list
    repeated MoegoPayCustomFeeApproval moego_pay_custom_fee_approvals = 1;
    // next page token
    string next_page_token = 2;
}

// CountMoegoPayCustomFeeApprovalsRequest
message CountMoegoPayCustomFeeApprovalsRequest {
    // query filters
    MoegoPayCustomFeeApprovalQueryFilters filters = 3;
}

// CountMoegoPayCustomFeeApprovalsResponse
message CountMoegoPayCustomFeeApprovalsResponse {
    // count
    int64 count = 1;
}

// ApproveMoegoPayCustomFeeApprovalRequest
message ApproveMoegoPayCustomFeeApprovalRequest {
    // id
    string id = 1 [(buf.validate.field).string.min_len = 1];
    // handler
    string handler = 2 [(buf.validate.field).string.min_len = 1];
}
 // ApproveMoegoPayCustomFeeApprovalResponse
message ApproveMoegoPayCustomFeeApprovalResponse {
    // approval
    MoegoPayCustomFeeApproval moego_pay_custom_fee_approval = 1;
}

// RejectMoegoPayCustomFeeApprovalRequest
message RejectMoegoPayCustomFeeApprovalRequest {
    // id
    string id = 1 [(buf.validate.field).string.min_len = 1];
    // handler
    string handler = 2 [(buf.validate.field).string.min_len = 1];
}

// RejectMoegoPayCustomFeeApprovalResponse
message RejectMoegoPayCustomFeeApprovalResponse {
    // approval
    MoegoPayCustomFeeApproval moego_pay_custom_fee_approval = 1;
}

// MoegoPayCustomFeeApproval
message MoegoPayCustomFeeApproval {
    // id
    string id = 1;
    // metadata
    Metadata metadata = 2;
    // company id
    int64 company_id = 3;
    // account id
    int64 account_id = 4;
    // owner email
    string owner_email = 5;
    // 终端收款手续费百分比，数字格式，如 "2.30" 表示 2.30%
    string terminal_percentage = 6;
    // 终端收款固定手续费，单位为 USD，数字格式，如 "5.10" 表示 $5.10
    string terminal_fixed = 7;
    // 非终端收款手续费百分比，数字格式
    string non_terminal_percentage = 8;
    // 非终端收款固定手续费，单位为 USD，数字格式
    string non_terminal_fixed = 9;
    // 每月最低交易额要求，单位为 USD，数字格式
    string min_volume = 10;
    // creator
    string creator = 11;
    // handler (approved / rejected by)
    optional string handler = 12;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: We need to do this because we do not use field behavior. --)
    // approval state
    ApprovalState approval_state = 13;
    // create time
    google.protobuf.Timestamp create_time = 14;
    // update time
    google.protobuf.Timestamp update_time = 15;
    // handle time (approve / reject time)
    optional google.protobuf.Timestamp handle_time = 16;

    // metadata, fields marked as optional mean their values may not exist
    message Metadata {
        // spif
        optional string spif = 1;
        // opportunity id
        optional string opportunity_id = 2;
        // contract id
        optional string contract_id = 3;
    }

    // approval state
    enum ApprovalState {
        // unspecified
        APPROVAL_STATE_UNSPECIFIED = 0;
        // ignored
        IGNORED = 1;
        // pending
        PENDING = 2;
        // approved
        APPROVED = 3;
        // rejected
        REJECTED = 4;
    }
}

// Query filters for MoegoPayCustomFeeApproval
message MoegoPayCustomFeeApprovalQueryFilters {
    // company_id
    optional int64 company_id = 1;
    // account_id
    optional int64 account_id = 2;
    // owner email (prefix like)
    optional string owner_email = 3;
    // (-- api-linter: core::0216::state-field-output-only=disabled
    //     aip.dev/not-precedent: We need to do this because we do not use field behavior, and this field is not output only. --)
    // approval states
    repeated MoegoPayCustomFeeApproval.ApprovalState approval_states = 4;
}
