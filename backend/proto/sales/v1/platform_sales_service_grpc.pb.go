// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/sales/v1/platform_sales_service.proto

package salespb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PlatformSalesService_SyncPlatformSales_FullMethodName = "/backend.proto.sales.v1.PlatformSalesService/SyncPlatformSales"
)

// PlatformSalesServiceClient is the client API for PlatformSalesService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PlatformSalesService
type PlatformSalesServiceClient interface {
	// SyncPlatformSales
	SyncPlatformSales(ctx context.Context, in *SyncPlatformSalesRequest, opts ...grpc.CallOption) (*SyncPlatformSalesResponse, error)
}

type platformSalesServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPlatformSalesServiceClient(cc grpc.ClientConnInterface) PlatformSalesServiceClient {
	return &platformSalesServiceClient{cc}
}

func (c *platformSalesServiceClient) SyncPlatformSales(ctx context.Context, in *SyncPlatformSalesRequest, opts ...grpc.CallOption) (*SyncPlatformSalesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncPlatformSalesResponse)
	err := c.cc.Invoke(ctx, PlatformSalesService_SyncPlatformSales_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PlatformSalesServiceServer is the server API for PlatformSalesService service.
// All implementations must embed UnimplementedPlatformSalesServiceServer
// for forward compatibility.
//
// PlatformSalesService
type PlatformSalesServiceServer interface {
	// SyncPlatformSales
	SyncPlatformSales(context.Context, *SyncPlatformSalesRequest) (*SyncPlatformSalesResponse, error)
	mustEmbedUnimplementedPlatformSalesServiceServer()
}

// UnimplementedPlatformSalesServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPlatformSalesServiceServer struct{}

func (UnimplementedPlatformSalesServiceServer) SyncPlatformSales(context.Context, *SyncPlatformSalesRequest) (*SyncPlatformSalesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncPlatformSales not implemented")
}
func (UnimplementedPlatformSalesServiceServer) mustEmbedUnimplementedPlatformSalesServiceServer() {}
func (UnimplementedPlatformSalesServiceServer) testEmbeddedByValue()                              {}

// UnsafePlatformSalesServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PlatformSalesServiceServer will
// result in compilation errors.
type UnsafePlatformSalesServiceServer interface {
	mustEmbedUnimplementedPlatformSalesServiceServer()
}

func RegisterPlatformSalesServiceServer(s grpc.ServiceRegistrar, srv PlatformSalesServiceServer) {
	// If the following call pancis, it indicates UnimplementedPlatformSalesServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PlatformSalesService_ServiceDesc, srv)
}

func _PlatformSalesService_SyncPlatformSales_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncPlatformSalesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PlatformSalesServiceServer).SyncPlatformSales(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PlatformSalesService_SyncPlatformSales_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PlatformSalesServiceServer).SyncPlatformSales(ctx, req.(*SyncPlatformSalesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PlatformSalesService_ServiceDesc is the grpc.ServiceDesc for PlatformSalesService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PlatformSalesService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.sales.v1.PlatformSalesService",
	HandlerType: (*PlatformSalesServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncPlatformSales",
			Handler:    _PlatformSalesService_SyncPlatformSales_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/sales/v1/platform_sales_service.proto",
}
