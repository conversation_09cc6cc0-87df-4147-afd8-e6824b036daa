

syntax = "proto3";

package backend.proto.customer.v2;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v2";

// 这个pb 是 customer 域下共用的结构
//
// universal organizational structure
message OrganizationRef {
  // organization type enumeration
  enum Type {
    // 0 is reserved for unspecified
    TYPE_UNSPECIFIED = 0;
    // 1 is reserved for business
    BUSINESS = 1;
    // 2 is reserved for company
    COMPANY = 2;
    // 3 is reserved for enterprise
    ENTERPRISE = 3;
    // system
    // 系统级别数据, 比如系统标签, 一般用不上
    SYSTEM = 10;
  }

  // type of the organization
  Type type = 1;
  // id
  int64 id = 2;
}

// direction of the sorting
enum Direction { 
  // default value
  DIRECTION_UNSPECIFIED = 0;
  // ascending
  ASC = 1;
  // descending
  DESC = 2;
}