// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: 入参不需要指定 output_only --)

syntax = "proto3";

package backend.proto.customer.v2;

import "backend/proto/customer/v2/common.proto";
import "backend/proto/customer/v2/data_migration.proto";
import "buf/validate/validate.proto";
import "google/api/field_behavior.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v2";

// DataMigrationService 提供数据迁移服务
service DataMigrationService {
  // ImportCustomers 导入客户
  rpc ImportCustomers(ImportCustomersRequest) returns (ImportCustomersResponse);
  // ListImportHistories 列出导入历史
  rpc ListImportHistories(ListImportHistoriesRequest) returns (ListImportHistoriesResponse);
}

// ImportCustomersRequest 导入客户请求
message ImportCustomersRequest {
  // options
  message Options {
    // 遇到唯一键冲突时是否覆盖
    bool overwrite_on_conflict = 1;
  }
  // 上传文件 uri
  string upload_file_uri = 1 [(buf.validate.field).string.uri = true];
  // organization
  OrganizationRef organization = 2 [(buf.validate.field).required = true];
  // options
  Options options = 3;
  // operator staff id
  int64 staff_id = 4;
}

// ImportCustomersResponse 导入客户响应
message ImportCustomersResponse {}

// ListImportHistoriesRequest 列出导入历史请求
message ListImportHistoriesRequest {
  // filter
  message Filter {
    // ids
    repeated int64 ids = 1;
    // state
    repeated ImportHistory.State states = 2;
    // organizations
    repeated OrganizationRef organizations = 3;
  }
  // filter
  Filter filter = 1 [(buf.validate.field).required = true];
  // page size
  int32 page_size = 2;
  // page token
  string page_token = 3;
}

// ListImportHistoriesResponse 列出导入历史响应
message ListImportHistoriesResponse {
  // import histories
  repeated ImportHistory import_histories = 1;
  // 下一页令牌
  optional string next_page_token = 2;
  // 总数量
  optional int64 total_size = 3;
}