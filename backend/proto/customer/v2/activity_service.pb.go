// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0148::human-names=disabled
//     aip.dev/not-precedent: 使用first_name和last_name作为字段名 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0132::request-field-types=disabled
//     aip.dev/not-precedent: 使用自定义过滤器类型 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用customer_id作为标识符 --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用自定义响应字段 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 使用自定义请求字段结构 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用name后缀 --)syntax = "proto3";
// (-- api-linter: core::0135::response-message-name=disabled
//     aip.dev/not-precedent: 使用空resp易于拓展 --)syntax = "proto3";
// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: 不使用behavior --)syntax = "proto3";
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent:  使用自定义分页方式 --)syntax = "proto3";

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/customer/v2/activity_service.proto

package customerpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// sorting field
type ListCustomerActivityLogsRequest_Sorting_Field int32

const (
	// 默认排序
	ListCustomerActivityLogsRequest_Sorting_FIELD_UNSPECIFIED ListCustomerActivityLogsRequest_Sorting_Field = 0
	// 客户ID
	ListCustomerActivityLogsRequest_Sorting_ID ListCustomerActivityLogsRequest_Sorting_Field = 1
	// 创建时间
	ListCustomerActivityLogsRequest_Sorting_CREATED_TIME ListCustomerActivityLogsRequest_Sorting_Field = 2
	// 更新时间
	ListCustomerActivityLogsRequest_Sorting_UPDATED_TIME ListCustomerActivityLogsRequest_Sorting_Field = 3
)

// Enum value maps for ListCustomerActivityLogsRequest_Sorting_Field.
var (
	ListCustomerActivityLogsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListCustomerActivityLogsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListCustomerActivityLogsRequest_Sorting_Field) Enum() *ListCustomerActivityLogsRequest_Sorting_Field {
	p := new(ListCustomerActivityLogsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListCustomerActivityLogsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomerActivityLogsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_service_proto_enumTypes[0].Descriptor()
}

func (ListCustomerActivityLogsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_service_proto_enumTypes[0]
}

func (x ListCustomerActivityLogsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomerActivityLogsRequest_Sorting_Field.Descriptor instead.
func (ListCustomerActivityLogsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{4, 1, 0}
}

// sorting field
type ListCustomerTasksRequest_Sorting_Field int32

const (
	// 默认排序
	ListCustomerTasksRequest_Sorting_FIELD_UNSPECIFIED ListCustomerTasksRequest_Sorting_Field = 0
	// 客户ID
	ListCustomerTasksRequest_Sorting_ID ListCustomerTasksRequest_Sorting_Field = 1
	// 创建时间
	ListCustomerTasksRequest_Sorting_CREATED_TIME ListCustomerTasksRequest_Sorting_Field = 2
	// 更新时间
	ListCustomerTasksRequest_Sorting_UPDATED_TIME ListCustomerTasksRequest_Sorting_Field = 3
)

// Enum value maps for ListCustomerTasksRequest_Sorting_Field.
var (
	ListCustomerTasksRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListCustomerTasksRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListCustomerTasksRequest_Sorting_Field) Enum() *ListCustomerTasksRequest_Sorting_Field {
	p := new(ListCustomerTasksRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListCustomerTasksRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomerTasksRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_service_proto_enumTypes[1].Descriptor()
}

func (ListCustomerTasksRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_service_proto_enumTypes[1]
}

func (x ListCustomerTasksRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomerTasksRequest_Sorting_Field.Descriptor instead.
func (ListCustomerTasksRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{14, 1, 0}
}

// sorting field
type ListLifeCyclesRequest_Sorting_Field int32

const (
	// 默认排序
	ListLifeCyclesRequest_Sorting_FIELD_UNSPECIFIED ListLifeCyclesRequest_Sorting_Field = 0
	// ID
	ListLifeCyclesRequest_Sorting_ID ListLifeCyclesRequest_Sorting_Field = 1
	// 创建时间
	ListLifeCyclesRequest_Sorting_CREATED_TIME ListLifeCyclesRequest_Sorting_Field = 2
	// 更新时间
	ListLifeCyclesRequest_Sorting_UPDATED_TIME ListLifeCyclesRequest_Sorting_Field = 3
)

// Enum value maps for ListLifeCyclesRequest_Sorting_Field.
var (
	ListLifeCyclesRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListLifeCyclesRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListLifeCyclesRequest_Sorting_Field) Enum() *ListLifeCyclesRequest_Sorting_Field {
	p := new(ListLifeCyclesRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListLifeCyclesRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListLifeCyclesRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_service_proto_enumTypes[2].Descriptor()
}

func (ListLifeCyclesRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_service_proto_enumTypes[2]
}

func (x ListLifeCyclesRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListLifeCyclesRequest_Sorting_Field.Descriptor instead.
func (ListLifeCyclesRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{22, 1, 0}
}

// sorting field
type ListActionStatesRequest_Sorting_Field int32

const (
	// 默认排序
	ListActionStatesRequest_Sorting_FIELD_UNSPECIFIED ListActionStatesRequest_Sorting_Field = 0
	// ID
	ListActionStatesRequest_Sorting_ID ListActionStatesRequest_Sorting_Field = 1
	// 创建时间
	ListActionStatesRequest_Sorting_CREATED_TIME ListActionStatesRequest_Sorting_Field = 2
	// 更新时间
	ListActionStatesRequest_Sorting_UPDATED_TIME ListActionStatesRequest_Sorting_Field = 3
)

// Enum value maps for ListActionStatesRequest_Sorting_Field.
var (
	ListActionStatesRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListActionStatesRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListActionStatesRequest_Sorting_Field) Enum() *ListActionStatesRequest_Sorting_Field {
	p := new(ListActionStatesRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListActionStatesRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListActionStatesRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_service_proto_enumTypes[3].Descriptor()
}

func (ListActionStatesRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_service_proto_enumTypes[3]
}

func (x ListActionStatesRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListActionStatesRequest_Sorting_Field.Descriptor instead.
func (ListActionStatesRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{30, 1, 0}
}

// sorting field
type ListTagsRequest_Sorting_Field int32

const (
	// 默认排序
	ListTagsRequest_Sorting_FIELD_UNSPECIFIED ListTagsRequest_Sorting_Field = 0
	// ID
	ListTagsRequest_Sorting_ID ListTagsRequest_Sorting_Field = 1
	// 创建时间
	ListTagsRequest_Sorting_CREATED_TIME ListTagsRequest_Sorting_Field = 2
	// 更新时间
	ListTagsRequest_Sorting_UPDATED_TIME ListTagsRequest_Sorting_Field = 3
)

// Enum value maps for ListTagsRequest_Sorting_Field.
var (
	ListTagsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListTagsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListTagsRequest_Sorting_Field) Enum() *ListTagsRequest_Sorting_Field {
	p := new(ListTagsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListTagsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListTagsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_service_proto_enumTypes[4].Descriptor()
}

func (ListTagsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_service_proto_enumTypes[4]
}

func (x ListTagsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListTagsRequest_Sorting_Field.Descriptor instead.
func (ListTagsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{38, 1, 0}
}

// sorting field
type ListCustomerTagsRequest_Sorting_Field int32

const (
	// 默认排序
	ListCustomerTagsRequest_Sorting_FIELD_UNSPECIFIED ListCustomerTagsRequest_Sorting_Field = 0
	// ID
	ListCustomerTagsRequest_Sorting_ID ListCustomerTagsRequest_Sorting_Field = 1
	// 创建时间
	ListCustomerTagsRequest_Sorting_CREATED_TIME ListCustomerTagsRequest_Sorting_Field = 2
	// 更新时间
	ListCustomerTagsRequest_Sorting_UPDATED_TIME ListCustomerTagsRequest_Sorting_Field = 3
)

// Enum value maps for ListCustomerTagsRequest_Sorting_Field.
var (
	ListCustomerTagsRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListCustomerTagsRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListCustomerTagsRequest_Sorting_Field) Enum() *ListCustomerTagsRequest_Sorting_Field {
	p := new(ListCustomerTagsRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListCustomerTagsRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomerTagsRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_service_proto_enumTypes[5].Descriptor()
}

func (ListCustomerTagsRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_service_proto_enumTypes[5]
}

func (x ListCustomerTagsRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomerTagsRequest_Sorting_Field.Descriptor instead.
func (ListCustomerTagsRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{46, 1, 0}
}

// sorting field
type ListNotesRequest_Sorting_Field int32

const (
	// 默认排序
	ListNotesRequest_Sorting_FIELD_UNSPECIFIED ListNotesRequest_Sorting_Field = 0
	// ID
	ListNotesRequest_Sorting_ID ListNotesRequest_Sorting_Field = 1
	// 创建时间
	ListNotesRequest_Sorting_CREATED_TIME ListNotesRequest_Sorting_Field = 2
	// 更新时间
	ListNotesRequest_Sorting_UPDATED_TIME ListNotesRequest_Sorting_Field = 3
)

// Enum value maps for ListNotesRequest_Sorting_Field.
var (
	ListNotesRequest_Sorting_Field_name = map[int32]string{
		0: "FIELD_UNSPECIFIED",
		1: "ID",
		2: "CREATED_TIME",
		3: "UPDATED_TIME",
	}
	ListNotesRequest_Sorting_Field_value = map[string]int32{
		"FIELD_UNSPECIFIED": 0,
		"ID":                1,
		"CREATED_TIME":      2,
		"UPDATED_TIME":      3,
	}
)

func (x ListNotesRequest_Sorting_Field) Enum() *ListNotesRequest_Sorting_Field {
	p := new(ListNotesRequest_Sorting_Field)
	*p = x
	return p
}

func (x ListNotesRequest_Sorting_Field) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListNotesRequest_Sorting_Field) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_activity_service_proto_enumTypes[6].Descriptor()
}

func (ListNotesRequest_Sorting_Field) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_activity_service_proto_enumTypes[6]
}

func (x ListNotesRequest_Sorting_Field) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListNotesRequest_Sorting_Field.Descriptor instead.
func (ListNotesRequest_Sorting_Field) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{52, 1, 0}
}

// CreateCustomerRequest 创建用户活动日志
type CreateCustomerActivityLogRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 客户名称
	CustomerName string `protobuf:"bytes,9,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// 客户电话
	CustomerPhoneNumber string `protobuf:"bytes,10,opt,name=customer_phone_number,json=customerPhoneNumber,proto3" json:"customer_phone_number,omitempty"`
	// 互动数据
	Action *ActivityLog_Action `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	// 公司名
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店名
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 记录来源
	Source        *SystemSource `protobuf:"bytes,5,opt,name=source,proto3,oneof" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerActivityLogRequest) Reset() {
	*x = CreateCustomerActivityLogRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerActivityLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerActivityLogRequest) ProtoMessage() {}

func (x *CreateCustomerActivityLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerActivityLogRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerActivityLogRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCustomerActivityLogRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateCustomerActivityLogRequest) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *CreateCustomerActivityLogRequest) GetCustomerPhoneNumber() string {
	if x != nil {
		return x.CustomerPhoneNumber
	}
	return ""
}

func (x *CreateCustomerActivityLogRequest) GetAction() *ActivityLog_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *CreateCustomerActivityLogRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCustomerActivityLogRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateCustomerActivityLogRequest) GetSource() *SystemSource {
	if x != nil {
		return x.Source
	}
	return nil
}

// CreateCustomerActivityLogResponse 创建用户活动日志
type CreateCustomerActivityLogResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ActivityLog
	Log           *ActivityLog `protobuf:"bytes,1,opt,name=log,proto3" json:"log,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerActivityLogResponse) Reset() {
	*x = CreateCustomerActivityLogResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerActivityLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerActivityLogResponse) ProtoMessage() {}

func (x *CreateCustomerActivityLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerActivityLogResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerActivityLogResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateCustomerActivityLogResponse) GetLog() *ActivityLog {
	if x != nil {
		return x.Log
	}
	return nil
}

// UpdateCustomerActivityLogRequest 更新用户活动日志
type UpdateCustomerActivityLogRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户活动日志记录ID
	LogId int64 `protobuf:"varint,1,opt,name=log_id,json=logId,proto3" json:"log_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 互动数据
	Action        *ActivityLog_Action `protobuf:"bytes,10,opt,name=action,proto3,oneof" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerActivityLogRequest) Reset() {
	*x = UpdateCustomerActivityLogRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerActivityLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerActivityLogRequest) ProtoMessage() {}

func (x *UpdateCustomerActivityLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerActivityLogRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomerActivityLogRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateCustomerActivityLogRequest) GetLogId() int64 {
	if x != nil {
		return x.LogId
	}
	return 0
}

func (x *UpdateCustomerActivityLogRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateCustomerActivityLogRequest) GetAction() *ActivityLog_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

// UpdateCustomerActivityLogResponse 创建用户活动日志
type UpdateCustomerActivityLogResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ActivityLog
	Log           *ActivityLog `protobuf:"bytes,1,opt,name=log,proto3" json:"log,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerActivityLogResponse) Reset() {
	*x = UpdateCustomerActivityLogResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerActivityLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerActivityLogResponse) ProtoMessage() {}

func (x *UpdateCustomerActivityLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerActivityLogResponse.ProtoReflect.Descriptor instead.
func (*UpdateCustomerActivityLogResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateCustomerActivityLogResponse) GetLog() *ActivityLog {
	if x != nil {
		return x.Log
	}
	return nil
}

// ListCustomerActivityLogsRequest 获取客户历史记录请求
type ListCustomerActivityLogsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListCustomerActivityLogsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListCustomerActivityLogsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌, 为空则不分页
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerActivityLogsRequest) Reset() {
	*x = ListCustomerActivityLogsRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerActivityLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerActivityLogsRequest) ProtoMessage() {}

func (x *ListCustomerActivityLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerActivityLogsRequest.ProtoReflect.Descriptor instead.
func (*ListCustomerActivityLogsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListCustomerActivityLogsRequest) GetFilter() *ListCustomerActivityLogsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomerActivityLogsRequest) GetSorting() *ListCustomerActivityLogsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListCustomerActivityLogsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCustomerActivityLogsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListCustomerActivityLogsResponse 获取客户历史记录响应
type ListCustomerActivityLogsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 历史记录列表
	ActivityLogs []*ActivityLog `protobuf:"bytes,1,rep,name=activity_logs,json=activityLogs,proto3" json:"activity_logs,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerActivityLogsResponse) Reset() {
	*x = ListCustomerActivityLogsResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerActivityLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerActivityLogsResponse) ProtoMessage() {}

func (x *ListCustomerActivityLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerActivityLogsResponse.ProtoReflect.Descriptor instead.
func (*ListCustomerActivityLogsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListCustomerActivityLogsResponse) GetActivityLogs() []*ActivityLog {
	if x != nil {
		return x.ActivityLogs
	}
	return nil
}

func (x *ListCustomerActivityLogsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListCustomerActivityLogsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// ConvertCustomerRequest 转换客户状态请求
type ConvertCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 转换前的LeadID
	CustomerId    int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertCustomerRequest) Reset() {
	*x = ConvertCustomerRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertCustomerRequest) ProtoMessage() {}

func (x *ConvertCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertCustomerRequest.ProtoReflect.Descriptor instead.
func (*ConvertCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{6}
}

func (x *ConvertCustomerRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// ConvertCustomerResponse 转换客户状态响应
type ConvertCustomerResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 转换后的CustomerID
	CustomerId    int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertCustomerResponse) Reset() {
	*x = ConvertCustomerResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertCustomerResponse) ProtoMessage() {}

func (x *ConvertCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertCustomerResponse.ProtoReflect.Descriptor instead.
func (*ConvertCustomerResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{7}
}

func (x *ConvertCustomerResponse) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// ConvertCustomersAttributeRequest 批量转换客户属性请求
type ConvertCustomersAttributeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户IDs
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 生命周期
	CustomizeLifeCycleId *int64 `protobuf:"varint,2,opt,name=customize_life_cycle_id,json=customizeLifeCycleId,proto3,oneof" json:"customize_life_cycle_id,omitempty"`
	// 行动状态
	CustomizeActionStateId *int64 `protobuf:"varint,3,opt,name=customize_action_state_id,json=customizeActionStateId,proto3,oneof" json:"customize_action_state_id,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ConvertCustomersAttributeRequest) Reset() {
	*x = ConvertCustomersAttributeRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertCustomersAttributeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertCustomersAttributeRequest) ProtoMessage() {}

func (x *ConvertCustomersAttributeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertCustomersAttributeRequest.ProtoReflect.Descriptor instead.
func (*ConvertCustomersAttributeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{8}
}

func (x *ConvertCustomersAttributeRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ConvertCustomersAttributeRequest) GetCustomizeLifeCycleId() int64 {
	if x != nil && x.CustomizeLifeCycleId != nil {
		return *x.CustomizeLifeCycleId
	}
	return 0
}

func (x *ConvertCustomersAttributeRequest) GetCustomizeActionStateId() int64 {
	if x != nil && x.CustomizeActionStateId != nil {
		return *x.CustomizeActionStateId
	}
	return 0
}

// ConvertCustomersAttributeRequest 批量转换客户属性响应
type ConvertCustomersAttributeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertCustomersAttributeResponse) Reset() {
	*x = ConvertCustomersAttributeResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertCustomersAttributeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertCustomersAttributeResponse) ProtoMessage() {}

func (x *ConvertCustomersAttributeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertCustomersAttributeResponse.ProtoReflect.Descriptor instead.
func (*ConvertCustomersAttributeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{9}
}

// CreateCustomerTaskRequest 创建客户任务列表
type CreateCustomerTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 任务名称
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
	// 任务分配员工
	AllocateStaffId *int64 `protobuf:"varint,11,opt,name=allocate_staff_id,json=allocateStaffId,proto3,oneof" json:"allocate_staff_id,omitempty"`
	// 任务预期完成的时间
	CompleteTime  *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=complete_time,json=completeTime,proto3,oneof" json:"complete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerTaskRequest) Reset() {
	*x = CreateCustomerTaskRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerTaskRequest) ProtoMessage() {}

func (x *CreateCustomerTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{10}
}

func (x *CreateCustomerTaskRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateCustomerTaskRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCustomerTaskRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateCustomerTaskRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateCustomerTaskRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateCustomerTaskRequest) GetAllocateStaffId() int64 {
	if x != nil && x.AllocateStaffId != nil {
		return *x.AllocateStaffId
	}
	return 0
}

func (x *CreateCustomerTaskRequest) GetCompleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompleteTime
	}
	return nil
}

// CreateCustomerTaskResponse 创建客户任务列表
type CreateCustomerTaskResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Task
	Task          *Task `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerTaskResponse) Reset() {
	*x = CreateCustomerTaskResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerTaskResponse) ProtoMessage() {}

func (x *CreateCustomerTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerTaskResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{11}
}

func (x *CreateCustomerTaskResponse) GetTask() *Task {
	if x != nil {
		return x.Task
	}
	return nil
}

// UpdateCustomerTaskRequest 更新客户任务
type UpdateCustomerTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 任务ID
	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 任务名称
	Name *string `protobuf:"bytes,10,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 任务分配员工
	AllocateStaffId *int64 `protobuf:"varint,11,opt,name=allocate_staff_id,json=allocateStaffId,proto3,oneof" json:"allocate_staff_id,omitempty"`
	// 任务预期完成的时间
	CompleteTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=complete_time,json=completeTime,proto3,oneof" json:"complete_time,omitempty"`
	// 任务状态
	State         *Task_State `protobuf:"varint,13,opt,name=state,proto3,enum=backend.proto.customer.v2.Task_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerTaskRequest) Reset() {
	*x = UpdateCustomerTaskRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerTaskRequest) ProtoMessage() {}

func (x *UpdateCustomerTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerTaskRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomerTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{12}
}

func (x *UpdateCustomerTaskRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UpdateCustomerTaskRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateCustomerTaskRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateCustomerTaskRequest) GetAllocateStaffId() int64 {
	if x != nil && x.AllocateStaffId != nil {
		return *x.AllocateStaffId
	}
	return 0
}

func (x *UpdateCustomerTaskRequest) GetCompleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompleteTime
	}
	return nil
}

func (x *UpdateCustomerTaskRequest) GetState() Task_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Task_STATE_UNSPECIFIED
}

// UpdateCustomerTaskResponse 更新客户任务
type UpdateCustomerTaskResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Task
	Task          *Task `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerTaskResponse) Reset() {
	*x = UpdateCustomerTaskResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerTaskResponse) ProtoMessage() {}

func (x *UpdateCustomerTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerTaskResponse.ProtoReflect.Descriptor instead.
func (*UpdateCustomerTaskResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateCustomerTaskResponse) GetTask() *Task {
	if x != nil {
		return x.Task
	}
	return nil
}

// ListCustomerTasksRequest 获取客户任务列表请求
type ListCustomerTasksRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListCustomerTasksRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListCustomerTasksRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌, 为空则不分页
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerTasksRequest) Reset() {
	*x = ListCustomerTasksRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerTasksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTasksRequest) ProtoMessage() {}

func (x *ListCustomerTasksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTasksRequest.ProtoReflect.Descriptor instead.
func (*ListCustomerTasksRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{14}
}

func (x *ListCustomerTasksRequest) GetFilter() *ListCustomerTasksRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomerTasksRequest) GetSorting() *ListCustomerTasksRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListCustomerTasksRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCustomerTasksRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListCustomerTasksResponse 获取客户任务列表响应
type ListCustomerTasksResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 任务列表
	Tasks []*Task `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerTasksResponse) Reset() {
	*x = ListCustomerTasksResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerTasksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTasksResponse) ProtoMessage() {}

func (x *ListCustomerTasksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTasksResponse.ProtoReflect.Descriptor instead.
func (*ListCustomerTasksResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{15}
}

func (x *ListCustomerTasksResponse) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

func (x *ListCustomerTasksResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListCustomerTasksResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// DeleteCustomerTaskRequest 删除客户任务列表请求
type DeleteCustomerTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 任务ID
	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// 操作员工ID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomerTaskRequest) Reset() {
	*x = DeleteCustomerTaskRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomerTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerTaskRequest) ProtoMessage() {}

func (x *DeleteCustomerTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerTaskRequest.ProtoReflect.Descriptor instead.
func (*DeleteCustomerTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteCustomerTaskRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *DeleteCustomerTaskRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// DeleteCustomerTaskResponse 删除客户任务列表响应
type DeleteCustomerTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomerTaskResponse) Reset() {
	*x = DeleteCustomerTaskResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomerTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerTaskResponse) ProtoMessage() {}

func (x *DeleteCustomerTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerTaskResponse.ProtoReflect.Descriptor instead.
func (*DeleteCustomerTaskResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{17}
}

// CreateLifeCycleRequest 创建生命周期请求
type CreateLifeCycleRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort          int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateLifeCycleRequest) Reset() {
	*x = CreateLifeCycleRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLifeCycleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLifeCycleRequest) ProtoMessage() {}

func (x *CreateLifeCycleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLifeCycleRequest.ProtoReflect.Descriptor instead.
func (*CreateLifeCycleRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{18}
}

func (x *CreateLifeCycleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateLifeCycleRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateLifeCycleRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateLifeCycleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateLifeCycleRequest) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// CreateLifeCycleResponse 创建生命周期响应
type CreateLifeCycleResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// LifeCycle
	LifeCycle     *LifeCycle `protobuf:"bytes,1,opt,name=life_cycle,json=lifeCycle,proto3" json:"life_cycle,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateLifeCycleResponse) Reset() {
	*x = CreateLifeCycleResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLifeCycleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLifeCycleResponse) ProtoMessage() {}

func (x *CreateLifeCycleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLifeCycleResponse.ProtoReflect.Descriptor instead.
func (*CreateLifeCycleResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{19}
}

func (x *CreateLifeCycleResponse) GetLifeCycle() *LifeCycle {
	if x != nil {
		return x.LifeCycle
	}
	return nil
}

// UpdateLifeCyclesRequest 批量更新生命周期请求
type UpdateLifeCyclesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 批量更新数据
	Updates []*UpdateLifeCyclesRequest_UpdateLifeCycle `protobuf:"bytes,1,rep,name=updates,proto3" json:"updates,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 公司ID
	CompanyId     int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLifeCyclesRequest) Reset() {
	*x = UpdateLifeCyclesRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLifeCyclesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLifeCyclesRequest) ProtoMessage() {}

func (x *UpdateLifeCyclesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLifeCyclesRequest.ProtoReflect.Descriptor instead.
func (*UpdateLifeCyclesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateLifeCyclesRequest) GetUpdates() []*UpdateLifeCyclesRequest_UpdateLifeCycle {
	if x != nil {
		return x.Updates
	}
	return nil
}

func (x *UpdateLifeCyclesRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateLifeCyclesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// UpdateLifeCyclesResponse 批量更更新生命周期响应
type UpdateLifeCyclesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLifeCyclesResponse) Reset() {
	*x = UpdateLifeCyclesResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLifeCyclesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLifeCyclesResponse) ProtoMessage() {}

func (x *UpdateLifeCyclesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLifeCyclesResponse.ProtoReflect.Descriptor instead.
func (*UpdateLifeCyclesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{21}
}

// ListLifeCyclesRequest 获取生命周期列表请求
type ListLifeCyclesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListLifeCyclesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListLifeCyclesRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌, 为空则不分页
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLifeCyclesRequest) Reset() {
	*x = ListLifeCyclesRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLifeCyclesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLifeCyclesRequest) ProtoMessage() {}

func (x *ListLifeCyclesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLifeCyclesRequest.ProtoReflect.Descriptor instead.
func (*ListLifeCyclesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{22}
}

func (x *ListLifeCyclesRequest) GetFilter() *ListLifeCyclesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListLifeCyclesRequest) GetSorting() *ListLifeCyclesRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListLifeCyclesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListLifeCyclesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListLifeCyclesResponse 获取生命周期列表响应
type ListLifeCyclesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 生命周期列表响应
	LifeCycles []*LifeCycle `protobuf:"bytes,1,rep,name=life_cycles,json=lifeCycles,proto3" json:"life_cycles,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLifeCyclesResponse) Reset() {
	*x = ListLifeCyclesResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLifeCyclesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLifeCyclesResponse) ProtoMessage() {}

func (x *ListLifeCyclesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLifeCyclesResponse.ProtoReflect.Descriptor instead.
func (*ListLifeCyclesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{23}
}

func (x *ListLifeCyclesResponse) GetLifeCycles() []*LifeCycle {
	if x != nil {
		return x.LifeCycles
	}
	return nil
}

func (x *ListLifeCyclesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListLifeCyclesResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// DeleteLifeCycleRequest 删除生命周期请求
type DeleteLifeCycleRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 操作员工ID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteLifeCycleRequest) Reset() {
	*x = DeleteLifeCycleRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteLifeCycleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLifeCycleRequest) ProtoMessage() {}

func (x *DeleteLifeCycleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLifeCycleRequest.ProtoReflect.Descriptor instead.
func (*DeleteLifeCycleRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteLifeCycleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteLifeCycleRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// DeleteLifeCycleResponse 删除生命周期响应
type DeleteLifeCycleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteLifeCycleResponse) Reset() {
	*x = DeleteLifeCycleResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteLifeCycleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLifeCycleResponse) ProtoMessage() {}

func (x *DeleteLifeCycleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLifeCycleResponse.ProtoReflect.Descriptor instead.
func (*DeleteLifeCycleResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{25}
}

// CreateActionStateRequest 创建行动状态请求
type CreateActionStateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	// 颜色
	Color         string `protobuf:"bytes,12,opt,name=color,proto3" json:"color,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateActionStateRequest) Reset() {
	*x = CreateActionStateRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateActionStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateActionStateRequest) ProtoMessage() {}

func (x *CreateActionStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateActionStateRequest.ProtoReflect.Descriptor instead.
func (*CreateActionStateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{26}
}

func (x *CreateActionStateRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateActionStateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateActionStateRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateActionStateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateActionStateRequest) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *CreateActionStateRequest) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// CreateActionStateResponse 创建行动状态响应
type CreateActionStateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ActionState
	ActionState   *ActionState `protobuf:"bytes,1,opt,name=action_state,json=actionState,proto3" json:"action_state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateActionStateResponse) Reset() {
	*x = CreateActionStateResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateActionStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateActionStateResponse) ProtoMessage() {}

func (x *CreateActionStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateActionStateResponse.ProtoReflect.Descriptor instead.
func (*CreateActionStateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{27}
}

func (x *CreateActionStateResponse) GetActionState() *ActionState {
	if x != nil {
		return x.ActionState
	}
	return nil
}

// UpdateActionStatesRequest 批量更新行动状态请求
type UpdateActionStatesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 批量更新数据
	Updates []*UpdateActionStatesRequest_UpdateActionState `protobuf:"bytes,1,rep,name=updates,proto3" json:"updates,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 公司ID
	CompanyId     int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateActionStatesRequest) Reset() {
	*x = UpdateActionStatesRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateActionStatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateActionStatesRequest) ProtoMessage() {}

func (x *UpdateActionStatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateActionStatesRequest.ProtoReflect.Descriptor instead.
func (*UpdateActionStatesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{28}
}

func (x *UpdateActionStatesRequest) GetUpdates() []*UpdateActionStatesRequest_UpdateActionState {
	if x != nil {
		return x.Updates
	}
	return nil
}

func (x *UpdateActionStatesRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateActionStatesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// UpdateActionStatesResponse 批量更新行动状态响应
type UpdateActionsStatesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateActionsStatesResponse) Reset() {
	*x = UpdateActionsStatesResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateActionsStatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateActionsStatesResponse) ProtoMessage() {}

func (x *UpdateActionsStatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateActionsStatesResponse.ProtoReflect.Descriptor instead.
func (*UpdateActionsStatesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{29}
}

// ListActionStatesRequest 获取行动状态列表请求
type ListActionStatesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListActionStatesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListActionStatesRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌, 为空则不分页
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActionStatesRequest) Reset() {
	*x = ListActionStatesRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActionStatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActionStatesRequest) ProtoMessage() {}

func (x *ListActionStatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActionStatesRequest.ProtoReflect.Descriptor instead.
func (*ListActionStatesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{30}
}

func (x *ListActionStatesRequest) GetFilter() *ListActionStatesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListActionStatesRequest) GetSorting() *ListActionStatesRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListActionStatesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListActionStatesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListActionStatesResponse 获取行动状态列表响应
type ListActionStatesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 行动状态列表
	ActionStates []*ActionState `protobuf:"bytes,1,rep,name=action_states,json=actionStates,proto3" json:"action_states,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActionStatesResponse) Reset() {
	*x = ListActionStatesResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActionStatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActionStatesResponse) ProtoMessage() {}

func (x *ListActionStatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActionStatesResponse.ProtoReflect.Descriptor instead.
func (*ListActionStatesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{31}
}

func (x *ListActionStatesResponse) GetActionStates() []*ActionState {
	if x != nil {
		return x.ActionStates
	}
	return nil
}

func (x *ListActionStatesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListActionStatesResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// DeleteActionStateRequest 删除行动状态请求
type DeleteActionStateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 操作员工ID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteActionStateRequest) Reset() {
	*x = DeleteActionStateRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteActionStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteActionStateRequest) ProtoMessage() {}

func (x *DeleteActionStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteActionStateRequest.ProtoReflect.Descriptor instead.
func (*DeleteActionStateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{32}
}

func (x *DeleteActionStateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteActionStateRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// DeleteActionStateResponse 删除行动状态响应
type DeleteActionStateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteActionStateResponse) Reset() {
	*x = DeleteActionStateResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteActionStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteActionStateResponse) ProtoMessage() {}

func (x *DeleteActionStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteActionStateResponse.ProtoReflect.Descriptor instead.
func (*DeleteActionStateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{33}
}

// CreateTagRequest 创建标签请求
type CreateTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort          int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTagRequest) Reset() {
	*x = CreateTagRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTagRequest) ProtoMessage() {}

func (x *CreateTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTagRequest.ProtoReflect.Descriptor instead.
func (*CreateTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{34}
}

func (x *CreateTagRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateTagRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateTagRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateTagRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTagRequest) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// CreateTagResponse 创建标签响应
type CreateTagResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Tag
	Tag           *Tag `protobuf:"bytes,1,opt,name=tag,proto3" json:"tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTagResponse) Reset() {
	*x = CreateTagResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTagResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTagResponse) ProtoMessage() {}

func (x *CreateTagResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTagResponse.ProtoReflect.Descriptor instead.
func (*CreateTagResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{35}
}

func (x *CreateTagResponse) GetTag() *Tag {
	if x != nil {
		return x.Tag
	}
	return nil
}

// UpdateTagsRequest 批量更新标签请求
type UpdateTagsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 批量更新数据
	Updates []*UpdateTagsRequest_UpdateTag `protobuf:"bytes,1,rep,name=updates,proto3" json:"updates,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 公司ID
	CompanyId     int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTagsRequest) Reset() {
	*x = UpdateTagsRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTagsRequest) ProtoMessage() {}

func (x *UpdateTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTagsRequest.ProtoReflect.Descriptor instead.
func (*UpdateTagsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateTagsRequest) GetUpdates() []*UpdateTagsRequest_UpdateTag {
	if x != nil {
		return x.Updates
	}
	return nil
}

func (x *UpdateTagsRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateTagsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// UpdateTagsResponse 批量更新标签响应
type UpdateTagsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTagsResponse) Reset() {
	*x = UpdateTagsResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTagsResponse) ProtoMessage() {}

func (x *UpdateTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTagsResponse.ProtoReflect.Descriptor instead.
func (*UpdateTagsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{37}
}

// ListTagsRequest 获取标签列表请求
type ListTagsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListTagsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListTagsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌, 为空则不分页
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTagsRequest) Reset() {
	*x = ListTagsRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTagsRequest) ProtoMessage() {}

func (x *ListTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTagsRequest.ProtoReflect.Descriptor instead.
func (*ListTagsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{38}
}

func (x *ListTagsRequest) GetFilter() *ListTagsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListTagsRequest) GetSorting() *ListTagsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListTagsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTagsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListTagsResponse 获取标签列表响应
type ListTagsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 标签列表
	Tags []*Tag `protobuf:"bytes,1,rep,name=tags,proto3" json:"tags,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTagsResponse) Reset() {
	*x = ListTagsResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTagsResponse) ProtoMessage() {}

func (x *ListTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTagsResponse.ProtoReflect.Descriptor instead.
func (*ListTagsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{39}
}

func (x *ListTagsResponse) GetTags() []*Tag {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ListTagsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListTagsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// DeleteTagRequest 删除标签请求
type DeleteTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 操作员工ID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTagRequest) Reset() {
	*x = DeleteTagRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTagRequest) ProtoMessage() {}

func (x *DeleteTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTagRequest.ProtoReflect.Descriptor instead.
func (*DeleteTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{40}
}

func (x *DeleteTagRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteTagRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// DeleteTagResponse 删除标签响应
type DeleteTagResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTagResponse) Reset() {
	*x = DeleteTagResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTagResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTagResponse) ProtoMessage() {}

func (x *DeleteTagResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTagResponse.ProtoReflect.Descriptor instead.
func (*DeleteTagResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{41}
}

// CoverCustomerTagRequest 覆盖客户标签请求
type CoverCustomerTagRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,3,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 标签列表
	TagIds        []int64 `protobuf:"varint,5,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CoverCustomerTagRequest) Reset() {
	*x = CoverCustomerTagRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CoverCustomerTagRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoverCustomerTagRequest) ProtoMessage() {}

func (x *CoverCustomerTagRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoverCustomerTagRequest.ProtoReflect.Descriptor instead.
func (*CoverCustomerTagRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{42}
}

func (x *CoverCustomerTagRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CoverCustomerTagRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CoverCustomerTagRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CoverCustomerTagRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CoverCustomerTagRequest) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

// CoverCustomerTagResponse 覆盖客户标签响应
type CoverCustomerTagResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CoverCustomerTagResponse) Reset() {
	*x = CoverCustomerTagResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CoverCustomerTagResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CoverCustomerTagResponse) ProtoMessage() {}

func (x *CoverCustomerTagResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CoverCustomerTagResponse.ProtoReflect.Descriptor instead.
func (*CoverCustomerTagResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{43}
}

// BatchAddCustomersTagsRequest 批量添加客户标签请求
type BatchAddCustomersTagsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 客户ID
	CustomerIds []int64 `protobuf:"varint,3,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 标签列表
	TagIds        []int64 `protobuf:"varint,5,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchAddCustomersTagsRequest) Reset() {
	*x = BatchAddCustomersTagsRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchAddCustomersTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchAddCustomersTagsRequest) ProtoMessage() {}

func (x *BatchAddCustomersTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchAddCustomersTagsRequest.ProtoReflect.Descriptor instead.
func (*BatchAddCustomersTagsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{44}
}

func (x *BatchAddCustomersTagsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *BatchAddCustomersTagsRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *BatchAddCustomersTagsRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *BatchAddCustomersTagsRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *BatchAddCustomersTagsRequest) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

// BatchAddCustomersTagsResponse 批量添加客户标签响应
type BatchAddCustomersTagsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchAddCustomersTagsResponse) Reset() {
	*x = BatchAddCustomersTagsResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchAddCustomersTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchAddCustomersTagsResponse) ProtoMessage() {}

func (x *BatchAddCustomersTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchAddCustomersTagsResponse.ProtoReflect.Descriptor instead.
func (*BatchAddCustomersTagsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{45}
}

// ListCustomerTagsRequest 获取客户标签列表请求
type ListCustomerTagsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListCustomerTagsRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListCustomerTagsRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌, 为空则不分页
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerTagsRequest) Reset() {
	*x = ListCustomerTagsRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerTagsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTagsRequest) ProtoMessage() {}

func (x *ListCustomerTagsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTagsRequest.ProtoReflect.Descriptor instead.
func (*ListCustomerTagsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{46}
}

func (x *ListCustomerTagsRequest) GetFilter() *ListCustomerTagsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomerTagsRequest) GetSorting() *ListCustomerTagsRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListCustomerTagsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListCustomerTagsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListCustomerTagsResponse 获取客户标签列表响应
type ListCustomerTagsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户标签列表
	CustomerTags []*CustomerTag `protobuf:"bytes,1,rep,name=customer_tags,json=customerTags,proto3" json:"customer_tags,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerTagsResponse) Reset() {
	*x = ListCustomerTagsResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerTagsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTagsResponse) ProtoMessage() {}

func (x *ListCustomerTagsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTagsResponse.ProtoReflect.Descriptor instead.
func (*ListCustomerTagsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{47}
}

func (x *ListCustomerTagsResponse) GetCustomerTags() []*CustomerTag {
	if x != nil {
		return x.CustomerTags
	}
	return nil
}

func (x *ListCustomerTagsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListCustomerTagsResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// CreateNoteRequest 创建备注请求
type CreateNoteRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,4,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 备注内容
	Text string `protobuf:"bytes,10,opt,name=text,proto3" json:"text,omitempty"`
	// 来源
	Source        *SystemSource `protobuf:"bytes,11,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNoteRequest) Reset() {
	*x = CreateNoteRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNoteRequest) ProtoMessage() {}

func (x *CreateNoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNoteRequest.ProtoReflect.Descriptor instead.
func (*CreateNoteRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{48}
}

func (x *CreateNoteRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateNoteRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateNoteRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateNoteRequest) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *CreateNoteRequest) GetSource() *SystemSource {
	if x != nil {
		return x.Source
	}
	return nil
}

// CreateNoteResponse 创建备注响应
type CreateNoteResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Note
	Note          *Note `protobuf:"bytes,1,opt,name=note,proto3" json:"note,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNoteResponse) Reset() {
	*x = CreateNoteResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNoteResponse) ProtoMessage() {}

func (x *CreateNoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNoteResponse.ProtoReflect.Descriptor instead.
func (*CreateNoteResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{49}
}

func (x *CreateNoteResponse) GetNote() *Note {
	if x != nil {
		return x.Note
	}
	return nil
}

// UpdateNotesRequest 批量更新备注请求
type UpdateNotesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 来源
	Source *SystemSource `protobuf:"bytes,2,opt,name=source,proto3" json:"source,omitempty"`
	// 备注内容
	Text          *string `protobuf:"bytes,3,opt,name=text,proto3,oneof" json:"text,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateNotesRequest) Reset() {
	*x = UpdateNotesRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotesRequest) ProtoMessage() {}

func (x *UpdateNotesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotesRequest.ProtoReflect.Descriptor instead.
func (*UpdateNotesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{50}
}

func (x *UpdateNotesRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateNotesRequest) GetSource() *SystemSource {
	if x != nil {
		return x.Source
	}
	return nil
}

func (x *UpdateNotesRequest) GetText() string {
	if x != nil && x.Text != nil {
		return *x.Text
	}
	return ""
}

// UpdateNotesResponse 批量更新备注响应
type UpdateNotesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Note
	Note          *Note `protobuf:"bytes,1,opt,name=note,proto3" json:"note,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateNotesResponse) Reset() {
	*x = UpdateNotesResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateNotesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateNotesResponse) ProtoMessage() {}

func (x *UpdateNotesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateNotesResponse.ProtoReflect.Descriptor instead.
func (*UpdateNotesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{51}
}

func (x *UpdateNotesResponse) GetNote() *Note {
	if x != nil {
		return x.Note
	}
	return nil
}

// ListNotesRequest 获取备注列表请求
type ListNotesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListNotesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 排序
	Sorting *ListNotesRequest_Sorting `protobuf:"bytes,2,opt,name=sorting,proto3" json:"sorting,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌, 为空则不分页
	PageToken     string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotesRequest) Reset() {
	*x = ListNotesRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotesRequest) ProtoMessage() {}

func (x *ListNotesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotesRequest.ProtoReflect.Descriptor instead.
func (*ListNotesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{52}
}

func (x *ListNotesRequest) GetFilter() *ListNotesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListNotesRequest) GetSorting() *ListNotesRequest_Sorting {
	if x != nil {
		return x.Sorting
	}
	return nil
}

func (x *ListNotesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListNotesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListNotesResponse 获取备注列表响应
type ListNotesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 备注列表
	Notes []*Note `protobuf:"bytes,1,rep,name=notes,proto3" json:"notes,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotesResponse) Reset() {
	*x = ListNotesResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotesResponse) ProtoMessage() {}

func (x *ListNotesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotesResponse.ProtoReflect.Descriptor instead.
func (*ListNotesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{53}
}

func (x *ListNotesResponse) GetNotes() []*Note {
	if x != nil {
		return x.Notes
	}
	return nil
}

func (x *ListNotesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListNotesResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// DeleteNoteRequest 删除备注请求
type DeleteNoteRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 操作员工ID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNoteRequest) Reset() {
	*x = DeleteNoteRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNoteRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNoteRequest) ProtoMessage() {}

func (x *DeleteNoteRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNoteRequest.ProtoReflect.Descriptor instead.
func (*DeleteNoteRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{54}
}

func (x *DeleteNoteRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteNoteRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// DeleteNoteResponse 删除备注响应
type DeleteNoteResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNoteResponse) Reset() {
	*x = DeleteNoteResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNoteResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNoteResponse) ProtoMessage() {}

func (x *DeleteNoteResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNoteResponse.ProtoReflect.Descriptor instead.
func (*DeleteNoteResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{55}
}

// CreateSourceRequest 创建来源请求
type CreateSourceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort          int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSourceRequest) Reset() {
	*x = CreateSourceRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSourceRequest) ProtoMessage() {}

func (x *CreateSourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSourceRequest.ProtoReflect.Descriptor instead.
func (*CreateSourceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{56}
}

func (x *CreateSourceRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateSourceRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateSourceRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateSourceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateSourceRequest) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// CreateSourceResponse 创建来源响应
type CreateSourceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Source
	Source        *Source `protobuf:"bytes,1,opt,name=source,proto3" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSourceResponse) Reset() {
	*x = CreateSourceResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSourceResponse) ProtoMessage() {}

func (x *CreateSourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSourceResponse.ProtoReflect.Descriptor instead.
func (*CreateSourceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{57}
}

func (x *CreateSourceResponse) GetSource() *Source {
	if x != nil {
		return x.Source
	}
	return nil
}

// UpdateSourcesRequest 批量更新来源请求
type UpdateSourcesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 批量更新数据
	Updates []*UpdateSourcesRequest_UpdateSource `protobuf:"bytes,1,rep,name=updates,proto3" json:"updates,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 公司ID
	CompanyId     int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSourcesRequest) Reset() {
	*x = UpdateSourcesRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSourcesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSourcesRequest) ProtoMessage() {}

func (x *UpdateSourcesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSourcesRequest.ProtoReflect.Descriptor instead.
func (*UpdateSourcesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{58}
}

func (x *UpdateSourcesRequest) GetUpdates() []*UpdateSourcesRequest_UpdateSource {
	if x != nil {
		return x.Updates
	}
	return nil
}

func (x *UpdateSourcesRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateSourcesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// UpdateSourcesResponse 批量更新来源响应
type UpdateSourcesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSourcesResponse) Reset() {
	*x = UpdateSourcesResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSourcesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSourcesResponse) ProtoMessage() {}

func (x *UpdateSourcesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSourcesResponse.ProtoReflect.Descriptor instead.
func (*UpdateSourcesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{59}
}

// ListSourcesRequest 获取来源列表请求
type ListSourcesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤器
	Filter *ListSourcesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页令牌, 为空则不分页
	PageToken     string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSourcesRequest) Reset() {
	*x = ListSourcesRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSourcesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSourcesRequest) ProtoMessage() {}

func (x *ListSourcesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSourcesRequest.ProtoReflect.Descriptor instead.
func (*ListSourcesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{60}
}

func (x *ListSourcesRequest) GetFilter() *ListSourcesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListSourcesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSourcesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListSourcesResponse 获取来源列表响应
type ListSourcesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 来源列表
	Sources []*Source `protobuf:"bytes,1,rep,name=sources,proto3" json:"sources,omitempty"`
	// 下一页令牌
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSourcesResponse) Reset() {
	*x = ListSourcesResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSourcesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSourcesResponse) ProtoMessage() {}

func (x *ListSourcesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSourcesResponse.ProtoReflect.Descriptor instead.
func (*ListSourcesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{61}
}

func (x *ListSourcesResponse) GetSources() []*Source {
	if x != nil {
		return x.Sources
	}
	return nil
}

func (x *ListSourcesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListSourcesResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// DeleteSourceRequest 删除来源请求
type DeleteSourceRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 操作员工ID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSourceRequest) Reset() {
	*x = DeleteSourceRequest{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSourceRequest) ProtoMessage() {}

func (x *DeleteSourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSourceRequest.ProtoReflect.Descriptor instead.
func (*DeleteSourceRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{62}
}

func (x *DeleteSourceRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteSourceRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// DeleteSourceResponse 删除来源响应
type DeleteSourceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSourceResponse) Reset() {
	*x = DeleteSourceResponse{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSourceResponse) ProtoMessage() {}

func (x *DeleteSourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSourceResponse.ProtoReflect.Descriptor instead.
func (*DeleteSourceResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{63}
}

// Filter 过滤器
type ListCustomerActivityLogsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 记录类型过滤
	Types []ActivityLog_Type `protobuf:"varint,2,rep,packed,name=types,proto3,enum=backend.proto.customer.v2.ActivityLog_Type" json:"types,omitempty"`
	// 公司ID
	Organizations []*OrganizationRef `protobuf:"bytes,3,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// IDs
	Ids           []int64 `protobuf:"varint,4,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerActivityLogsRequest_Filter) Reset() {
	*x = ListCustomerActivityLogsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerActivityLogsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerActivityLogsRequest_Filter) ProtoMessage() {}

func (x *ListCustomerActivityLogsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerActivityLogsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomerActivityLogsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListCustomerActivityLogsRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListCustomerActivityLogsRequest_Filter) GetTypes() []ActivityLog_Type {
	if x != nil {
		return x.Types
	}
	return nil
}

func (x *ListCustomerActivityLogsRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListCustomerActivityLogsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

// sorting
type ListCustomerActivityLogsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListCustomerActivityLogsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListCustomerActivityLogsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerActivityLogsRequest_Sorting) Reset() {
	*x = ListCustomerActivityLogsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerActivityLogsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerActivityLogsRequest_Sorting) ProtoMessage() {}

func (x *ListCustomerActivityLogsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerActivityLogsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListCustomerActivityLogsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{4, 1}
}

func (x *ListCustomerActivityLogsRequest_Sorting) GetField() ListCustomerActivityLogsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListCustomerActivityLogsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListCustomerActivityLogsRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// filter
type ListCustomerTasksRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// customer ids
	CustomerIds   []int64 `protobuf:"varint,2,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerTasksRequest_Filter) Reset() {
	*x = ListCustomerTasksRequest_Filter{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerTasksRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTasksRequest_Filter) ProtoMessage() {}

func (x *ListCustomerTasksRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTasksRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomerTasksRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{14, 0}
}

func (x *ListCustomerTasksRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListCustomerTasksRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// sorting
type ListCustomerTasksRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListCustomerTasksRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListCustomerTasksRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerTasksRequest_Sorting) Reset() {
	*x = ListCustomerTasksRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerTasksRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTasksRequest_Sorting) ProtoMessage() {}

func (x *ListCustomerTasksRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTasksRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListCustomerTasksRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{14, 1}
}

func (x *ListCustomerTasksRequest_Sorting) GetField() ListCustomerTasksRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListCustomerTasksRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListCustomerTasksRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// 批量更新结构
type UpdateLifeCyclesRequest_UpdateLifeCycle struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 排序
	Sort          *int32 `protobuf:"varint,3,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) Reset() {
	*x = UpdateLifeCyclesRequest_UpdateLifeCycle{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLifeCyclesRequest_UpdateLifeCycle) ProtoMessage() {}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLifeCyclesRequest_UpdateLifeCycle.ProtoReflect.Descriptor instead.
func (*UpdateLifeCyclesRequest_UpdateLifeCycle) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{20, 0}
}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) GetSort() int32 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

// filter
type ListLifeCyclesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// organizations
	Organizations []*OrganizationRef `protobuf:"bytes,2,rep,name=organizations,proto3" json:"organizations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLifeCyclesRequest_Filter) Reset() {
	*x = ListLifeCyclesRequest_Filter{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLifeCyclesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLifeCyclesRequest_Filter) ProtoMessage() {}

func (x *ListLifeCyclesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLifeCyclesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListLifeCyclesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{22, 0}
}

func (x *ListLifeCyclesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListLifeCyclesRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

// sorting
type ListLifeCyclesRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListLifeCyclesRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListLifeCyclesRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLifeCyclesRequest_Sorting) Reset() {
	*x = ListLifeCyclesRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLifeCyclesRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLifeCyclesRequest_Sorting) ProtoMessage() {}

func (x *ListLifeCyclesRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLifeCyclesRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListLifeCyclesRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{22, 1}
}

func (x *ListLifeCyclesRequest_Sorting) GetField() ListLifeCyclesRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListLifeCyclesRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListLifeCyclesRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// 批量更新结构
type UpdateActionStatesRequest_UpdateActionState struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 排序
	Sort *int32 `protobuf:"varint,3,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// 颜色
	Color         *string `protobuf:"bytes,4,opt,name=color,proto3,oneof" json:"color,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateActionStatesRequest_UpdateActionState) Reset() {
	*x = UpdateActionStatesRequest_UpdateActionState{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateActionStatesRequest_UpdateActionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateActionStatesRequest_UpdateActionState) ProtoMessage() {}

func (x *UpdateActionStatesRequest_UpdateActionState) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateActionStatesRequest_UpdateActionState.ProtoReflect.Descriptor instead.
func (*UpdateActionStatesRequest_UpdateActionState) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{28, 0}
}

func (x *UpdateActionStatesRequest_UpdateActionState) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateActionStatesRequest_UpdateActionState) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateActionStatesRequest_UpdateActionState) GetSort() int32 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *UpdateActionStatesRequest_UpdateActionState) GetColor() string {
	if x != nil && x.Color != nil {
		return *x.Color
	}
	return ""
}

// filter
type ListActionStatesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// organizations
	Organizations []*OrganizationRef `protobuf:"bytes,2,rep,name=organizations,proto3" json:"organizations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActionStatesRequest_Filter) Reset() {
	*x = ListActionStatesRequest_Filter{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActionStatesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActionStatesRequest_Filter) ProtoMessage() {}

func (x *ListActionStatesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActionStatesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListActionStatesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{30, 0}
}

func (x *ListActionStatesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListActionStatesRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

// sorting
type ListActionStatesRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListActionStatesRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListActionStatesRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActionStatesRequest_Sorting) Reset() {
	*x = ListActionStatesRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActionStatesRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActionStatesRequest_Sorting) ProtoMessage() {}

func (x *ListActionStatesRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActionStatesRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListActionStatesRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{30, 1}
}

func (x *ListActionStatesRequest_Sorting) GetField() ListActionStatesRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListActionStatesRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListActionStatesRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// 批量更新结构
type UpdateTagsRequest_UpdateTag struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 排序
	Sort          *int32 `protobuf:"varint,3,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTagsRequest_UpdateTag) Reset() {
	*x = UpdateTagsRequest_UpdateTag{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTagsRequest_UpdateTag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTagsRequest_UpdateTag) ProtoMessage() {}

func (x *UpdateTagsRequest_UpdateTag) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTagsRequest_UpdateTag.ProtoReflect.Descriptor instead.
func (*UpdateTagsRequest_UpdateTag) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{36, 0}
}

func (x *UpdateTagsRequest_UpdateTag) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTagsRequest_UpdateTag) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateTagsRequest_UpdateTag) GetSort() int32 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

// filter
type ListTagsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// organizations
	Organizations []*OrganizationRef `protobuf:"bytes,2,rep,name=organizations,proto3" json:"organizations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTagsRequest_Filter) Reset() {
	*x = ListTagsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTagsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTagsRequest_Filter) ProtoMessage() {}

func (x *ListTagsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTagsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListTagsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{38, 0}
}

func (x *ListTagsRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListTagsRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

// sorting
type ListTagsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListTagsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListTagsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTagsRequest_Sorting) Reset() {
	*x = ListTagsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTagsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTagsRequest_Sorting) ProtoMessage() {}

func (x *ListTagsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTagsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListTagsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{38, 1}
}

func (x *ListTagsRequest_Sorting) GetField() ListTagsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListTagsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListTagsRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// filter
type ListCustomerTagsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// customer ids
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// tag ids
	TagIds        []int64 `protobuf:"varint,2,rep,packed,name=tag_ids,json=tagIds,proto3" json:"tag_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerTagsRequest_Filter) Reset() {
	*x = ListCustomerTagsRequest_Filter{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerTagsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTagsRequest_Filter) ProtoMessage() {}

func (x *ListCustomerTagsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTagsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomerTagsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{46, 0}
}

func (x *ListCustomerTagsRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListCustomerTagsRequest_Filter) GetTagIds() []int64 {
	if x != nil {
		return x.TagIds
	}
	return nil
}

// sorting
type ListCustomerTagsRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListCustomerTagsRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListCustomerTagsRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerTagsRequest_Sorting) Reset() {
	*x = ListCustomerTagsRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerTagsRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTagsRequest_Sorting) ProtoMessage() {}

func (x *ListCustomerTagsRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTagsRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListCustomerTagsRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{46, 1}
}

func (x *ListCustomerTagsRequest_Sorting) GetField() ListCustomerTagsRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListCustomerTagsRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListCustomerTagsRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// filter
type ListNotesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// organizations
	Organizations []*OrganizationRef `protobuf:"bytes,2,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// customer ids
	CustomerIds   []int64 `protobuf:"varint,3,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotesRequest_Filter) Reset() {
	*x = ListNotesRequest_Filter{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotesRequest_Filter) ProtoMessage() {}

func (x *ListNotesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListNotesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{52, 0}
}

func (x *ListNotesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListNotesRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListNotesRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// sorting
type ListNotesRequest_Sorting struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 排序字段
	Field ListNotesRequest_Sorting_Field `protobuf:"varint,1,opt,name=field,proto3,enum=backend.proto.customer.v2.ListNotesRequest_Sorting_Field" json:"field,omitempty"`
	// 排序方向
	Direction     Direction `protobuf:"varint,2,opt,name=direction,proto3,enum=backend.proto.customer.v2.Direction" json:"direction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListNotesRequest_Sorting) Reset() {
	*x = ListNotesRequest_Sorting{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListNotesRequest_Sorting) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListNotesRequest_Sorting) ProtoMessage() {}

func (x *ListNotesRequest_Sorting) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListNotesRequest_Sorting.ProtoReflect.Descriptor instead.
func (*ListNotesRequest_Sorting) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{52, 1}
}

func (x *ListNotesRequest_Sorting) GetField() ListNotesRequest_Sorting_Field {
	if x != nil {
		return x.Field
	}
	return ListNotesRequest_Sorting_FIELD_UNSPECIFIED
}

func (x *ListNotesRequest_Sorting) GetDirection() Direction {
	if x != nil {
		return x.Direction
	}
	return Direction_DIRECTION_UNSPECIFIED
}

// 批量更新结构
type UpdateSourcesRequest_UpdateSource struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 排序
	Sort          *int32 `protobuf:"varint,3,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSourcesRequest_UpdateSource) Reset() {
	*x = UpdateSourcesRequest_UpdateSource{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSourcesRequest_UpdateSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSourcesRequest_UpdateSource) ProtoMessage() {}

func (x *UpdateSourcesRequest_UpdateSource) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSourcesRequest_UpdateSource.ProtoReflect.Descriptor instead.
func (*UpdateSourcesRequest_UpdateSource) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{58, 0}
}

func (x *UpdateSourcesRequest_UpdateSource) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateSourcesRequest_UpdateSource) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateSourcesRequest_UpdateSource) GetSort() int32 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

// 过滤器
type ListSourcesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// organizations
	Organizations []*OrganizationRef `protobuf:"bytes,1,rep,name=organizations,proto3" json:"organizations,omitempty"`
	// IDs
	Ids           []int64 `protobuf:"varint,2,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListSourcesRequest_Filter) Reset() {
	*x = ListSourcesRequest_Filter{}
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListSourcesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSourcesRequest_Filter) ProtoMessage() {}

func (x *ListSourcesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_activity_service_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSourcesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListSourcesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP(), []int{60, 0}
}

func (x *ListSourcesRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

func (x *ListSourcesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

var File_backend_proto_customer_v2_activity_service_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v2_activity_service_proto_rawDesc = "" +
	"\n" +
	"0backend/proto/customer/v2/activity_service.proto\x12\x19backend.proto.customer.v2\x1a(backend/proto/customer/v2/activity.proto\x1a&backend/proto/customer/v2/common.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xf4\x02\n" +
	" CreateCustomerActivityLogRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\x12#\n" +
	"\rcustomer_name\x18\t \x01(\tR\fcustomerName\x122\n" +
	"\x15customer_phone_number\x18\n" +
	" \x01(\tR\x13customerPhoneNumber\x12E\n" +
	"\x06action\x18\x02 \x01(\v2-.backend.proto.customer.v2.ActivityLog.ActionR\x06action\x12\x1d\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x04 \x01(\x03R\n" +
	"businessId\x12D\n" +
	"\x06source\x18\x05 \x01(\v2'.backend.proto.customer.v2.SystemSourceH\x00R\x06source\x88\x01\x01B\t\n" +
	"\a_source\"]\n" +
	"!CreateCustomerActivityLogResponse\x128\n" +
	"\x03log\x18\x01 \x01(\v2&.backend.proto.customer.v2.ActivityLogR\x03log\"\xab\x01\n" +
	" UpdateCustomerActivityLogRequest\x12\x15\n" +
	"\x06log_id\x18\x01 \x01(\x03R\x05logId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12J\n" +
	"\x06action\x18\n" +
	" \x01(\v2-.backend.proto.customer.v2.ActivityLog.ActionH\x00R\x06action\x88\x01\x01B\t\n" +
	"\a_action\"]\n" +
	"!UpdateCustomerActivityLogResponse\x128\n" +
	"\x03log\x18\x01 \x01(\v2&.backend.proto.customer.v2.ActivityLogR\x03log\"\xf3\x05\n" +
	"\x1fListCustomerActivityLogsRequest\x12Y\n" +
	"\x06filter\x18\x01 \x01(\v2A.backend.proto.customer.v2.ListCustomerActivityLogsRequest.FilterR\x06filter\x12\\\n" +
	"\asorting\x18\x02 \x01(\v2B.backend.proto.customer.v2.ListCustomerActivityLogsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a\xd2\x01\n" +
	"\x06Filter\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\x12A\n" +
	"\x05types\x18\x02 \x03(\x0e2+.backend.proto.customer.v2.ActivityLog.TypeR\x05types\x12P\n" +
	"\rorganizations\x18\x03 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x12\x10\n" +
	"\x03ids\x18\x04 \x03(\x03R\x03ids\x1a\xf9\x01\n" +
	"\aSorting\x12^\n" +
	"\x05field\x18\x01 \x01(\x0e2H.backend.proto.customer.v2.ListCustomerActivityLogsRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xca\x01\n" +
	" ListCustomerActivityLogsResponse\x12K\n" +
	"\ractivity_logs\x18\x01 \x03(\v2&.backend.proto.customer.v2.ActivityLogR\factivityLogs\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"9\n" +
	"\x16ConvertCustomerRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\":\n" +
	"\x17ConvertCustomerResponse\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\"\xfb\x01\n" +
	" ConvertCustomersAttributeRequest\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\x12:\n" +
	"\x17customize_life_cycle_id\x18\x02 \x01(\x03H\x00R\x14customizeLifeCycleId\x88\x01\x01\x12>\n" +
	"\x19customize_action_state_id\x18\x03 \x01(\x03H\x01R\x16customizeActionStateId\x88\x01\x01B\x1a\n" +
	"\x18_customize_life_cycle_idB\x1c\n" +
	"\x1a_customize_action_state_id\"#\n" +
	"!ConvertCustomersAttributeResponse\"\xca\x02\n" +
	"\x19CreateCustomerTaskRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x03 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\bstaff_id\x18\x04 \x01(\x03R\astaffId\x12\x12\n" +
	"\x04name\x18\n" +
	" \x01(\tR\x04name\x12/\n" +
	"\x11allocate_staff_id\x18\v \x01(\x03H\x00R\x0fallocateStaffId\x88\x01\x01\x12D\n" +
	"\rcomplete_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampH\x01R\fcompleteTime\x88\x01\x01B\x14\n" +
	"\x12_allocate_staff_idB\x10\n" +
	"\x0e_complete_time\"Q\n" +
	"\x1aCreateCustomerTaskResponse\x123\n" +
	"\x04task\x18\x01 \x01(\v2\x1f.backend.proto.customer.v2.TaskR\x04task\"\xdc\x02\n" +
	"\x19UpdateCustomerTaskRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\x03R\x06taskId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12\x17\n" +
	"\x04name\x18\n" +
	" \x01(\tH\x00R\x04name\x88\x01\x01\x12/\n" +
	"\x11allocate_staff_id\x18\v \x01(\x03H\x01R\x0fallocateStaffId\x88\x01\x01\x12D\n" +
	"\rcomplete_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampH\x02R\fcompleteTime\x88\x01\x01\x12@\n" +
	"\x05state\x18\r \x01(\x0e2%.backend.proto.customer.v2.Task.StateH\x03R\x05state\x88\x01\x01B\a\n" +
	"\x05_nameB\x14\n" +
	"\x12_allocate_staff_idB\x10\n" +
	"\x0e_complete_timeB\b\n" +
	"\x06_state\"Q\n" +
	"\x1aUpdateCustomerTaskResponse\x123\n" +
	"\x04task\x18\x01 \x01(\v2\x1f.backend.proto.customer.v2.TaskR\x04task\"\xc9\x04\n" +
	"\x18ListCustomerTasksRequest\x12Z\n" +
	"\x06filter\x18\x01 \x01(\v2:.backend.proto.customer.v2.ListCustomerTasksRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12U\n" +
	"\asorting\x18\x02 \x01(\v2;.backend.proto.customer.v2.ListCustomerTasksRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a=\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12!\n" +
	"\fcustomer_ids\x18\x02 \x03(\x03R\vcustomerIds\x1a\xf2\x01\n" +
	"\aSorting\x12W\n" +
	"\x05field\x18\x01 \x01(\x0e2A.backend.proto.customer.v2.ListCustomerTasksRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xad\x01\n" +
	"\x19ListCustomerTasksResponse\x125\n" +
	"\x05tasks\x18\x01 \x03(\v2\x1f.backend.proto.customer.v2.TaskR\x05tasks\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"O\n" +
	"\x19DeleteCustomerTaskRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\x03R\x06taskId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\"\x1c\n" +
	"\x1aDeleteCustomerTaskResponse\"\x9b\x01\n" +
	"\x16CreateLifeCycleRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\bstaff_id\x18\x03 \x01(\x03R\astaffId\x12\x12\n" +
	"\x04name\x18\n" +
	" \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\v \x01(\x05R\x04sort\"^\n" +
	"\x17CreateLifeCycleResponse\x12C\n" +
	"\n" +
	"life_cycle\x18\x01 \x01(\v2$.backend.proto.customer.v2.LifeCycleR\tlifeCycle\"\x98\x02\n" +
	"\x17UpdateLifeCyclesRequest\x12\\\n" +
	"\aupdates\x18\x01 \x03(\v2B.backend.proto.customer.v2.UpdateLifeCyclesRequest.UpdateLifeCycleR\aupdates\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03R\tcompanyId\x1ae\n" +
	"\x0fUpdateLifeCycle\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tH\x00R\x04name\x88\x01\x01\x12\x17\n" +
	"\x04sort\x18\x03 \x01(\x05H\x01R\x04sort\x88\x01\x01B\a\n" +
	"\x05_nameB\a\n" +
	"\x05_sort\"\x1a\n" +
	"\x18UpdateLifeCyclesResponse\"\xec\x04\n" +
	"\x15ListLifeCyclesRequest\x12W\n" +
	"\x06filter\x18\x01 \x01(\v27.backend.proto.customer.v2.ListLifeCyclesRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12R\n" +
	"\asorting\x18\x02 \x01(\v28.backend.proto.customer.v2.ListLifeCyclesRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1al\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12P\n" +
	"\rorganizations\x18\x02 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x1a\xef\x01\n" +
	"\aSorting\x12T\n" +
	"\x05field\x18\x01 \x01(\x0e2>.backend.proto.customer.v2.ListLifeCyclesRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xba\x01\n" +
	"\x16ListLifeCyclesResponse\x12E\n" +
	"\vlife_cycles\x18\x01 \x03(\v2$.backend.proto.customer.v2.LifeCycleR\n" +
	"lifeCycles\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"C\n" +
	"\x16DeleteLifeCycleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\"\x19\n" +
	"\x17DeleteLifeCycleResponse\"\xb3\x01\n" +
	"\x18CreateActionStateRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\bstaff_id\x18\x03 \x01(\x03R\astaffId\x12\x12\n" +
	"\x04name\x18\n" +
	" \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\v \x01(\x05R\x04sort\x12\x14\n" +
	"\x05color\x18\f \x01(\tR\x05color\"f\n" +
	"\x19CreateActionStateResponse\x12I\n" +
	"\faction_state\x18\x01 \x01(\v2&.backend.proto.customer.v2.ActionStateR\vactionState\"\xc6\x02\n" +
	"\x19UpdateActionStatesRequest\x12`\n" +
	"\aupdates\x18\x01 \x03(\v2F.backend.proto.customer.v2.UpdateActionStatesRequest.UpdateActionStateR\aupdates\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03R\tcompanyId\x1a\x8c\x01\n" +
	"\x11UpdateActionState\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tH\x00R\x04name\x88\x01\x01\x12\x17\n" +
	"\x04sort\x18\x03 \x01(\x05H\x01R\x04sort\x88\x01\x01\x12\x19\n" +
	"\x05color\x18\x04 \x01(\tH\x02R\x05color\x88\x01\x01B\a\n" +
	"\x05_nameB\a\n" +
	"\x05_sortB\b\n" +
	"\x06_color\"\x1d\n" +
	"\x1bUpdateActionsStatesResponse\"\xf4\x04\n" +
	"\x17ListActionStatesRequest\x12Y\n" +
	"\x06filter\x18\x01 \x01(\v29.backend.proto.customer.v2.ListActionStatesRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12T\n" +
	"\asorting\x18\x02 \x01(\v2:.backend.proto.customer.v2.ListActionStatesRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1al\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12P\n" +
	"\rorganizations\x18\x02 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x1a\xf1\x01\n" +
	"\aSorting\x12V\n" +
	"\x05field\x18\x01 \x01(\<EMAIL>\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xc2\x01\n" +
	"\x18ListActionStatesResponse\x12K\n" +
	"\raction_states\x18\x01 \x03(\v2&.backend.proto.customer.v2.ActionStateR\factionStates\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"E\n" +
	"\x18DeleteActionStateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\"\x1b\n" +
	"\x19DeleteActionStateResponse\"\x95\x01\n" +
	"\x10CreateTagRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\bstaff_id\x18\x03 \x01(\x03R\astaffId\x12\x12\n" +
	"\x04name\x18\n" +
	" \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\v \x01(\x05R\x04sort\"E\n" +
	"\x11CreateTagResponse\x120\n" +
	"\x03tag\x18\x01 \x01(\v2\x1e.backend.proto.customer.v2.TagR\x03tag\"\x80\x02\n" +
	"\x11UpdateTagsRequest\x12P\n" +
	"\aupdates\x18\x01 \x03(\v26.backend.proto.customer.v2.UpdateTagsRequest.UpdateTagR\aupdates\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03R\tcompanyId\x1a_\n" +
	"\tUpdateTag\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tH\x00R\x04name\x88\x01\x01\x12\x17\n" +
	"\x04sort\x18\x03 \x01(\x05H\x01R\x04sort\x88\x01\x01B\a\n" +
	"\x05_nameB\a\n" +
	"\x05_sort\"\x14\n" +
	"\x12UpdateTagsResponse\"\xd4\x04\n" +
	"\x0fListTagsRequest\x12Q\n" +
	"\x06filter\x18\x01 \x01(\v21.backend.proto.customer.v2.ListTagsRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12L\n" +
	"\asorting\x18\x02 \x01(\v22.backend.proto.customer.v2.ListTagsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1al\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12P\n" +
	"\rorganizations\x18\x02 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x1a\xe9\x01\n" +
	"\aSorting\x12N\n" +
	"\x05field\x18\x01 \x01(\x0e28.backend.proto.customer.v2.ListTagsRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xa1\x01\n" +
	"\x10ListTagsResponse\x122\n" +
	"\x04tags\x18\x01 \x03(\v2\x1e.backend.proto.customer.v2.TagR\x04tags\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"=\n" +
	"\x10DeleteTagRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\"\x13\n" +
	"\x11DeleteTagResponse\"\xae\x01\n" +
	"\x17CoverCustomerTagRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x1f\n" +
	"\vcustomer_id\x18\x03 \x01(\x03R\n" +
	"customerId\x12\x19\n" +
	"\bstaff_id\x18\x04 \x01(\x03R\astaffId\x12\x17\n" +
	"\atag_ids\x18\x05 \x03(\x03R\x06tagIds\"\x1a\n" +
	"\x18CoverCustomerTagResponse\"\xb5\x01\n" +
	"\x1cBatchAddCustomersTagsRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12!\n" +
	"\fcustomer_ids\x18\x03 \x03(\x03R\vcustomerIds\x12\x19\n" +
	"\bstaff_id\x18\x04 \x01(\x03R\astaffId\x12\x17\n" +
	"\atag_ids\x18\x05 \x03(\x03R\x06tagIds\"\x1f\n" +
	"\x1dBatchAddCustomersTagsResponse\"\xcc\x04\n" +
	"\x17ListCustomerTagsRequest\x12Y\n" +
	"\x06filter\x18\x01 \x01(\v29.backend.proto.customer.v2.ListCustomerTagsRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12T\n" +
	"\asorting\x18\x02 \x01(\v2:.backend.proto.customer.v2.ListCustomerTagsRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1aD\n" +
	"\x06Filter\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\x12\x17\n" +
	"\atag_ids\x18\x02 \x03(\x03R\x06tagIds\x1a\xf1\x01\n" +
	"\aSorting\x12V\n" +
	"\x05field\x18\x01 \x01(\<EMAIL>\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xc2\x01\n" +
	"\x18ListCustomerTagsResponse\x12K\n" +
	"\rcustomer_tags\x18\x01 \x03(\v2&.backend.proto.customer.v2.CustomerTagR\fcustomerTags\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"\xc9\x01\n" +
	"\x11CreateNoteRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x1f\n" +
	"\vcustomer_id\x18\x04 \x01(\x03R\n" +
	"customerId\x12\x12\n" +
	"\x04text\x18\n" +
	" \x01(\tR\x04text\x12?\n" +
	"\x06source\x18\v \x01(\v2'.backend.proto.customer.v2.SystemSourceR\x06source\"I\n" +
	"\x12CreateNoteResponse\x123\n" +
	"\x04note\x18\x01 \x01(\v2\x1f.backend.proto.customer.v2.NoteR\x04note\"\x87\x01\n" +
	"\x12UpdateNotesRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12?\n" +
	"\x06source\x18\x02 \x01(\v2'.backend.proto.customer.v2.SystemSourceR\x06source\x12\x17\n" +
	"\x04text\x18\x03 \x01(\tH\x00R\x04text\x88\x01\x01B\a\n" +
	"\x05_text\"J\n" +
	"\x13UpdateNotesResponse\x123\n" +
	"\x04note\x18\x01 \x01(\v2\x1f.backend.proto.customer.v2.NoteR\x04note\"\xfc\x04\n" +
	"\x10ListNotesRequest\x12R\n" +
	"\x06filter\x18\x01 \x01(\v22.backend.proto.customer.v2.ListNotesRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12M\n" +
	"\asorting\x18\x02 \x01(\v23.backend.proto.customer.v2.ListNotesRequest.SortingR\asorting\x12'\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tR\tpageToken\x1a\x8f\x01\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12P\n" +
	"\rorganizations\x18\x02 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x12!\n" +
	"\fcustomer_ids\x18\x03 \x03(\x03R\vcustomerIds\x1a\xea\x01\n" +
	"\aSorting\x12O\n" +
	"\x05field\x18\x01 \x01(\x0e29.backend.proto.customer.v2.ListNotesRequest.Sorting.FieldR\x05field\x12B\n" +
	"\tdirection\x18\x02 \x01(\x0e2$.backend.proto.customer.v2.DirectionR\tdirection\"J\n" +
	"\x05Field\x12\x15\n" +
	"\x11FIELD_UNSPECIFIED\x10\x00\x12\x06\n" +
	"\x02ID\x10\x01\x12\x10\n" +
	"\fCREATED_TIME\x10\x02\x12\x10\n" +
	"\fUPDATED_TIME\x10\x03\"\xa5\x01\n" +
	"\x11ListNotesResponse\x125\n" +
	"\x05notes\x18\x01 \x03(\v2\x1f.backend.proto.customer.v2.NoteR\x05notes\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\">\n" +
	"\x11DeleteNoteRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\"\x14\n" +
	"\x12DeleteNoteResponse\"\x98\x01\n" +
	"\x13CreateSourceRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\bstaff_id\x18\x03 \x01(\x03R\astaffId\x12\x12\n" +
	"\x04name\x18\n" +
	" \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\v \x01(\x05R\x04sort\"Q\n" +
	"\x14CreateSourceResponse\x129\n" +
	"\x06source\x18\x01 \x01(\v2!.backend.proto.customer.v2.SourceR\x06source\"\x8c\x02\n" +
	"\x14UpdateSourcesRequest\x12V\n" +
	"\aupdates\x18\x01 \x03(\v2<.backend.proto.customer.v2.UpdateSourcesRequest.UpdateSourceR\aupdates\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03R\tcompanyId\x1ab\n" +
	"\fUpdateSource\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tH\x00R\x04name\x88\x01\x01\x12\x17\n" +
	"\x04sort\x18\x03 \x01(\x05H\x01R\x04sort\x88\x01\x01B\a\n" +
	"\x05_nameB\a\n" +
	"\x05_sort\"\x17\n" +
	"\x15UpdateSourcesResponse\"\x98\x02\n" +
	"\x12ListSourcesRequest\x12L\n" +
	"\x06filter\x18\x01 \x01(\v24.backend.proto.customer.v2.ListSourcesRequest.FilterR\x06filter\x12'\n" +
	"\tpage_size\x18\x02 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x03 \x01(\tR\tpageToken\x1al\n" +
	"\x06Filter\x12P\n" +
	"\rorganizations\x18\x01 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\x12\x10\n" +
	"\x03ids\x18\x02 \x03(\x03R\x03ids\"\xad\x01\n" +
	"\x13ListSourcesResponse\x12;\n" +
	"\asources\x18\x01 \x03(\v2!.backend.proto.customer.v2.SourceR\asources\x12&\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tR\rnextPageToken\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x00R\ttotalSize\x88\x01\x01B\r\n" +
	"\v_total_size\"@\n" +
	"\x13DeleteSourceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\"\x16\n" +
	"\x14DeleteSourceResponse2\xf9\x1e\n" +
	"\x0fActivityService\x12\x96\x01\n" +
	"\x19CreateCustomerActivityLog\x12;.backend.proto.customer.v2.CreateCustomerActivityLogRequest\x1a<.backend.proto.customer.v2.CreateCustomerActivityLogResponse\x12\x96\x01\n" +
	"\x19UpdateCustomerActivityLog\x12;.backend.proto.customer.v2.UpdateCustomerActivityLogRequest\x1a<.backend.proto.customer.v2.UpdateCustomerActivityLogResponse\x12\x93\x01\n" +
	"\x18ListCustomerActivityLogs\x12:.backend.proto.customer.v2.ListCustomerActivityLogsRequest\x1a;.backend.proto.customer.v2.ListCustomerActivityLogsResponse\x12x\n" +
	"\x0fConvertCustomer\x121.backend.proto.customer.v2.ConvertCustomerRequest\x1a2.backend.proto.customer.v2.ConvertCustomerResponse\x12\x96\x01\n" +
	"\x19ConvertCustomersAttribute\x12;.backend.proto.customer.v2.ConvertCustomersAttributeRequest\x1a<.backend.proto.customer.v2.ConvertCustomersAttributeResponse\x12\x81\x01\n" +
	"\x12CreateCustomerTask\x124.backend.proto.customer.v2.CreateCustomerTaskRequest\x1a5.backend.proto.customer.v2.CreateCustomerTaskResponse\x12\x81\x01\n" +
	"\x12UpdateCustomerTask\x124.backend.proto.customer.v2.UpdateCustomerTaskRequest\x1a5.backend.proto.customer.v2.UpdateCustomerTaskResponse\x12~\n" +
	"\x11ListCustomerTasks\x123.backend.proto.customer.v2.ListCustomerTasksRequest\x1a4.backend.proto.customer.v2.ListCustomerTasksResponse\x12\x81\x01\n" +
	"\x12DeleteCustomerTask\x124.backend.proto.customer.v2.DeleteCustomerTaskRequest\x1a5.backend.proto.customer.v2.DeleteCustomerTaskResponse\x12x\n" +
	"\x0fCreateLifeCycle\x121.backend.proto.customer.v2.CreateLifeCycleRequest\x1a2.backend.proto.customer.v2.CreateLifeCycleResponse\x12{\n" +
	"\x10UpdateLifeCycles\x122.backend.proto.customer.v2.UpdateLifeCyclesRequest\x1a3.backend.proto.customer.v2.UpdateLifeCyclesResponse\x12u\n" +
	"\x0eListLifeCycles\x120.backend.proto.customer.v2.ListLifeCyclesRequest\x1a1.backend.proto.customer.v2.ListLifeCyclesResponse\x12x\n" +
	"\x0fDeleteLifeCycle\x121.backend.proto.customer.v2.DeleteLifeCycleRequest\x1a2.backend.proto.customer.v2.DeleteLifeCycleResponse\x12~\n" +
	"\x11CreateActionState\x123.backend.proto.customer.v2.CreateActionStateRequest\x1a4.backend.proto.customer.v2.CreateActionStateResponse\x12\x82\x01\n" +
	"\x12UpdateActionStates\x124.backend.proto.customer.v2.UpdateActionStatesRequest\x1a6.backend.proto.customer.v2.UpdateActionsStatesResponse\x12{\n" +
	"\x10ListActionStates\x122.backend.proto.customer.v2.ListActionStatesRequest\x1a3.backend.proto.customer.v2.ListActionStatesResponse\x12~\n" +
	"\x11DeleteActionState\x123.backend.proto.customer.v2.DeleteActionStateRequest\x1a4.backend.proto.customer.v2.DeleteActionStateResponse\x12f\n" +
	"\tCreateTag\x12+.backend.proto.customer.v2.CreateTagRequest\x1a,.backend.proto.customer.v2.CreateTagResponse\x12i\n" +
	"\n" +
	"UpdateTags\x12,.backend.proto.customer.v2.UpdateTagsRequest\x1a-.backend.proto.customer.v2.UpdateTagsResponse\x12c\n" +
	"\bListTags\x12*.backend.proto.customer.v2.ListTagsRequest\x1a+.backend.proto.customer.v2.ListTagsResponse\x12f\n" +
	"\tDeleteTag\x12+.backend.proto.customer.v2.DeleteTagRequest\x1a,.backend.proto.customer.v2.DeleteTagResponse\x12{\n" +
	"\x10CoverCustomerTag\x122.backend.proto.customer.v2.CoverCustomerTagRequest\x1a3.backend.proto.customer.v2.CoverCustomerTagResponse\x12\x8a\x01\n" +
	"\x15BatchAddCustomersTags\x127.backend.proto.customer.v2.BatchAddCustomersTagsRequest\x1a8.backend.proto.customer.v2.BatchAddCustomersTagsResponse\x12{\n" +
	"\x10ListCustomerTags\x122.backend.proto.customer.v2.ListCustomerTagsRequest\x1a3.backend.proto.customer.v2.ListCustomerTagsResponse\x12i\n" +
	"\n" +
	"CreateNote\x12,.backend.proto.customer.v2.CreateNoteRequest\x1a-.backend.proto.customer.v2.CreateNoteResponse\x12l\n" +
	"\vUpdateNotes\x12-.backend.proto.customer.v2.UpdateNotesRequest\x1a..backend.proto.customer.v2.UpdateNotesResponse\x12f\n" +
	"\tListNotes\x12+.backend.proto.customer.v2.ListNotesRequest\x1a,.backend.proto.customer.v2.ListNotesResponse\x12i\n" +
	"\n" +
	"DeleteNote\x12,.backend.proto.customer.v2.DeleteNoteRequest\x1a-.backend.proto.customer.v2.DeleteNoteResponse\x12o\n" +
	"\fCreateSource\x12..backend.proto.customer.v2.CreateSourceRequest\x1a/.backend.proto.customer.v2.CreateSourceResponse\x12r\n" +
	"\rUpdateSources\x12/.backend.proto.customer.v2.UpdateSourcesRequest\x1a0.backend.proto.customer.v2.UpdateSourcesResponse\x12l\n" +
	"\vListSources\x12-.backend.proto.customer.v2.ListSourcesRequest\x1a..backend.proto.customer.v2.ListSourcesResponse\x12o\n" +
	"\fDeleteSource\x12..backend.proto.customer.v2.DeleteSourceRequest\x1a/.backend.proto.customer.v2.DeleteSourceResponseBk\n" +
	"#com.moego.backend.proto.customer.v2P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v2_activity_service_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v2_activity_service_proto_rawDescData []byte
)

func file_backend_proto_customer_v2_activity_service_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v2_activity_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v2_activity_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_activity_service_proto_rawDesc), len(file_backend_proto_customer_v2_activity_service_proto_rawDesc)))
	})
	return file_backend_proto_customer_v2_activity_service_proto_rawDescData
}

var file_backend_proto_customer_v2_activity_service_proto_enumTypes = make([]protoimpl.EnumInfo, 7)
var file_backend_proto_customer_v2_activity_service_proto_msgTypes = make([]protoimpl.MessageInfo, 83)
var file_backend_proto_customer_v2_activity_service_proto_goTypes = []any{
	(ListCustomerActivityLogsRequest_Sorting_Field)(0),  // 0: backend.proto.customer.v2.ListCustomerActivityLogsRequest.Sorting.Field
	(ListCustomerTasksRequest_Sorting_Field)(0),         // 1: backend.proto.customer.v2.ListCustomerTasksRequest.Sorting.Field
	(ListLifeCyclesRequest_Sorting_Field)(0),            // 2: backend.proto.customer.v2.ListLifeCyclesRequest.Sorting.Field
	(ListActionStatesRequest_Sorting_Field)(0),          // 3: backend.proto.customer.v2.ListActionStatesRequest.Sorting.Field
	(ListTagsRequest_Sorting_Field)(0),                  // 4: backend.proto.customer.v2.ListTagsRequest.Sorting.Field
	(ListCustomerTagsRequest_Sorting_Field)(0),          // 5: backend.proto.customer.v2.ListCustomerTagsRequest.Sorting.Field
	(ListNotesRequest_Sorting_Field)(0),                 // 6: backend.proto.customer.v2.ListNotesRequest.Sorting.Field
	(*CreateCustomerActivityLogRequest)(nil),            // 7: backend.proto.customer.v2.CreateCustomerActivityLogRequest
	(*CreateCustomerActivityLogResponse)(nil),           // 8: backend.proto.customer.v2.CreateCustomerActivityLogResponse
	(*UpdateCustomerActivityLogRequest)(nil),            // 9: backend.proto.customer.v2.UpdateCustomerActivityLogRequest
	(*UpdateCustomerActivityLogResponse)(nil),           // 10: backend.proto.customer.v2.UpdateCustomerActivityLogResponse
	(*ListCustomerActivityLogsRequest)(nil),             // 11: backend.proto.customer.v2.ListCustomerActivityLogsRequest
	(*ListCustomerActivityLogsResponse)(nil),            // 12: backend.proto.customer.v2.ListCustomerActivityLogsResponse
	(*ConvertCustomerRequest)(nil),                      // 13: backend.proto.customer.v2.ConvertCustomerRequest
	(*ConvertCustomerResponse)(nil),                     // 14: backend.proto.customer.v2.ConvertCustomerResponse
	(*ConvertCustomersAttributeRequest)(nil),            // 15: backend.proto.customer.v2.ConvertCustomersAttributeRequest
	(*ConvertCustomersAttributeResponse)(nil),           // 16: backend.proto.customer.v2.ConvertCustomersAttributeResponse
	(*CreateCustomerTaskRequest)(nil),                   // 17: backend.proto.customer.v2.CreateCustomerTaskRequest
	(*CreateCustomerTaskResponse)(nil),                  // 18: backend.proto.customer.v2.CreateCustomerTaskResponse
	(*UpdateCustomerTaskRequest)(nil),                   // 19: backend.proto.customer.v2.UpdateCustomerTaskRequest
	(*UpdateCustomerTaskResponse)(nil),                  // 20: backend.proto.customer.v2.UpdateCustomerTaskResponse
	(*ListCustomerTasksRequest)(nil),                    // 21: backend.proto.customer.v2.ListCustomerTasksRequest
	(*ListCustomerTasksResponse)(nil),                   // 22: backend.proto.customer.v2.ListCustomerTasksResponse
	(*DeleteCustomerTaskRequest)(nil),                   // 23: backend.proto.customer.v2.DeleteCustomerTaskRequest
	(*DeleteCustomerTaskResponse)(nil),                  // 24: backend.proto.customer.v2.DeleteCustomerTaskResponse
	(*CreateLifeCycleRequest)(nil),                      // 25: backend.proto.customer.v2.CreateLifeCycleRequest
	(*CreateLifeCycleResponse)(nil),                     // 26: backend.proto.customer.v2.CreateLifeCycleResponse
	(*UpdateLifeCyclesRequest)(nil),                     // 27: backend.proto.customer.v2.UpdateLifeCyclesRequest
	(*UpdateLifeCyclesResponse)(nil),                    // 28: backend.proto.customer.v2.UpdateLifeCyclesResponse
	(*ListLifeCyclesRequest)(nil),                       // 29: backend.proto.customer.v2.ListLifeCyclesRequest
	(*ListLifeCyclesResponse)(nil),                      // 30: backend.proto.customer.v2.ListLifeCyclesResponse
	(*DeleteLifeCycleRequest)(nil),                      // 31: backend.proto.customer.v2.DeleteLifeCycleRequest
	(*DeleteLifeCycleResponse)(nil),                     // 32: backend.proto.customer.v2.DeleteLifeCycleResponse
	(*CreateActionStateRequest)(nil),                    // 33: backend.proto.customer.v2.CreateActionStateRequest
	(*CreateActionStateResponse)(nil),                   // 34: backend.proto.customer.v2.CreateActionStateResponse
	(*UpdateActionStatesRequest)(nil),                   // 35: backend.proto.customer.v2.UpdateActionStatesRequest
	(*UpdateActionsStatesResponse)(nil),                 // 36: backend.proto.customer.v2.UpdateActionsStatesResponse
	(*ListActionStatesRequest)(nil),                     // 37: backend.proto.customer.v2.ListActionStatesRequest
	(*ListActionStatesResponse)(nil),                    // 38: backend.proto.customer.v2.ListActionStatesResponse
	(*DeleteActionStateRequest)(nil),                    // 39: backend.proto.customer.v2.DeleteActionStateRequest
	(*DeleteActionStateResponse)(nil),                   // 40: backend.proto.customer.v2.DeleteActionStateResponse
	(*CreateTagRequest)(nil),                            // 41: backend.proto.customer.v2.CreateTagRequest
	(*CreateTagResponse)(nil),                           // 42: backend.proto.customer.v2.CreateTagResponse
	(*UpdateTagsRequest)(nil),                           // 43: backend.proto.customer.v2.UpdateTagsRequest
	(*UpdateTagsResponse)(nil),                          // 44: backend.proto.customer.v2.UpdateTagsResponse
	(*ListTagsRequest)(nil),                             // 45: backend.proto.customer.v2.ListTagsRequest
	(*ListTagsResponse)(nil),                            // 46: backend.proto.customer.v2.ListTagsResponse
	(*DeleteTagRequest)(nil),                            // 47: backend.proto.customer.v2.DeleteTagRequest
	(*DeleteTagResponse)(nil),                           // 48: backend.proto.customer.v2.DeleteTagResponse
	(*CoverCustomerTagRequest)(nil),                     // 49: backend.proto.customer.v2.CoverCustomerTagRequest
	(*CoverCustomerTagResponse)(nil),                    // 50: backend.proto.customer.v2.CoverCustomerTagResponse
	(*BatchAddCustomersTagsRequest)(nil),                // 51: backend.proto.customer.v2.BatchAddCustomersTagsRequest
	(*BatchAddCustomersTagsResponse)(nil),               // 52: backend.proto.customer.v2.BatchAddCustomersTagsResponse
	(*ListCustomerTagsRequest)(nil),                     // 53: backend.proto.customer.v2.ListCustomerTagsRequest
	(*ListCustomerTagsResponse)(nil),                    // 54: backend.proto.customer.v2.ListCustomerTagsResponse
	(*CreateNoteRequest)(nil),                           // 55: backend.proto.customer.v2.CreateNoteRequest
	(*CreateNoteResponse)(nil),                          // 56: backend.proto.customer.v2.CreateNoteResponse
	(*UpdateNotesRequest)(nil),                          // 57: backend.proto.customer.v2.UpdateNotesRequest
	(*UpdateNotesResponse)(nil),                         // 58: backend.proto.customer.v2.UpdateNotesResponse
	(*ListNotesRequest)(nil),                            // 59: backend.proto.customer.v2.ListNotesRequest
	(*ListNotesResponse)(nil),                           // 60: backend.proto.customer.v2.ListNotesResponse
	(*DeleteNoteRequest)(nil),                           // 61: backend.proto.customer.v2.DeleteNoteRequest
	(*DeleteNoteResponse)(nil),                          // 62: backend.proto.customer.v2.DeleteNoteResponse
	(*CreateSourceRequest)(nil),                         // 63: backend.proto.customer.v2.CreateSourceRequest
	(*CreateSourceResponse)(nil),                        // 64: backend.proto.customer.v2.CreateSourceResponse
	(*UpdateSourcesRequest)(nil),                        // 65: backend.proto.customer.v2.UpdateSourcesRequest
	(*UpdateSourcesResponse)(nil),                       // 66: backend.proto.customer.v2.UpdateSourcesResponse
	(*ListSourcesRequest)(nil),                          // 67: backend.proto.customer.v2.ListSourcesRequest
	(*ListSourcesResponse)(nil),                         // 68: backend.proto.customer.v2.ListSourcesResponse
	(*DeleteSourceRequest)(nil),                         // 69: backend.proto.customer.v2.DeleteSourceRequest
	(*DeleteSourceResponse)(nil),                        // 70: backend.proto.customer.v2.DeleteSourceResponse
	(*ListCustomerActivityLogsRequest_Filter)(nil),      // 71: backend.proto.customer.v2.ListCustomerActivityLogsRequest.Filter
	(*ListCustomerActivityLogsRequest_Sorting)(nil),     // 72: backend.proto.customer.v2.ListCustomerActivityLogsRequest.Sorting
	(*ListCustomerTasksRequest_Filter)(nil),             // 73: backend.proto.customer.v2.ListCustomerTasksRequest.Filter
	(*ListCustomerTasksRequest_Sorting)(nil),            // 74: backend.proto.customer.v2.ListCustomerTasksRequest.Sorting
	(*UpdateLifeCyclesRequest_UpdateLifeCycle)(nil),     // 75: backend.proto.customer.v2.UpdateLifeCyclesRequest.UpdateLifeCycle
	(*ListLifeCyclesRequest_Filter)(nil),                // 76: backend.proto.customer.v2.ListLifeCyclesRequest.Filter
	(*ListLifeCyclesRequest_Sorting)(nil),               // 77: backend.proto.customer.v2.ListLifeCyclesRequest.Sorting
	(*UpdateActionStatesRequest_UpdateActionState)(nil), // 78: backend.proto.customer.v2.UpdateActionStatesRequest.UpdateActionState
	(*ListActionStatesRequest_Filter)(nil),              // 79: backend.proto.customer.v2.ListActionStatesRequest.Filter
	(*ListActionStatesRequest_Sorting)(nil),             // 80: backend.proto.customer.v2.ListActionStatesRequest.Sorting
	(*UpdateTagsRequest_UpdateTag)(nil),                 // 81: backend.proto.customer.v2.UpdateTagsRequest.UpdateTag
	(*ListTagsRequest_Filter)(nil),                      // 82: backend.proto.customer.v2.ListTagsRequest.Filter
	(*ListTagsRequest_Sorting)(nil),                     // 83: backend.proto.customer.v2.ListTagsRequest.Sorting
	(*ListCustomerTagsRequest_Filter)(nil),              // 84: backend.proto.customer.v2.ListCustomerTagsRequest.Filter
	(*ListCustomerTagsRequest_Sorting)(nil),             // 85: backend.proto.customer.v2.ListCustomerTagsRequest.Sorting
	(*ListNotesRequest_Filter)(nil),                     // 86: backend.proto.customer.v2.ListNotesRequest.Filter
	(*ListNotesRequest_Sorting)(nil),                    // 87: backend.proto.customer.v2.ListNotesRequest.Sorting
	(*UpdateSourcesRequest_UpdateSource)(nil),           // 88: backend.proto.customer.v2.UpdateSourcesRequest.UpdateSource
	(*ListSourcesRequest_Filter)(nil),                   // 89: backend.proto.customer.v2.ListSourcesRequest.Filter
	(*ActivityLog_Action)(nil),                          // 90: backend.proto.customer.v2.ActivityLog.Action
	(*SystemSource)(nil),                                // 91: backend.proto.customer.v2.SystemSource
	(*ActivityLog)(nil),                                 // 92: backend.proto.customer.v2.ActivityLog
	(*timestamppb.Timestamp)(nil),                       // 93: google.protobuf.Timestamp
	(*Task)(nil),                                        // 94: backend.proto.customer.v2.Task
	(Task_State)(0),                                     // 95: backend.proto.customer.v2.Task.State
	(*LifeCycle)(nil),                                   // 96: backend.proto.customer.v2.LifeCycle
	(*ActionState)(nil),                                 // 97: backend.proto.customer.v2.ActionState
	(*Tag)(nil),                                         // 98: backend.proto.customer.v2.Tag
	(*CustomerTag)(nil),                                 // 99: backend.proto.customer.v2.CustomerTag
	(*Note)(nil),                                        // 100: backend.proto.customer.v2.Note
	(*Source)(nil),                                      // 101: backend.proto.customer.v2.Source
	(ActivityLog_Type)(0),                               // 102: backend.proto.customer.v2.ActivityLog.Type
	(*OrganizationRef)(nil),                             // 103: backend.proto.customer.v2.OrganizationRef
	(Direction)(0),                                      // 104: backend.proto.customer.v2.Direction
}
var file_backend_proto_customer_v2_activity_service_proto_depIdxs = []int32{
	90,  // 0: backend.proto.customer.v2.CreateCustomerActivityLogRequest.action:type_name -> backend.proto.customer.v2.ActivityLog.Action
	91,  // 1: backend.proto.customer.v2.CreateCustomerActivityLogRequest.source:type_name -> backend.proto.customer.v2.SystemSource
	92,  // 2: backend.proto.customer.v2.CreateCustomerActivityLogResponse.log:type_name -> backend.proto.customer.v2.ActivityLog
	90,  // 3: backend.proto.customer.v2.UpdateCustomerActivityLogRequest.action:type_name -> backend.proto.customer.v2.ActivityLog.Action
	92,  // 4: backend.proto.customer.v2.UpdateCustomerActivityLogResponse.log:type_name -> backend.proto.customer.v2.ActivityLog
	71,  // 5: backend.proto.customer.v2.ListCustomerActivityLogsRequest.filter:type_name -> backend.proto.customer.v2.ListCustomerActivityLogsRequest.Filter
	72,  // 6: backend.proto.customer.v2.ListCustomerActivityLogsRequest.sorting:type_name -> backend.proto.customer.v2.ListCustomerActivityLogsRequest.Sorting
	92,  // 7: backend.proto.customer.v2.ListCustomerActivityLogsResponse.activity_logs:type_name -> backend.proto.customer.v2.ActivityLog
	93,  // 8: backend.proto.customer.v2.CreateCustomerTaskRequest.complete_time:type_name -> google.protobuf.Timestamp
	94,  // 9: backend.proto.customer.v2.CreateCustomerTaskResponse.task:type_name -> backend.proto.customer.v2.Task
	93,  // 10: backend.proto.customer.v2.UpdateCustomerTaskRequest.complete_time:type_name -> google.protobuf.Timestamp
	95,  // 11: backend.proto.customer.v2.UpdateCustomerTaskRequest.state:type_name -> backend.proto.customer.v2.Task.State
	94,  // 12: backend.proto.customer.v2.UpdateCustomerTaskResponse.task:type_name -> backend.proto.customer.v2.Task
	73,  // 13: backend.proto.customer.v2.ListCustomerTasksRequest.filter:type_name -> backend.proto.customer.v2.ListCustomerTasksRequest.Filter
	74,  // 14: backend.proto.customer.v2.ListCustomerTasksRequest.sorting:type_name -> backend.proto.customer.v2.ListCustomerTasksRequest.Sorting
	94,  // 15: backend.proto.customer.v2.ListCustomerTasksResponse.tasks:type_name -> backend.proto.customer.v2.Task
	96,  // 16: backend.proto.customer.v2.CreateLifeCycleResponse.life_cycle:type_name -> backend.proto.customer.v2.LifeCycle
	75,  // 17: backend.proto.customer.v2.UpdateLifeCyclesRequest.updates:type_name -> backend.proto.customer.v2.UpdateLifeCyclesRequest.UpdateLifeCycle
	76,  // 18: backend.proto.customer.v2.ListLifeCyclesRequest.filter:type_name -> backend.proto.customer.v2.ListLifeCyclesRequest.Filter
	77,  // 19: backend.proto.customer.v2.ListLifeCyclesRequest.sorting:type_name -> backend.proto.customer.v2.ListLifeCyclesRequest.Sorting
	96,  // 20: backend.proto.customer.v2.ListLifeCyclesResponse.life_cycles:type_name -> backend.proto.customer.v2.LifeCycle
	97,  // 21: backend.proto.customer.v2.CreateActionStateResponse.action_state:type_name -> backend.proto.customer.v2.ActionState
	78,  // 22: backend.proto.customer.v2.UpdateActionStatesRequest.updates:type_name -> backend.proto.customer.v2.UpdateActionStatesRequest.UpdateActionState
	79,  // 23: backend.proto.customer.v2.ListActionStatesRequest.filter:type_name -> backend.proto.customer.v2.ListActionStatesRequest.Filter
	80,  // 24: backend.proto.customer.v2.ListActionStatesRequest.sorting:type_name -> backend.proto.customer.v2.ListActionStatesRequest.Sorting
	97,  // 25: backend.proto.customer.v2.ListActionStatesResponse.action_states:type_name -> backend.proto.customer.v2.ActionState
	98,  // 26: backend.proto.customer.v2.CreateTagResponse.tag:type_name -> backend.proto.customer.v2.Tag
	81,  // 27: backend.proto.customer.v2.UpdateTagsRequest.updates:type_name -> backend.proto.customer.v2.UpdateTagsRequest.UpdateTag
	82,  // 28: backend.proto.customer.v2.ListTagsRequest.filter:type_name -> backend.proto.customer.v2.ListTagsRequest.Filter
	83,  // 29: backend.proto.customer.v2.ListTagsRequest.sorting:type_name -> backend.proto.customer.v2.ListTagsRequest.Sorting
	98,  // 30: backend.proto.customer.v2.ListTagsResponse.tags:type_name -> backend.proto.customer.v2.Tag
	84,  // 31: backend.proto.customer.v2.ListCustomerTagsRequest.filter:type_name -> backend.proto.customer.v2.ListCustomerTagsRequest.Filter
	85,  // 32: backend.proto.customer.v2.ListCustomerTagsRequest.sorting:type_name -> backend.proto.customer.v2.ListCustomerTagsRequest.Sorting
	99,  // 33: backend.proto.customer.v2.ListCustomerTagsResponse.customer_tags:type_name -> backend.proto.customer.v2.CustomerTag
	91,  // 34: backend.proto.customer.v2.CreateNoteRequest.source:type_name -> backend.proto.customer.v2.SystemSource
	100, // 35: backend.proto.customer.v2.CreateNoteResponse.note:type_name -> backend.proto.customer.v2.Note
	91,  // 36: backend.proto.customer.v2.UpdateNotesRequest.source:type_name -> backend.proto.customer.v2.SystemSource
	100, // 37: backend.proto.customer.v2.UpdateNotesResponse.note:type_name -> backend.proto.customer.v2.Note
	86,  // 38: backend.proto.customer.v2.ListNotesRequest.filter:type_name -> backend.proto.customer.v2.ListNotesRequest.Filter
	87,  // 39: backend.proto.customer.v2.ListNotesRequest.sorting:type_name -> backend.proto.customer.v2.ListNotesRequest.Sorting
	100, // 40: backend.proto.customer.v2.ListNotesResponse.notes:type_name -> backend.proto.customer.v2.Note
	101, // 41: backend.proto.customer.v2.CreateSourceResponse.source:type_name -> backend.proto.customer.v2.Source
	88,  // 42: backend.proto.customer.v2.UpdateSourcesRequest.updates:type_name -> backend.proto.customer.v2.UpdateSourcesRequest.UpdateSource
	89,  // 43: backend.proto.customer.v2.ListSourcesRequest.filter:type_name -> backend.proto.customer.v2.ListSourcesRequest.Filter
	101, // 44: backend.proto.customer.v2.ListSourcesResponse.sources:type_name -> backend.proto.customer.v2.Source
	102, // 45: backend.proto.customer.v2.ListCustomerActivityLogsRequest.Filter.types:type_name -> backend.proto.customer.v2.ActivityLog.Type
	103, // 46: backend.proto.customer.v2.ListCustomerActivityLogsRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	0,   // 47: backend.proto.customer.v2.ListCustomerActivityLogsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListCustomerActivityLogsRequest.Sorting.Field
	104, // 48: backend.proto.customer.v2.ListCustomerActivityLogsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	1,   // 49: backend.proto.customer.v2.ListCustomerTasksRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListCustomerTasksRequest.Sorting.Field
	104, // 50: backend.proto.customer.v2.ListCustomerTasksRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	103, // 51: backend.proto.customer.v2.ListLifeCyclesRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	2,   // 52: backend.proto.customer.v2.ListLifeCyclesRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListLifeCyclesRequest.Sorting.Field
	104, // 53: backend.proto.customer.v2.ListLifeCyclesRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	103, // 54: backend.proto.customer.v2.ListActionStatesRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	3,   // 55: backend.proto.customer.v2.ListActionStatesRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListActionStatesRequest.Sorting.Field
	104, // 56: backend.proto.customer.v2.ListActionStatesRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	103, // 57: backend.proto.customer.v2.ListTagsRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	4,   // 58: backend.proto.customer.v2.ListTagsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListTagsRequest.Sorting.Field
	104, // 59: backend.proto.customer.v2.ListTagsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	5,   // 60: backend.proto.customer.v2.ListCustomerTagsRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListCustomerTagsRequest.Sorting.Field
	104, // 61: backend.proto.customer.v2.ListCustomerTagsRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	103, // 62: backend.proto.customer.v2.ListNotesRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	6,   // 63: backend.proto.customer.v2.ListNotesRequest.Sorting.field:type_name -> backend.proto.customer.v2.ListNotesRequest.Sorting.Field
	104, // 64: backend.proto.customer.v2.ListNotesRequest.Sorting.direction:type_name -> backend.proto.customer.v2.Direction
	103, // 65: backend.proto.customer.v2.ListSourcesRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	7,   // 66: backend.proto.customer.v2.ActivityService.CreateCustomerActivityLog:input_type -> backend.proto.customer.v2.CreateCustomerActivityLogRequest
	9,   // 67: backend.proto.customer.v2.ActivityService.UpdateCustomerActivityLog:input_type -> backend.proto.customer.v2.UpdateCustomerActivityLogRequest
	11,  // 68: backend.proto.customer.v2.ActivityService.ListCustomerActivityLogs:input_type -> backend.proto.customer.v2.ListCustomerActivityLogsRequest
	13,  // 69: backend.proto.customer.v2.ActivityService.ConvertCustomer:input_type -> backend.proto.customer.v2.ConvertCustomerRequest
	15,  // 70: backend.proto.customer.v2.ActivityService.ConvertCustomersAttribute:input_type -> backend.proto.customer.v2.ConvertCustomersAttributeRequest
	17,  // 71: backend.proto.customer.v2.ActivityService.CreateCustomerTask:input_type -> backend.proto.customer.v2.CreateCustomerTaskRequest
	19,  // 72: backend.proto.customer.v2.ActivityService.UpdateCustomerTask:input_type -> backend.proto.customer.v2.UpdateCustomerTaskRequest
	21,  // 73: backend.proto.customer.v2.ActivityService.ListCustomerTasks:input_type -> backend.proto.customer.v2.ListCustomerTasksRequest
	23,  // 74: backend.proto.customer.v2.ActivityService.DeleteCustomerTask:input_type -> backend.proto.customer.v2.DeleteCustomerTaskRequest
	25,  // 75: backend.proto.customer.v2.ActivityService.CreateLifeCycle:input_type -> backend.proto.customer.v2.CreateLifeCycleRequest
	27,  // 76: backend.proto.customer.v2.ActivityService.UpdateLifeCycles:input_type -> backend.proto.customer.v2.UpdateLifeCyclesRequest
	29,  // 77: backend.proto.customer.v2.ActivityService.ListLifeCycles:input_type -> backend.proto.customer.v2.ListLifeCyclesRequest
	31,  // 78: backend.proto.customer.v2.ActivityService.DeleteLifeCycle:input_type -> backend.proto.customer.v2.DeleteLifeCycleRequest
	33,  // 79: backend.proto.customer.v2.ActivityService.CreateActionState:input_type -> backend.proto.customer.v2.CreateActionStateRequest
	35,  // 80: backend.proto.customer.v2.ActivityService.UpdateActionStates:input_type -> backend.proto.customer.v2.UpdateActionStatesRequest
	37,  // 81: backend.proto.customer.v2.ActivityService.ListActionStates:input_type -> backend.proto.customer.v2.ListActionStatesRequest
	39,  // 82: backend.proto.customer.v2.ActivityService.DeleteActionState:input_type -> backend.proto.customer.v2.DeleteActionStateRequest
	41,  // 83: backend.proto.customer.v2.ActivityService.CreateTag:input_type -> backend.proto.customer.v2.CreateTagRequest
	43,  // 84: backend.proto.customer.v2.ActivityService.UpdateTags:input_type -> backend.proto.customer.v2.UpdateTagsRequest
	45,  // 85: backend.proto.customer.v2.ActivityService.ListTags:input_type -> backend.proto.customer.v2.ListTagsRequest
	47,  // 86: backend.proto.customer.v2.ActivityService.DeleteTag:input_type -> backend.proto.customer.v2.DeleteTagRequest
	49,  // 87: backend.proto.customer.v2.ActivityService.CoverCustomerTag:input_type -> backend.proto.customer.v2.CoverCustomerTagRequest
	51,  // 88: backend.proto.customer.v2.ActivityService.BatchAddCustomersTags:input_type -> backend.proto.customer.v2.BatchAddCustomersTagsRequest
	53,  // 89: backend.proto.customer.v2.ActivityService.ListCustomerTags:input_type -> backend.proto.customer.v2.ListCustomerTagsRequest
	55,  // 90: backend.proto.customer.v2.ActivityService.CreateNote:input_type -> backend.proto.customer.v2.CreateNoteRequest
	57,  // 91: backend.proto.customer.v2.ActivityService.UpdateNotes:input_type -> backend.proto.customer.v2.UpdateNotesRequest
	59,  // 92: backend.proto.customer.v2.ActivityService.ListNotes:input_type -> backend.proto.customer.v2.ListNotesRequest
	61,  // 93: backend.proto.customer.v2.ActivityService.DeleteNote:input_type -> backend.proto.customer.v2.DeleteNoteRequest
	63,  // 94: backend.proto.customer.v2.ActivityService.CreateSource:input_type -> backend.proto.customer.v2.CreateSourceRequest
	65,  // 95: backend.proto.customer.v2.ActivityService.UpdateSources:input_type -> backend.proto.customer.v2.UpdateSourcesRequest
	67,  // 96: backend.proto.customer.v2.ActivityService.ListSources:input_type -> backend.proto.customer.v2.ListSourcesRequest
	69,  // 97: backend.proto.customer.v2.ActivityService.DeleteSource:input_type -> backend.proto.customer.v2.DeleteSourceRequest
	8,   // 98: backend.proto.customer.v2.ActivityService.CreateCustomerActivityLog:output_type -> backend.proto.customer.v2.CreateCustomerActivityLogResponse
	10,  // 99: backend.proto.customer.v2.ActivityService.UpdateCustomerActivityLog:output_type -> backend.proto.customer.v2.UpdateCustomerActivityLogResponse
	12,  // 100: backend.proto.customer.v2.ActivityService.ListCustomerActivityLogs:output_type -> backend.proto.customer.v2.ListCustomerActivityLogsResponse
	14,  // 101: backend.proto.customer.v2.ActivityService.ConvertCustomer:output_type -> backend.proto.customer.v2.ConvertCustomerResponse
	16,  // 102: backend.proto.customer.v2.ActivityService.ConvertCustomersAttribute:output_type -> backend.proto.customer.v2.ConvertCustomersAttributeResponse
	18,  // 103: backend.proto.customer.v2.ActivityService.CreateCustomerTask:output_type -> backend.proto.customer.v2.CreateCustomerTaskResponse
	20,  // 104: backend.proto.customer.v2.ActivityService.UpdateCustomerTask:output_type -> backend.proto.customer.v2.UpdateCustomerTaskResponse
	22,  // 105: backend.proto.customer.v2.ActivityService.ListCustomerTasks:output_type -> backend.proto.customer.v2.ListCustomerTasksResponse
	24,  // 106: backend.proto.customer.v2.ActivityService.DeleteCustomerTask:output_type -> backend.proto.customer.v2.DeleteCustomerTaskResponse
	26,  // 107: backend.proto.customer.v2.ActivityService.CreateLifeCycle:output_type -> backend.proto.customer.v2.CreateLifeCycleResponse
	28,  // 108: backend.proto.customer.v2.ActivityService.UpdateLifeCycles:output_type -> backend.proto.customer.v2.UpdateLifeCyclesResponse
	30,  // 109: backend.proto.customer.v2.ActivityService.ListLifeCycles:output_type -> backend.proto.customer.v2.ListLifeCyclesResponse
	32,  // 110: backend.proto.customer.v2.ActivityService.DeleteLifeCycle:output_type -> backend.proto.customer.v2.DeleteLifeCycleResponse
	34,  // 111: backend.proto.customer.v2.ActivityService.CreateActionState:output_type -> backend.proto.customer.v2.CreateActionStateResponse
	36,  // 112: backend.proto.customer.v2.ActivityService.UpdateActionStates:output_type -> backend.proto.customer.v2.UpdateActionsStatesResponse
	38,  // 113: backend.proto.customer.v2.ActivityService.ListActionStates:output_type -> backend.proto.customer.v2.ListActionStatesResponse
	40,  // 114: backend.proto.customer.v2.ActivityService.DeleteActionState:output_type -> backend.proto.customer.v2.DeleteActionStateResponse
	42,  // 115: backend.proto.customer.v2.ActivityService.CreateTag:output_type -> backend.proto.customer.v2.CreateTagResponse
	44,  // 116: backend.proto.customer.v2.ActivityService.UpdateTags:output_type -> backend.proto.customer.v2.UpdateTagsResponse
	46,  // 117: backend.proto.customer.v2.ActivityService.ListTags:output_type -> backend.proto.customer.v2.ListTagsResponse
	48,  // 118: backend.proto.customer.v2.ActivityService.DeleteTag:output_type -> backend.proto.customer.v2.DeleteTagResponse
	50,  // 119: backend.proto.customer.v2.ActivityService.CoverCustomerTag:output_type -> backend.proto.customer.v2.CoverCustomerTagResponse
	52,  // 120: backend.proto.customer.v2.ActivityService.BatchAddCustomersTags:output_type -> backend.proto.customer.v2.BatchAddCustomersTagsResponse
	54,  // 121: backend.proto.customer.v2.ActivityService.ListCustomerTags:output_type -> backend.proto.customer.v2.ListCustomerTagsResponse
	56,  // 122: backend.proto.customer.v2.ActivityService.CreateNote:output_type -> backend.proto.customer.v2.CreateNoteResponse
	58,  // 123: backend.proto.customer.v2.ActivityService.UpdateNotes:output_type -> backend.proto.customer.v2.UpdateNotesResponse
	60,  // 124: backend.proto.customer.v2.ActivityService.ListNotes:output_type -> backend.proto.customer.v2.ListNotesResponse
	62,  // 125: backend.proto.customer.v2.ActivityService.DeleteNote:output_type -> backend.proto.customer.v2.DeleteNoteResponse
	64,  // 126: backend.proto.customer.v2.ActivityService.CreateSource:output_type -> backend.proto.customer.v2.CreateSourceResponse
	66,  // 127: backend.proto.customer.v2.ActivityService.UpdateSources:output_type -> backend.proto.customer.v2.UpdateSourcesResponse
	68,  // 128: backend.proto.customer.v2.ActivityService.ListSources:output_type -> backend.proto.customer.v2.ListSourcesResponse
	70,  // 129: backend.proto.customer.v2.ActivityService.DeleteSource:output_type -> backend.proto.customer.v2.DeleteSourceResponse
	98,  // [98:130] is the sub-list for method output_type
	66,  // [66:98] is the sub-list for method input_type
	66,  // [66:66] is the sub-list for extension type_name
	66,  // [66:66] is the sub-list for extension extendee
	0,   // [0:66] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v2_activity_service_proto_init() }
func file_backend_proto_customer_v2_activity_service_proto_init() {
	if File_backend_proto_customer_v2_activity_service_proto != nil {
		return
	}
	file_backend_proto_customer_v2_activity_proto_init()
	file_backend_proto_customer_v2_common_proto_init()
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[5].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[8].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[10].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[15].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[23].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[31].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[39].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[47].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[50].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[53].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[61].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[68].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[71].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[74].OneofWrappers = []any{}
	file_backend_proto_customer_v2_activity_service_proto_msgTypes[81].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_activity_service_proto_rawDesc), len(file_backend_proto_customer_v2_activity_service_proto_rawDesc)),
			NumEnums:      7,
			NumMessages:   83,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_customer_v2_activity_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v2_activity_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v2_activity_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v2_activity_service_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v2_activity_service_proto = out.File
	file_backend_proto_customer_v2_activity_service_proto_goTypes = nil
	file_backend_proto_customer_v2_activity_service_proto_depIdxs = nil
}
