// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/customer/v2/data_migration.proto

package customerpb

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// status
type ImportHistory_State int32

const (
	// 未指定的状态
	ImportHistory_STATE_UNSPECIFIED ImportHistory_State = 0
	// 已成功
	ImportHistory_SUCCEEDED ImportHistory_State = 1
	// 进行中
	ImportHistory_IN_PROGRESS ImportHistory_State = 2
	// 失败
	ImportHistory_FAILED ImportHistory_State = 3
)

// Enum value maps for ImportHistory_State.
var (
	ImportHistory_State_name = map[int32]string{
		0: "STATE_UNSPECIFIED",
		1: "SUCCEEDED",
		2: "IN_PROGRESS",
		3: "FAILED",
	}
	ImportHistory_State_value = map[string]int32{
		"STATE_UNSPECIFIED": 0,
		"SUCCEEDED":         1,
		"IN_PROGRESS":       2,
		"FAILED":            3,
	}
)

func (x ImportHistory_State) Enum() *ImportHistory_State {
	p := new(ImportHistory_State)
	*p = x
	return p
}

func (x ImportHistory_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ImportHistory_State) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_data_migration_proto_enumTypes[0].Descriptor()
}

func (ImportHistory_State) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_data_migration_proto_enumTypes[0]
}

func (x ImportHistory_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ImportHistory_State.Descriptor instead.
func (ImportHistory_State) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_data_migration_proto_rawDescGZIP(), []int{0, 0}
}

// ImportHistory 导入历史
type ImportHistory struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// target organization
	Organization *OrganizationRef `protobuf:"bytes,3,opt,name=organization,proto3" json:"organization,omitempty"`
	// state
	State ImportHistory_State `protobuf:"varint,2,opt,name=state,proto3,enum=backend.proto.customer.v2.ImportHistory_State" json:"state,omitempty"`
	// upload file uri
	UploadFileUri string `protobuf:"bytes,4,opt,name=upload_file_uri,json=uploadFileUri,proto3" json:"upload_file_uri,omitempty"`
	// error message
	ErrorMessage string `protobuf:"bytes,5,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	// 创建时间
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// 更新时间
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// 删除时间
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportHistory) Reset() {
	*x = ImportHistory{}
	mi := &file_backend_proto_customer_v2_data_migration_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportHistory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportHistory) ProtoMessage() {}

func (x *ImportHistory) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_data_migration_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportHistory.ProtoReflect.Descriptor instead.
func (*ImportHistory) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_data_migration_proto_rawDescGZIP(), []int{0}
}

func (x *ImportHistory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ImportHistory) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *ImportHistory) GetState() ImportHistory_State {
	if x != nil {
		return x.State
	}
	return ImportHistory_STATE_UNSPECIFIED
}

func (x *ImportHistory) GetUploadFileUri() string {
	if x != nil {
		return x.UploadFileUri
	}
	return ""
}

func (x *ImportHistory) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

func (x *ImportHistory) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ImportHistory) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *ImportHistory) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

var File_backend_proto_customer_v2_data_migration_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v2_data_migration_proto_rawDesc = "" +
	"\n" +
	".backend/proto/customer/v2/data_migration.proto\x12\x19backend.proto.customer.v2\x1a&backend/proto/customer/v2/common.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xa9\x04\n" +
	"\rImportHistory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12N\n" +
	"\forganization\x18\x03 \x01(\v2*.backend.proto.customer.v2.OrganizationRefR\forganization\x12I\n" +
	"\x05state\x18\x02 \x01(\x0e2..backend.proto.customer.v2.ImportHistory.StateB\x03\xe0A\x03R\x05state\x12&\n" +
	"\x0fupload_file_uri\x18\x04 \x01(\tR\ruploadFileUri\x12#\n" +
	"\rerror_message\x18\x05 \x01(\tR\ferrorMessage\x12@\n" +
	"\vcreate_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"createTime\x12@\n" +
	"\vupdate_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampB\x03\xe0A\x03R\n" +
	"updateTime\x12@\n" +
	"\vdelete_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampH\x00R\n" +
	"deleteTime\x88\x01\x01\"J\n" +
	"\x05State\x12\x15\n" +
	"\x11STATE_UNSPECIFIED\x10\x00\x12\r\n" +
	"\tSUCCEEDED\x10\x01\x12\x0f\n" +
	"\vIN_PROGRESS\x10\x02\x12\n" +
	"\n" +
	"\x06FAILED\x10\x03B\x0e\n" +
	"\f_delete_timeBk\n" +
	"#com.moego.backend.proto.customer.v2P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v2_data_migration_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v2_data_migration_proto_rawDescData []byte
)

func file_backend_proto_customer_v2_data_migration_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v2_data_migration_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v2_data_migration_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_data_migration_proto_rawDesc), len(file_backend_proto_customer_v2_data_migration_proto_rawDesc)))
	})
	return file_backend_proto_customer_v2_data_migration_proto_rawDescData
}

var file_backend_proto_customer_v2_data_migration_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_customer_v2_data_migration_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_customer_v2_data_migration_proto_goTypes = []any{
	(ImportHistory_State)(0),      // 0: backend.proto.customer.v2.ImportHistory.State
	(*ImportHistory)(nil),         // 1: backend.proto.customer.v2.ImportHistory
	(*OrganizationRef)(nil),       // 2: backend.proto.customer.v2.OrganizationRef
	(*timestamppb.Timestamp)(nil), // 3: google.protobuf.Timestamp
}
var file_backend_proto_customer_v2_data_migration_proto_depIdxs = []int32{
	2, // 0: backend.proto.customer.v2.ImportHistory.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	0, // 1: backend.proto.customer.v2.ImportHistory.state:type_name -> backend.proto.customer.v2.ImportHistory.State
	3, // 2: backend.proto.customer.v2.ImportHistory.create_time:type_name -> google.protobuf.Timestamp
	3, // 3: backend.proto.customer.v2.ImportHistory.update_time:type_name -> google.protobuf.Timestamp
	3, // 4: backend.proto.customer.v2.ImportHistory.delete_time:type_name -> google.protobuf.Timestamp
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v2_data_migration_proto_init() }
func file_backend_proto_customer_v2_data_migration_proto_init() {
	if File_backend_proto_customer_v2_data_migration_proto != nil {
		return
	}
	file_backend_proto_customer_v2_common_proto_init()
	file_backend_proto_customer_v2_data_migration_proto_msgTypes[0].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_data_migration_proto_rawDesc), len(file_backend_proto_customer_v2_data_migration_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_customer_v2_data_migration_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v2_data_migration_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v2_data_migration_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v2_data_migration_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v2_data_migration_proto = out.File
	file_backend_proto_customer_v2_data_migration_proto_goTypes = nil
	file_backend_proto_customer_v2_data_migration_proto_depIdxs = nil
}
