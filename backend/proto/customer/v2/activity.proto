// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用name后缀 --)
// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: 不使用behavior --)
// (-- api-linter: core::0216::synonyms=disabled
//     aip.dev/not-precedent: 对齐 moego 内部使用习惯 --)
// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: 对齐 moego 内部使用习惯 --)
syntax = "proto3";

package backend.proto.customer.v2;

import "backend/proto/customer/v2/common.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v2";

// 生命周期状态
message LifeCycle {
  // ID
  int64 id = 1;
  // 名称
  string name = 2;
  // 排序
  int32 sort = 3;
  // 是否默认 0-否 1-是
  int32 is_default = 4;
  // 公司名
  int64 company_id = 5;
}

// 行动状态
message ActionState {
  // ID
  int64 id = 1;
  // 名称
  string name = 2;
  // 排序
  int32 sort = 3;
  // 颜色
  string color = 4;
  // 公司名
  int64 company_id = 5;
}

// Task 表示任务信息
message Task {
  // State 表示任务状态
  enum State {
    // 未指定的状态
    STATE_UNSPECIFIED = 0;
    // 新任务
    NEW = 1;
    // 已完成
    FINISH = 2;
  }

  // 任务ID
  int64 id = 1;
  // 任务名称
  string name = 2;
  // 分配员工
  optional int64 allocate_staff_id = 3;
  // 预期完成的时间
  optional google.protobuf.Timestamp complete_time = 4;
  // 任务状态
  State state = 5;
}

// ActivityLog 表示活动记录
message ActivityLog {
  // SMS 表示短信历史记录
  message Message {
    // State 表示短信状态
    enum State {
      // 未指定的状态
      STATE_UNSPECIFIED = 0;
      // 发送成功
      SUCCEEDED = 1;
      // 发送失败
      FAILED = 2;
    }
    // Direction 表示收发方向
    enum Direction {
      // 未指定的状态
      DIRECTION_UNSPECIFIED = 0;
      // 发送
      SEND = 1;
      // 接收
      RECEIVE = 2;
    }
    // 短信ID
    int64 message_id = 1;
    // 短信内容
    string text = 2;
    // 发送状态
    State state = 3;
    // 失败原因
    string fail_reason = 4;
    // 收发方向
    Direction direction = 5;
  }

  // Call 表示通话历史记录
  message Call {
    // State 表示通话状态
    enum State {
      // 未指定的状态
      STATE_UNSPECIFIED = 0;
      // 接通
      ANSWERED = 1;
      // 未接
      NO_ANSWER = 2;
    }
    // Direction 表示收发方向
    enum Direction {
      // 未指定的状态
      DIRECTION_UNSPECIFIED = 0;
      // 用户联系商家
      INCOMING = 1;
      // 商家联系用户
      OUTGOING = 2;
    }
    // 通话ID
    int64 call_id = 1;
    // 通话记录
    string text = 2;
    // 通话状态
    State state = 3;
    // 失败原因
    string fail_reason = 4;
    // 收发方向
    Direction direction = 5;
  }

  // Note 表示备注历史记录
  message Note {
    // 备注内容
    string text = 1;
  }

  // Task 表示任务历史记录
  message Task {
    // Type 表示任务历史记录类型
    enum Type {
      // 未指定的记录类型
      TYPE_UNSPECIFIED = 0;
      // 创建任务
      CREATE = 1;
      // 更新任务
      UPDATE = 2;
      // 完成任务
      FINISH = 3;
      // 删除任务
      DELETE = 4;
    }
    // 记录类型
    Type type = 1;
    // 任务信息
    backend.proto.customer.v2.Task task = 2;
  }

  // Convert 表示类型转化记录
  message Convert {
    // 转换类型
    enum ConvertType {
      // 未指定的记录类型
      CONVERT_TYPE_UNSPECIFIED = 0;
      // LEAD
      LEAD = 1;
      // CUSTOMER
      CUSTOMER = 2;
    }
    // 转化前类型
    ConvertType origin_type = 1;
    // 转化前的ID
    int64 origin_id = 2;
    // 转化后的类型
    ConvertType target_type = 3;
    // 转化后的ID
    int64 target_id = 4;
  }

  // Create 表示用户创建记录
  message Create {}

  // Type 表示历史记录类型
  enum Type {
    // 未指定的记录类型
    TYPE_UNSPECIFIED = 0;
    // 短信记录
    MESSAGE = 1;
    // 通话记录
    CALL = 2;
    // 备注记录
    NOTE = 3;
    // 任务记录
    TASK = 4;
    // 类型转化的记录
    CONVERT = 5;
    // 用户创建记录
    CREATE = 6;
  }

  // actions
  message Action {
    oneof action {
      // 短信消息
      Message message = 1;
      // 通话记录
      Call call = 2;
      // 备注记录
      Note note = 3;
      // 任务记录
      Task task = 4;
      // 转化记录
      Convert convert = 5;
      // 创建记录
      Create create = 6;
    }
  }
  // 记录ID
  int64 id = 1;
  // 客户ID
  int64 customer_id = 2;
  // 客户名称
  string customer_name = 10;
  // 客户电话
  string customer_phone_number = 11;
  // 记录类型
  Type type = 3;
  // 记录数据
  Action action = 4;
  // 创建时间
  google.protobuf.Timestamp create_time = 5;
  // 记录来源
  SystemSource source = 6;
}

// Note 表示摘要
message Note {
  // id
  int64 id = 1;
  // 备注内容
  string text = 2;
  // 创建来源
  SystemSource create_source = 3;
  // 更新来源
  SystemSource update_source = 4;
  // 创建时间
  google.protobuf.Timestamp create_time = 5;
  // 更新时间
  google.protobuf.Timestamp update_time = 6;
  // CustomerID
  int64 customer_id = 7;
}

// Tag 标签
message Tag {
  // id
  int64 id = 1;
  // 名称
  string name = 2;
  // 排序
  int32 sort = 3;
  // 组织
  OrganizationRef organization_ref = 4;
}

// CustomerTag 客户标签
message CustomerTag {
  // id
  int64 id = 1;
  // 标签
  Tag tag = 2;
  // 客户ID
  int64 customer_id = 3;
}

// 用户自定义来源
message Source {
  // id
  int64 id = 1;
  // 名称
  string name = 2;
  // 排序
  int32 sort = 3;
  // 组织
  OrganizationRef organization_ref = 4;
}

// SystemSource 系统维护的来源字段
message SystemSource {
  // Source 来源类型
  enum Source {
    // 未指定的记录类型
    SOURCE_UNSPECIFIED = 0;
    // 员工操作
    STAFF = 1;
    // 预约
    APPOINTMENT = 2;
    // OB
    ONLINE_BOOKING = 3;
    // product(retail)
    PRODUCT = 4;
    // package
    PACKAGE = 5;
    // fulfillment(new appointment)
    FULFILLMENT = 6;
    // membership
    MEMBERSHIP = 7;
  }
  // 记录来源
  Source source = 1;
  // 记录来源ID
  int64 source_id = 2;
  // 记录来源名称
  string source_name = 3;
}

