syntax = "proto3";

package backend.proto.customer.v2;

import "backend/proto/customer/v2/common.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v2";

// ImportHistory 导入历史
message ImportHistory {
  // status
  enum State {
    // 未指定的状态
    STATE_UNSPECIFIED = 0;
    // 已成功
    SUCCEEDED = 1;
    // 进行中
    IN_PROGRESS = 2;
    // 失败
    FAILED = 3;
  }
  // id
  int64 id = 1;
  // target organization
  OrganizationRef organization = 3;
  // state
  State state = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
  // upload file uri
  string upload_file_uri = 4;
  // error message
  string error_message = 5;
  // 创建时间
  google.protobuf.Timestamp create_time = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
  // 更新时间
  google.protobuf.Timestamp update_time = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
  // 删除时间
  optional google.protobuf.Timestamp delete_time = 8;
}