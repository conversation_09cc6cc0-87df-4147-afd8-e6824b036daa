// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/customer/v2/common.proto

package customerpb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// direction of the sorting
type Direction int32

const (
	// default value
	Direction_DIRECTION_UNSPECIFIED Direction = 0
	// ascending
	Direction_ASC Direction = 1
	// descending
	Direction_DESC Direction = 2
)

// Enum value maps for Direction.
var (
	Direction_name = map[int32]string{
		0: "DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	Direction_value = map[string]int32{
		"DIRECTION_UNSPECIFIED": 0,
		"ASC":                   1,
		"DESC":                  2,
	}
)

func (x Direction) Enum() *Direction {
	p := new(Direction)
	*p = x
	return p
}

func (x Direction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Direction) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_common_proto_enumTypes[0].Descriptor()
}

func (Direction) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_common_proto_enumTypes[0]
}

func (x Direction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Direction.Descriptor instead.
func (Direction) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_common_proto_rawDescGZIP(), []int{0}
}

// organization type enumeration
type OrganizationRef_Type int32

const (
	// 0 is reserved for unspecified
	OrganizationRef_TYPE_UNSPECIFIED OrganizationRef_Type = 0
	// 1 is reserved for business
	OrganizationRef_BUSINESS OrganizationRef_Type = 1
	// 2 is reserved for company
	OrganizationRef_COMPANY OrganizationRef_Type = 2
	// 3 is reserved for enterprise
	OrganizationRef_ENTERPRISE OrganizationRef_Type = 3
	// system
	// 系统级别数据, 比如系统标签, 一般用不上
	OrganizationRef_SYSTEM OrganizationRef_Type = 10
)

// Enum value maps for OrganizationRef_Type.
var (
	OrganizationRef_Type_name = map[int32]string{
		0:  "TYPE_UNSPECIFIED",
		1:  "BUSINESS",
		2:  "COMPANY",
		3:  "ENTERPRISE",
		10: "SYSTEM",
	}
	OrganizationRef_Type_value = map[string]int32{
		"TYPE_UNSPECIFIED": 0,
		"BUSINESS":         1,
		"COMPANY":          2,
		"ENTERPRISE":       3,
		"SYSTEM":           10,
	}
)

func (x OrganizationRef_Type) Enum() *OrganizationRef_Type {
	p := new(OrganizationRef_Type)
	*p = x
	return p
}

func (x OrganizationRef_Type) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (OrganizationRef_Type) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v2_common_proto_enumTypes[1].Descriptor()
}

func (OrganizationRef_Type) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v2_common_proto_enumTypes[1]
}

func (x OrganizationRef_Type) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use OrganizationRef_Type.Descriptor instead.
func (OrganizationRef_Type) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_common_proto_rawDescGZIP(), []int{0, 0}
}

// 这个pb 是 customer 域下共用的结构
//
// universal organizational structure
type OrganizationRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// type of the organization
	Type OrganizationRef_Type `protobuf:"varint,1,opt,name=type,proto3,enum=backend.proto.customer.v2.OrganizationRef_Type" json:"type,omitempty"`
	// id
	Id            int64 `protobuf:"varint,2,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OrganizationRef) Reset() {
	*x = OrganizationRef{}
	mi := &file_backend_proto_customer_v2_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OrganizationRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OrganizationRef) ProtoMessage() {}

func (x *OrganizationRef) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OrganizationRef.ProtoReflect.Descriptor instead.
func (*OrganizationRef) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_common_proto_rawDescGZIP(), []int{0}
}

func (x *OrganizationRef) GetType() OrganizationRef_Type {
	if x != nil {
		return x.Type
	}
	return OrganizationRef_TYPE_UNSPECIFIED
}

func (x *OrganizationRef) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

var File_backend_proto_customer_v2_common_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v2_common_proto_rawDesc = "" +
	"\n" +
	"&backend/proto/customer/v2/common.proto\x12\x19backend.proto.customer.v2\"\xbb\x01\n" +
	"\x0fOrganizationRef\x12C\n" +
	"\x04type\x18\x01 \x01(\x0e2/.backend.proto.customer.v2.OrganizationRef.TypeR\x04type\x12\x0e\n" +
	"\x02id\x18\x02 \x01(\x03R\x02id\"S\n" +
	"\x04Type\x12\x14\n" +
	"\x10TYPE_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bBUSINESS\x10\x01\x12\v\n" +
	"\aCOMPANY\x10\x02\x12\x0e\n" +
	"\n" +
	"ENTERPRISE\x10\x03\x12\n" +
	"\n" +
	"\x06SYSTEM\x10\n" +
	"*9\n" +
	"\tDirection\x12\x19\n" +
	"\x15DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02Bk\n" +
	"#com.moego.backend.proto.customer.v2P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v2_common_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v2_common_proto_rawDescData []byte
)

func file_backend_proto_customer_v2_common_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v2_common_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v2_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_common_proto_rawDesc), len(file_backend_proto_customer_v2_common_proto_rawDesc)))
	})
	return file_backend_proto_customer_v2_common_proto_rawDescData
}

var file_backend_proto_customer_v2_common_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_backend_proto_customer_v2_common_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_backend_proto_customer_v2_common_proto_goTypes = []any{
	(Direction)(0),            // 0: backend.proto.customer.v2.Direction
	(OrganizationRef_Type)(0), // 1: backend.proto.customer.v2.OrganizationRef.Type
	(*OrganizationRef)(nil),   // 2: backend.proto.customer.v2.OrganizationRef
}
var file_backend_proto_customer_v2_common_proto_depIdxs = []int32{
	1, // 0: backend.proto.customer.v2.OrganizationRef.type:type_name -> backend.proto.customer.v2.OrganizationRef.Type
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v2_common_proto_init() }
func file_backend_proto_customer_v2_common_proto_init() {
	if File_backend_proto_customer_v2_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_common_proto_rawDesc), len(file_backend_proto_customer_v2_common_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_customer_v2_common_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v2_common_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v2_common_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v2_common_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v2_common_proto = out.File
	file_backend_proto_customer_v2_common_proto_goTypes = nil
	file_backend_proto_customer_v2_common_proto_depIdxs = nil
}
