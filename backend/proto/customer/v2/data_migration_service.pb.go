// (-- api-linter: core::0216::state-field-output-only=disabled
//     aip.dev/not-precedent: 入参不需要指定 output_only --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/customer/v2/data_migration_service.proto

package customerpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ImportCustomersRequest 导入客户请求
type ImportCustomersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 上传文件 uri
	UploadFileUri string `protobuf:"bytes,1,opt,name=upload_file_uri,json=uploadFileUri,proto3" json:"upload_file_uri,omitempty"`
	// organization
	Organization *OrganizationRef `protobuf:"bytes,2,opt,name=organization,proto3" json:"organization,omitempty"`
	// options
	Options *ImportCustomersRequest_Options `protobuf:"bytes,3,opt,name=options,proto3" json:"options,omitempty"`
	// operator staff id
	StaffId       int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportCustomersRequest) Reset() {
	*x = ImportCustomersRequest{}
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportCustomersRequest) ProtoMessage() {}

func (x *ImportCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportCustomersRequest.ProtoReflect.Descriptor instead.
func (*ImportCustomersRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_data_migration_service_proto_rawDescGZIP(), []int{0}
}

func (x *ImportCustomersRequest) GetUploadFileUri() string {
	if x != nil {
		return x.UploadFileUri
	}
	return ""
}

func (x *ImportCustomersRequest) GetOrganization() *OrganizationRef {
	if x != nil {
		return x.Organization
	}
	return nil
}

func (x *ImportCustomersRequest) GetOptions() *ImportCustomersRequest_Options {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *ImportCustomersRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// ImportCustomersResponse 导入客户响应
type ImportCustomersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ImportCustomersResponse) Reset() {
	*x = ImportCustomersResponse{}
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportCustomersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportCustomersResponse) ProtoMessage() {}

func (x *ImportCustomersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportCustomersResponse.ProtoReflect.Descriptor instead.
func (*ImportCustomersResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_data_migration_service_proto_rawDescGZIP(), []int{1}
}

// ListImportHistoriesRequest 列出导入历史请求
type ListImportHistoriesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// filter
	Filter *ListImportHistoriesRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// page size
	PageSize int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// page token
	PageToken     string `protobuf:"bytes,3,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListImportHistoriesRequest) Reset() {
	*x = ListImportHistoriesRequest{}
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListImportHistoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImportHistoriesRequest) ProtoMessage() {}

func (x *ListImportHistoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImportHistoriesRequest.ProtoReflect.Descriptor instead.
func (*ListImportHistoriesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_data_migration_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListImportHistoriesRequest) GetFilter() *ListImportHistoriesRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListImportHistoriesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListImportHistoriesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// ListImportHistoriesResponse 列出导入历史响应
type ListImportHistoriesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// import histories
	ImportHistories []*ImportHistory `protobuf:"bytes,1,rep,name=import_histories,json=importHistories,proto3" json:"import_histories,omitempty"`
	// 下一页令牌
	NextPageToken *string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3,oneof" json:"next_page_token,omitempty"`
	// 总数量
	TotalSize     *int64 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3,oneof" json:"total_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListImportHistoriesResponse) Reset() {
	*x = ListImportHistoriesResponse{}
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListImportHistoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImportHistoriesResponse) ProtoMessage() {}

func (x *ListImportHistoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImportHistoriesResponse.ProtoReflect.Descriptor instead.
func (*ListImportHistoriesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_data_migration_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListImportHistoriesResponse) GetImportHistories() []*ImportHistory {
	if x != nil {
		return x.ImportHistories
	}
	return nil
}

func (x *ListImportHistoriesResponse) GetNextPageToken() string {
	if x != nil && x.NextPageToken != nil {
		return *x.NextPageToken
	}
	return ""
}

func (x *ListImportHistoriesResponse) GetTotalSize() int64 {
	if x != nil && x.TotalSize != nil {
		return *x.TotalSize
	}
	return 0
}

// options
type ImportCustomersRequest_Options struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 遇到唯一键冲突时是否覆盖
	OverwriteOnConflict bool `protobuf:"varint,1,opt,name=overwrite_on_conflict,json=overwriteOnConflict,proto3" json:"overwrite_on_conflict,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ImportCustomersRequest_Options) Reset() {
	*x = ImportCustomersRequest_Options{}
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ImportCustomersRequest_Options) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ImportCustomersRequest_Options) ProtoMessage() {}

func (x *ImportCustomersRequest_Options) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ImportCustomersRequest_Options.ProtoReflect.Descriptor instead.
func (*ImportCustomersRequest_Options) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_data_migration_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *ImportCustomersRequest_Options) GetOverwriteOnConflict() bool {
	if x != nil {
		return x.OverwriteOnConflict
	}
	return false
}

// filter
type ListImportHistoriesRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ids
	Ids []int64 `protobuf:"varint,1,rep,packed,name=ids,proto3" json:"ids,omitempty"`
	// state
	States []ImportHistory_State `protobuf:"varint,2,rep,packed,name=states,proto3,enum=backend.proto.customer.v2.ImportHistory_State" json:"states,omitempty"`
	// organizations
	Organizations []*OrganizationRef `protobuf:"bytes,3,rep,name=organizations,proto3" json:"organizations,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListImportHistoriesRequest_Filter) Reset() {
	*x = ListImportHistoriesRequest_Filter{}
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListImportHistoriesRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListImportHistoriesRequest_Filter) ProtoMessage() {}

func (x *ListImportHistoriesRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListImportHistoriesRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListImportHistoriesRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v2_data_migration_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *ListImportHistoriesRequest_Filter) GetIds() []int64 {
	if x != nil {
		return x.Ids
	}
	return nil
}

func (x *ListImportHistoriesRequest_Filter) GetStates() []ImportHistory_State {
	if x != nil {
		return x.States
	}
	return nil
}

func (x *ListImportHistoriesRequest_Filter) GetOrganizations() []*OrganizationRef {
	if x != nil {
		return x.Organizations
	}
	return nil
}

var File_backend_proto_customer_v2_data_migration_service_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v2_data_migration_service_proto_rawDesc = "" +
	"\n" +
	"6backend/proto/customer/v2/data_migration_service.proto\x12\x19backend.proto.customer.v2\x1a&backend/proto/customer/v2/common.proto\x1a.backend/proto/customer/v2/data_migration.proto\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/api/field_behavior.proto\"\xd1\x02\n" +
	"\x16ImportCustomersRequest\x120\n" +
	"\x0fupload_file_uri\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x88\x01\x01R\ruploadFileUri\x12V\n" +
	"\forganization\x18\x02 \x01(\v2*.backend.proto.customer.v2.OrganizationRefB\x06\xbaH\x03\xc8\x01\x01R\forganization\x12S\n" +
	"\aoptions\x18\x03 \x01(\v29.backend.proto.customer.v2.ImportCustomersRequest.OptionsR\aoptions\x12\x19\n" +
	"\bstaff_id\x18\x04 \x01(\x03R\astaffId\x1a=\n" +
	"\aOptions\x122\n" +
	"\x15overwrite_on_conflict\x18\x01 \x01(\bR\x13overwriteOnConflict\"\x19\n" +
	"\x17ImportCustomersResponse\"\xed\x02\n" +
	"\x1aListImportHistoriesRequest\x12\\\n" +
	"\x06filter\x18\x01 \x01(\v2<.backend.proto.customer.v2.ListImportHistoriesRequest.FilterB\x06\xbaH\x03\xc8\x01\x01R\x06filter\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x1d\n" +
	"\n" +
	"page_token\x18\x03 \x01(\tR\tpageToken\x1a\xb4\x01\n" +
	"\x06Filter\x12\x10\n" +
	"\x03ids\x18\x01 \x03(\x03R\x03ids\x12F\n" +
	"\x06states\x18\x02 \x03(\x0e2..backend.proto.customer.v2.ImportHistory.StateR\x06states\x12P\n" +
	"\rorganizations\x18\x03 \x03(\v2*.backend.proto.customer.v2.OrganizationRefR\rorganizations\"\xe6\x01\n" +
	"\x1bListImportHistoriesResponse\x12S\n" +
	"\x10import_histories\x18\x01 \x03(\v2(.backend.proto.customer.v2.ImportHistoryR\x0fimportHistories\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tH\x00R\rnextPageToken\x88\x01\x01\x12\"\n" +
	"\n" +
	"total_size\x18\x03 \x01(\x03H\x01R\ttotalSize\x88\x01\x01B\x12\n" +
	"\x10_next_page_tokenB\r\n" +
	"\v_total_size2\x97\x02\n" +
	"\x14DataMigrationService\x12x\n" +
	"\x0fImportCustomers\x121.backend.proto.customer.v2.ImportCustomersRequest\x1a2.backend.proto.customer.v2.ImportCustomersResponse\x12\x84\x01\n" +
	"\x13ListImportHistories\x125.backend.proto.customer.v2.ListImportHistoriesRequest\x1a6.backend.proto.customer.v2.ListImportHistoriesResponseBk\n" +
	"#com.moego.backend.proto.customer.v2P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v2;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v2_data_migration_service_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v2_data_migration_service_proto_rawDescData []byte
)

func file_backend_proto_customer_v2_data_migration_service_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v2_data_migration_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v2_data_migration_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_data_migration_service_proto_rawDesc), len(file_backend_proto_customer_v2_data_migration_service_proto_rawDesc)))
	})
	return file_backend_proto_customer_v2_data_migration_service_proto_rawDescData
}

var file_backend_proto_customer_v2_data_migration_service_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_backend_proto_customer_v2_data_migration_service_proto_goTypes = []any{
	(*ImportCustomersRequest)(nil),            // 0: backend.proto.customer.v2.ImportCustomersRequest
	(*ImportCustomersResponse)(nil),           // 1: backend.proto.customer.v2.ImportCustomersResponse
	(*ListImportHistoriesRequest)(nil),        // 2: backend.proto.customer.v2.ListImportHistoriesRequest
	(*ListImportHistoriesResponse)(nil),       // 3: backend.proto.customer.v2.ListImportHistoriesResponse
	(*ImportCustomersRequest_Options)(nil),    // 4: backend.proto.customer.v2.ImportCustomersRequest.Options
	(*ListImportHistoriesRequest_Filter)(nil), // 5: backend.proto.customer.v2.ListImportHistoriesRequest.Filter
	(*OrganizationRef)(nil),                   // 6: backend.proto.customer.v2.OrganizationRef
	(*ImportHistory)(nil),                     // 7: backend.proto.customer.v2.ImportHistory
	(ImportHistory_State)(0),                  // 8: backend.proto.customer.v2.ImportHistory.State
}
var file_backend_proto_customer_v2_data_migration_service_proto_depIdxs = []int32{
	6, // 0: backend.proto.customer.v2.ImportCustomersRequest.organization:type_name -> backend.proto.customer.v2.OrganizationRef
	4, // 1: backend.proto.customer.v2.ImportCustomersRequest.options:type_name -> backend.proto.customer.v2.ImportCustomersRequest.Options
	5, // 2: backend.proto.customer.v2.ListImportHistoriesRequest.filter:type_name -> backend.proto.customer.v2.ListImportHistoriesRequest.Filter
	7, // 3: backend.proto.customer.v2.ListImportHistoriesResponse.import_histories:type_name -> backend.proto.customer.v2.ImportHistory
	8, // 4: backend.proto.customer.v2.ListImportHistoriesRequest.Filter.states:type_name -> backend.proto.customer.v2.ImportHistory.State
	6, // 5: backend.proto.customer.v2.ListImportHistoriesRequest.Filter.organizations:type_name -> backend.proto.customer.v2.OrganizationRef
	0, // 6: backend.proto.customer.v2.DataMigrationService.ImportCustomers:input_type -> backend.proto.customer.v2.ImportCustomersRequest
	2, // 7: backend.proto.customer.v2.DataMigrationService.ListImportHistories:input_type -> backend.proto.customer.v2.ListImportHistoriesRequest
	1, // 8: backend.proto.customer.v2.DataMigrationService.ImportCustomers:output_type -> backend.proto.customer.v2.ImportCustomersResponse
	3, // 9: backend.proto.customer.v2.DataMigrationService.ListImportHistories:output_type -> backend.proto.customer.v2.ListImportHistoriesResponse
	8, // [8:10] is the sub-list for method output_type
	6, // [6:8] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v2_data_migration_service_proto_init() }
func file_backend_proto_customer_v2_data_migration_service_proto_init() {
	if File_backend_proto_customer_v2_data_migration_service_proto != nil {
		return
	}
	file_backend_proto_customer_v2_common_proto_init()
	file_backend_proto_customer_v2_data_migration_proto_init()
	file_backend_proto_customer_v2_data_migration_service_proto_msgTypes[3].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v2_data_migration_service_proto_rawDesc), len(file_backend_proto_customer_v2_data_migration_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_customer_v2_data_migration_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v2_data_migration_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_customer_v2_data_migration_service_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v2_data_migration_service_proto = out.File
	file_backend_proto_customer_v2_data_migration_service_proto_goTypes = nil
	file_backend_proto_customer_v2_data_migration_service_proto_depIdxs = nil
}
