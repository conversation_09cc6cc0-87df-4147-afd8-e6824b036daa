// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: ID检索接口不需要分页 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 无游标分页需求 --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 响应不包含分页 --)
// (-- api-linter: core::0158::response-repeated-first-field=disabled
//     aip.dev/not-precedent: 自定义响应结构 --)
// (-- api-linter: core::0158::response-plural-first-field=disabled
//     aip.dev/not-precedent: 自定义响应结构 --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/customer/v1/customer_query_service.proto

package customerpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CustomerQueryService_SearchCustomerIds_FullMethodName             = "/backend.proto.customer.v1.CustomerQueryService/SearchCustomerIds"
	CustomerQueryService_SearchCustomerIdsByLastName_FullMethodName   = "/backend.proto.customer.v1.CustomerQueryService/SearchCustomerIdsByLastName"
	CustomerQueryService_FilterCustomerIds_FullMethodName             = "/backend.proto.customer.v1.CustomerQueryService/FilterCustomerIds"
	CustomerQueryService_ListCustomerIds_FullMethodName               = "/backend.proto.customer.v1.CustomerQueryService/ListCustomerIds"
	CustomerQueryService_ValidateActiveCustomerIds_FullMethodName     = "/backend.proto.customer.v1.CustomerQueryService/ValidateActiveCustomerIds"
	CustomerQueryService_GetCustomersBasicInfo_FullMethodName         = "/backend.proto.customer.v1.CustomerQueryService/GetCustomersBasicInfo"
	CustomerQueryService_SearchContactCustomerIds_FullMethodName      = "/backend.proto.customer.v1.CustomerQueryService/SearchContactCustomerIds"
	CustomerQueryService_FilterContactCustomerIds_FullMethodName      = "/backend.proto.customer.v1.CustomerQueryService/FilterContactCustomerIds"
	CustomerQueryService_CountFilterContactCustomerIds_FullMethodName = "/backend.proto.customer.v1.CustomerQueryService/CountFilterContactCustomerIds"
	CustomerQueryService_GetPrimaryPhones_FullMethodName              = "/backend.proto.customer.v1.CustomerQueryService/GetPrimaryPhones"
	CustomerQueryService_SearchAddressCustomerIds_FullMethodName      = "/backend.proto.customer.v1.CustomerQueryService/SearchAddressCustomerIds"
	CustomerQueryService_FilterCustomerIdsByZip_FullMethodName        = "/backend.proto.customer.v1.CustomerQueryService/FilterCustomerIdsByZip"
	CustomerQueryService_CountFilterAddressCustomerIds_FullMethodName = "/backend.proto.customer.v1.CustomerQueryService/CountFilterAddressCustomerIds"
	CustomerQueryService_FilterCustomerIdsByTag_FullMethodName        = "/backend.proto.customer.v1.CustomerQueryService/FilterCustomerIdsByTag"
	CustomerQueryService_CalculateCustomerMetrics_FullMethodName      = "/backend.proto.customer.v1.CustomerQueryService/CalculateCustomerMetrics"
)

// CustomerQueryServiceClient is the client API for CustomerQueryService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CustomerQueryService 提供 moe_customer 查询能力（只读）
// (-- api-linter: core::0191::file-layout=disabled
//
//	aip.dev/not-precedent: 本文件需聚合全部消息后定义 service --)
//
// (-- api-linter: core::0136::prepositions=disabled
//
//	aip.dev/not-precedent: 内部查询接口使用介词以保持语义清晰 --)
type CustomerQueryServiceClient interface {
	// Customer
	// 搜索客户ID
	SearchCustomerIds(ctx context.Context, in *SearchCustomerIdsRequest, opts ...grpc.CallOption) (*SearchCustomerIdsResponse, error)
	// 按姓氏搜索客户ID
	SearchCustomerIdsByLastName(ctx context.Context, in *SearchCustomerIdsByLastNameRequest, opts ...grpc.CallOption) (*SearchCustomerIdsByLastNameResponse, error)
	// 过滤客户ID
	FilterCustomerIds(ctx context.Context, in *FilterCustomerIdsRequest, opts ...grpc.CallOption) (*FilterCustomerIdsResponse, error)
	// 列出客户ID
	ListCustomerIds(ctx context.Context, in *ListCustomerIdsRequest, opts ...grpc.CallOption) (*ListCustomerIdsResponse, error)
	// 验证活跃客户ID
	ValidateActiveCustomerIds(ctx context.Context, in *ValidateActiveCustomerIdsRequest, opts ...grpc.CallOption) (*ValidateActiveCustomerIdsResponse, error)
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: 内部查询返回列表容器结构 --)
	//
	// 获取客户基础信息
	GetCustomersBasicInfo(ctx context.Context, in *GetCustomersBasicInfoRequest, opts ...grpc.CallOption) (*GetCustomersBasicInfoResponse, error)
	// Contact
	// 搜索联系方式客户ID
	SearchContactCustomerIds(ctx context.Context, in *SearchContactCustomerIdsRequest, opts ...grpc.CallOption) (*SearchContactCustomerIdsResponse, error)
	// 按联系方式过滤客户ID
	FilterContactCustomerIds(ctx context.Context, in *FilterContactCustomerIdsRequest, opts ...grpc.CallOption) (*FilterContactCustomerIdsResponse, error)
	// 按联系方式计数过滤客户ID
	CountFilterContactCustomerIds(ctx context.Context, in *CountFilterContactCustomerIdsRequest, opts ...grpc.CallOption) (*CountFilterContactCustomerIdsResponse, error)
	// 获取主要电话号码
	GetPrimaryPhones(ctx context.Context, in *GetPrimaryPhonesRequest, opts ...grpc.CallOption) (*PrimaryPhones, error)
	// Address
	// 搜索地址客户ID
	SearchAddressCustomerIds(ctx context.Context, in *SearchAddressCustomerIdsRequest, opts ...grpc.CallOption) (*SearchAddressCustomerIdsResponse, error)
	// 按邮编过滤客户ID
	FilterCustomerIdsByZip(ctx context.Context, in *FilterCustomerIdsByZipRequest, opts ...grpc.CallOption) (*FilterCustomerIdsByZipResponse, error)
	// 按地址计数过滤客户ID
	CountFilterAddressCustomerIds(ctx context.Context, in *CountFilterAddressCustomerIdsRequest, opts ...grpc.CallOption) (*CountFilterAddressCustomerIdsResponse, error)
	// // Pet
	// // 搜索宠物客户ID
	// rpc SearchPetCustomerIds(SearchPetCustomerIdsRequest) returns (SearchPetCustomerIdsResponse);
	// // 宠物全文搜索
	// rpc SearchPetsFulltext(SearchPetsFulltextRequest) returns (SearchPetsFulltextResponse);
	// // 按宠物过滤客户ID
	// rpc FilterPetCustomerIds(FilterPetCustomerIdsRequest) returns (FilterPetCustomerIdsResponse);
	// // 按宠物计数过滤客户ID
	// rpc CountFilterPetCustomerIds(CountFilterPetCustomerIdsRequest) returns (CountFilterPetCustomerIdsResponse);
	// // 获取宠物类型分布
	// rpc GetPetTypeDistribution(GetPetTypeDistributionRequest) returns (PetTypeDistribution);
	// // 列出客户宠物
	// rpc ListPetsForCustomers(ListPetsForCustomersRequest) returns (ListPetsForCustomersResponse);
	// Tag Binding
	// 按标签过滤客户ID
	FilterCustomerIdsByTag(ctx context.Context, in *FilterCustomerIdsByTagRequest, opts ...grpc.CallOption) (*FilterCustomerIdsByTagResponse, error)
	// 计算客户指标
	CalculateCustomerMetrics(ctx context.Context, in *CalculateCustomerMetricsRequest, opts ...grpc.CallOption) (*CalculateCustomerMetricsResponse, error)
}

type customerQueryServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCustomerQueryServiceClient(cc grpc.ClientConnInterface) CustomerQueryServiceClient {
	return &customerQueryServiceClient{cc}
}

func (c *customerQueryServiceClient) SearchCustomerIds(ctx context.Context, in *SearchCustomerIdsRequest, opts ...grpc.CallOption) (*SearchCustomerIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCustomerIdsResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_SearchCustomerIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) SearchCustomerIdsByLastName(ctx context.Context, in *SearchCustomerIdsByLastNameRequest, opts ...grpc.CallOption) (*SearchCustomerIdsByLastNameResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchCustomerIdsByLastNameResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_SearchCustomerIdsByLastName_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) FilterCustomerIds(ctx context.Context, in *FilterCustomerIdsRequest, opts ...grpc.CallOption) (*FilterCustomerIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FilterCustomerIdsResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_FilterCustomerIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) ListCustomerIds(ctx context.Context, in *ListCustomerIdsRequest, opts ...grpc.CallOption) (*ListCustomerIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomerIdsResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_ListCustomerIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) ValidateActiveCustomerIds(ctx context.Context, in *ValidateActiveCustomerIdsRequest, opts ...grpc.CallOption) (*ValidateActiveCustomerIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidateActiveCustomerIdsResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_ValidateActiveCustomerIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) GetCustomersBasicInfo(ctx context.Context, in *GetCustomersBasicInfoRequest, opts ...grpc.CallOption) (*GetCustomersBasicInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCustomersBasicInfoResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_GetCustomersBasicInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) SearchContactCustomerIds(ctx context.Context, in *SearchContactCustomerIdsRequest, opts ...grpc.CallOption) (*SearchContactCustomerIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchContactCustomerIdsResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_SearchContactCustomerIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) FilterContactCustomerIds(ctx context.Context, in *FilterContactCustomerIdsRequest, opts ...grpc.CallOption) (*FilterContactCustomerIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FilterContactCustomerIdsResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_FilterContactCustomerIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) CountFilterContactCustomerIds(ctx context.Context, in *CountFilterContactCustomerIdsRequest, opts ...grpc.CallOption) (*CountFilterContactCustomerIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountFilterContactCustomerIdsResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_CountFilterContactCustomerIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) GetPrimaryPhones(ctx context.Context, in *GetPrimaryPhonesRequest, opts ...grpc.CallOption) (*PrimaryPhones, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PrimaryPhones)
	err := c.cc.Invoke(ctx, CustomerQueryService_GetPrimaryPhones_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) SearchAddressCustomerIds(ctx context.Context, in *SearchAddressCustomerIdsRequest, opts ...grpc.CallOption) (*SearchAddressCustomerIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchAddressCustomerIdsResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_SearchAddressCustomerIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) FilterCustomerIdsByZip(ctx context.Context, in *FilterCustomerIdsByZipRequest, opts ...grpc.CallOption) (*FilterCustomerIdsByZipResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FilterCustomerIdsByZipResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_FilterCustomerIdsByZip_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) CountFilterAddressCustomerIds(ctx context.Context, in *CountFilterAddressCustomerIdsRequest, opts ...grpc.CallOption) (*CountFilterAddressCustomerIdsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CountFilterAddressCustomerIdsResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_CountFilterAddressCustomerIds_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) FilterCustomerIdsByTag(ctx context.Context, in *FilterCustomerIdsByTagRequest, opts ...grpc.CallOption) (*FilterCustomerIdsByTagResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FilterCustomerIdsByTagResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_FilterCustomerIdsByTag_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerQueryServiceClient) CalculateCustomerMetrics(ctx context.Context, in *CalculateCustomerMetricsRequest, opts ...grpc.CallOption) (*CalculateCustomerMetricsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CalculateCustomerMetricsResponse)
	err := c.cc.Invoke(ctx, CustomerQueryService_CalculateCustomerMetrics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomerQueryServiceServer is the server API for CustomerQueryService service.
// All implementations must embed UnimplementedCustomerQueryServiceServer
// for forward compatibility.
//
// CustomerQueryService 提供 moe_customer 查询能力（只读）
// (-- api-linter: core::0191::file-layout=disabled
//
//	aip.dev/not-precedent: 本文件需聚合全部消息后定义 service --)
//
// (-- api-linter: core::0136::prepositions=disabled
//
//	aip.dev/not-precedent: 内部查询接口使用介词以保持语义清晰 --)
type CustomerQueryServiceServer interface {
	// Customer
	// 搜索客户ID
	SearchCustomerIds(context.Context, *SearchCustomerIdsRequest) (*SearchCustomerIdsResponse, error)
	// 按姓氏搜索客户ID
	SearchCustomerIdsByLastName(context.Context, *SearchCustomerIdsByLastNameRequest) (*SearchCustomerIdsByLastNameResponse, error)
	// 过滤客户ID
	FilterCustomerIds(context.Context, *FilterCustomerIdsRequest) (*FilterCustomerIdsResponse, error)
	// 列出客户ID
	ListCustomerIds(context.Context, *ListCustomerIdsRequest) (*ListCustomerIdsResponse, error)
	// 验证活跃客户ID
	ValidateActiveCustomerIds(context.Context, *ValidateActiveCustomerIdsRequest) (*ValidateActiveCustomerIdsResponse, error)
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: 内部查询返回列表容器结构 --)
	//
	// 获取客户基础信息
	GetCustomersBasicInfo(context.Context, *GetCustomersBasicInfoRequest) (*GetCustomersBasicInfoResponse, error)
	// Contact
	// 搜索联系方式客户ID
	SearchContactCustomerIds(context.Context, *SearchContactCustomerIdsRequest) (*SearchContactCustomerIdsResponse, error)
	// 按联系方式过滤客户ID
	FilterContactCustomerIds(context.Context, *FilterContactCustomerIdsRequest) (*FilterContactCustomerIdsResponse, error)
	// 按联系方式计数过滤客户ID
	CountFilterContactCustomerIds(context.Context, *CountFilterContactCustomerIdsRequest) (*CountFilterContactCustomerIdsResponse, error)
	// 获取主要电话号码
	GetPrimaryPhones(context.Context, *GetPrimaryPhonesRequest) (*PrimaryPhones, error)
	// Address
	// 搜索地址客户ID
	SearchAddressCustomerIds(context.Context, *SearchAddressCustomerIdsRequest) (*SearchAddressCustomerIdsResponse, error)
	// 按邮编过滤客户ID
	FilterCustomerIdsByZip(context.Context, *FilterCustomerIdsByZipRequest) (*FilterCustomerIdsByZipResponse, error)
	// 按地址计数过滤客户ID
	CountFilterAddressCustomerIds(context.Context, *CountFilterAddressCustomerIdsRequest) (*CountFilterAddressCustomerIdsResponse, error)
	// // Pet
	// // 搜索宠物客户ID
	// rpc SearchPetCustomerIds(SearchPetCustomerIdsRequest) returns (SearchPetCustomerIdsResponse);
	// // 宠物全文搜索
	// rpc SearchPetsFulltext(SearchPetsFulltextRequest) returns (SearchPetsFulltextResponse);
	// // 按宠物过滤客户ID
	// rpc FilterPetCustomerIds(FilterPetCustomerIdsRequest) returns (FilterPetCustomerIdsResponse);
	// // 按宠物计数过滤客户ID
	// rpc CountFilterPetCustomerIds(CountFilterPetCustomerIdsRequest) returns (CountFilterPetCustomerIdsResponse);
	// // 获取宠物类型分布
	// rpc GetPetTypeDistribution(GetPetTypeDistributionRequest) returns (PetTypeDistribution);
	// // 列出客户宠物
	// rpc ListPetsForCustomers(ListPetsForCustomersRequest) returns (ListPetsForCustomersResponse);
	// Tag Binding
	// 按标签过滤客户ID
	FilterCustomerIdsByTag(context.Context, *FilterCustomerIdsByTagRequest) (*FilterCustomerIdsByTagResponse, error)
	// 计算客户指标
	CalculateCustomerMetrics(context.Context, *CalculateCustomerMetricsRequest) (*CalculateCustomerMetricsResponse, error)
	mustEmbedUnimplementedCustomerQueryServiceServer()
}

// UnimplementedCustomerQueryServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCustomerQueryServiceServer struct{}

func (UnimplementedCustomerQueryServiceServer) SearchCustomerIds(context.Context, *SearchCustomerIdsRequest) (*SearchCustomerIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCustomerIds not implemented")
}
func (UnimplementedCustomerQueryServiceServer) SearchCustomerIdsByLastName(context.Context, *SearchCustomerIdsByLastNameRequest) (*SearchCustomerIdsByLastNameResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchCustomerIdsByLastName not implemented")
}
func (UnimplementedCustomerQueryServiceServer) FilterCustomerIds(context.Context, *FilterCustomerIdsRequest) (*FilterCustomerIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterCustomerIds not implemented")
}
func (UnimplementedCustomerQueryServiceServer) ListCustomerIds(context.Context, *ListCustomerIdsRequest) (*ListCustomerIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerIds not implemented")
}
func (UnimplementedCustomerQueryServiceServer) ValidateActiveCustomerIds(context.Context, *ValidateActiveCustomerIdsRequest) (*ValidateActiveCustomerIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateActiveCustomerIds not implemented")
}
func (UnimplementedCustomerQueryServiceServer) GetCustomersBasicInfo(context.Context, *GetCustomersBasicInfoRequest) (*GetCustomersBasicInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomersBasicInfo not implemented")
}
func (UnimplementedCustomerQueryServiceServer) SearchContactCustomerIds(context.Context, *SearchContactCustomerIdsRequest) (*SearchContactCustomerIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchContactCustomerIds not implemented")
}
func (UnimplementedCustomerQueryServiceServer) FilterContactCustomerIds(context.Context, *FilterContactCustomerIdsRequest) (*FilterContactCustomerIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterContactCustomerIds not implemented")
}
func (UnimplementedCustomerQueryServiceServer) CountFilterContactCustomerIds(context.Context, *CountFilterContactCustomerIdsRequest) (*CountFilterContactCustomerIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountFilterContactCustomerIds not implemented")
}
func (UnimplementedCustomerQueryServiceServer) GetPrimaryPhones(context.Context, *GetPrimaryPhonesRequest) (*PrimaryPhones, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPrimaryPhones not implemented")
}
func (UnimplementedCustomerQueryServiceServer) SearchAddressCustomerIds(context.Context, *SearchAddressCustomerIdsRequest) (*SearchAddressCustomerIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchAddressCustomerIds not implemented")
}
func (UnimplementedCustomerQueryServiceServer) FilterCustomerIdsByZip(context.Context, *FilterCustomerIdsByZipRequest) (*FilterCustomerIdsByZipResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterCustomerIdsByZip not implemented")
}
func (UnimplementedCustomerQueryServiceServer) CountFilterAddressCustomerIds(context.Context, *CountFilterAddressCustomerIdsRequest) (*CountFilterAddressCustomerIdsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountFilterAddressCustomerIds not implemented")
}
func (UnimplementedCustomerQueryServiceServer) FilterCustomerIdsByTag(context.Context, *FilterCustomerIdsByTagRequest) (*FilterCustomerIdsByTagResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FilterCustomerIdsByTag not implemented")
}
func (UnimplementedCustomerQueryServiceServer) CalculateCustomerMetrics(context.Context, *CalculateCustomerMetricsRequest) (*CalculateCustomerMetricsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CalculateCustomerMetrics not implemented")
}
func (UnimplementedCustomerQueryServiceServer) mustEmbedUnimplementedCustomerQueryServiceServer() {}
func (UnimplementedCustomerQueryServiceServer) testEmbeddedByValue()                              {}

// UnsafeCustomerQueryServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomerQueryServiceServer will
// result in compilation errors.
type UnsafeCustomerQueryServiceServer interface {
	mustEmbedUnimplementedCustomerQueryServiceServer()
}

func RegisterCustomerQueryServiceServer(s grpc.ServiceRegistrar, srv CustomerQueryServiceServer) {
	// If the following call pancis, it indicates UnimplementedCustomerQueryServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CustomerQueryService_ServiceDesc, srv)
}

func _CustomerQueryService_SearchCustomerIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCustomerIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).SearchCustomerIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_SearchCustomerIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).SearchCustomerIds(ctx, req.(*SearchCustomerIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_SearchCustomerIdsByLastName_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchCustomerIdsByLastNameRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).SearchCustomerIdsByLastName(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_SearchCustomerIdsByLastName_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).SearchCustomerIdsByLastName(ctx, req.(*SearchCustomerIdsByLastNameRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_FilterCustomerIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterCustomerIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).FilterCustomerIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_FilterCustomerIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).FilterCustomerIds(ctx, req.(*FilterCustomerIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_ListCustomerIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).ListCustomerIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_ListCustomerIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).ListCustomerIds(ctx, req.(*ListCustomerIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_ValidateActiveCustomerIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateActiveCustomerIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).ValidateActiveCustomerIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_ValidateActiveCustomerIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).ValidateActiveCustomerIds(ctx, req.(*ValidateActiveCustomerIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_GetCustomersBasicInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomersBasicInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).GetCustomersBasicInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_GetCustomersBasicInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).GetCustomersBasicInfo(ctx, req.(*GetCustomersBasicInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_SearchContactCustomerIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchContactCustomerIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).SearchContactCustomerIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_SearchContactCustomerIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).SearchContactCustomerIds(ctx, req.(*SearchContactCustomerIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_FilterContactCustomerIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterContactCustomerIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).FilterContactCustomerIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_FilterContactCustomerIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).FilterContactCustomerIds(ctx, req.(*FilterContactCustomerIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_CountFilterContactCustomerIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountFilterContactCustomerIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).CountFilterContactCustomerIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_CountFilterContactCustomerIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).CountFilterContactCustomerIds(ctx, req.(*CountFilterContactCustomerIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_GetPrimaryPhones_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrimaryPhonesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).GetPrimaryPhones(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_GetPrimaryPhones_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).GetPrimaryPhones(ctx, req.(*GetPrimaryPhonesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_SearchAddressCustomerIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchAddressCustomerIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).SearchAddressCustomerIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_SearchAddressCustomerIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).SearchAddressCustomerIds(ctx, req.(*SearchAddressCustomerIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_FilterCustomerIdsByZip_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterCustomerIdsByZipRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).FilterCustomerIdsByZip(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_FilterCustomerIdsByZip_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).FilterCustomerIdsByZip(ctx, req.(*FilterCustomerIdsByZipRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_CountFilterAddressCustomerIds_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CountFilterAddressCustomerIdsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).CountFilterAddressCustomerIds(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_CountFilterAddressCustomerIds_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).CountFilterAddressCustomerIds(ctx, req.(*CountFilterAddressCustomerIdsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_FilterCustomerIdsByTag_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FilterCustomerIdsByTagRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).FilterCustomerIdsByTag(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_FilterCustomerIdsByTag_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).FilterCustomerIdsByTag(ctx, req.(*FilterCustomerIdsByTagRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerQueryService_CalculateCustomerMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CalculateCustomerMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerQueryServiceServer).CalculateCustomerMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerQueryService_CalculateCustomerMetrics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerQueryServiceServer).CalculateCustomerMetrics(ctx, req.(*CalculateCustomerMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CustomerQueryService_ServiceDesc is the grpc.ServiceDesc for CustomerQueryService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomerQueryService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.customer.v1.CustomerQueryService",
	HandlerType: (*CustomerQueryServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SearchCustomerIds",
			Handler:    _CustomerQueryService_SearchCustomerIds_Handler,
		},
		{
			MethodName: "SearchCustomerIdsByLastName",
			Handler:    _CustomerQueryService_SearchCustomerIdsByLastName_Handler,
		},
		{
			MethodName: "FilterCustomerIds",
			Handler:    _CustomerQueryService_FilterCustomerIds_Handler,
		},
		{
			MethodName: "ListCustomerIds",
			Handler:    _CustomerQueryService_ListCustomerIds_Handler,
		},
		{
			MethodName: "ValidateActiveCustomerIds",
			Handler:    _CustomerQueryService_ValidateActiveCustomerIds_Handler,
		},
		{
			MethodName: "GetCustomersBasicInfo",
			Handler:    _CustomerQueryService_GetCustomersBasicInfo_Handler,
		},
		{
			MethodName: "SearchContactCustomerIds",
			Handler:    _CustomerQueryService_SearchContactCustomerIds_Handler,
		},
		{
			MethodName: "FilterContactCustomerIds",
			Handler:    _CustomerQueryService_FilterContactCustomerIds_Handler,
		},
		{
			MethodName: "CountFilterContactCustomerIds",
			Handler:    _CustomerQueryService_CountFilterContactCustomerIds_Handler,
		},
		{
			MethodName: "GetPrimaryPhones",
			Handler:    _CustomerQueryService_GetPrimaryPhones_Handler,
		},
		{
			MethodName: "SearchAddressCustomerIds",
			Handler:    _CustomerQueryService_SearchAddressCustomerIds_Handler,
		},
		{
			MethodName: "FilterCustomerIdsByZip",
			Handler:    _CustomerQueryService_FilterCustomerIdsByZip_Handler,
		},
		{
			MethodName: "CountFilterAddressCustomerIds",
			Handler:    _CustomerQueryService_CountFilterAddressCustomerIds_Handler,
		},
		{
			MethodName: "FilterCustomerIdsByTag",
			Handler:    _CustomerQueryService_FilterCustomerIdsByTag_Handler,
		},
		{
			MethodName: "CalculateCustomerMetrics",
			Handler:    _CustomerQueryService_CalculateCustomerMetrics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/customer/v1/customer_query_service.proto",
}
