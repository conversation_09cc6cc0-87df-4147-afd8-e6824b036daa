// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0148::human-names=disabled
//     aip.dev/not-precedent: 使用first_name和last_name作为字段名 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0132::request-field-types=disabled
//     aip.dev/not-precedent: 使用自定义过滤器类型 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用customer_id作为标识符 --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用自定义响应字段 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 使用自定义请求字段结构 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用name后缀 --)

syntax = "proto3";

package backend.proto.customer.v1;

import "backend/proto/customer/v1/customer.proto";
import "backend/proto/customer/v1/customer_view_models.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/timestamp.proto";
import "buf/validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v1;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v1";

// CustomerService 提供客户管理相关的服务
service CustomerService {
  // CreateCustomer 创建新客户
  rpc CreateCustomer(CreateCustomerRequest) returns (CreateCustomerResponse);
  // UpdateCustomer 更新客户信息
  rpc UpdateCustomer(UpdateCustomerRequest) returns (UpdateCustomerResponse);
  // ListCustomers 获取客户列表
  rpc ListCustomers(ListCustomersRequest) returns (ListCustomersResponse);
  // GetCustomer 获取客户详情
  // buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
  rpc GetCustomer(GetCustomerRequest) returns (backend.proto.customer.v1.Customer);
  // deleted 删除客户
  rpc DeleteCustomer(DeleteCustomerRequest) returns (google.protobuf.Empty);
  // SyncCustomerSearch 同步客户数据到搜索
  rpc SyncCustomerSearch(SyncCustomerSearchRequest) returns (SyncCustomerSearchResponse);

  // CreateCustomerHistoryLog 创建客户活动日志
  rpc CreateCustomerHistoryLog(CreateCustomerHistoryLogRequest) returns (CreateCustomerHistoryLogResponse);
  // ListCustomerHistoryLogs 获取客户历史记录
  rpc UpdateCustomerHistoryLog(UpdateCustomerHistoryLogRequest) returns (UpdateCustomerHistoryLogResponse);
  // ListCustomerHistoryLogs 获取客户历史记录
  rpc ListCustomerHistoryLogs(ListCustomerHistoryLogsRequest) returns (ListCustomerHistoryLogsResponse);

  // ConvertCustomer 转换客户状态
  rpc ConvertCustomer(ConvertCustomerRequest) returns (ConvertCustomerResponse);
  // ConvertCustomersAttribute 批量转换客户属性
  rpc ConvertCustomersAttribute(ConvertCustomersAttributeRequest) returns (ConvertCustomersAttributeResponse);

  // CreateCustomerTask 创建客户任务
  rpc CreateCustomerTask(CreateCustomerTaskRequest) returns (CreateCustomerTaskResponse);
  // UpdateCustomerTask 更新客户任务
  rpc UpdateCustomerTask(UpdateCustomerTaskRequest) returns (UpdateCustomerTaskResponse);
  // ListCustomerTasks 获取客户任务列表
  rpc ListCustomerTasks(ListCustomerTasksRequest) returns (ListCustomerTasksResponse);
  // DeleteCustomerTask 删除客户任务列表
  rpc DeleteCustomerTask(DeleteCustomerTaskRequest) returns (google.protobuf.Empty);

  // Address
  // CreateAddress 创建地址
  rpc CreateAddress(CreateAddressRequest) returns (CreateAddressResponse);
  // UpdateAddress 更新地址
  rpc UpdateAddress(UpdateAddressRequest) returns (UpdateAddressResponse);
  // DeleteAddress 删除地址
  rpc DeleteAddress(DeleteAddressRequest) returns (google.protobuf.Empty);
  // ListAddresses 获取地址列表
  rpc ListAddresses(ListAddressesRequest) returns (ListAddressesResponse);

  // LifeCycle
  // CreateLifeCycle 创建生命周期
  rpc CreateLifeCycle(CreateLifeCycleRequest) returns (CreateLifeCycleResponse);
  // UpdateLifeCycle 更新生命周期
  rpc UpdateLifeCycles(UpdateLifeCyclesRequest) returns (UpdateLifeCyclesResponse);
  // ListLifeCycles 获取生命周期列表
  rpc ListLifeCycles(ListLifeCyclesRequest) returns (ListLifeCyclesResponse);
  // DeleteLifeCycle 删除生命周期
  rpc DeleteLifeCycle(DeleteLifeCycleRequest) returns (google.protobuf.Empty);

  // ActionState
  // CreateActionState 创建行动状态
  rpc CreateActionState(CreateActionStateRequest) returns (CreateActionStateResponse);
  // UpdateActionState 更新行动状态
  rpc UpdateActionStates(UpdateActionStatesRequest) returns (UpdateActionsStatesResponse);
  // ListActionStates 获取行动状态列表
  rpc ListActionStates(ListActionStatesRequest) returns (ListActionStatesResponse);
  // DeleteActionState 删除行动状态
  rpc DeleteActionState(DeleteActionStateRequest) returns (google.protobuf.Empty);

  // View
  // CreateView 创建用户视图
  rpc CreateView(CreateViewRequest) returns (CreateViewResponse);
  // UpdateView 更新用户视图
  rpc UpdateView(UpdateViewRequest) returns (UpdateViewResponse);
  // ListViews 获取用户视图列表
  rpc ListViews(ListViewsRequest) returns (ListViewsResponse);
  // DeleteView 删除用户视图
  rpc DeleteView(DeleteViewRequest) returns (google.protobuf.Empty);
}

// CreateCustomerRequest 创建客户请求
message CreateCustomerRequest {
  // customer
  backend.proto.customer.v1.Customer customer = 1;
  // staff_id
  optional int64 staff_id = 2;
  // staff_name
  optional string staff_name = 3;
}

// CreateCustomerResponse 创建客户响应
message CreateCustomerResponse {
  // customer
  backend.proto.customer.v1.Customer customer = 1;
  // is exist
  bool is_exist = 2;
}

// UpdateCustomerRequest 更新客户请求
message UpdateCustomerRequest {
  // update customer request
  message UpdateCustomer {
    // 客户ID
    int64 id = 1;
    // 生命周期状态
    optional backend.proto.customer.v1.Customer.LifeCycle life_cycle = 2 [(google.api.field_behavior) = OUTPUT_ONLY];
    // 行动状态
    optional backend.proto.customer.v1.Customer.ActionState action_state = 3 [(google.api.field_behavior) = OUTPUT_ONLY];
    // 客户来源
    optional string source = 4;
    //  Preferred business
    optional int64 preferred_business_id = 5;

    // 头像路径
    optional string avatar_path = 12;
    // 名
    optional string given_name = 13;
    // 姓
    optional string family_name = 14;
    // 邮箱
    optional string email = 15;
    // 电话
    optional string phone_number = 16;
    // 生日
    optional google.protobuf.Timestamp birth_time = 17;

    // update address
    UpdateAddress address = 18;

    // update contact
    UpdateContact contact = 19;

    // 关联的员工ID
    optional int64 allocate_staff_id = 20;

    // 附加信息
    optional backend.proto.customer.v1.Customer.AdditionalInfo additional_info = 21;

    // 关联自定义生命周期ID
    optional int64 customize_life_cycle_id = 23;
    // 关联自定义行动状态ID
    optional int64 customize_action_state_id = 24;

    // 用户头像颜色
    optional string client_color = 25;

    // 自定义字段
    optional google.protobuf.Struct custom_fields = 26;
  }

  // update address
  message UpdateAddress {
    // id
    int64 id = 1;
    // 地址1
    optional string address1 = 2;
    // 地址2
    optional string address2 = 3;
    // 城市
    optional string city = 4;
    // 州/省
    optional string state = 5;
    // 区域代码 (替代 country)
    optional string region_code = 6;
    // 邮编
    optional string zipcode = 7;
    // 纬度
    optional string lat = 8;
    // 经度
    optional string lng = 9;
    // 是否为主地址
    // 注意, 这里的枚举和数据库不一致
    // 数据库中未重构前是0和1, 目前用枚举表示, service 会转换枚举和db中的数据
    // 上游无需关心转换, 直接使用枚举即可
    optional backend.proto.customer.v1.Address.IsPrimary is_primary = 10;
  }

  // contact 表示联系信息
  message UpdateContact {
    // 联系人ID
    int64 id = 1;
    // 名
    optional string given_name = 2;
    // 姓
    optional string family_name = 3;
    // 电话号码
    optional string phone_number = 4;
    // 邮箱
    optional string email = 5;
    // 职位
    optional string title = 6;
    // is_primary 表示是否为主联系人
    // 注意, 这里的枚举和数据库不一致
    // 数据库中未重构前是0和1, 目前用枚举表示, service 需要转换枚举和db中的数据
    // 上游无需关心转换, 直接使用枚举即可
    optional backend.proto.customer.v1.CustomerContact.IsPrimary is_primary = 7;
    // E164格式电话号码
    optional string e164_phone_number = 8;
  }

  // update customer request
  UpdateCustomer customer = 1;
}

// UpdateCustomerResponse 更新客户响应
message UpdateCustomerResponse {}

// ListCustomersRequest 获取客户列表请求
message ListCustomersRequest {
  // Filter 过滤器
  message Filter {
    // 行动状态过滤
    optional Customer.ActionState action_state = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

    // type
    optional Customer.Type type = 2;

    // customer ids
    repeated int64 customer_ids = 3;

    // keyword
    optional string keyword = 4;

    // 生命周期过滤
    optional Customer.LifeCycle life_cycle = 5;

    // 关联自定义生命周期ID
    optional int64 customize_life_cycle_id = 6;

    // 关联自定义行动状态ID
    optional int64 customize_action_state_id = 7;

    // 主电话号码筛选
    optional string main_phone_number = 8;
  }
  // order, service 中不会有默认排序，需要上游控制
  enum OrderField {
    // 默认排序
    ORDER_FIELD_UNSPECIFIED = 0;
    // 创建时间
    CREATE_TIME = 1;
    // 更新时间
    UPDATE_TIME = 2;
    // 客户ID
    ID = 3;
  }

  // 排序方向
  enum OrderDirection {
    // 默认排序
    ORDER_DIRECTION_UNSPECIFIED = 0;
    // 升序
    ASC = 1;
    // 降序
    DESC = 2;
  }
  // 过滤条件
  optional Filter filter = 1;
  // company id 必填
  int64 company_id = 2 [(buf.validate.field).int64.gt = 0];
  // 每页数量，允许为0
  optional int32 page_size = 3 [
    (buf.validate.field).int32.lte = 1000,
    (buf.validate.field).int32.gte = 0
  ];
  // 页码，从1开始
  optional int32 page_num = 4 [(buf.validate.field).int32.gte = 1];
  // 排序字段
  optional OrderField order_field = 5;
  // 排序方向
  optional OrderDirection order_direction = 6;
}

// ListCustomersResponse 获取客户列表响应
message ListCustomersResponse {
  // 客户列表
  repeated Customer customers = 1;
  // 总数
  int32 total = 2;
}

// GetCustomerDetailRequest 获取客户详情请求
message GetCustomerRequest {
  // 客户ID
  int64 customer_id = 1;
}

// DeleteCustomerRequest 删除客户请求
message DeleteCustomerRequest {
  // 客户ID
  int64 customer_id = 1;
}

// SyncCustomerSearchRequest 同步客户数据到搜索请求
message SyncCustomerSearchRequest {
  // 客户ID
  int64 customer_id = 1;
}

// SyncCustomerSearchResponse 同步客户数据到搜索响应
message SyncCustomerSearchResponse {}

// ConvertCustomerRequest 转换客户状态请求
message ConvertCustomerRequest {
  // 客户ID
  int64 customer_id = 1;
}

// ConvertCustomerResponse 转换客户状态响应
message ConvertCustomerResponse {}

// ConvertCustomersAttributeRequest 批量转换客户属性请求
message ConvertCustomersAttributeRequest{
  // 客户IDs
  repeated int64 customer_ids = 1;
  // 生命周期
  optional int64 customize_life_cycle_id = 2;
  // 行动状态
  optional int64 customize_action_state_id = 3;
}

// ConvertCustomersAttributeRequest 批量转换客户属性响应
message ConvertCustomersAttributeResponse{
}

// CreateCustomerHistoryLogRequest 创建用户活动日志
message CreateCustomerHistoryLogRequest {
  // 客户ID
  int64 customer_id = 1;
  // 客户名称
  string customer_name = 9;
  // 客户电话
  string customer_phone_number = 10;
  // 互动数据
  HistoryLog.Action action = 2;
  // 公司名
  int64 company_id = 3;
  // 门店名
  int64 business_id = 4;
  // 操作员工ID
  int64 staff_id = 5;
  // 记录来源
  optional HistoryLog.Source source = 6;
  // 记录来源ID
  optional int64 source_id = 7;
  // 记录来源名称
  optional string source_name = 8;
}

// CreateCustomerHistoryLogResponse 创建用户活动日志
message CreateCustomerHistoryLogResponse {
  // 客户活动日志记录ID
  int64 log_id = 1;
}

// UpdateCustomerHistoryLogRequest 更新用户活动日志
message UpdateCustomerHistoryLogRequest {
  // 客户活动日志记录ID
  int64 log_id = 1;
  // 操作员工ID
  int64 staff_id = 2;

  // 互动数据
  optional HistoryLog.Action action = 10;
}

// UpdateCustomerHistoryLogResponse 创建用户活动日志
message UpdateCustomerHistoryLogResponse {}

// ListCustomerHistoryLogsRequest 获取客户历史记录请求
message ListCustomerHistoryLogsRequest {
  // Filter 过滤器
  message Filter {
    // 记录类型过滤
    optional HistoryLog.Type type = 1;
    // 公司ID 获取公司维度数据时customer_id参数可以不传
    optional int64 company_id = 2;
  }
  // 客户ID
  optional int64 customer_id = 1;
  // 过滤条件
  optional Filter filter = 2;
  // 每页数量，允许为0
  optional int32 page_size = 3 [
    (buf.validate.field).int32.lte = 1000,
    (buf.validate.field).int32.gte = 0
  ];
  // 页码，从1开始
  optional int32 page_num = 4 [(buf.validate.field).int32.gte = 1];
}

// ListCustomerHistoryLogsResponse 获取客户历史记录响应
message ListCustomerHistoryLogsResponse {
  // 历史记录列表
  repeated HistoryLog history_logs = 1;
  // 总数
  int32 total = 2;
}

// CreateCustomerTaskRequest 创建客户任务列表
message CreateCustomerTaskRequest {
  // 客户ID
  int64 customer_id = 1;
  // 公司ID
  int64 company_id = 2;
  // 门店ID
  int64 business_id = 3;
  // 操作员工ID
  int64 staff_id = 4;

  // 任务名称
  string name = 10;
  // 任务分配员工
  optional int64 allocate_staff_id = 11;
  // 任务预期完成的时间
  optional google.protobuf.Timestamp complete_time = 12;
}

// CreateCustomerTaskResponse 创建客户任务列表
message CreateCustomerTaskResponse {
  // 任务ID
  int64 task_id = 1;
}

// UpdateCustomerTaskRequest 更新客户任务
message UpdateCustomerTaskRequest {
  // 任务ID
  int64 task_id = 1;
  // 操作员工ID
  int64 staff_id = 2;

  // 任务名称
  optional string name = 10;
  // 任务分配员工
  optional int64 allocate_staff_id = 11;
  // 任务预期完成的时间
  optional google.protobuf.Timestamp complete_time = 12;
  // 任务状态
  optional Task.State state = 13 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// UpdateCustomerTaskResponse 更新客户任务
message UpdateCustomerTaskResponse {}

// ListCustomerTasksRequest 获取客户任务列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
message ListCustomerTasksRequest {
  // 客户ID
  int64 customer_id = 1;
}

// ListCustomerTasksResponse 获取客户任务列表响应
message ListCustomerTasksResponse {
  // 任务列表
  repeated Task tasks = 1;
}

// DeleteCustomerTaskRequest 删除客户任务列表请求
message DeleteCustomerTaskRequest {
  // 任务ID
  int64 task_id = 1;
  // 操作员工ID
  int64 staff_id = 2;
}

// CreateAddress request
message CreateAddressRequest {
  // 客户ID
  int64 customer_id = 1 [(buf.validate.field).int64.gt = 0];
  // 地址
  Address address = 2;
}

// CreateAddress response
message CreateAddressResponse {
  // 地址ID
  int64 address_id = 1;
}

// UpdateAddress request
message UpdateAddressRequest {
  // update address message
  message UpdateAddress {
    // 地址ID (要更新哪个地址)
    int64 address_id = 1;
    // 地址1
    optional string address1 = 2;
    // 地址2
    optional string address2 = 3;
    // 城市
    optional string city = 4;
    // 州/省
    optional string state = 5;
    // 区域代码 (替代 country)
    optional string region_code = 6;
    // 邮编
    optional string zipcode = 7;
    // 纬度
    optional string lat = 8;
    // 经度
    optional string lng = 9;
    // 是否为主地址
    // 注意, 这里的枚举和数据库不一致
    // 数据库中未重构前是0和1, 目前用枚举表示, service 需要转换枚举和db中的数据
    // 上游无需关心转换, 直接使用枚举即可
    optional backend.proto.customer.v1.Address.IsPrimary is_primary = 10;
  }
  // update address
  UpdateAddress address = 1;
}

// UpdateAddress response
message UpdateAddressResponse {}

// DeleteAddress request
message DeleteAddressRequest {
  // 地址ID
  int64 address_id = 1;
}

// ListAddresses request
message ListAddressesRequest {
  // order, service 中不会有默认排序，需要上游控制
  enum OrderField {
    // 默认排序
    ORDER_FIELD_UNSPECIFIED = 0;
    // 创建时间
    CREATE_TIME = 1;
    // 更新时间
    UPDATE_TIME = 2;
    // 地址ID
    ID = 3;
  }

  // 排序方向
  enum OrderDirection {
    // 默认排序
    ORDER_DIRECTION_UNSPECIFIED = 0;
    // 升序
    ASC = 1;
    // 降序
    DESC = 2;
  }

  // (-- api-linter: core::0132::request-parent-field=disabled
  //     aip.dev/not-precedent: 使用 int64 作为 customer id 类型 --)
  // parent, 这里是 customer id
  int64 parent = 1 [(google.api.field_behavior) = REQUIRED];
  // 每页数量，允许为0
  optional int32 page_size = 2 [
    (buf.validate.field).int32.lte = 1000,
    (buf.validate.field).int32.gte = 0
  ];
  // 页码，从1开始
  optional int32 page_num = 3 [(buf.validate.field).int32.gte = 1];

  // 排序字段
  optional OrderField order_field = 4;

  // 排序方向
  optional OrderDirection order_direction = 5;
}

// ListAddresses response
message ListAddressesResponse {
  // 地址列表
  repeated Address addresses = 1;
  // 总数
  int32 total = 2;
}

// CreateLifeCycleRequest 创建生命周期请求
message CreateLifeCycleRequest {
  // 公司ID
  int64 company_id = 1;
  // 门店ID
  int64 business_id = 2;
  // 操作员工ID
  int64 staff_id = 3;

  // 名称
  string name = 10;
  // 排序
  int32 sort = 11;
}

// CreateLifeCycleResponse 创建生命周期响应
message CreateLifeCycleResponse {
  // LifeCycle ID
  int64 id = 1;
}

// UpdateLifeCyclesRequest 批量更新生命周期请求
message UpdateLifeCyclesRequest {
  // 批量更新结构
  message UpdateLifeCycle {
    // ID
    int64 id = 1;
    // 名称
    optional string name = 2;
    // 排序
    optional int32 sort = 3;
  }

  // 批量更新数据
  repeated UpdateLifeCycle updates = 1;
  // 操作员工ID
  int64 staff_id = 2;
  // 公司ID
  int64 company_id = 3;
}

// UpdateLifeCyclesResponse 批量更更新生命周期响应
message UpdateLifeCyclesResponse {}

// ListLifeCyclesRequest 获取生命周期列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
message ListLifeCyclesRequest {
  // 公司ID
  int64 company_id = 1;
}

// ListLifeCyclesResponse 获取生命周期列表响应
message ListLifeCyclesResponse {
  // 生命周期列表响应
  repeated CustomizeLifeCycle life_cycles = 1;
}

// DeleteLifeCycleRequest 删除生命周期请求
message DeleteLifeCycleRequest {
  // ID
  int64 id = 1;
  // 操作员工ID
  int64 staff_id = 2;
}

// CreateActionStateRequest 创建行动状态请求
message CreateActionStateRequest {
  // 公司ID
  int64 company_id = 1;
  // 门店ID
  int64 business_id = 2;
  // 操作员工ID
  int64 staff_id = 3;

  // 名称
  string name = 10;
  // 排序
  int32 sort = 11;
  // 颜色
  string color = 12;
}

// CreateActionStateResponse 创建行动状态响应
message CreateActionStateResponse {
  // ActionState ID
  int64 id = 1;
}

// UpdateActionStatesRequest 批量更新行动状态请求
message UpdateActionStatesRequest {
  // 批量更新结构
  message UpdateActionState {
    // ID
    int64 id = 1;
    // 名称
    optional string name = 2;
    // 排序
    optional int32 sort = 3;
    // 颜色
    optional string color = 4;
  }

  // 批量更新数据
  repeated UpdateActionState updates = 1;
  // 操作员工ID
  int64 staff_id = 2;
  // 公司ID
  int64 company_id = 3;
}

// UpdateActionStatesResponse 批量更新行动状态响应
message UpdateActionsStatesResponse {}

// ListActionStatesRequest 获取行动状态列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
message ListActionStatesRequest {
  // 公司ID
  int64 company_id = 1;
}

// ListActionStatesResponse 获取行动状态列表响应
message ListActionStatesResponse {
  // 行动状态列表
  repeated CustomizeActionState action_states = 1;
}

// DeleteActionStateRequest 删除行动状态请求
message DeleteActionStateRequest {
  // ID
  int64 id = 1;
  // 操作员工ID
  int64 staff_id = 2;
}

// CreateViewRequest 创建视图请求
message CreateViewRequest {
  // 视图名称
  string title = 1;
  // 显示字段列表
  repeated string fields = 2;
  // 排序配置
  CustomerView.OrderBy order_by = 3;
  // 筛选条件
  CustomerView.Filter filter = 4;
  // 员工 ID
  int64 staff_id = 5;
  // 公司 ID
  int64 company_id = 6;
  // 类型
  CustomerView.Type type = 7;
}

// CreateViewResponse 创建视图响应
message CreateViewResponse {
  // View ID
  int64 id = 1;
}

// UpdateViewRequest 更新视图请求
message UpdateViewRequest {
  // 视图 ID
  int64 id = 1;
  // 视图名称
  optional string title = 2;
  // 显示字段列表
  repeated string fields = 3;
  // 排序配置
  optional CustomerView.OrderBy order_by = 4;
  // 筛选条件
  optional CustomerView.Filter filter = 5;
  // 员工 ID
  int64 staff_id = 6;
  // 公司 ID
  int64 company_id = 7;
}

// UpdateViewResponse 更新视图响应
message UpdateViewResponse {}

// ListViewsRequest 获取视图列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
message ListViewsRequest {
  // 公司 ID
  int64 company_id = 1;
  // 员工 ID
  int64 staff_id = 2;
  // 类型
  optional CustomerView.Type type = 3;
}

// ListViewsResponse 获取视图列表响应
message ListViewsResponse {
  // 视图列表
  repeated CustomerView views = 1;
}

// DeleteViewRequest 删除视图请求
message DeleteViewRequest {
  // 视图 ID
  int64 id = 1;
  // 公司 ID
  int64 company_id = 2;
  // 员工 ID
  int64 staff_id = 3;
}

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
enum ErrCode {
  // (-- api-linter: core::0126::unspecified=disabled
  //     aip.dev/not-precedent: We need to do this because
  //     the content of the error code is automatically generated by
  //     the script and is exclusive to each service.
  //     Please do not turn off this linter for the rest of the enum --)
  // 成功
  ERR_CODE_OK = 0;
  // 本服务自动分配的全局唯一的起始错误码
  ERR_CODE_UNSPECIFIED = 119500;
  // 客户不存在
  ERR_CODE_CUSTOMER_NOT_FOUND = 119501;
  // 客户已存在
  ERR_CODE_CUSTOMER_ALREADY_EXISTS = 119502;
  // 无效的客户ID
  ERR_CODE_INVALID_CUSTOMER_ID = 119503;
  // 无效的客户名称
  ERR_CODE_INVALID_CUSTOMER_NAME = 119504;
  // 客户已删除
  ERR_CODE_CUSTOMER_DELETED = 119505;

  // 地址不存在
  ERR_CODE_ADDRESS_NOT_FOUND = 119601;
  // 无效的地址信息
  ERR_CODE_INVALID_ADDRESS = 119602;
  // 超出地址数量限制
  ERR_CODE_ADDRESS_LIMIT_EXCEEDED = 119603;

  // 任务不存在
  ERR_CODE_TASK_NOT_FOUND = 119701;
  // 任务已完成
  ERR_CODE_TASK_ALREADY_COMPLETED = 119702;
  // 无效的任务状态
  ERR_CODE_INVALID_TASK_STATUS = 119703;

  // Action State Name 已经存在
  ERR_CODE_ACTION_STATE_NAME_EXIST = 119704;
  // Life Cycle Name 已经存在
  ERR_CODE_LIFE_CYCLE_NAME_EXIST = 119705;
  // View Name 已经存在
  ERR_CODE_VIEW_NAME_EXIST = 119706;
}
