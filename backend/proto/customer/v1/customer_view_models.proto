syntax = "proto3";

package backend.proto.customer.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/customer/v1;customerpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.customer.v1";

// 客户视图
message CustomerView {
  // 排序规则
  message OrderBy {
    // 排序字段
    string property = 1;
    // 排序方向
    string order = 2;
  }

  // 筛选条件
  message Filter {
    // 筛选类型
    optional string type = 1;
    // 子筛选条件
    repeated Filter filters = 2;
    // 操作符
    optional string operator = 3;
    // 筛选字段
    optional string property = 4;
    // 筛选值
    optional string value = 5;
    // 多个值
    repeated string values = 6;
  }

  // 类型
  enum Type {
    // 未指定字段
    TYPE_UNSPECIFIED = 0;
    // 客户
    CUSTOMER = 1;
    // 潜客
    LEAD = 2;
  }

  // 视图 ID
  int64 id = 1;
  // 公司 ID
  int64 company_id = 2;
  // 员工 ID
  int64 staff_id = 3;
  // 是否为默认视图 0-否 1-是
  int32 is_default = 4;
  // 视图标题
  string title = 5;
  // 显示字段列表
  repeated string fields = 6;
  // 排序规则
  OrderBy order_by = 7;
  // 筛选条件
  Filter filter = 8;
  // 类型
  Type type = 9;
}
