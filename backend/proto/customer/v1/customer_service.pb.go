// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0148::human-names=disabled
//     aip.dev/not-precedent: 使用first_name和last_name作为字段名 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0132::request-field-types=disabled
//     aip.dev/not-precedent: 使用自定义过滤器类型 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用customer_id作为标识符 --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用自定义响应字段 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 使用自定义请求字段结构 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用name后缀 --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/customer/v1/customer_service.proto

package customerpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 119500
	// 客户不存在
	ErrCode_ERR_CODE_CUSTOMER_NOT_FOUND ErrCode = 119501
	// 客户已存在
	ErrCode_ERR_CODE_CUSTOMER_ALREADY_EXISTS ErrCode = 119502
	// 无效的客户ID
	ErrCode_ERR_CODE_INVALID_CUSTOMER_ID ErrCode = 119503
	// 无效的客户名称
	ErrCode_ERR_CODE_INVALID_CUSTOMER_NAME ErrCode = 119504
	// 客户已删除
	ErrCode_ERR_CODE_CUSTOMER_DELETED ErrCode = 119505
	// 地址不存在
	ErrCode_ERR_CODE_ADDRESS_NOT_FOUND ErrCode = 119601
	// 无效的地址信息
	ErrCode_ERR_CODE_INVALID_ADDRESS ErrCode = 119602
	// 超出地址数量限制
	ErrCode_ERR_CODE_ADDRESS_LIMIT_EXCEEDED ErrCode = 119603
	// 任务不存在
	ErrCode_ERR_CODE_TASK_NOT_FOUND ErrCode = 119701
	// 任务已完成
	ErrCode_ERR_CODE_TASK_ALREADY_COMPLETED ErrCode = 119702
	// 无效的任务状态
	ErrCode_ERR_CODE_INVALID_TASK_STATUS ErrCode = 119703
	// Action State Name 已经存在
	ErrCode_ERR_CODE_ACTION_STATE_NAME_EXIST ErrCode = 119704
	// Life Cycle Name 已经存在
	ErrCode_ERR_CODE_LIFE_CYCLE_NAME_EXIST ErrCode = 119705
	// View Name 已经存在
	ErrCode_ERR_CODE_VIEW_NAME_EXIST ErrCode = 119706
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		119500: "ERR_CODE_UNSPECIFIED",
		119501: "ERR_CODE_CUSTOMER_NOT_FOUND",
		119502: "ERR_CODE_CUSTOMER_ALREADY_EXISTS",
		119503: "ERR_CODE_INVALID_CUSTOMER_ID",
		119504: "ERR_CODE_INVALID_CUSTOMER_NAME",
		119505: "ERR_CODE_CUSTOMER_DELETED",
		119601: "ERR_CODE_ADDRESS_NOT_FOUND",
		119602: "ERR_CODE_INVALID_ADDRESS",
		119603: "ERR_CODE_ADDRESS_LIMIT_EXCEEDED",
		119701: "ERR_CODE_TASK_NOT_FOUND",
		119702: "ERR_CODE_TASK_ALREADY_COMPLETED",
		119703: "ERR_CODE_INVALID_TASK_STATUS",
		119704: "ERR_CODE_ACTION_STATE_NAME_EXIST",
		119705: "ERR_CODE_LIFE_CYCLE_NAME_EXIST",
		119706: "ERR_CODE_VIEW_NAME_EXIST",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":                      0,
		"ERR_CODE_UNSPECIFIED":             119500,
		"ERR_CODE_CUSTOMER_NOT_FOUND":      119501,
		"ERR_CODE_CUSTOMER_ALREADY_EXISTS": 119502,
		"ERR_CODE_INVALID_CUSTOMER_ID":     119503,
		"ERR_CODE_INVALID_CUSTOMER_NAME":   119504,
		"ERR_CODE_CUSTOMER_DELETED":        119505,
		"ERR_CODE_ADDRESS_NOT_FOUND":       119601,
		"ERR_CODE_INVALID_ADDRESS":         119602,
		"ERR_CODE_ADDRESS_LIMIT_EXCEEDED":  119603,
		"ERR_CODE_TASK_NOT_FOUND":          119701,
		"ERR_CODE_TASK_ALREADY_COMPLETED":  119702,
		"ERR_CODE_INVALID_TASK_STATUS":     119703,
		"ERR_CODE_ACTION_STATE_NAME_EXIST": 119704,
		"ERR_CODE_LIFE_CYCLE_NAME_EXIST":   119705,
		"ERR_CODE_VIEW_NAME_EXIST":         119706,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{0}
}

// order, service 中不会有默认排序，需要上游控制
type ListCustomersRequest_OrderField int32

const (
	// 默认排序
	ListCustomersRequest_ORDER_FIELD_UNSPECIFIED ListCustomersRequest_OrderField = 0
	// 创建时间
	ListCustomersRequest_CREATE_TIME ListCustomersRequest_OrderField = 1
	// 更新时间
	ListCustomersRequest_UPDATE_TIME ListCustomersRequest_OrderField = 2
	// 客户ID
	ListCustomersRequest_ID ListCustomersRequest_OrderField = 3
)

// Enum value maps for ListCustomersRequest_OrderField.
var (
	ListCustomersRequest_OrderField_name = map[int32]string{
		0: "ORDER_FIELD_UNSPECIFIED",
		1: "CREATE_TIME",
		2: "UPDATE_TIME",
		3: "ID",
	}
	ListCustomersRequest_OrderField_value = map[string]int32{
		"ORDER_FIELD_UNSPECIFIED": 0,
		"CREATE_TIME":             1,
		"UPDATE_TIME":             2,
		"ID":                      3,
	}
)

func (x ListCustomersRequest_OrderField) Enum() *ListCustomersRequest_OrderField {
	p := new(ListCustomersRequest_OrderField)
	*p = x
	return p
}

func (x ListCustomersRequest_OrderField) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomersRequest_OrderField) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_service_proto_enumTypes[1].Descriptor()
}

func (ListCustomersRequest_OrderField) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_service_proto_enumTypes[1]
}

func (x ListCustomersRequest_OrderField) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomersRequest_OrderField.Descriptor instead.
func (ListCustomersRequest_OrderField) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{4, 0}
}

// 排序方向
type ListCustomersRequest_OrderDirection int32

const (
	// 默认排序
	ListCustomersRequest_ORDER_DIRECTION_UNSPECIFIED ListCustomersRequest_OrderDirection = 0
	// 升序
	ListCustomersRequest_ASC ListCustomersRequest_OrderDirection = 1
	// 降序
	ListCustomersRequest_DESC ListCustomersRequest_OrderDirection = 2
)

// Enum value maps for ListCustomersRequest_OrderDirection.
var (
	ListCustomersRequest_OrderDirection_name = map[int32]string{
		0: "ORDER_DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	ListCustomersRequest_OrderDirection_value = map[string]int32{
		"ORDER_DIRECTION_UNSPECIFIED": 0,
		"ASC":                         1,
		"DESC":                        2,
	}
)

func (x ListCustomersRequest_OrderDirection) Enum() *ListCustomersRequest_OrderDirection {
	p := new(ListCustomersRequest_OrderDirection)
	*p = x
	return p
}

func (x ListCustomersRequest_OrderDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListCustomersRequest_OrderDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_service_proto_enumTypes[2].Descriptor()
}

func (ListCustomersRequest_OrderDirection) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_service_proto_enumTypes[2]
}

func (x ListCustomersRequest_OrderDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListCustomersRequest_OrderDirection.Descriptor instead.
func (ListCustomersRequest_OrderDirection) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{4, 1}
}

// order, service 中不会有默认排序，需要上游控制
type ListAddressesRequest_OrderField int32

const (
	// 默认排序
	ListAddressesRequest_ORDER_FIELD_UNSPECIFIED ListAddressesRequest_OrderField = 0
	// 创建时间
	ListAddressesRequest_CREATE_TIME ListAddressesRequest_OrderField = 1
	// 更新时间
	ListAddressesRequest_UPDATE_TIME ListAddressesRequest_OrderField = 2
	// 地址ID
	ListAddressesRequest_ID ListAddressesRequest_OrderField = 3
)

// Enum value maps for ListAddressesRequest_OrderField.
var (
	ListAddressesRequest_OrderField_name = map[int32]string{
		0: "ORDER_FIELD_UNSPECIFIED",
		1: "CREATE_TIME",
		2: "UPDATE_TIME",
		3: "ID",
	}
	ListAddressesRequest_OrderField_value = map[string]int32{
		"ORDER_FIELD_UNSPECIFIED": 0,
		"CREATE_TIME":             1,
		"UPDATE_TIME":             2,
		"ID":                      3,
	}
)

func (x ListAddressesRequest_OrderField) Enum() *ListAddressesRequest_OrderField {
	p := new(ListAddressesRequest_OrderField)
	*p = x
	return p
}

func (x ListAddressesRequest_OrderField) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAddressesRequest_OrderField) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_service_proto_enumTypes[3].Descriptor()
}

func (ListAddressesRequest_OrderField) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_service_proto_enumTypes[3]
}

func (x ListAddressesRequest_OrderField) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAddressesRequest_OrderField.Descriptor instead.
func (ListAddressesRequest_OrderField) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{32, 0}
}

// 排序方向
type ListAddressesRequest_OrderDirection int32

const (
	// 默认排序
	ListAddressesRequest_ORDER_DIRECTION_UNSPECIFIED ListAddressesRequest_OrderDirection = 0
	// 升序
	ListAddressesRequest_ASC ListAddressesRequest_OrderDirection = 1
	// 降序
	ListAddressesRequest_DESC ListAddressesRequest_OrderDirection = 2
)

// Enum value maps for ListAddressesRequest_OrderDirection.
var (
	ListAddressesRequest_OrderDirection_name = map[int32]string{
		0: "ORDER_DIRECTION_UNSPECIFIED",
		1: "ASC",
		2: "DESC",
	}
	ListAddressesRequest_OrderDirection_value = map[string]int32{
		"ORDER_DIRECTION_UNSPECIFIED": 0,
		"ASC":                         1,
		"DESC":                        2,
	}
)

func (x ListAddressesRequest_OrderDirection) Enum() *ListAddressesRequest_OrderDirection {
	p := new(ListAddressesRequest_OrderDirection)
	*p = x
	return p
}

func (x ListAddressesRequest_OrderDirection) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListAddressesRequest_OrderDirection) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_customer_v1_customer_service_proto_enumTypes[4].Descriptor()
}

func (ListAddressesRequest_OrderDirection) Type() protoreflect.EnumType {
	return &file_backend_proto_customer_v1_customer_service_proto_enumTypes[4]
}

func (x ListAddressesRequest_OrderDirection) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListAddressesRequest_OrderDirection.Descriptor instead.
func (ListAddressesRequest_OrderDirection) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{32, 1}
}

// CreateCustomerRequest 创建客户请求
type CreateCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// customer
	Customer *Customer `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	// staff_id
	StaffId *int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3,oneof" json:"staff_id,omitempty"`
	// staff_name
	StaffName     *string `protobuf:"bytes,3,opt,name=staff_name,json=staffName,proto3,oneof" json:"staff_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerRequest) Reset() {
	*x = CreateCustomerRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerRequest) ProtoMessage() {}

func (x *CreateCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{0}
}

func (x *CreateCustomerRequest) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *CreateCustomerRequest) GetStaffId() int64 {
	if x != nil && x.StaffId != nil {
		return *x.StaffId
	}
	return 0
}

func (x *CreateCustomerRequest) GetStaffName() string {
	if x != nil && x.StaffName != nil {
		return *x.StaffName
	}
	return ""
}

// CreateCustomerResponse 创建客户响应
type CreateCustomerResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// customer
	Customer *Customer `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	// is exist
	IsExist       bool `protobuf:"varint,2,opt,name=is_exist,json=isExist,proto3" json:"is_exist,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerResponse) Reset() {
	*x = CreateCustomerResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerResponse) ProtoMessage() {}

func (x *CreateCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateCustomerResponse) GetCustomer() *Customer {
	if x != nil {
		return x.Customer
	}
	return nil
}

func (x *CreateCustomerResponse) GetIsExist() bool {
	if x != nil {
		return x.IsExist
	}
	return false
}

// UpdateCustomerRequest 更新客户请求
type UpdateCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// update customer request
	Customer      *UpdateCustomerRequest_UpdateCustomer `protobuf:"bytes,1,opt,name=customer,proto3" json:"customer,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerRequest) Reset() {
	*x = UpdateCustomerRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRequest) ProtoMessage() {}

func (x *UpdateCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateCustomerRequest) GetCustomer() *UpdateCustomerRequest_UpdateCustomer {
	if x != nil {
		return x.Customer
	}
	return nil
}

// UpdateCustomerResponse 更新客户响应
type UpdateCustomerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerResponse) Reset() {
	*x = UpdateCustomerResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerResponse) ProtoMessage() {}

func (x *UpdateCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerResponse.ProtoReflect.Descriptor instead.
func (*UpdateCustomerResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{3}
}

// ListCustomersRequest 获取客户列表请求
type ListCustomersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListCustomersRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// company id 必填
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 每页数量，允许为0
	PageSize *int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// 页码，从1开始
	PageNum *int32 `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3,oneof" json:"page_num,omitempty"`
	// 排序字段
	OrderField *ListCustomersRequest_OrderField `protobuf:"varint,5,opt,name=order_field,json=orderField,proto3,enum=backend.proto.customer.v1.ListCustomersRequest_OrderField,oneof" json:"order_field,omitempty"`
	// 排序方向
	OrderDirection *ListCustomersRequest_OrderDirection `protobuf:"varint,6,opt,name=order_direction,json=orderDirection,proto3,enum=backend.proto.customer.v1.ListCustomersRequest_OrderDirection,oneof" json:"order_direction,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListCustomersRequest) Reset() {
	*x = ListCustomersRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersRequest) ProtoMessage() {}

func (x *ListCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersRequest.ProtoReflect.Descriptor instead.
func (*ListCustomersRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{4}
}

func (x *ListCustomersRequest) GetFilter() *ListCustomersRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomersRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListCustomersRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *ListCustomersRequest) GetPageNum() int32 {
	if x != nil && x.PageNum != nil {
		return *x.PageNum
	}
	return 0
}

func (x *ListCustomersRequest) GetOrderField() ListCustomersRequest_OrderField {
	if x != nil && x.OrderField != nil {
		return *x.OrderField
	}
	return ListCustomersRequest_ORDER_FIELD_UNSPECIFIED
}

func (x *ListCustomersRequest) GetOrderDirection() ListCustomersRequest_OrderDirection {
	if x != nil && x.OrderDirection != nil {
		return *x.OrderDirection
	}
	return ListCustomersRequest_ORDER_DIRECTION_UNSPECIFIED
}

// ListCustomersResponse 获取客户列表响应
type ListCustomersResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户列表
	Customers []*Customer `protobuf:"bytes,1,rep,name=customers,proto3" json:"customers,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomersResponse) Reset() {
	*x = ListCustomersResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersResponse) ProtoMessage() {}

func (x *ListCustomersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersResponse.ProtoReflect.Descriptor instead.
func (*ListCustomersResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListCustomersResponse) GetCustomers() []*Customer {
	if x != nil {
		return x.Customers
	}
	return nil
}

func (x *ListCustomersResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// GetCustomerDetailRequest 获取客户详情请求
type GetCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId    int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomerRequest) Reset() {
	*x = GetCustomerRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomerRequest) ProtoMessage() {}

func (x *GetCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomerRequest.ProtoReflect.Descriptor instead.
func (*GetCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{6}
}

func (x *GetCustomerRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// DeleteCustomerRequest 删除客户请求
type DeleteCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId    int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomerRequest) Reset() {
	*x = DeleteCustomerRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerRequest) ProtoMessage() {}

func (x *DeleteCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerRequest.ProtoReflect.Descriptor instead.
func (*DeleteCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteCustomerRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// SyncCustomerSearchRequest 同步客户数据到搜索请求
type SyncCustomerSearchRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId    int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncCustomerSearchRequest) Reset() {
	*x = SyncCustomerSearchRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncCustomerSearchRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncCustomerSearchRequest) ProtoMessage() {}

func (x *SyncCustomerSearchRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncCustomerSearchRequest.ProtoReflect.Descriptor instead.
func (*SyncCustomerSearchRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{8}
}

func (x *SyncCustomerSearchRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// SyncCustomerSearchResponse 同步客户数据到搜索响应
type SyncCustomerSearchResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncCustomerSearchResponse) Reset() {
	*x = SyncCustomerSearchResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncCustomerSearchResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncCustomerSearchResponse) ProtoMessage() {}

func (x *SyncCustomerSearchResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncCustomerSearchResponse.ProtoReflect.Descriptor instead.
func (*SyncCustomerSearchResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{9}
}

// ConvertCustomerRequest 转换客户状态请求
type ConvertCustomerRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId    int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertCustomerRequest) Reset() {
	*x = ConvertCustomerRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertCustomerRequest) ProtoMessage() {}

func (x *ConvertCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertCustomerRequest.ProtoReflect.Descriptor instead.
func (*ConvertCustomerRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{10}
}

func (x *ConvertCustomerRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// ConvertCustomerResponse 转换客户状态响应
type ConvertCustomerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertCustomerResponse) Reset() {
	*x = ConvertCustomerResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertCustomerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertCustomerResponse) ProtoMessage() {}

func (x *ConvertCustomerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertCustomerResponse.ProtoReflect.Descriptor instead.
func (*ConvertCustomerResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{11}
}

// ConvertCustomersAttributeRequest 批量转换客户属性请求
type ConvertCustomersAttributeRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户IDs
	CustomerIds []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// 生命周期
	CustomizeLifeCycleId *int64 `protobuf:"varint,2,opt,name=customize_life_cycle_id,json=customizeLifeCycleId,proto3,oneof" json:"customize_life_cycle_id,omitempty"`
	// 行动状态
	CustomizeActionStateId *int64 `protobuf:"varint,3,opt,name=customize_action_state_id,json=customizeActionStateId,proto3,oneof" json:"customize_action_state_id,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ConvertCustomersAttributeRequest) Reset() {
	*x = ConvertCustomersAttributeRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertCustomersAttributeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertCustomersAttributeRequest) ProtoMessage() {}

func (x *ConvertCustomersAttributeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertCustomersAttributeRequest.ProtoReflect.Descriptor instead.
func (*ConvertCustomersAttributeRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{12}
}

func (x *ConvertCustomersAttributeRequest) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ConvertCustomersAttributeRequest) GetCustomizeLifeCycleId() int64 {
	if x != nil && x.CustomizeLifeCycleId != nil {
		return *x.CustomizeLifeCycleId
	}
	return 0
}

func (x *ConvertCustomersAttributeRequest) GetCustomizeActionStateId() int64 {
	if x != nil && x.CustomizeActionStateId != nil {
		return *x.CustomizeActionStateId
	}
	return 0
}

// ConvertCustomersAttributeRequest 批量转换客户属性响应
type ConvertCustomersAttributeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConvertCustomersAttributeResponse) Reset() {
	*x = ConvertCustomersAttributeResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConvertCustomersAttributeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConvertCustomersAttributeResponse) ProtoMessage() {}

func (x *ConvertCustomersAttributeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConvertCustomersAttributeResponse.ProtoReflect.Descriptor instead.
func (*ConvertCustomersAttributeResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{13}
}

// CreateCustomerHistoryLogRequest 创建用户活动日志
type CreateCustomerHistoryLogRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 客户名称
	CustomerName string `protobuf:"bytes,9,opt,name=customer_name,json=customerName,proto3" json:"customer_name,omitempty"`
	// 客户电话
	CustomerPhoneNumber string `protobuf:"bytes,10,opt,name=customer_phone_number,json=customerPhoneNumber,proto3" json:"customer_phone_number,omitempty"`
	// 互动数据
	Action *HistoryLog_Action `protobuf:"bytes,2,opt,name=action,proto3" json:"action,omitempty"`
	// 公司名
	CompanyId int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店名
	BusinessId int64 `protobuf:"varint,4,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 记录来源
	Source *HistoryLog_Source `protobuf:"varint,6,opt,name=source,proto3,enum=backend.proto.customer.v1.HistoryLog_Source,oneof" json:"source,omitempty"`
	// 记录来源ID
	SourceId *int64 `protobuf:"varint,7,opt,name=source_id,json=sourceId,proto3,oneof" json:"source_id,omitempty"`
	// 记录来源名称
	SourceName    *string `protobuf:"bytes,8,opt,name=source_name,json=sourceName,proto3,oneof" json:"source_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerHistoryLogRequest) Reset() {
	*x = CreateCustomerHistoryLogRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerHistoryLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerHistoryLogRequest) ProtoMessage() {}

func (x *CreateCustomerHistoryLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerHistoryLogRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerHistoryLogRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{14}
}

func (x *CreateCustomerHistoryLogRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateCustomerHistoryLogRequest) GetCustomerName() string {
	if x != nil {
		return x.CustomerName
	}
	return ""
}

func (x *CreateCustomerHistoryLogRequest) GetCustomerPhoneNumber() string {
	if x != nil {
		return x.CustomerPhoneNumber
	}
	return ""
}

func (x *CreateCustomerHistoryLogRequest) GetAction() *HistoryLog_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

func (x *CreateCustomerHistoryLogRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCustomerHistoryLogRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateCustomerHistoryLogRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateCustomerHistoryLogRequest) GetSource() HistoryLog_Source {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return HistoryLog_SOURCE_UNSPECIFIED
}

func (x *CreateCustomerHistoryLogRequest) GetSourceId() int64 {
	if x != nil && x.SourceId != nil {
		return *x.SourceId
	}
	return 0
}

func (x *CreateCustomerHistoryLogRequest) GetSourceName() string {
	if x != nil && x.SourceName != nil {
		return *x.SourceName
	}
	return ""
}

// CreateCustomerHistoryLogResponse 创建用户活动日志
type CreateCustomerHistoryLogResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户活动日志记录ID
	LogId         int64 `protobuf:"varint,1,opt,name=log_id,json=logId,proto3" json:"log_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerHistoryLogResponse) Reset() {
	*x = CreateCustomerHistoryLogResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerHistoryLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerHistoryLogResponse) ProtoMessage() {}

func (x *CreateCustomerHistoryLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerHistoryLogResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerHistoryLogResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{15}
}

func (x *CreateCustomerHistoryLogResponse) GetLogId() int64 {
	if x != nil {
		return x.LogId
	}
	return 0
}

// UpdateCustomerHistoryLogRequest 更新用户活动日志
type UpdateCustomerHistoryLogRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户活动日志记录ID
	LogId int64 `protobuf:"varint,1,opt,name=log_id,json=logId,proto3" json:"log_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 互动数据
	Action        *HistoryLog_Action `protobuf:"bytes,10,opt,name=action,proto3,oneof" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerHistoryLogRequest) Reset() {
	*x = UpdateCustomerHistoryLogRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerHistoryLogRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerHistoryLogRequest) ProtoMessage() {}

func (x *UpdateCustomerHistoryLogRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerHistoryLogRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomerHistoryLogRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{16}
}

func (x *UpdateCustomerHistoryLogRequest) GetLogId() int64 {
	if x != nil {
		return x.LogId
	}
	return 0
}

func (x *UpdateCustomerHistoryLogRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateCustomerHistoryLogRequest) GetAction() *HistoryLog_Action {
	if x != nil {
		return x.Action
	}
	return nil
}

// UpdateCustomerHistoryLogResponse 创建用户活动日志
type UpdateCustomerHistoryLogResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerHistoryLogResponse) Reset() {
	*x = UpdateCustomerHistoryLogResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerHistoryLogResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerHistoryLogResponse) ProtoMessage() {}

func (x *UpdateCustomerHistoryLogResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerHistoryLogResponse.ProtoReflect.Descriptor instead.
func (*UpdateCustomerHistoryLogResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{17}
}

// ListCustomerHistoryLogsRequest 获取客户历史记录请求
type ListCustomerHistoryLogsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId *int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3,oneof" json:"customer_id,omitempty"`
	// 过滤条件
	Filter *ListCustomerHistoryLogsRequest_Filter `protobuf:"bytes,2,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// 每页数量，允许为0
	PageSize *int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// 页码，从1开始
	PageNum       *int32 `protobuf:"varint,4,opt,name=page_num,json=pageNum,proto3,oneof" json:"page_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerHistoryLogsRequest) Reset() {
	*x = ListCustomerHistoryLogsRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerHistoryLogsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerHistoryLogsRequest) ProtoMessage() {}

func (x *ListCustomerHistoryLogsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerHistoryLogsRequest.ProtoReflect.Descriptor instead.
func (*ListCustomerHistoryLogsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{18}
}

func (x *ListCustomerHistoryLogsRequest) GetCustomerId() int64 {
	if x != nil && x.CustomerId != nil {
		return *x.CustomerId
	}
	return 0
}

func (x *ListCustomerHistoryLogsRequest) GetFilter() *ListCustomerHistoryLogsRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListCustomerHistoryLogsRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *ListCustomerHistoryLogsRequest) GetPageNum() int32 {
	if x != nil && x.PageNum != nil {
		return *x.PageNum
	}
	return 0
}

// ListCustomerHistoryLogsResponse 获取客户历史记录响应
type ListCustomerHistoryLogsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 历史记录列表
	HistoryLogs []*HistoryLog `protobuf:"bytes,1,rep,name=history_logs,json=historyLogs,proto3" json:"history_logs,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerHistoryLogsResponse) Reset() {
	*x = ListCustomerHistoryLogsResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerHistoryLogsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerHistoryLogsResponse) ProtoMessage() {}

func (x *ListCustomerHistoryLogsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerHistoryLogsResponse.ProtoReflect.Descriptor instead.
func (*ListCustomerHistoryLogsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{19}
}

func (x *ListCustomerHistoryLogsResponse) GetHistoryLogs() []*HistoryLog {
	if x != nil {
		return x.HistoryLogs
	}
	return nil
}

func (x *ListCustomerHistoryLogsResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// CreateCustomerTaskRequest 创建客户任务列表
type CreateCustomerTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,4,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 任务名称
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
	// 任务分配员工
	AllocateStaffId *int64 `protobuf:"varint,11,opt,name=allocate_staff_id,json=allocateStaffId,proto3,oneof" json:"allocate_staff_id,omitempty"`
	// 任务预期完成的时间
	CompleteTime  *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=complete_time,json=completeTime,proto3,oneof" json:"complete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerTaskRequest) Reset() {
	*x = CreateCustomerTaskRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerTaskRequest) ProtoMessage() {}

func (x *CreateCustomerTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateCustomerTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{20}
}

func (x *CreateCustomerTaskRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateCustomerTaskRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateCustomerTaskRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateCustomerTaskRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateCustomerTaskRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateCustomerTaskRequest) GetAllocateStaffId() int64 {
	if x != nil && x.AllocateStaffId != nil {
		return *x.AllocateStaffId
	}
	return 0
}

func (x *CreateCustomerTaskRequest) GetCompleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompleteTime
	}
	return nil
}

// CreateCustomerTaskResponse 创建客户任务列表
type CreateCustomerTaskResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 任务ID
	TaskId        int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateCustomerTaskResponse) Reset() {
	*x = CreateCustomerTaskResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateCustomerTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateCustomerTaskResponse) ProtoMessage() {}

func (x *CreateCustomerTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateCustomerTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateCustomerTaskResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{21}
}

func (x *CreateCustomerTaskResponse) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

// UpdateCustomerTaskRequest 更新客户任务
type UpdateCustomerTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 任务ID
	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 任务名称
	Name *string `protobuf:"bytes,10,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 任务分配员工
	AllocateStaffId *int64 `protobuf:"varint,11,opt,name=allocate_staff_id,json=allocateStaffId,proto3,oneof" json:"allocate_staff_id,omitempty"`
	// 任务预期完成的时间
	CompleteTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=complete_time,json=completeTime,proto3,oneof" json:"complete_time,omitempty"`
	// 任务状态
	State         *Task_State `protobuf:"varint,13,opt,name=state,proto3,enum=backend.proto.customer.v1.Task_State,oneof" json:"state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerTaskRequest) Reset() {
	*x = UpdateCustomerTaskRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerTaskRequest) ProtoMessage() {}

func (x *UpdateCustomerTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerTaskRequest.ProtoReflect.Descriptor instead.
func (*UpdateCustomerTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{22}
}

func (x *UpdateCustomerTaskRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *UpdateCustomerTaskRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateCustomerTaskRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateCustomerTaskRequest) GetAllocateStaffId() int64 {
	if x != nil && x.AllocateStaffId != nil {
		return *x.AllocateStaffId
	}
	return 0
}

func (x *UpdateCustomerTaskRequest) GetCompleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CompleteTime
	}
	return nil
}

func (x *UpdateCustomerTaskRequest) GetState() Task_State {
	if x != nil && x.State != nil {
		return *x.State
	}
	return Task_STATE_UNSPECIFIED
}

// UpdateCustomerTaskResponse 更新客户任务
type UpdateCustomerTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerTaskResponse) Reset() {
	*x = UpdateCustomerTaskResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerTaskResponse) ProtoMessage() {}

func (x *UpdateCustomerTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerTaskResponse.ProtoReflect.Descriptor instead.
func (*UpdateCustomerTaskResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{23}
}

// ListCustomerTasksRequest 获取客户任务列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
type ListCustomerTasksRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId    int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerTasksRequest) Reset() {
	*x = ListCustomerTasksRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerTasksRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTasksRequest) ProtoMessage() {}

func (x *ListCustomerTasksRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTasksRequest.ProtoReflect.Descriptor instead.
func (*ListCustomerTasksRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{24}
}

func (x *ListCustomerTasksRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

// ListCustomerTasksResponse 获取客户任务列表响应
type ListCustomerTasksResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 任务列表
	Tasks         []*Task `protobuf:"bytes,1,rep,name=tasks,proto3" json:"tasks,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerTasksResponse) Reset() {
	*x = ListCustomerTasksResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerTasksResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerTasksResponse) ProtoMessage() {}

func (x *ListCustomerTasksResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerTasksResponse.ProtoReflect.Descriptor instead.
func (*ListCustomerTasksResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{25}
}

func (x *ListCustomerTasksResponse) GetTasks() []*Task {
	if x != nil {
		return x.Tasks
	}
	return nil
}

// DeleteCustomerTaskRequest 删除客户任务列表请求
type DeleteCustomerTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 任务ID
	TaskId int64 `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
	// 操作员工ID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteCustomerTaskRequest) Reset() {
	*x = DeleteCustomerTaskRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteCustomerTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteCustomerTaskRequest) ProtoMessage() {}

func (x *DeleteCustomerTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteCustomerTaskRequest.ProtoReflect.Descriptor instead.
func (*DeleteCustomerTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{26}
}

func (x *DeleteCustomerTaskRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *DeleteCustomerTaskRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// CreateAddress request
type CreateAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 地址
	Address       *Address `protobuf:"bytes,2,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAddressRequest) Reset() {
	*x = CreateAddressRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAddressRequest) ProtoMessage() {}

func (x *CreateAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAddressRequest.ProtoReflect.Descriptor instead.
func (*CreateAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{27}
}

func (x *CreateAddressRequest) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *CreateAddressRequest) GetAddress() *Address {
	if x != nil {
		return x.Address
	}
	return nil
}

// CreateAddress response
type CreateAddressResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID
	AddressId     int64 `protobuf:"varint,1,opt,name=address_id,json=addressId,proto3" json:"address_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateAddressResponse) Reset() {
	*x = CreateAddressResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateAddressResponse) ProtoMessage() {}

func (x *CreateAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateAddressResponse.ProtoReflect.Descriptor instead.
func (*CreateAddressResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{28}
}

func (x *CreateAddressResponse) GetAddressId() int64 {
	if x != nil {
		return x.AddressId
	}
	return 0
}

// UpdateAddress request
type UpdateAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// update address
	Address       *UpdateAddressRequest_UpdateAddress `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAddressRequest) Reset() {
	*x = UpdateAddressRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddressRequest) ProtoMessage() {}

func (x *UpdateAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddressRequest.ProtoReflect.Descriptor instead.
func (*UpdateAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateAddressRequest) GetAddress() *UpdateAddressRequest_UpdateAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

// UpdateAddress response
type UpdateAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAddressResponse) Reset() {
	*x = UpdateAddressResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddressResponse) ProtoMessage() {}

func (x *UpdateAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddressResponse.ProtoReflect.Descriptor instead.
func (*UpdateAddressResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{30}
}

// DeleteAddress request
type DeleteAddressRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID
	AddressId     int64 `protobuf:"varint,1,opt,name=address_id,json=addressId,proto3" json:"address_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteAddressRequest) Reset() {
	*x = DeleteAddressRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteAddressRequest) ProtoMessage() {}

func (x *DeleteAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteAddressRequest.ProtoReflect.Descriptor instead.
func (*DeleteAddressRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{31}
}

func (x *DeleteAddressRequest) GetAddressId() int64 {
	if x != nil {
		return x.AddressId
	}
	return 0
}

// ListAddresses request
type ListAddressesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// (-- api-linter: core::0132::request-parent-field=disabled
	//
	//	aip.dev/not-precedent: 使用 int64 作为 customer id 类型 --)
	//
	// parent, 这里是 customer id
	Parent int64 `protobuf:"varint,1,opt,name=parent,proto3" json:"parent,omitempty"`
	// 每页数量，允许为0
	PageSize *int32 `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3,oneof" json:"page_size,omitempty"`
	// 页码，从1开始
	PageNum *int32 `protobuf:"varint,3,opt,name=page_num,json=pageNum,proto3,oneof" json:"page_num,omitempty"`
	// 排序字段
	OrderField *ListAddressesRequest_OrderField `protobuf:"varint,4,opt,name=order_field,json=orderField,proto3,enum=backend.proto.customer.v1.ListAddressesRequest_OrderField,oneof" json:"order_field,omitempty"`
	// 排序方向
	OrderDirection *ListAddressesRequest_OrderDirection `protobuf:"varint,5,opt,name=order_direction,json=orderDirection,proto3,enum=backend.proto.customer.v1.ListAddressesRequest_OrderDirection,oneof" json:"order_direction,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ListAddressesRequest) Reset() {
	*x = ListAddressesRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressesRequest) ProtoMessage() {}

func (x *ListAddressesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressesRequest.ProtoReflect.Descriptor instead.
func (*ListAddressesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{32}
}

func (x *ListAddressesRequest) GetParent() int64 {
	if x != nil {
		return x.Parent
	}
	return 0
}

func (x *ListAddressesRequest) GetPageSize() int32 {
	if x != nil && x.PageSize != nil {
		return *x.PageSize
	}
	return 0
}

func (x *ListAddressesRequest) GetPageNum() int32 {
	if x != nil && x.PageNum != nil {
		return *x.PageNum
	}
	return 0
}

func (x *ListAddressesRequest) GetOrderField() ListAddressesRequest_OrderField {
	if x != nil && x.OrderField != nil {
		return *x.OrderField
	}
	return ListAddressesRequest_ORDER_FIELD_UNSPECIFIED
}

func (x *ListAddressesRequest) GetOrderDirection() ListAddressesRequest_OrderDirection {
	if x != nil && x.OrderDirection != nil {
		return *x.OrderDirection
	}
	return ListAddressesRequest_ORDER_DIRECTION_UNSPECIFIED
}

// ListAddresses response
type ListAddressesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址列表
	Addresses []*Address `protobuf:"bytes,1,rep,name=addresses,proto3" json:"addresses,omitempty"`
	// 总数
	Total         int32 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListAddressesResponse) Reset() {
	*x = ListAddressesResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListAddressesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListAddressesResponse) ProtoMessage() {}

func (x *ListAddressesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListAddressesResponse.ProtoReflect.Descriptor instead.
func (*ListAddressesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{33}
}

func (x *ListAddressesResponse) GetAddresses() []*Address {
	if x != nil {
		return x.Addresses
	}
	return nil
}

func (x *ListAddressesResponse) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

// CreateLifeCycleRequest 创建生命周期请求
type CreateLifeCycleRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort          int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateLifeCycleRequest) Reset() {
	*x = CreateLifeCycleRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLifeCycleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLifeCycleRequest) ProtoMessage() {}

func (x *CreateLifeCycleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLifeCycleRequest.ProtoReflect.Descriptor instead.
func (*CreateLifeCycleRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{34}
}

func (x *CreateLifeCycleRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateLifeCycleRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateLifeCycleRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateLifeCycleRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateLifeCycleRequest) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

// CreateLifeCycleResponse 创建生命周期响应
type CreateLifeCycleResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// LifeCycle ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateLifeCycleResponse) Reset() {
	*x = CreateLifeCycleResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateLifeCycleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateLifeCycleResponse) ProtoMessage() {}

func (x *CreateLifeCycleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateLifeCycleResponse.ProtoReflect.Descriptor instead.
func (*CreateLifeCycleResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{35}
}

func (x *CreateLifeCycleResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// UpdateLifeCyclesRequest 批量更新生命周期请求
type UpdateLifeCyclesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 批量更新数据
	Updates []*UpdateLifeCyclesRequest_UpdateLifeCycle `protobuf:"bytes,1,rep,name=updates,proto3" json:"updates,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 公司ID
	CompanyId     int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLifeCyclesRequest) Reset() {
	*x = UpdateLifeCyclesRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLifeCyclesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLifeCyclesRequest) ProtoMessage() {}

func (x *UpdateLifeCyclesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLifeCyclesRequest.ProtoReflect.Descriptor instead.
func (*UpdateLifeCyclesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{36}
}

func (x *UpdateLifeCyclesRequest) GetUpdates() []*UpdateLifeCyclesRequest_UpdateLifeCycle {
	if x != nil {
		return x.Updates
	}
	return nil
}

func (x *UpdateLifeCyclesRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateLifeCyclesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// UpdateLifeCyclesResponse 批量更更新生命周期响应
type UpdateLifeCyclesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLifeCyclesResponse) Reset() {
	*x = UpdateLifeCyclesResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLifeCyclesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLifeCyclesResponse) ProtoMessage() {}

func (x *UpdateLifeCyclesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLifeCyclesResponse.ProtoReflect.Descriptor instead.
func (*UpdateLifeCyclesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{37}
}

// ListLifeCyclesRequest 获取生命周期列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
type ListLifeCyclesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId     int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLifeCyclesRequest) Reset() {
	*x = ListLifeCyclesRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLifeCyclesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLifeCyclesRequest) ProtoMessage() {}

func (x *ListLifeCyclesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLifeCyclesRequest.ProtoReflect.Descriptor instead.
func (*ListLifeCyclesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{38}
}

func (x *ListLifeCyclesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// ListLifeCyclesResponse 获取生命周期列表响应
type ListLifeCyclesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 生命周期列表响应
	LifeCycles    []*CustomizeLifeCycle `protobuf:"bytes,1,rep,name=life_cycles,json=lifeCycles,proto3" json:"life_cycles,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListLifeCyclesResponse) Reset() {
	*x = ListLifeCyclesResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListLifeCyclesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListLifeCyclesResponse) ProtoMessage() {}

func (x *ListLifeCyclesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListLifeCyclesResponse.ProtoReflect.Descriptor instead.
func (*ListLifeCyclesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{39}
}

func (x *ListLifeCyclesResponse) GetLifeCycles() []*CustomizeLifeCycle {
	if x != nil {
		return x.LifeCycles
	}
	return nil
}

// DeleteLifeCycleRequest 删除生命周期请求
type DeleteLifeCycleRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 操作员工ID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteLifeCycleRequest) Reset() {
	*x = DeleteLifeCycleRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteLifeCycleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteLifeCycleRequest) ProtoMessage() {}

func (x *DeleteLifeCycleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteLifeCycleRequest.ProtoReflect.Descriptor instead.
func (*DeleteLifeCycleRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{40}
}

func (x *DeleteLifeCycleRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteLifeCycleRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// CreateActionStateRequest 创建行动状态请求
type CreateActionStateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 门店ID
	BusinessId int64 `protobuf:"varint,2,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 名称
	Name string `protobuf:"bytes,10,opt,name=name,proto3" json:"name,omitempty"`
	// 排序
	Sort int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	// 颜色
	Color         string `protobuf:"bytes,12,opt,name=color,proto3" json:"color,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateActionStateRequest) Reset() {
	*x = CreateActionStateRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateActionStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateActionStateRequest) ProtoMessage() {}

func (x *CreateActionStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateActionStateRequest.ProtoReflect.Descriptor instead.
func (*CreateActionStateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{41}
}

func (x *CreateActionStateRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateActionStateRequest) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *CreateActionStateRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateActionStateRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateActionStateRequest) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *CreateActionStateRequest) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// CreateActionStateResponse 创建行动状态响应
type CreateActionStateResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ActionState ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateActionStateResponse) Reset() {
	*x = CreateActionStateResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateActionStateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateActionStateResponse) ProtoMessage() {}

func (x *CreateActionStateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateActionStateResponse.ProtoReflect.Descriptor instead.
func (*CreateActionStateResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{42}
}

func (x *CreateActionStateResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// UpdateActionStatesRequest 批量更新行动状态请求
type UpdateActionStatesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 批量更新数据
	Updates []*UpdateActionStatesRequest_UpdateActionState `protobuf:"bytes,1,rep,name=updates,proto3" json:"updates,omitempty"`
	// 操作员工ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 公司ID
	CompanyId     int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateActionStatesRequest) Reset() {
	*x = UpdateActionStatesRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateActionStatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateActionStatesRequest) ProtoMessage() {}

func (x *UpdateActionStatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateActionStatesRequest.ProtoReflect.Descriptor instead.
func (*UpdateActionStatesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{43}
}

func (x *UpdateActionStatesRequest) GetUpdates() []*UpdateActionStatesRequest_UpdateActionState {
	if x != nil {
		return x.Updates
	}
	return nil
}

func (x *UpdateActionStatesRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateActionStatesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// UpdateActionStatesResponse 批量更新行动状态响应
type UpdateActionsStatesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateActionsStatesResponse) Reset() {
	*x = UpdateActionsStatesResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateActionsStatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateActionsStatesResponse) ProtoMessage() {}

func (x *UpdateActionsStatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateActionsStatesResponse.ProtoReflect.Descriptor instead.
func (*UpdateActionsStatesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{44}
}

// ListActionStatesRequest 获取行动状态列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
type ListActionStatesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司ID
	CompanyId     int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActionStatesRequest) Reset() {
	*x = ListActionStatesRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActionStatesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActionStatesRequest) ProtoMessage() {}

func (x *ListActionStatesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActionStatesRequest.ProtoReflect.Descriptor instead.
func (*ListActionStatesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{45}
}

func (x *ListActionStatesRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// ListActionStatesResponse 获取行动状态列表响应
type ListActionStatesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 行动状态列表
	ActionStates  []*CustomizeActionState `protobuf:"bytes,1,rep,name=action_states,json=actionStates,proto3" json:"action_states,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListActionStatesResponse) Reset() {
	*x = ListActionStatesResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListActionStatesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListActionStatesResponse) ProtoMessage() {}

func (x *ListActionStatesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListActionStatesResponse.ProtoReflect.Descriptor instead.
func (*ListActionStatesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{46}
}

func (x *ListActionStatesResponse) GetActionStates() []*CustomizeActionState {
	if x != nil {
		return x.ActionStates
	}
	return nil
}

// DeleteActionStateRequest 删除行动状态请求
type DeleteActionStateRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 操作员工ID
	StaffId       int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteActionStateRequest) Reset() {
	*x = DeleteActionStateRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteActionStateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteActionStateRequest) ProtoMessage() {}

func (x *DeleteActionStateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteActionStateRequest.ProtoReflect.Descriptor instead.
func (*DeleteActionStateRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{47}
}

func (x *DeleteActionStateRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteActionStateRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// CreateViewRequest 创建视图请求
type CreateViewRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 视图名称
	Title string `protobuf:"bytes,1,opt,name=title,proto3" json:"title,omitempty"`
	// 显示字段列表
	Fields []string `protobuf:"bytes,2,rep,name=fields,proto3" json:"fields,omitempty"`
	// 排序配置
	OrderBy *CustomerView_OrderBy `protobuf:"bytes,3,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// 筛选条件
	Filter *CustomerView_Filter `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// 员工 ID
	StaffId int64 `protobuf:"varint,5,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 公司 ID
	CompanyId int64 `protobuf:"varint,6,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 类型
	Type          CustomerView_Type `protobuf:"varint,7,opt,name=type,proto3,enum=backend.proto.customer.v1.CustomerView_Type" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateViewRequest) Reset() {
	*x = CreateViewRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateViewRequest) ProtoMessage() {}

func (x *CreateViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateViewRequest.ProtoReflect.Descriptor instead.
func (*CreateViewRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{48}
}

func (x *CreateViewRequest) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

func (x *CreateViewRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *CreateViewRequest) GetOrderBy() *CustomerView_OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *CreateViewRequest) GetFilter() *CustomerView_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *CreateViewRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *CreateViewRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *CreateViewRequest) GetType() CustomerView_Type {
	if x != nil {
		return x.Type
	}
	return CustomerView_TYPE_UNSPECIFIED
}

// CreateViewResponse 创建视图响应
type CreateViewResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// View ID
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateViewResponse) Reset() {
	*x = CreateViewResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateViewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateViewResponse) ProtoMessage() {}

func (x *CreateViewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateViewResponse.ProtoReflect.Descriptor instead.
func (*CreateViewResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{49}
}

func (x *CreateViewResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// UpdateViewRequest 更新视图请求
type UpdateViewRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 视图 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 视图名称
	Title *string `protobuf:"bytes,2,opt,name=title,proto3,oneof" json:"title,omitempty"`
	// 显示字段列表
	Fields []string `protobuf:"bytes,3,rep,name=fields,proto3" json:"fields,omitempty"`
	// 排序配置
	OrderBy *CustomerView_OrderBy `protobuf:"bytes,4,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
	// 筛选条件
	Filter *CustomerView_Filter `protobuf:"bytes,5,opt,name=filter,proto3,oneof" json:"filter,omitempty"`
	// 员工 ID
	StaffId int64 `protobuf:"varint,6,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 公司 ID
	CompanyId     int64 `protobuf:"varint,7,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateViewRequest) Reset() {
	*x = UpdateViewRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateViewRequest) ProtoMessage() {}

func (x *UpdateViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateViewRequest.ProtoReflect.Descriptor instead.
func (*UpdateViewRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{50}
}

func (x *UpdateViewRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateViewRequest) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *UpdateViewRequest) GetFields() []string {
	if x != nil {
		return x.Fields
	}
	return nil
}

func (x *UpdateViewRequest) GetOrderBy() *CustomerView_OrderBy {
	if x != nil {
		return x.OrderBy
	}
	return nil
}

func (x *UpdateViewRequest) GetFilter() *CustomerView_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *UpdateViewRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *UpdateViewRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

// UpdateViewResponse 更新视图响应
type UpdateViewResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateViewResponse) Reset() {
	*x = UpdateViewResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateViewResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateViewResponse) ProtoMessage() {}

func (x *UpdateViewResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateViewResponse.ProtoReflect.Descriptor instead.
func (*UpdateViewResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{51}
}

// ListViewsRequest 获取视图列表请求
// (-- api-linter: core::0158::request-page-size-field=disabled
//
//	aip.dev/not-precedent: 没有分页的需求, 所以没有必要设置page_size --)
type ListViewsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 公司 ID
	CompanyId int64 `protobuf:"varint,1,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 员工 ID
	StaffId int64 `protobuf:"varint,2,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	// 类型
	Type          *CustomerView_Type `protobuf:"varint,3,opt,name=type,proto3,enum=backend.proto.customer.v1.CustomerView_Type,oneof" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListViewsRequest) Reset() {
	*x = ListViewsRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListViewsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListViewsRequest) ProtoMessage() {}

func (x *ListViewsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListViewsRequest.ProtoReflect.Descriptor instead.
func (*ListViewsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{52}
}

func (x *ListViewsRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *ListViewsRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

func (x *ListViewsRequest) GetType() CustomerView_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return CustomerView_TYPE_UNSPECIFIED
}

// ListViewsResponse 获取视图列表响应
type ListViewsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 视图列表
	Views         []*CustomerView `protobuf:"bytes,1,rep,name=views,proto3" json:"views,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListViewsResponse) Reset() {
	*x = ListViewsResponse{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListViewsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListViewsResponse) ProtoMessage() {}

func (x *ListViewsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListViewsResponse.ProtoReflect.Descriptor instead.
func (*ListViewsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{53}
}

func (x *ListViewsResponse) GetViews() []*CustomerView {
	if x != nil {
		return x.Views
	}
	return nil
}

// DeleteViewRequest 删除视图请求
type DeleteViewRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 视图 ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 公司 ID
	CompanyId int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3" json:"company_id,omitempty"`
	// 员工 ID
	StaffId       int64 `protobuf:"varint,3,opt,name=staff_id,json=staffId,proto3" json:"staff_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteViewRequest) Reset() {
	*x = DeleteViewRequest{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteViewRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteViewRequest) ProtoMessage() {}

func (x *DeleteViewRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteViewRequest.ProtoReflect.Descriptor instead.
func (*DeleteViewRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{54}
}

func (x *DeleteViewRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DeleteViewRequest) GetCompanyId() int64 {
	if x != nil {
		return x.CompanyId
	}
	return 0
}

func (x *DeleteViewRequest) GetStaffId() int64 {
	if x != nil {
		return x.StaffId
	}
	return 0
}

// update customer request
type UpdateCustomerRequest_UpdateCustomer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 生命周期状态
	LifeCycle *Customer_LifeCycle `protobuf:"varint,2,opt,name=life_cycle,json=lifeCycle,proto3,enum=backend.proto.customer.v1.Customer_LifeCycle,oneof" json:"life_cycle,omitempty"`
	// 行动状态
	ActionState *Customer_ActionState `protobuf:"varint,3,opt,name=action_state,json=actionState,proto3,enum=backend.proto.customer.v1.Customer_ActionState,oneof" json:"action_state,omitempty"`
	// 客户来源
	Source *string `protobuf:"bytes,4,opt,name=source,proto3,oneof" json:"source,omitempty"`
	// Preferred business
	PreferredBusinessId *int64 `protobuf:"varint,5,opt,name=preferred_business_id,json=preferredBusinessId,proto3,oneof" json:"preferred_business_id,omitempty"`
	// 头像路径
	AvatarPath *string `protobuf:"bytes,12,opt,name=avatar_path,json=avatarPath,proto3,oneof" json:"avatar_path,omitempty"`
	// 名
	GivenName *string `protobuf:"bytes,13,opt,name=given_name,json=givenName,proto3,oneof" json:"given_name,omitempty"`
	// 姓
	FamilyName *string `protobuf:"bytes,14,opt,name=family_name,json=familyName,proto3,oneof" json:"family_name,omitempty"`
	// 邮箱
	Email *string `protobuf:"bytes,15,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// 电话
	PhoneNumber *string `protobuf:"bytes,16,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
	// 生日
	BirthTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=birth_time,json=birthTime,proto3,oneof" json:"birth_time,omitempty"`
	// update address
	Address *UpdateCustomerRequest_UpdateAddress `protobuf:"bytes,18,opt,name=address,proto3" json:"address,omitempty"`
	// update contact
	Contact *UpdateCustomerRequest_UpdateContact `protobuf:"bytes,19,opt,name=contact,proto3" json:"contact,omitempty"`
	// 关联的员工ID
	AllocateStaffId *int64 `protobuf:"varint,20,opt,name=allocate_staff_id,json=allocateStaffId,proto3,oneof" json:"allocate_staff_id,omitempty"`
	// 附加信息
	AdditionalInfo *Customer_AdditionalInfo `protobuf:"bytes,21,opt,name=additional_info,json=additionalInfo,proto3,oneof" json:"additional_info,omitempty"`
	// 关联自定义生命周期ID
	CustomizeLifeCycleId *int64 `protobuf:"varint,23,opt,name=customize_life_cycle_id,json=customizeLifeCycleId,proto3,oneof" json:"customize_life_cycle_id,omitempty"`
	// 关联自定义行动状态ID
	CustomizeActionStateId *int64 `protobuf:"varint,24,opt,name=customize_action_state_id,json=customizeActionStateId,proto3,oneof" json:"customize_action_state_id,omitempty"`
	// 用户头像颜色
	ClientColor *string `protobuf:"bytes,25,opt,name=client_color,json=clientColor,proto3,oneof" json:"client_color,omitempty"`
	// 自定义字段
	CustomFields  *structpb.Struct `protobuf:"bytes,26,opt,name=custom_fields,json=customFields,proto3,oneof" json:"custom_fields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerRequest_UpdateCustomer) Reset() {
	*x = UpdateCustomerRequest_UpdateCustomer{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRequest_UpdateCustomer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRequest_UpdateCustomer) ProtoMessage() {}

func (x *UpdateCustomerRequest_UpdateCustomer) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRequest_UpdateCustomer.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRequest_UpdateCustomer) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetLifeCycle() Customer_LifeCycle {
	if x != nil && x.LifeCycle != nil {
		return *x.LifeCycle
	}
	return Customer_LIFE_CYCLE_UNSPECIFIED
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetActionState() Customer_ActionState {
	if x != nil && x.ActionState != nil {
		return *x.ActionState
	}
	return Customer_ACTION_STATE_UNSPECIFIED
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetSource() string {
	if x != nil && x.Source != nil {
		return *x.Source
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetPreferredBusinessId() int64 {
	if x != nil && x.PreferredBusinessId != nil {
		return *x.PreferredBusinessId
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetAvatarPath() string {
	if x != nil && x.AvatarPath != nil {
		return *x.AvatarPath
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetGivenName() string {
	if x != nil && x.GivenName != nil {
		return *x.GivenName
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetFamilyName() string {
	if x != nil && x.FamilyName != nil {
		return *x.FamilyName
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetBirthTime() *timestamppb.Timestamp {
	if x != nil {
		return x.BirthTime
	}
	return nil
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetAddress() *UpdateCustomerRequest_UpdateAddress {
	if x != nil {
		return x.Address
	}
	return nil
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetContact() *UpdateCustomerRequest_UpdateContact {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetAllocateStaffId() int64 {
	if x != nil && x.AllocateStaffId != nil {
		return *x.AllocateStaffId
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetAdditionalInfo() *Customer_AdditionalInfo {
	if x != nil {
		return x.AdditionalInfo
	}
	return nil
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetCustomizeLifeCycleId() int64 {
	if x != nil && x.CustomizeLifeCycleId != nil {
		return *x.CustomizeLifeCycleId
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetCustomizeActionStateId() int64 {
	if x != nil && x.CustomizeActionStateId != nil {
		return *x.CustomizeActionStateId
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetClientColor() string {
	if x != nil && x.ClientColor != nil {
		return *x.ClientColor
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateCustomer) GetCustomFields() *structpb.Struct {
	if x != nil {
		return x.CustomFields
	}
	return nil
}

// update address
type UpdateCustomerRequest_UpdateAddress struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 地址1
	Address1 *string `protobuf:"bytes,2,opt,name=address1,proto3,oneof" json:"address1,omitempty"`
	// 地址2
	Address2 *string `protobuf:"bytes,3,opt,name=address2,proto3,oneof" json:"address2,omitempty"`
	// 城市
	City *string `protobuf:"bytes,4,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// 州/省
	State *string `protobuf:"bytes,5,opt,name=state,proto3,oneof" json:"state,omitempty"`
	// 区域代码 (替代 country)
	RegionCode *string `protobuf:"bytes,6,opt,name=region_code,json=regionCode,proto3,oneof" json:"region_code,omitempty"`
	// 邮编
	Zipcode *string `protobuf:"bytes,7,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// 纬度
	Lat *string `protobuf:"bytes,8,opt,name=lat,proto3,oneof" json:"lat,omitempty"`
	// 经度
	Lng *string `protobuf:"bytes,9,opt,name=lng,proto3,oneof" json:"lng,omitempty"`
	// 是否为主地址
	// 注意, 这里的枚举和数据库不一致
	// 数据库中未重构前是0和1, 目前用枚举表示, service 会转换枚举和db中的数据
	// 上游无需关心转换, 直接使用枚举即可
	IsPrimary     *Address_IsPrimary `protobuf:"varint,10,opt,name=is_primary,json=isPrimary,proto3,enum=backend.proto.customer.v1.Address_IsPrimary,oneof" json:"is_primary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateCustomerRequest_UpdateAddress) Reset() {
	*x = UpdateCustomerRequest_UpdateAddress{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRequest_UpdateAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRequest_UpdateAddress) ProtoMessage() {}

func (x *UpdateCustomerRequest_UpdateAddress) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRequest_UpdateAddress.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRequest_UpdateAddress) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{2, 1}
}

func (x *UpdateCustomerRequest_UpdateAddress) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateAddress) GetAddress1() string {
	if x != nil && x.Address1 != nil {
		return *x.Address1
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateAddress) GetAddress2() string {
	if x != nil && x.Address2 != nil {
		return *x.Address2
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateAddress) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateAddress) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateAddress) GetRegionCode() string {
	if x != nil && x.RegionCode != nil {
		return *x.RegionCode
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateAddress) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateAddress) GetLat() string {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateAddress) GetLng() string {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateAddress) GetIsPrimary() Address_IsPrimary {
	if x != nil && x.IsPrimary != nil {
		return *x.IsPrimary
	}
	return Address_IS_PRIMARY_UNSPECIFIED
}

// contact 表示联系信息
type UpdateCustomerRequest_UpdateContact struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 联系人ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名
	GivenName *string `protobuf:"bytes,2,opt,name=given_name,json=givenName,proto3,oneof" json:"given_name,omitempty"`
	// 姓
	FamilyName *string `protobuf:"bytes,3,opt,name=family_name,json=familyName,proto3,oneof" json:"family_name,omitempty"`
	// 电话号码
	PhoneNumber *string `protobuf:"bytes,4,opt,name=phone_number,json=phoneNumber,proto3,oneof" json:"phone_number,omitempty"`
	// 邮箱
	Email *string `protobuf:"bytes,5,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// 职位
	Title *string `protobuf:"bytes,6,opt,name=title,proto3,oneof" json:"title,omitempty"`
	// is_primary 表示是否为主联系人
	// 注意, 这里的枚举和数据库不一致
	// 数据库中未重构前是0和1, 目前用枚举表示, service 需要转换枚举和db中的数据
	// 上游无需关心转换, 直接使用枚举即可
	IsPrimary *CustomerContact_IsPrimary `protobuf:"varint,7,opt,name=is_primary,json=isPrimary,proto3,enum=backend.proto.customer.v1.CustomerContact_IsPrimary,oneof" json:"is_primary,omitempty"`
	// E164格式电话号码
	E164PhoneNumber *string `protobuf:"bytes,8,opt,name=e164_phone_number,json=e164PhoneNumber,proto3,oneof" json:"e164_phone_number,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdateCustomerRequest_UpdateContact) Reset() {
	*x = UpdateCustomerRequest_UpdateContact{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateCustomerRequest_UpdateContact) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateCustomerRequest_UpdateContact) ProtoMessage() {}

func (x *UpdateCustomerRequest_UpdateContact) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateCustomerRequest_UpdateContact.ProtoReflect.Descriptor instead.
func (*UpdateCustomerRequest_UpdateContact) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{2, 2}
}

func (x *UpdateCustomerRequest_UpdateContact) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateCustomerRequest_UpdateContact) GetGivenName() string {
	if x != nil && x.GivenName != nil {
		return *x.GivenName
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateContact) GetFamilyName() string {
	if x != nil && x.FamilyName != nil {
		return *x.FamilyName
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateContact) GetPhoneNumber() string {
	if x != nil && x.PhoneNumber != nil {
		return *x.PhoneNumber
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateContact) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateContact) GetTitle() string {
	if x != nil && x.Title != nil {
		return *x.Title
	}
	return ""
}

func (x *UpdateCustomerRequest_UpdateContact) GetIsPrimary() CustomerContact_IsPrimary {
	if x != nil && x.IsPrimary != nil {
		return *x.IsPrimary
	}
	return CustomerContact_IS_PRIMARY_UNSPECIFIED
}

func (x *UpdateCustomerRequest_UpdateContact) GetE164PhoneNumber() string {
	if x != nil && x.E164PhoneNumber != nil {
		return *x.E164PhoneNumber
	}
	return ""
}

// Filter 过滤器
type ListCustomersRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 行动状态过滤
	ActionState *Customer_ActionState `protobuf:"varint,1,opt,name=action_state,json=actionState,proto3,enum=backend.proto.customer.v1.Customer_ActionState,oneof" json:"action_state,omitempty"`
	// type
	Type *Customer_Type `protobuf:"varint,2,opt,name=type,proto3,enum=backend.proto.customer.v1.Customer_Type,oneof" json:"type,omitempty"`
	// customer ids
	CustomerIds []int64 `protobuf:"varint,3,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	// keyword
	Keyword *string `protobuf:"bytes,4,opt,name=keyword,proto3,oneof" json:"keyword,omitempty"`
	// 生命周期过滤
	LifeCycle *Customer_LifeCycle `protobuf:"varint,5,opt,name=life_cycle,json=lifeCycle,proto3,enum=backend.proto.customer.v1.Customer_LifeCycle,oneof" json:"life_cycle,omitempty"`
	// 关联自定义生命周期ID
	CustomizeLifeCycleId *int64 `protobuf:"varint,6,opt,name=customize_life_cycle_id,json=customizeLifeCycleId,proto3,oneof" json:"customize_life_cycle_id,omitempty"`
	// 关联自定义行动状态ID
	CustomizeActionStateId *int64 `protobuf:"varint,7,opt,name=customize_action_state_id,json=customizeActionStateId,proto3,oneof" json:"customize_action_state_id,omitempty"`
	// 主电话号码筛选
	MainPhoneNumber *string `protobuf:"bytes,8,opt,name=main_phone_number,json=mainPhoneNumber,proto3,oneof" json:"main_phone_number,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListCustomersRequest_Filter) Reset() {
	*x = ListCustomersRequest_Filter{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomersRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomersRequest_Filter) ProtoMessage() {}

func (x *ListCustomersRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomersRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomersRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{4, 0}
}

func (x *ListCustomersRequest_Filter) GetActionState() Customer_ActionState {
	if x != nil && x.ActionState != nil {
		return *x.ActionState
	}
	return Customer_ACTION_STATE_UNSPECIFIED
}

func (x *ListCustomersRequest_Filter) GetType() Customer_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return Customer_TYPE_UNSPECIFIED
}

func (x *ListCustomersRequest_Filter) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

func (x *ListCustomersRequest_Filter) GetKeyword() string {
	if x != nil && x.Keyword != nil {
		return *x.Keyword
	}
	return ""
}

func (x *ListCustomersRequest_Filter) GetLifeCycle() Customer_LifeCycle {
	if x != nil && x.LifeCycle != nil {
		return *x.LifeCycle
	}
	return Customer_LIFE_CYCLE_UNSPECIFIED
}

func (x *ListCustomersRequest_Filter) GetCustomizeLifeCycleId() int64 {
	if x != nil && x.CustomizeLifeCycleId != nil {
		return *x.CustomizeLifeCycleId
	}
	return 0
}

func (x *ListCustomersRequest_Filter) GetCustomizeActionStateId() int64 {
	if x != nil && x.CustomizeActionStateId != nil {
		return *x.CustomizeActionStateId
	}
	return 0
}

func (x *ListCustomersRequest_Filter) GetMainPhoneNumber() string {
	if x != nil && x.MainPhoneNumber != nil {
		return *x.MainPhoneNumber
	}
	return ""
}

// Filter 过滤器
type ListCustomerHistoryLogsRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 记录类型过滤
	Type *HistoryLog_Type `protobuf:"varint,1,opt,name=type,proto3,enum=backend.proto.customer.v1.HistoryLog_Type,oneof" json:"type,omitempty"`
	// 公司ID 获取公司维度数据时customer_id参数可以不传
	CompanyId     *int64 `protobuf:"varint,2,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListCustomerHistoryLogsRequest_Filter) Reset() {
	*x = ListCustomerHistoryLogsRequest_Filter{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListCustomerHistoryLogsRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListCustomerHistoryLogsRequest_Filter) ProtoMessage() {}

func (x *ListCustomerHistoryLogsRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListCustomerHistoryLogsRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListCustomerHistoryLogsRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{18, 0}
}

func (x *ListCustomerHistoryLogsRequest_Filter) GetType() HistoryLog_Type {
	if x != nil && x.Type != nil {
		return *x.Type
	}
	return HistoryLog_TYPE_UNSPECIFIED
}

func (x *ListCustomerHistoryLogsRequest_Filter) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

// update address message
type UpdateAddressRequest_UpdateAddress struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 地址ID (要更新哪个地址)
	AddressId int64 `protobuf:"varint,1,opt,name=address_id,json=addressId,proto3" json:"address_id,omitempty"`
	// 地址1
	Address1 *string `protobuf:"bytes,2,opt,name=address1,proto3,oneof" json:"address1,omitempty"`
	// 地址2
	Address2 *string `protobuf:"bytes,3,opt,name=address2,proto3,oneof" json:"address2,omitempty"`
	// 城市
	City *string `protobuf:"bytes,4,opt,name=city,proto3,oneof" json:"city,omitempty"`
	// 州/省
	State *string `protobuf:"bytes,5,opt,name=state,proto3,oneof" json:"state,omitempty"`
	// 区域代码 (替代 country)
	RegionCode *string `protobuf:"bytes,6,opt,name=region_code,json=regionCode,proto3,oneof" json:"region_code,omitempty"`
	// 邮编
	Zipcode *string `protobuf:"bytes,7,opt,name=zipcode,proto3,oneof" json:"zipcode,omitempty"`
	// 纬度
	Lat *string `protobuf:"bytes,8,opt,name=lat,proto3,oneof" json:"lat,omitempty"`
	// 经度
	Lng *string `protobuf:"bytes,9,opt,name=lng,proto3,oneof" json:"lng,omitempty"`
	// 是否为主地址
	// 注意, 这里的枚举和数据库不一致
	// 数据库中未重构前是0和1, 目前用枚举表示, service 需要转换枚举和db中的数据
	// 上游无需关心转换, 直接使用枚举即可
	IsPrimary     *Address_IsPrimary `protobuf:"varint,10,opt,name=is_primary,json=isPrimary,proto3,enum=backend.proto.customer.v1.Address_IsPrimary,oneof" json:"is_primary,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateAddressRequest_UpdateAddress) Reset() {
	*x = UpdateAddressRequest_UpdateAddress{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateAddressRequest_UpdateAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateAddressRequest_UpdateAddress) ProtoMessage() {}

func (x *UpdateAddressRequest_UpdateAddress) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateAddressRequest_UpdateAddress.ProtoReflect.Descriptor instead.
func (*UpdateAddressRequest_UpdateAddress) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{29, 0}
}

func (x *UpdateAddressRequest_UpdateAddress) GetAddressId() int64 {
	if x != nil {
		return x.AddressId
	}
	return 0
}

func (x *UpdateAddressRequest_UpdateAddress) GetAddress1() string {
	if x != nil && x.Address1 != nil {
		return *x.Address1
	}
	return ""
}

func (x *UpdateAddressRequest_UpdateAddress) GetAddress2() string {
	if x != nil && x.Address2 != nil {
		return *x.Address2
	}
	return ""
}

func (x *UpdateAddressRequest_UpdateAddress) GetCity() string {
	if x != nil && x.City != nil {
		return *x.City
	}
	return ""
}

func (x *UpdateAddressRequest_UpdateAddress) GetState() string {
	if x != nil && x.State != nil {
		return *x.State
	}
	return ""
}

func (x *UpdateAddressRequest_UpdateAddress) GetRegionCode() string {
	if x != nil && x.RegionCode != nil {
		return *x.RegionCode
	}
	return ""
}

func (x *UpdateAddressRequest_UpdateAddress) GetZipcode() string {
	if x != nil && x.Zipcode != nil {
		return *x.Zipcode
	}
	return ""
}

func (x *UpdateAddressRequest_UpdateAddress) GetLat() string {
	if x != nil && x.Lat != nil {
		return *x.Lat
	}
	return ""
}

func (x *UpdateAddressRequest_UpdateAddress) GetLng() string {
	if x != nil && x.Lng != nil {
		return *x.Lng
	}
	return ""
}

func (x *UpdateAddressRequest_UpdateAddress) GetIsPrimary() Address_IsPrimary {
	if x != nil && x.IsPrimary != nil {
		return *x.IsPrimary
	}
	return Address_IS_PRIMARY_UNSPECIFIED
}

// 批量更新结构
type UpdateLifeCyclesRequest_UpdateLifeCycle struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 排序
	Sort          *int32 `protobuf:"varint,3,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) Reset() {
	*x = UpdateLifeCyclesRequest_UpdateLifeCycle{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateLifeCyclesRequest_UpdateLifeCycle) ProtoMessage() {}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateLifeCyclesRequest_UpdateLifeCycle.ProtoReflect.Descriptor instead.
func (*UpdateLifeCyclesRequest_UpdateLifeCycle) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{36, 0}
}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateLifeCyclesRequest_UpdateLifeCycle) GetSort() int32 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

// 批量更新结构
type UpdateActionStatesRequest_UpdateActionState struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 名称
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 排序
	Sort *int32 `protobuf:"varint,3,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// 颜色
	Color         *string `protobuf:"bytes,4,opt,name=color,proto3,oneof" json:"color,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateActionStatesRequest_UpdateActionState) Reset() {
	*x = UpdateActionStatesRequest_UpdateActionState{}
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateActionStatesRequest_UpdateActionState) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateActionStatesRequest_UpdateActionState) ProtoMessage() {}

func (x *UpdateActionStatesRequest_UpdateActionState) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_customer_v1_customer_service_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateActionStatesRequest_UpdateActionState.ProtoReflect.Descriptor instead.
func (*UpdateActionStatesRequest_UpdateActionState) Descriptor() ([]byte, []int) {
	return file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP(), []int{43, 0}
}

func (x *UpdateActionStatesRequest_UpdateActionState) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateActionStatesRequest_UpdateActionState) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateActionStatesRequest_UpdateActionState) GetSort() int32 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *UpdateActionStatesRequest_UpdateActionState) GetColor() string {
	if x != nil && x.Color != nil {
		return *x.Color
	}
	return ""
}

var File_backend_proto_customer_v1_customer_service_proto protoreflect.FileDescriptor

const file_backend_proto_customer_v1_customer_service_proto_rawDesc = "" +
	"\n" +
	"0backend/proto/customer/v1/customer_service.proto\x12\x19backend.proto.customer.v1\x1a(backend/proto/customer/v1/customer.proto\x1a4backend/proto/customer/v1/customer_view_models.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\"\xb8\x01\n" +
	"\x15CreateCustomerRequest\x12?\n" +
	"\bcustomer\x18\x01 \x01(\v2#.backend.proto.customer.v1.CustomerR\bcustomer\x12\x1e\n" +
	"\bstaff_id\x18\x02 \x01(\x03H\x00R\astaffId\x88\x01\x01\x12\"\n" +
	"\n" +
	"staff_name\x18\x03 \x01(\tH\x01R\tstaffName\x88\x01\x01B\v\n" +
	"\t_staff_idB\r\n" +
	"\v_staff_name\"t\n" +
	"\x16CreateCustomerResponse\x12?\n" +
	"\bcustomer\x18\x01 \x01(\v2#.backend.proto.customer.v1.CustomerR\bcustomer\x12\x19\n" +
	"\bis_exist\x18\x02 \x01(\bR\aisExist\"\xec\x12\n" +
	"\x15UpdateCustomerRequest\x12[\n" +
	"\bcustomer\x18\x01 \x01(\v2?.backend.proto.customer.v1.UpdateCustomerRequest.UpdateCustomerR\bcustomer\x1a\xf2\n" +
	"\n" +
	"\x0eUpdateCustomer\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12V\n" +
	"\n" +
	"life_cycle\x18\x02 \x01(\x0e2-.backend.proto.customer.v1.Customer.LifeCycleB\x03\xe0A\x03H\x00R\tlifeCycle\x88\x01\x01\x12\\\n" +
	"\faction_state\x18\x03 \x01(\x0e2/.backend.proto.customer.v1.Customer.ActionStateB\x03\xe0A\x03H\x01R\vactionState\x88\x01\x01\x12\x1b\n" +
	"\x06source\x18\x04 \x01(\tH\x02R\x06source\x88\x01\x01\x127\n" +
	"\x15preferred_business_id\x18\x05 \x01(\x03H\x03R\x13preferredBusinessId\x88\x01\x01\x12$\n" +
	"\vavatar_path\x18\f \x01(\tH\x04R\n" +
	"avatarPath\x88\x01\x01\x12\"\n" +
	"\n" +
	"given_name\x18\r \x01(\tH\x05R\tgivenName\x88\x01\x01\x12$\n" +
	"\vfamily_name\x18\x0e \x01(\tH\x06R\n" +
	"familyName\x88\x01\x01\x12\x19\n" +
	"\x05email\x18\x0f \x01(\tH\aR\x05email\x88\x01\x01\x12&\n" +
	"\fphone_number\x18\x10 \x01(\tH\bR\vphoneNumber\x88\x01\x01\x12>\n" +
	"\n" +
	"birth_time\x18\x11 \x01(\v2\x1a.google.protobuf.TimestampH\tR\tbirthTime\x88\x01\x01\x12X\n" +
	"\aaddress\x18\x12 \x01(\v2>.backend.proto.customer.v1.UpdateCustomerRequest.UpdateAddressR\aaddress\x12X\n" +
	"\acontact\x18\x13 \x01(\v2>.backend.proto.customer.v1.UpdateCustomerRequest.UpdateContactR\acontact\x12/\n" +
	"\x11allocate_staff_id\x18\x14 \x01(\x03H\n" +
	"R\x0fallocateStaffId\x88\x01\x01\x12`\n" +
	"\x0fadditional_info\x18\x15 \x01(\v22.backend.proto.customer.v1.Customer.AdditionalInfoH\vR\x0eadditionalInfo\x88\x01\x01\x12:\n" +
	"\x17customize_life_cycle_id\x18\x17 \x01(\x03H\fR\x14customizeLifeCycleId\x88\x01\x01\x12>\n" +
	"\x19customize_action_state_id\x18\x18 \x01(\x03H\rR\x16customizeActionStateId\x88\x01\x01\x12&\n" +
	"\fclient_color\x18\x19 \x01(\tH\x0eR\vclientColor\x88\x01\x01\x12A\n" +
	"\rcustom_fields\x18\x1a \x01(\v2\x17.google.protobuf.StructH\x0fR\fcustomFields\x88\x01\x01B\r\n" +
	"\v_life_cycleB\x0f\n" +
	"\r_action_stateB\t\n" +
	"\a_sourceB\x18\n" +
	"\x16_preferred_business_idB\x0e\n" +
	"\f_avatar_pathB\r\n" +
	"\v_given_nameB\x0e\n" +
	"\f_family_nameB\b\n" +
	"\x06_emailB\x0f\n" +
	"\r_phone_numberB\r\n" +
	"\v_birth_timeB\x14\n" +
	"\x12_allocate_staff_idB\x12\n" +
	"\x10_additional_infoB\x1a\n" +
	"\x18_customize_life_cycle_idB\x1c\n" +
	"\x1a_customize_action_state_idB\x0f\n" +
	"\r_client_colorB\x10\n" +
	"\x0e_custom_fields\x1a\xc2\x03\n" +
	"\rUpdateAddress\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\baddress1\x18\x02 \x01(\tH\x00R\baddress1\x88\x01\x01\x12\x1f\n" +
	"\baddress2\x18\x03 \x01(\tH\x01R\baddress2\x88\x01\x01\x12\x17\n" +
	"\x04city\x18\x04 \x01(\tH\x02R\x04city\x88\x01\x01\x12\x19\n" +
	"\x05state\x18\x05 \x01(\tH\x03R\x05state\x88\x01\x01\x12$\n" +
	"\vregion_code\x18\x06 \x01(\tH\x04R\n" +
	"regionCode\x88\x01\x01\x12\x1d\n" +
	"\azipcode\x18\a \x01(\tH\x05R\azipcode\x88\x01\x01\x12\x15\n" +
	"\x03lat\x18\b \x01(\tH\x06R\x03lat\x88\x01\x01\x12\x15\n" +
	"\x03lng\x18\t \x01(\tH\aR\x03lng\x88\x01\x01\x12P\n" +
	"\n" +
	"is_primary\x18\n" +
	" \x01(\x0e2,.backend.proto.customer.v1.Address.IsPrimaryH\bR\tisPrimary\x88\x01\x01B\v\n" +
	"\t_address1B\v\n" +
	"\t_address2B\a\n" +
	"\x05_cityB\b\n" +
	"\x06_stateB\x0e\n" +
	"\f_region_codeB\n" +
	"\n" +
	"\b_zipcodeB\x06\n" +
	"\x04_latB\x06\n" +
	"\x04_lngB\r\n" +
	"\v_is_primary\x1a\xbb\x03\n" +
	"\rUpdateContact\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\"\n" +
	"\n" +
	"given_name\x18\x02 \x01(\tH\x00R\tgivenName\x88\x01\x01\x12$\n" +
	"\vfamily_name\x18\x03 \x01(\tH\x01R\n" +
	"familyName\x88\x01\x01\x12&\n" +
	"\fphone_number\x18\x04 \x01(\tH\x02R\vphoneNumber\x88\x01\x01\x12\x19\n" +
	"\x05email\x18\x05 \x01(\tH\x03R\x05email\x88\x01\x01\x12\x19\n" +
	"\x05title\x18\x06 \x01(\tH\x04R\x05title\x88\x01\x01\x12X\n" +
	"\n" +
	"is_primary\x18\a \x01(\x0e24.backend.proto.customer.v1.CustomerContact.IsPrimaryH\x05R\tisPrimary\x88\x01\x01\x12/\n" +
	"\x11e164_phone_number\x18\b \x01(\tH\x06R\x0fe164PhoneNumber\x88\x01\x01B\r\n" +
	"\v_given_nameB\x0e\n" +
	"\f_family_nameB\x0f\n" +
	"\r_phone_numberB\b\n" +
	"\x06_emailB\b\n" +
	"\x06_titleB\r\n" +
	"\v_is_primaryB\x14\n" +
	"\x12_e164_phone_number\"\x18\n" +
	"\x16UpdateCustomerResponse\"\x92\n" +
	"\n" +
	"\x14ListCustomersRequest\x12S\n" +
	"\x06filter\x18\x01 \x01(\v26.backend.proto.customer.v1.ListCustomersRequest.FilterH\x00R\x06filter\x88\x01\x01\x12&\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\tcompanyId\x12,\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00H\x01R\bpageSize\x88\x01\x01\x12'\n" +
	"\bpage_num\x18\x04 \x01(\x05B\a\xbaH\x04\x1a\x02(\x01H\x02R\apageNum\x88\x01\x01\x12`\n" +
	"\vorder_field\x18\x05 \x01(\x0e2:.backend.proto.customer.v1.ListCustomersRequest.OrderFieldH\x03R\n" +
	"orderField\x88\x01\x01\x12l\n" +
	"\x0forder_direction\x18\x06 \x01(\x0e2>.backend.proto.customer.v1.ListCustomersRequest.OrderDirectionH\x04R\x0eorderDirection\x88\x01\x01\x1a\xf0\x04\n" +
	"\x06Filter\x12\\\n" +
	"\faction_state\x18\x01 \x01(\x0e2/.backend.proto.customer.v1.Customer.ActionStateB\x03\xe0A\x03H\x00R\vactionState\x88\x01\x01\x12A\n" +
	"\x04type\x18\x02 \x01(\x0e2(.backend.proto.customer.v1.Customer.TypeH\x01R\x04type\x88\x01\x01\x12!\n" +
	"\fcustomer_ids\x18\x03 \x03(\x03R\vcustomerIds\x12\x1d\n" +
	"\akeyword\x18\x04 \x01(\tH\x02R\akeyword\x88\x01\x01\x12Q\n" +
	"\n" +
	"life_cycle\x18\x05 \x01(\x0e2-.backend.proto.customer.v1.Customer.LifeCycleH\x03R\tlifeCycle\x88\x01\x01\x12:\n" +
	"\x17customize_life_cycle_id\x18\x06 \x01(\x03H\x04R\x14customizeLifeCycleId\x88\x01\x01\x12>\n" +
	"\x19customize_action_state_id\x18\a \x01(\x03H\x05R\x16customizeActionStateId\x88\x01\x01\x12/\n" +
	"\x11main_phone_number\x18\b \x01(\tH\x06R\x0fmainPhoneNumber\x88\x01\x01B\x0f\n" +
	"\r_action_stateB\a\n" +
	"\x05_typeB\n" +
	"\n" +
	"\b_keywordB\r\n" +
	"\v_life_cycleB\x1a\n" +
	"\x18_customize_life_cycle_idB\x1c\n" +
	"\x1a_customize_action_state_idB\x14\n" +
	"\x12_main_phone_number\"S\n" +
	"\n" +
	"OrderField\x12\x1b\n" +
	"\x17ORDER_FIELD_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vCREATE_TIME\x10\x01\x12\x0f\n" +
	"\vUPDATE_TIME\x10\x02\x12\x06\n" +
	"\x02ID\x10\x03\"D\n" +
	"\x0eOrderDirection\x12\x1f\n" +
	"\x1bORDER_DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02B\t\n" +
	"\a_filterB\f\n" +
	"\n" +
	"_page_sizeB\v\n" +
	"\t_page_numB\x0e\n" +
	"\f_order_fieldB\x12\n" +
	"\x10_order_direction\"p\n" +
	"\x15ListCustomersResponse\x12A\n" +
	"\tcustomers\x18\x01 \x03(\v2#.backend.proto.customer.v1.CustomerR\tcustomers\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"5\n" +
	"\x12GetCustomerRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\"8\n" +
	"\x15DeleteCustomerRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\"<\n" +
	"\x19SyncCustomerSearchRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\"\x1c\n" +
	"\x1aSyncCustomerSearchResponse\"9\n" +
	"\x16ConvertCustomerRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\"\x19\n" +
	"\x17ConvertCustomerResponse\"\xfb\x01\n" +
	" ConvertCustomersAttributeRequest\x12!\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03R\vcustomerIds\x12:\n" +
	"\x17customize_life_cycle_id\x18\x02 \x01(\x03H\x00R\x14customizeLifeCycleId\x88\x01\x01\x12>\n" +
	"\x19customize_action_state_id\x18\x03 \x01(\x03H\x01R\x16customizeActionStateId\x88\x01\x01B\x1a\n" +
	"\x18_customize_life_cycle_idB\x1c\n" +
	"\x1a_customize_action_state_id\"#\n" +
	"!ConvertCustomersAttributeResponse\"\xf8\x03\n" +
	"\x1fCreateCustomerHistoryLogRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\x12#\n" +
	"\rcustomer_name\x18\t \x01(\tR\fcustomerName\x122\n" +
	"\x15customer_phone_number\x18\n" +
	" \x01(\tR\x13customerPhoneNumber\x12D\n" +
	"\x06action\x18\x02 \x01(\v2,.backend.proto.customer.v1.HistoryLog.ActionR\x06action\x12\x1d\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x04 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\bstaff_id\x18\x05 \x01(\x03R\astaffId\x12I\n" +
	"\x06source\x18\x06 \x01(\x0e2,.backend.proto.customer.v1.HistoryLog.SourceH\x00R\x06source\x88\x01\x01\x12 \n" +
	"\tsource_id\x18\a \x01(\x03H\x01R\bsourceId\x88\x01\x01\x12$\n" +
	"\vsource_name\x18\b \x01(\tH\x02R\n" +
	"sourceName\x88\x01\x01B\t\n" +
	"\a_sourceB\f\n" +
	"\n" +
	"_source_idB\x0e\n" +
	"\f_source_name\"9\n" +
	" CreateCustomerHistoryLogResponse\x12\x15\n" +
	"\x06log_id\x18\x01 \x01(\x03R\x05logId\"\xa9\x01\n" +
	"\x1fUpdateCustomerHistoryLogRequest\x12\x15\n" +
	"\x06log_id\x18\x01 \x01(\x03R\x05logId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12I\n" +
	"\x06action\x18\n" +
	" \x01(\v2,.backend.proto.customer.v1.HistoryLog.ActionH\x00R\x06action\x88\x01\x01B\t\n" +
	"\a_action\"\"\n" +
	" UpdateCustomerHistoryLogResponse\"\xbe\x03\n" +
	"\x1eListCustomerHistoryLogsRequest\x12$\n" +
	"\vcustomer_id\x18\x01 \x01(\x03H\x00R\n" +
	"customerId\x88\x01\x01\x12]\n" +
	"\x06filter\x18\x02 \x01(\<EMAIL>\x01R\x06filter\x88\x01\x01\x12,\n" +
	"\tpage_size\x18\x03 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00H\x02R\bpageSize\x88\x01\x01\x12'\n" +
	"\bpage_num\x18\x04 \x01(\x05B\a\xbaH\x04\x1a\x02(\x01H\x03R\apageNum\x88\x01\x01\x1a\x89\x01\n" +
	"\x06Filter\x12C\n" +
	"\x04type\x18\x01 \x01(\x0e2*.backend.proto.customer.v1.HistoryLog.TypeH\x00R\x04type\x88\x01\x01\x12\"\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03H\x01R\tcompanyId\x88\x01\x01B\a\n" +
	"\x05_typeB\r\n" +
	"\v_company_idB\x0e\n" +
	"\f_customer_idB\t\n" +
	"\a_filterB\f\n" +
	"\n" +
	"_page_sizeB\v\n" +
	"\t_page_num\"\x81\x01\n" +
	"\x1fListCustomerHistoryLogsResponse\x12H\n" +
	"\fhistory_logs\x18\x01 \x03(\v2%.backend.proto.customer.v1.HistoryLogR\vhistoryLogs\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"\xca\x02\n" +
	"\x19CreateCustomerTaskRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x03 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\bstaff_id\x18\x04 \x01(\x03R\astaffId\x12\x12\n" +
	"\x04name\x18\n" +
	" \x01(\tR\x04name\x12/\n" +
	"\x11allocate_staff_id\x18\v \x01(\x03H\x00R\x0fallocateStaffId\x88\x01\x01\x12D\n" +
	"\rcomplete_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampH\x01R\fcompleteTime\x88\x01\x01B\x14\n" +
	"\x12_allocate_staff_idB\x10\n" +
	"\x0e_complete_time\"5\n" +
	"\x1aCreateCustomerTaskResponse\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\x03R\x06taskId\"\xe1\x02\n" +
	"\x19UpdateCustomerTaskRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\x03R\x06taskId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12\x17\n" +
	"\x04name\x18\n" +
	" \x01(\tH\x00R\x04name\x88\x01\x01\x12/\n" +
	"\x11allocate_staff_id\x18\v \x01(\x03H\x01R\x0fallocateStaffId\x88\x01\x01\x12D\n" +
	"\rcomplete_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampH\x02R\fcompleteTime\x88\x01\x01\x12E\n" +
	"\x05state\x18\r \x01(\x0e2%.backend.proto.customer.v1.Task.StateB\x03\xe0A\x03H\x03R\x05state\x88\x01\x01B\a\n" +
	"\x05_nameB\x14\n" +
	"\x12_allocate_staff_idB\x10\n" +
	"\x0e_complete_timeB\b\n" +
	"\x06_state\"\x1c\n" +
	"\x1aUpdateCustomerTaskResponse\";\n" +
	"\x18ListCustomerTasksRequest\x12\x1f\n" +
	"\vcustomer_id\x18\x01 \x01(\x03R\n" +
	"customerId\"R\n" +
	"\x19ListCustomerTasksResponse\x125\n" +
	"\x05tasks\x18\x01 \x03(\v2\x1f.backend.proto.customer.v1.TaskR\x05tasks\"O\n" +
	"\x19DeleteCustomerTaskRequest\x12\x17\n" +
	"\atask_id\x18\x01 \x01(\x03R\x06taskId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\"~\n" +
	"\x14CreateAddressRequest\x12(\n" +
	"\vcustomer_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\n" +
	"customerId\x12<\n" +
	"\aaddress\x18\x02 \x01(\v2\".backend.proto.customer.v1.AddressR\aaddress\"6\n" +
	"\x15CreateAddressResponse\x12\x1d\n" +
	"\n" +
	"address_id\x18\x01 \x01(\x03R\taddressId\"\xc3\x04\n" +
	"\x14UpdateAddressRequest\x12W\n" +
	"\aaddress\x18\x01 \x01(\v2=.backend.proto.customer.v1.UpdateAddressRequest.UpdateAddressR\aaddress\x1a\xd1\x03\n" +
	"\rUpdateAddress\x12\x1d\n" +
	"\n" +
	"address_id\x18\x01 \x01(\x03R\taddressId\x12\x1f\n" +
	"\baddress1\x18\x02 \x01(\tH\x00R\baddress1\x88\x01\x01\x12\x1f\n" +
	"\baddress2\x18\x03 \x01(\tH\x01R\baddress2\x88\x01\x01\x12\x17\n" +
	"\x04city\x18\x04 \x01(\tH\x02R\x04city\x88\x01\x01\x12\x19\n" +
	"\x05state\x18\x05 \x01(\tH\x03R\x05state\x88\x01\x01\x12$\n" +
	"\vregion_code\x18\x06 \x01(\tH\x04R\n" +
	"regionCode\x88\x01\x01\x12\x1d\n" +
	"\azipcode\x18\a \x01(\tH\x05R\azipcode\x88\x01\x01\x12\x15\n" +
	"\x03lat\x18\b \x01(\tH\x06R\x03lat\x88\x01\x01\x12\x15\n" +
	"\x03lng\x18\t \x01(\tH\aR\x03lng\x88\x01\x01\x12P\n" +
	"\n" +
	"is_primary\x18\n" +
	" \x01(\x0e2,.backend.proto.customer.v1.Address.IsPrimaryH\bR\tisPrimary\x88\x01\x01B\v\n" +
	"\t_address1B\v\n" +
	"\t_address2B\a\n" +
	"\x05_cityB\b\n" +
	"\x06_stateB\x0e\n" +
	"\f_region_codeB\n" +
	"\n" +
	"\b_zipcodeB\x06\n" +
	"\x04_latB\x06\n" +
	"\x04_lngB\r\n" +
	"\v_is_primary\"\x17\n" +
	"\x15UpdateAddressResponse\"5\n" +
	"\x14DeleteAddressRequest\x12\x1d\n" +
	"\n" +
	"address_id\x18\x01 \x01(\x03R\taddressId\"\xb4\x04\n" +
	"\x14ListAddressesRequest\x12\x1b\n" +
	"\x06parent\x18\x01 \x01(\x03B\x03\xe0A\x02R\x06parent\x12,\n" +
	"\tpage_size\x18\x02 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\xe8\a(\x00H\x00R\bpageSize\x88\x01\x01\x12'\n" +
	"\bpage_num\x18\x03 \x01(\x05B\a\xbaH\x04\x1a\x02(\x01H\x01R\apageNum\x88\x01\x01\x12`\n" +
	"\vorder_field\x18\x04 \x01(\x0e2:.backend.proto.customer.v1.ListAddressesRequest.OrderFieldH\x02R\n" +
	"orderField\x88\x01\x01\x12l\n" +
	"\x0forder_direction\x18\x05 \x01(\x0e2>.backend.proto.customer.v1.ListAddressesRequest.OrderDirectionH\x03R\x0eorderDirection\x88\x01\x01\"S\n" +
	"\n" +
	"OrderField\x12\x1b\n" +
	"\x17ORDER_FIELD_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vCREATE_TIME\x10\x01\x12\x0f\n" +
	"\vUPDATE_TIME\x10\x02\x12\x06\n" +
	"\x02ID\x10\x03\"D\n" +
	"\x0eOrderDirection\x12\x1f\n" +
	"\x1bORDER_DIRECTION_UNSPECIFIED\x10\x00\x12\a\n" +
	"\x03ASC\x10\x01\x12\b\n" +
	"\x04DESC\x10\x02B\f\n" +
	"\n" +
	"_page_sizeB\v\n" +
	"\t_page_numB\x0e\n" +
	"\f_order_fieldB\x12\n" +
	"\x10_order_direction\"o\n" +
	"\x15ListAddressesResponse\x12@\n" +
	"\taddresses\x18\x01 \x03(\v2\".backend.proto.customer.v1.AddressR\taddresses\x12\x14\n" +
	"\x05total\x18\x02 \x01(\x05R\x05total\"\x9b\x01\n" +
	"\x16CreateLifeCycleRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\bstaff_id\x18\x03 \x01(\x03R\astaffId\x12\x12\n" +
	"\x04name\x18\n" +
	" \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\v \x01(\x05R\x04sort\")\n" +
	"\x17CreateLifeCycleResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\x98\x02\n" +
	"\x17UpdateLifeCyclesRequest\x12\\\n" +
	"\aupdates\x18\x01 \x03(\v2B.backend.proto.customer.v1.UpdateLifeCyclesRequest.UpdateLifeCycleR\aupdates\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03R\tcompanyId\x1ae\n" +
	"\x0fUpdateLifeCycle\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tH\x00R\x04name\x88\x01\x01\x12\x17\n" +
	"\x04sort\x18\x03 \x01(\x05H\x01R\x04sort\x88\x01\x01B\a\n" +
	"\x05_nameB\a\n" +
	"\x05_sort\"\x1a\n" +
	"\x18UpdateLifeCyclesResponse\"6\n" +
	"\x15ListLifeCyclesRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\"h\n" +
	"\x16ListLifeCyclesResponse\x12N\n" +
	"\vlife_cycles\x18\x01 \x03(\v2-.backend.proto.customer.v1.CustomizeLifeCycleR\n" +
	"lifeCycles\"C\n" +
	"\x16DeleteLifeCycleRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\"\xb3\x01\n" +
	"\x18CreateActionStateRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x1f\n" +
	"\vbusiness_id\x18\x02 \x01(\x03R\n" +
	"businessId\x12\x19\n" +
	"\bstaff_id\x18\x03 \x01(\x03R\astaffId\x12\x12\n" +
	"\x04name\x18\n" +
	" \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\v \x01(\x05R\x04sort\x12\x14\n" +
	"\x05color\x18\f \x01(\tR\x05color\"+\n" +
	"\x19CreateActionStateResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xc6\x02\n" +
	"\x19UpdateActionStatesRequest\x12`\n" +
	"\aupdates\x18\x01 \x03(\v2F.backend.proto.customer.v1.UpdateActionStatesRequest.UpdateActionStateR\aupdates\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03R\tcompanyId\x1a\x8c\x01\n" +
	"\x11UpdateActionState\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tH\x00R\x04name\x88\x01\x01\x12\x17\n" +
	"\x04sort\x18\x03 \x01(\x05H\x01R\x04sort\x88\x01\x01\x12\x19\n" +
	"\x05color\x18\x04 \x01(\tH\x02R\x05color\x88\x01\x01B\a\n" +
	"\x05_nameB\a\n" +
	"\x05_sortB\b\n" +
	"\x06_color\"\x1d\n" +
	"\x1bUpdateActionsStatesResponse\"8\n" +
	"\x17ListActionStatesRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\"p\n" +
	"\x18ListActionStatesResponse\x12T\n" +
	"\raction_states\x18\x01 \x03(\v2/.backend.proto.customer.v1.CustomizeActionStateR\factionStates\"E\n" +
	"\x18DeleteActionStateRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\"\xd1\x02\n" +
	"\x11CreateViewRequest\x12\x14\n" +
	"\x05title\x18\x01 \x01(\tR\x05title\x12\x16\n" +
	"\x06fields\x18\x02 \x03(\tR\x06fields\x12J\n" +
	"\border_by\x18\x03 \x01(\v2/.backend.proto.customer.v1.CustomerView.OrderByR\aorderBy\x12F\n" +
	"\x06filter\x18\x04 \x01(\v2..backend.proto.customer.v1.CustomerView.FilterR\x06filter\x12\x19\n" +
	"\bstaff_id\x18\x05 \x01(\x03R\astaffId\x12\x1d\n" +
	"\n" +
	"company_id\x18\x06 \x01(\x03R\tcompanyId\x12@\n" +
	"\x04type\x18\a \x01(\x0e2,.backend.proto.customer.v1.CustomerView.TypeR\x04type\"$\n" +
	"\x12CreateViewResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"\xd0\x02\n" +
	"\x11UpdateViewRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x19\n" +
	"\x05title\x18\x02 \x01(\tH\x00R\x05title\x88\x01\x01\x12\x16\n" +
	"\x06fields\x18\x03 \x03(\tR\x06fields\x12O\n" +
	"\border_by\x18\x04 \x01(\v2/.backend.proto.customer.v1.CustomerView.OrderByH\x01R\aorderBy\x88\x01\x01\x12K\n" +
	"\x06filter\x18\x05 \x01(\v2..backend.proto.customer.v1.CustomerView.FilterH\x02R\x06filter\x88\x01\x01\x12\x19\n" +
	"\bstaff_id\x18\x06 \x01(\x03R\astaffId\x12\x1d\n" +
	"\n" +
	"company_id\x18\a \x01(\x03R\tcompanyIdB\b\n" +
	"\x06_titleB\v\n" +
	"\t_order_byB\t\n" +
	"\a_filter\"\x14\n" +
	"\x12UpdateViewResponse\"\x9c\x01\n" +
	"\x10ListViewsRequest\x12\x1d\n" +
	"\n" +
	"company_id\x18\x01 \x01(\x03R\tcompanyId\x12\x19\n" +
	"\bstaff_id\x18\x02 \x01(\x03R\astaffId\x12E\n" +
	"\x04type\x18\x03 \x01(\x0e2,.backend.proto.customer.v1.CustomerView.TypeH\x00R\x04type\x88\x01\x01B\a\n" +
	"\x05_type\"R\n" +
	"\x11ListViewsResponse\x12=\n" +
	"\x05views\x18\x01 \x03(\v2'.backend.proto.customer.v1.CustomerViewR\x05views\"]\n" +
	"\x11DeleteViewRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"company_id\x18\x02 \x01(\x03R\tcompanyId\x12\x19\n" +
	"\bstaff_id\x18\x03 \x01(\x03R\astaffId*\xad\x04\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10̥\a\x12!\n" +
	"\x1bERR_CODE_CUSTOMER_NOT_FOUND\x10ͥ\a\x12&\n" +
	" ERR_CODE_CUSTOMER_ALREADY_EXISTS\x10Υ\a\x12\"\n" +
	"\x1cERR_CODE_INVALID_CUSTOMER_ID\x10ϥ\a\x12$\n" +
	"\x1eERR_CODE_INVALID_CUSTOMER_NAME\x10Х\a\x12\x1f\n" +
	"\x19ERR_CODE_CUSTOMER_DELETED\x10ѥ\a\x12 \n" +
	"\x1aERR_CODE_ADDRESS_NOT_FOUND\x10\xb1\xa6\a\x12\x1e\n" +
	"\x18ERR_CODE_INVALID_ADDRESS\x10\xb2\xa6\a\x12%\n" +
	"\x1fERR_CODE_ADDRESS_LIMIT_EXCEEDED\x10\xb3\xa6\a\x12\x1d\n" +
	"\x17ERR_CODE_TASK_NOT_FOUND\x10\x95\xa7\a\x12%\n" +
	"\x1fERR_CODE_TASK_ALREADY_COMPLETED\x10\x96\xa7\a\x12\"\n" +
	"\x1cERR_CODE_INVALID_TASK_STATUS\x10\x97\xa7\a\x12&\n" +
	" ERR_CODE_ACTION_STATE_NAME_EXIST\x10\x98\xa7\a\x12$\n" +
	"\x1eERR_CODE_LIFE_CYCLE_NAME_EXIST\x10\x99\xa7\a\x12\x1e\n" +
	"\x18ERR_CODE_VIEW_NAME_EXIST\x10\x9a\xa7\a2\xec\x1c\n" +
	"\x0fCustomerService\x12u\n" +
	"\x0eCreateCustomer\x120.backend.proto.customer.v1.CreateCustomerRequest\x1a1.backend.proto.customer.v1.CreateCustomerResponse\x12u\n" +
	"\x0eUpdateCustomer\x120.backend.proto.customer.v1.UpdateCustomerRequest\x1a1.backend.proto.customer.v1.UpdateCustomerResponse\x12r\n" +
	"\rListCustomers\x12/.backend.proto.customer.v1.ListCustomersRequest\x1a0.backend.proto.customer.v1.ListCustomersResponse\x12a\n" +
	"\vGetCustomer\x12-.backend.proto.customer.v1.GetCustomerRequest\x1a#.backend.proto.customer.v1.Customer\x12Z\n" +
	"\x0eDeleteCustomer\x120.backend.proto.customer.v1.DeleteCustomerRequest\x1a\x16.google.protobuf.Empty\x12\x81\x01\n" +
	"\x12SyncCustomerSearch\x124.backend.proto.customer.v1.SyncCustomerSearchRequest\x1a5.backend.proto.customer.v1.SyncCustomerSearchResponse\x12\x93\x01\n" +
	"\x18CreateCustomerHistoryLog\x12:.backend.proto.customer.v1.CreateCustomerHistoryLogRequest\x1a;.backend.proto.customer.v1.CreateCustomerHistoryLogResponse\x12\x93\x01\n" +
	"\x18UpdateCustomerHistoryLog\x12:.backend.proto.customer.v1.UpdateCustomerHistoryLogRequest\x1a;.backend.proto.customer.v1.UpdateCustomerHistoryLogResponse\x12\x90\x01\n" +
	"\x17ListCustomerHistoryLogs\x129.backend.proto.customer.v1.ListCustomerHistoryLogsRequest\x1a:.backend.proto.customer.v1.ListCustomerHistoryLogsResponse\x12x\n" +
	"\x0fConvertCustomer\x121.backend.proto.customer.v1.ConvertCustomerRequest\x1a2.backend.proto.customer.v1.ConvertCustomerResponse\x12\x96\x01\n" +
	"\x19ConvertCustomersAttribute\x12;.backend.proto.customer.v1.ConvertCustomersAttributeRequest\x1a<.backend.proto.customer.v1.ConvertCustomersAttributeResponse\x12\x81\x01\n" +
	"\x12CreateCustomerTask\x124.backend.proto.customer.v1.CreateCustomerTaskRequest\x1a5.backend.proto.customer.v1.CreateCustomerTaskResponse\x12\x81\x01\n" +
	"\x12UpdateCustomerTask\x124.backend.proto.customer.v1.UpdateCustomerTaskRequest\x1a5.backend.proto.customer.v1.UpdateCustomerTaskResponse\x12~\n" +
	"\x11ListCustomerTasks\x123.backend.proto.customer.v1.ListCustomerTasksRequest\x1a4.backend.proto.customer.v1.ListCustomerTasksResponse\x12b\n" +
	"\x12DeleteCustomerTask\x124.backend.proto.customer.v1.DeleteCustomerTaskRequest\x1a\x16.google.protobuf.Empty\x12r\n" +
	"\rCreateAddress\x12/.backend.proto.customer.v1.CreateAddressRequest\x1a0.backend.proto.customer.v1.CreateAddressResponse\x12r\n" +
	"\rUpdateAddress\x12/.backend.proto.customer.v1.UpdateAddressRequest\x1a0.backend.proto.customer.v1.UpdateAddressResponse\x12X\n" +
	"\rDeleteAddress\x12/.backend.proto.customer.v1.DeleteAddressRequest\x1a\x16.google.protobuf.Empty\x12r\n" +
	"\rListAddresses\x12/.backend.proto.customer.v1.ListAddressesRequest\x1a0.backend.proto.customer.v1.ListAddressesResponse\x12x\n" +
	"\x0fCreateLifeCycle\x121.backend.proto.customer.v1.CreateLifeCycleRequest\x1a2.backend.proto.customer.v1.CreateLifeCycleResponse\x12{\n" +
	"\x10UpdateLifeCycles\x122.backend.proto.customer.v1.UpdateLifeCyclesRequest\x1a3.backend.proto.customer.v1.UpdateLifeCyclesResponse\x12u\n" +
	"\x0eListLifeCycles\x120.backend.proto.customer.v1.ListLifeCyclesRequest\x1a1.backend.proto.customer.v1.ListLifeCyclesResponse\x12\\\n" +
	"\x0fDeleteLifeCycle\x121.backend.proto.customer.v1.DeleteLifeCycleRequest\x1a\x16.google.protobuf.Empty\x12~\n" +
	"\x11CreateActionState\x123.backend.proto.customer.v1.CreateActionStateRequest\x1a4.backend.proto.customer.v1.CreateActionStateResponse\x12\x82\x01\n" +
	"\x12UpdateActionStates\x124.backend.proto.customer.v1.UpdateActionStatesRequest\x1a6.backend.proto.customer.v1.UpdateActionsStatesResponse\x12{\n" +
	"\x10ListActionStates\x122.backend.proto.customer.v1.ListActionStatesRequest\x1a3.backend.proto.customer.v1.ListActionStatesResponse\x12`\n" +
	"\x11DeleteActionState\x123.backend.proto.customer.v1.DeleteActionStateRequest\x1a\x16.google.protobuf.Empty\x12i\n" +
	"\n" +
	"CreateView\x12,.backend.proto.customer.v1.CreateViewRequest\x1a-.backend.proto.customer.v1.CreateViewResponse\x12i\n" +
	"\n" +
	"UpdateView\x12,.backend.proto.customer.v1.UpdateViewRequest\x1a-.backend.proto.customer.v1.UpdateViewResponse\x12f\n" +
	"\tListViews\x12+.backend.proto.customer.v1.ListViewsRequest\x1a,.backend.proto.customer.v1.ListViewsResponse\x12R\n" +
	"\n" +
	"DeleteView\x12,.backend.proto.customer.v1.DeleteViewRequest\x1a\x16.google.protobuf.EmptyBk\n" +
	"#com.moego.backend.proto.customer.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/customer/v1;customerpbb\x06proto3"

var (
	file_backend_proto_customer_v1_customer_service_proto_rawDescOnce sync.Once
	file_backend_proto_customer_v1_customer_service_proto_rawDescData []byte
)

func file_backend_proto_customer_v1_customer_service_proto_rawDescGZIP() []byte {
	file_backend_proto_customer_v1_customer_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_customer_v1_customer_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v1_customer_service_proto_rawDesc), len(file_backend_proto_customer_v1_customer_service_proto_rawDesc)))
	})
	return file_backend_proto_customer_v1_customer_service_proto_rawDescData
}

var file_backend_proto_customer_v1_customer_service_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_backend_proto_customer_v1_customer_service_proto_msgTypes = make([]protoimpl.MessageInfo, 63)
var file_backend_proto_customer_v1_customer_service_proto_goTypes = []any{
	(ErrCode)(0),                                        // 0: backend.proto.customer.v1.ErrCode
	(ListCustomersRequest_OrderField)(0),                // 1: backend.proto.customer.v1.ListCustomersRequest.OrderField
	(ListCustomersRequest_OrderDirection)(0),            // 2: backend.proto.customer.v1.ListCustomersRequest.OrderDirection
	(ListAddressesRequest_OrderField)(0),                // 3: backend.proto.customer.v1.ListAddressesRequest.OrderField
	(ListAddressesRequest_OrderDirection)(0),            // 4: backend.proto.customer.v1.ListAddressesRequest.OrderDirection
	(*CreateCustomerRequest)(nil),                       // 5: backend.proto.customer.v1.CreateCustomerRequest
	(*CreateCustomerResponse)(nil),                      // 6: backend.proto.customer.v1.CreateCustomerResponse
	(*UpdateCustomerRequest)(nil),                       // 7: backend.proto.customer.v1.UpdateCustomerRequest
	(*UpdateCustomerResponse)(nil),                      // 8: backend.proto.customer.v1.UpdateCustomerResponse
	(*ListCustomersRequest)(nil),                        // 9: backend.proto.customer.v1.ListCustomersRequest
	(*ListCustomersResponse)(nil),                       // 10: backend.proto.customer.v1.ListCustomersResponse
	(*GetCustomerRequest)(nil),                          // 11: backend.proto.customer.v1.GetCustomerRequest
	(*DeleteCustomerRequest)(nil),                       // 12: backend.proto.customer.v1.DeleteCustomerRequest
	(*SyncCustomerSearchRequest)(nil),                   // 13: backend.proto.customer.v1.SyncCustomerSearchRequest
	(*SyncCustomerSearchResponse)(nil),                  // 14: backend.proto.customer.v1.SyncCustomerSearchResponse
	(*ConvertCustomerRequest)(nil),                      // 15: backend.proto.customer.v1.ConvertCustomerRequest
	(*ConvertCustomerResponse)(nil),                     // 16: backend.proto.customer.v1.ConvertCustomerResponse
	(*ConvertCustomersAttributeRequest)(nil),            // 17: backend.proto.customer.v1.ConvertCustomersAttributeRequest
	(*ConvertCustomersAttributeResponse)(nil),           // 18: backend.proto.customer.v1.ConvertCustomersAttributeResponse
	(*CreateCustomerHistoryLogRequest)(nil),             // 19: backend.proto.customer.v1.CreateCustomerHistoryLogRequest
	(*CreateCustomerHistoryLogResponse)(nil),            // 20: backend.proto.customer.v1.CreateCustomerHistoryLogResponse
	(*UpdateCustomerHistoryLogRequest)(nil),             // 21: backend.proto.customer.v1.UpdateCustomerHistoryLogRequest
	(*UpdateCustomerHistoryLogResponse)(nil),            // 22: backend.proto.customer.v1.UpdateCustomerHistoryLogResponse
	(*ListCustomerHistoryLogsRequest)(nil),              // 23: backend.proto.customer.v1.ListCustomerHistoryLogsRequest
	(*ListCustomerHistoryLogsResponse)(nil),             // 24: backend.proto.customer.v1.ListCustomerHistoryLogsResponse
	(*CreateCustomerTaskRequest)(nil),                   // 25: backend.proto.customer.v1.CreateCustomerTaskRequest
	(*CreateCustomerTaskResponse)(nil),                  // 26: backend.proto.customer.v1.CreateCustomerTaskResponse
	(*UpdateCustomerTaskRequest)(nil),                   // 27: backend.proto.customer.v1.UpdateCustomerTaskRequest
	(*UpdateCustomerTaskResponse)(nil),                  // 28: backend.proto.customer.v1.UpdateCustomerTaskResponse
	(*ListCustomerTasksRequest)(nil),                    // 29: backend.proto.customer.v1.ListCustomerTasksRequest
	(*ListCustomerTasksResponse)(nil),                   // 30: backend.proto.customer.v1.ListCustomerTasksResponse
	(*DeleteCustomerTaskRequest)(nil),                   // 31: backend.proto.customer.v1.DeleteCustomerTaskRequest
	(*CreateAddressRequest)(nil),                        // 32: backend.proto.customer.v1.CreateAddressRequest
	(*CreateAddressResponse)(nil),                       // 33: backend.proto.customer.v1.CreateAddressResponse
	(*UpdateAddressRequest)(nil),                        // 34: backend.proto.customer.v1.UpdateAddressRequest
	(*UpdateAddressResponse)(nil),                       // 35: backend.proto.customer.v1.UpdateAddressResponse
	(*DeleteAddressRequest)(nil),                        // 36: backend.proto.customer.v1.DeleteAddressRequest
	(*ListAddressesRequest)(nil),                        // 37: backend.proto.customer.v1.ListAddressesRequest
	(*ListAddressesResponse)(nil),                       // 38: backend.proto.customer.v1.ListAddressesResponse
	(*CreateLifeCycleRequest)(nil),                      // 39: backend.proto.customer.v1.CreateLifeCycleRequest
	(*CreateLifeCycleResponse)(nil),                     // 40: backend.proto.customer.v1.CreateLifeCycleResponse
	(*UpdateLifeCyclesRequest)(nil),                     // 41: backend.proto.customer.v1.UpdateLifeCyclesRequest
	(*UpdateLifeCyclesResponse)(nil),                    // 42: backend.proto.customer.v1.UpdateLifeCyclesResponse
	(*ListLifeCyclesRequest)(nil),                       // 43: backend.proto.customer.v1.ListLifeCyclesRequest
	(*ListLifeCyclesResponse)(nil),                      // 44: backend.proto.customer.v1.ListLifeCyclesResponse
	(*DeleteLifeCycleRequest)(nil),                      // 45: backend.proto.customer.v1.DeleteLifeCycleRequest
	(*CreateActionStateRequest)(nil),                    // 46: backend.proto.customer.v1.CreateActionStateRequest
	(*CreateActionStateResponse)(nil),                   // 47: backend.proto.customer.v1.CreateActionStateResponse
	(*UpdateActionStatesRequest)(nil),                   // 48: backend.proto.customer.v1.UpdateActionStatesRequest
	(*UpdateActionsStatesResponse)(nil),                 // 49: backend.proto.customer.v1.UpdateActionsStatesResponse
	(*ListActionStatesRequest)(nil),                     // 50: backend.proto.customer.v1.ListActionStatesRequest
	(*ListActionStatesResponse)(nil),                    // 51: backend.proto.customer.v1.ListActionStatesResponse
	(*DeleteActionStateRequest)(nil),                    // 52: backend.proto.customer.v1.DeleteActionStateRequest
	(*CreateViewRequest)(nil),                           // 53: backend.proto.customer.v1.CreateViewRequest
	(*CreateViewResponse)(nil),                          // 54: backend.proto.customer.v1.CreateViewResponse
	(*UpdateViewRequest)(nil),                           // 55: backend.proto.customer.v1.UpdateViewRequest
	(*UpdateViewResponse)(nil),                          // 56: backend.proto.customer.v1.UpdateViewResponse
	(*ListViewsRequest)(nil),                            // 57: backend.proto.customer.v1.ListViewsRequest
	(*ListViewsResponse)(nil),                           // 58: backend.proto.customer.v1.ListViewsResponse
	(*DeleteViewRequest)(nil),                           // 59: backend.proto.customer.v1.DeleteViewRequest
	(*UpdateCustomerRequest_UpdateCustomer)(nil),        // 60: backend.proto.customer.v1.UpdateCustomerRequest.UpdateCustomer
	(*UpdateCustomerRequest_UpdateAddress)(nil),         // 61: backend.proto.customer.v1.UpdateCustomerRequest.UpdateAddress
	(*UpdateCustomerRequest_UpdateContact)(nil),         // 62: backend.proto.customer.v1.UpdateCustomerRequest.UpdateContact
	(*ListCustomersRequest_Filter)(nil),                 // 63: backend.proto.customer.v1.ListCustomersRequest.Filter
	(*ListCustomerHistoryLogsRequest_Filter)(nil),       // 64: backend.proto.customer.v1.ListCustomerHistoryLogsRequest.Filter
	(*UpdateAddressRequest_UpdateAddress)(nil),          // 65: backend.proto.customer.v1.UpdateAddressRequest.UpdateAddress
	(*UpdateLifeCyclesRequest_UpdateLifeCycle)(nil),     // 66: backend.proto.customer.v1.UpdateLifeCyclesRequest.UpdateLifeCycle
	(*UpdateActionStatesRequest_UpdateActionState)(nil), // 67: backend.proto.customer.v1.UpdateActionStatesRequest.UpdateActionState
	(*Customer)(nil),                                    // 68: backend.proto.customer.v1.Customer
	(*HistoryLog_Action)(nil),                           // 69: backend.proto.customer.v1.HistoryLog.Action
	(HistoryLog_Source)(0),                              // 70: backend.proto.customer.v1.HistoryLog.Source
	(*HistoryLog)(nil),                                  // 71: backend.proto.customer.v1.HistoryLog
	(*timestamppb.Timestamp)(nil),                       // 72: google.protobuf.Timestamp
	(Task_State)(0),                                     // 73: backend.proto.customer.v1.Task.State
	(*Task)(nil),                                        // 74: backend.proto.customer.v1.Task
	(*Address)(nil),                                     // 75: backend.proto.customer.v1.Address
	(*CustomizeLifeCycle)(nil),                          // 76: backend.proto.customer.v1.CustomizeLifeCycle
	(*CustomizeActionState)(nil),                        // 77: backend.proto.customer.v1.CustomizeActionState
	(*CustomerView_OrderBy)(nil),                        // 78: backend.proto.customer.v1.CustomerView.OrderBy
	(*CustomerView_Filter)(nil),                         // 79: backend.proto.customer.v1.CustomerView.Filter
	(CustomerView_Type)(0),                              // 80: backend.proto.customer.v1.CustomerView.Type
	(*CustomerView)(nil),                                // 81: backend.proto.customer.v1.CustomerView
	(Customer_LifeCycle)(0),                             // 82: backend.proto.customer.v1.Customer.LifeCycle
	(Customer_ActionState)(0),                           // 83: backend.proto.customer.v1.Customer.ActionState
	(*Customer_AdditionalInfo)(nil),                     // 84: backend.proto.customer.v1.Customer.AdditionalInfo
	(*structpb.Struct)(nil),                             // 85: google.protobuf.Struct
	(Address_IsPrimary)(0),                              // 86: backend.proto.customer.v1.Address.IsPrimary
	(CustomerContact_IsPrimary)(0),                      // 87: backend.proto.customer.v1.CustomerContact.IsPrimary
	(Customer_Type)(0),                                  // 88: backend.proto.customer.v1.Customer.Type
	(HistoryLog_Type)(0),                                // 89: backend.proto.customer.v1.HistoryLog.Type
	(*emptypb.Empty)(nil),                               // 90: google.protobuf.Empty
}
var file_backend_proto_customer_v1_customer_service_proto_depIdxs = []int32{
	68, // 0: backend.proto.customer.v1.CreateCustomerRequest.customer:type_name -> backend.proto.customer.v1.Customer
	68, // 1: backend.proto.customer.v1.CreateCustomerResponse.customer:type_name -> backend.proto.customer.v1.Customer
	60, // 2: backend.proto.customer.v1.UpdateCustomerRequest.customer:type_name -> backend.proto.customer.v1.UpdateCustomerRequest.UpdateCustomer
	63, // 3: backend.proto.customer.v1.ListCustomersRequest.filter:type_name -> backend.proto.customer.v1.ListCustomersRequest.Filter
	1,  // 4: backend.proto.customer.v1.ListCustomersRequest.order_field:type_name -> backend.proto.customer.v1.ListCustomersRequest.OrderField
	2,  // 5: backend.proto.customer.v1.ListCustomersRequest.order_direction:type_name -> backend.proto.customer.v1.ListCustomersRequest.OrderDirection
	68, // 6: backend.proto.customer.v1.ListCustomersResponse.customers:type_name -> backend.proto.customer.v1.Customer
	69, // 7: backend.proto.customer.v1.CreateCustomerHistoryLogRequest.action:type_name -> backend.proto.customer.v1.HistoryLog.Action
	70, // 8: backend.proto.customer.v1.CreateCustomerHistoryLogRequest.source:type_name -> backend.proto.customer.v1.HistoryLog.Source
	69, // 9: backend.proto.customer.v1.UpdateCustomerHistoryLogRequest.action:type_name -> backend.proto.customer.v1.HistoryLog.Action
	64, // 10: backend.proto.customer.v1.ListCustomerHistoryLogsRequest.filter:type_name -> backend.proto.customer.v1.ListCustomerHistoryLogsRequest.Filter
	71, // 11: backend.proto.customer.v1.ListCustomerHistoryLogsResponse.history_logs:type_name -> backend.proto.customer.v1.HistoryLog
	72, // 12: backend.proto.customer.v1.CreateCustomerTaskRequest.complete_time:type_name -> google.protobuf.Timestamp
	72, // 13: backend.proto.customer.v1.UpdateCustomerTaskRequest.complete_time:type_name -> google.protobuf.Timestamp
	73, // 14: backend.proto.customer.v1.UpdateCustomerTaskRequest.state:type_name -> backend.proto.customer.v1.Task.State
	74, // 15: backend.proto.customer.v1.ListCustomerTasksResponse.tasks:type_name -> backend.proto.customer.v1.Task
	75, // 16: backend.proto.customer.v1.CreateAddressRequest.address:type_name -> backend.proto.customer.v1.Address
	65, // 17: backend.proto.customer.v1.UpdateAddressRequest.address:type_name -> backend.proto.customer.v1.UpdateAddressRequest.UpdateAddress
	3,  // 18: backend.proto.customer.v1.ListAddressesRequest.order_field:type_name -> backend.proto.customer.v1.ListAddressesRequest.OrderField
	4,  // 19: backend.proto.customer.v1.ListAddressesRequest.order_direction:type_name -> backend.proto.customer.v1.ListAddressesRequest.OrderDirection
	75, // 20: backend.proto.customer.v1.ListAddressesResponse.addresses:type_name -> backend.proto.customer.v1.Address
	66, // 21: backend.proto.customer.v1.UpdateLifeCyclesRequest.updates:type_name -> backend.proto.customer.v1.UpdateLifeCyclesRequest.UpdateLifeCycle
	76, // 22: backend.proto.customer.v1.ListLifeCyclesResponse.life_cycles:type_name -> backend.proto.customer.v1.CustomizeLifeCycle
	67, // 23: backend.proto.customer.v1.UpdateActionStatesRequest.updates:type_name -> backend.proto.customer.v1.UpdateActionStatesRequest.UpdateActionState
	77, // 24: backend.proto.customer.v1.ListActionStatesResponse.action_states:type_name -> backend.proto.customer.v1.CustomizeActionState
	78, // 25: backend.proto.customer.v1.CreateViewRequest.order_by:type_name -> backend.proto.customer.v1.CustomerView.OrderBy
	79, // 26: backend.proto.customer.v1.CreateViewRequest.filter:type_name -> backend.proto.customer.v1.CustomerView.Filter
	80, // 27: backend.proto.customer.v1.CreateViewRequest.type:type_name -> backend.proto.customer.v1.CustomerView.Type
	78, // 28: backend.proto.customer.v1.UpdateViewRequest.order_by:type_name -> backend.proto.customer.v1.CustomerView.OrderBy
	79, // 29: backend.proto.customer.v1.UpdateViewRequest.filter:type_name -> backend.proto.customer.v1.CustomerView.Filter
	80, // 30: backend.proto.customer.v1.ListViewsRequest.type:type_name -> backend.proto.customer.v1.CustomerView.Type
	81, // 31: backend.proto.customer.v1.ListViewsResponse.views:type_name -> backend.proto.customer.v1.CustomerView
	82, // 32: backend.proto.customer.v1.UpdateCustomerRequest.UpdateCustomer.life_cycle:type_name -> backend.proto.customer.v1.Customer.LifeCycle
	83, // 33: backend.proto.customer.v1.UpdateCustomerRequest.UpdateCustomer.action_state:type_name -> backend.proto.customer.v1.Customer.ActionState
	72, // 34: backend.proto.customer.v1.UpdateCustomerRequest.UpdateCustomer.birth_time:type_name -> google.protobuf.Timestamp
	61, // 35: backend.proto.customer.v1.UpdateCustomerRequest.UpdateCustomer.address:type_name -> backend.proto.customer.v1.UpdateCustomerRequest.UpdateAddress
	62, // 36: backend.proto.customer.v1.UpdateCustomerRequest.UpdateCustomer.contact:type_name -> backend.proto.customer.v1.UpdateCustomerRequest.UpdateContact
	84, // 37: backend.proto.customer.v1.UpdateCustomerRequest.UpdateCustomer.additional_info:type_name -> backend.proto.customer.v1.Customer.AdditionalInfo
	85, // 38: backend.proto.customer.v1.UpdateCustomerRequest.UpdateCustomer.custom_fields:type_name -> google.protobuf.Struct
	86, // 39: backend.proto.customer.v1.UpdateCustomerRequest.UpdateAddress.is_primary:type_name -> backend.proto.customer.v1.Address.IsPrimary
	87, // 40: backend.proto.customer.v1.UpdateCustomerRequest.UpdateContact.is_primary:type_name -> backend.proto.customer.v1.CustomerContact.IsPrimary
	83, // 41: backend.proto.customer.v1.ListCustomersRequest.Filter.action_state:type_name -> backend.proto.customer.v1.Customer.ActionState
	88, // 42: backend.proto.customer.v1.ListCustomersRequest.Filter.type:type_name -> backend.proto.customer.v1.Customer.Type
	82, // 43: backend.proto.customer.v1.ListCustomersRequest.Filter.life_cycle:type_name -> backend.proto.customer.v1.Customer.LifeCycle
	89, // 44: backend.proto.customer.v1.ListCustomerHistoryLogsRequest.Filter.type:type_name -> backend.proto.customer.v1.HistoryLog.Type
	86, // 45: backend.proto.customer.v1.UpdateAddressRequest.UpdateAddress.is_primary:type_name -> backend.proto.customer.v1.Address.IsPrimary
	5,  // 46: backend.proto.customer.v1.CustomerService.CreateCustomer:input_type -> backend.proto.customer.v1.CreateCustomerRequest
	7,  // 47: backend.proto.customer.v1.CustomerService.UpdateCustomer:input_type -> backend.proto.customer.v1.UpdateCustomerRequest
	9,  // 48: backend.proto.customer.v1.CustomerService.ListCustomers:input_type -> backend.proto.customer.v1.ListCustomersRequest
	11, // 49: backend.proto.customer.v1.CustomerService.GetCustomer:input_type -> backend.proto.customer.v1.GetCustomerRequest
	12, // 50: backend.proto.customer.v1.CustomerService.DeleteCustomer:input_type -> backend.proto.customer.v1.DeleteCustomerRequest
	13, // 51: backend.proto.customer.v1.CustomerService.SyncCustomerSearch:input_type -> backend.proto.customer.v1.SyncCustomerSearchRequest
	19, // 52: backend.proto.customer.v1.CustomerService.CreateCustomerHistoryLog:input_type -> backend.proto.customer.v1.CreateCustomerHistoryLogRequest
	21, // 53: backend.proto.customer.v1.CustomerService.UpdateCustomerHistoryLog:input_type -> backend.proto.customer.v1.UpdateCustomerHistoryLogRequest
	23, // 54: backend.proto.customer.v1.CustomerService.ListCustomerHistoryLogs:input_type -> backend.proto.customer.v1.ListCustomerHistoryLogsRequest
	15, // 55: backend.proto.customer.v1.CustomerService.ConvertCustomer:input_type -> backend.proto.customer.v1.ConvertCustomerRequest
	17, // 56: backend.proto.customer.v1.CustomerService.ConvertCustomersAttribute:input_type -> backend.proto.customer.v1.ConvertCustomersAttributeRequest
	25, // 57: backend.proto.customer.v1.CustomerService.CreateCustomerTask:input_type -> backend.proto.customer.v1.CreateCustomerTaskRequest
	27, // 58: backend.proto.customer.v1.CustomerService.UpdateCustomerTask:input_type -> backend.proto.customer.v1.UpdateCustomerTaskRequest
	29, // 59: backend.proto.customer.v1.CustomerService.ListCustomerTasks:input_type -> backend.proto.customer.v1.ListCustomerTasksRequest
	31, // 60: backend.proto.customer.v1.CustomerService.DeleteCustomerTask:input_type -> backend.proto.customer.v1.DeleteCustomerTaskRequest
	32, // 61: backend.proto.customer.v1.CustomerService.CreateAddress:input_type -> backend.proto.customer.v1.CreateAddressRequest
	34, // 62: backend.proto.customer.v1.CustomerService.UpdateAddress:input_type -> backend.proto.customer.v1.UpdateAddressRequest
	36, // 63: backend.proto.customer.v1.CustomerService.DeleteAddress:input_type -> backend.proto.customer.v1.DeleteAddressRequest
	37, // 64: backend.proto.customer.v1.CustomerService.ListAddresses:input_type -> backend.proto.customer.v1.ListAddressesRequest
	39, // 65: backend.proto.customer.v1.CustomerService.CreateLifeCycle:input_type -> backend.proto.customer.v1.CreateLifeCycleRequest
	41, // 66: backend.proto.customer.v1.CustomerService.UpdateLifeCycles:input_type -> backend.proto.customer.v1.UpdateLifeCyclesRequest
	43, // 67: backend.proto.customer.v1.CustomerService.ListLifeCycles:input_type -> backend.proto.customer.v1.ListLifeCyclesRequest
	45, // 68: backend.proto.customer.v1.CustomerService.DeleteLifeCycle:input_type -> backend.proto.customer.v1.DeleteLifeCycleRequest
	46, // 69: backend.proto.customer.v1.CustomerService.CreateActionState:input_type -> backend.proto.customer.v1.CreateActionStateRequest
	48, // 70: backend.proto.customer.v1.CustomerService.UpdateActionStates:input_type -> backend.proto.customer.v1.UpdateActionStatesRequest
	50, // 71: backend.proto.customer.v1.CustomerService.ListActionStates:input_type -> backend.proto.customer.v1.ListActionStatesRequest
	52, // 72: backend.proto.customer.v1.CustomerService.DeleteActionState:input_type -> backend.proto.customer.v1.DeleteActionStateRequest
	53, // 73: backend.proto.customer.v1.CustomerService.CreateView:input_type -> backend.proto.customer.v1.CreateViewRequest
	55, // 74: backend.proto.customer.v1.CustomerService.UpdateView:input_type -> backend.proto.customer.v1.UpdateViewRequest
	57, // 75: backend.proto.customer.v1.CustomerService.ListViews:input_type -> backend.proto.customer.v1.ListViewsRequest
	59, // 76: backend.proto.customer.v1.CustomerService.DeleteView:input_type -> backend.proto.customer.v1.DeleteViewRequest
	6,  // 77: backend.proto.customer.v1.CustomerService.CreateCustomer:output_type -> backend.proto.customer.v1.CreateCustomerResponse
	8,  // 78: backend.proto.customer.v1.CustomerService.UpdateCustomer:output_type -> backend.proto.customer.v1.UpdateCustomerResponse
	10, // 79: backend.proto.customer.v1.CustomerService.ListCustomers:output_type -> backend.proto.customer.v1.ListCustomersResponse
	68, // 80: backend.proto.customer.v1.CustomerService.GetCustomer:output_type -> backend.proto.customer.v1.Customer
	90, // 81: backend.proto.customer.v1.CustomerService.DeleteCustomer:output_type -> google.protobuf.Empty
	14, // 82: backend.proto.customer.v1.CustomerService.SyncCustomerSearch:output_type -> backend.proto.customer.v1.SyncCustomerSearchResponse
	20, // 83: backend.proto.customer.v1.CustomerService.CreateCustomerHistoryLog:output_type -> backend.proto.customer.v1.CreateCustomerHistoryLogResponse
	22, // 84: backend.proto.customer.v1.CustomerService.UpdateCustomerHistoryLog:output_type -> backend.proto.customer.v1.UpdateCustomerHistoryLogResponse
	24, // 85: backend.proto.customer.v1.CustomerService.ListCustomerHistoryLogs:output_type -> backend.proto.customer.v1.ListCustomerHistoryLogsResponse
	16, // 86: backend.proto.customer.v1.CustomerService.ConvertCustomer:output_type -> backend.proto.customer.v1.ConvertCustomerResponse
	18, // 87: backend.proto.customer.v1.CustomerService.ConvertCustomersAttribute:output_type -> backend.proto.customer.v1.ConvertCustomersAttributeResponse
	26, // 88: backend.proto.customer.v1.CustomerService.CreateCustomerTask:output_type -> backend.proto.customer.v1.CreateCustomerTaskResponse
	28, // 89: backend.proto.customer.v1.CustomerService.UpdateCustomerTask:output_type -> backend.proto.customer.v1.UpdateCustomerTaskResponse
	30, // 90: backend.proto.customer.v1.CustomerService.ListCustomerTasks:output_type -> backend.proto.customer.v1.ListCustomerTasksResponse
	90, // 91: backend.proto.customer.v1.CustomerService.DeleteCustomerTask:output_type -> google.protobuf.Empty
	33, // 92: backend.proto.customer.v1.CustomerService.CreateAddress:output_type -> backend.proto.customer.v1.CreateAddressResponse
	35, // 93: backend.proto.customer.v1.CustomerService.UpdateAddress:output_type -> backend.proto.customer.v1.UpdateAddressResponse
	90, // 94: backend.proto.customer.v1.CustomerService.DeleteAddress:output_type -> google.protobuf.Empty
	38, // 95: backend.proto.customer.v1.CustomerService.ListAddresses:output_type -> backend.proto.customer.v1.ListAddressesResponse
	40, // 96: backend.proto.customer.v1.CustomerService.CreateLifeCycle:output_type -> backend.proto.customer.v1.CreateLifeCycleResponse
	42, // 97: backend.proto.customer.v1.CustomerService.UpdateLifeCycles:output_type -> backend.proto.customer.v1.UpdateLifeCyclesResponse
	44, // 98: backend.proto.customer.v1.CustomerService.ListLifeCycles:output_type -> backend.proto.customer.v1.ListLifeCyclesResponse
	90, // 99: backend.proto.customer.v1.CustomerService.DeleteLifeCycle:output_type -> google.protobuf.Empty
	47, // 100: backend.proto.customer.v1.CustomerService.CreateActionState:output_type -> backend.proto.customer.v1.CreateActionStateResponse
	49, // 101: backend.proto.customer.v1.CustomerService.UpdateActionStates:output_type -> backend.proto.customer.v1.UpdateActionsStatesResponse
	51, // 102: backend.proto.customer.v1.CustomerService.ListActionStates:output_type -> backend.proto.customer.v1.ListActionStatesResponse
	90, // 103: backend.proto.customer.v1.CustomerService.DeleteActionState:output_type -> google.protobuf.Empty
	54, // 104: backend.proto.customer.v1.CustomerService.CreateView:output_type -> backend.proto.customer.v1.CreateViewResponse
	56, // 105: backend.proto.customer.v1.CustomerService.UpdateView:output_type -> backend.proto.customer.v1.UpdateViewResponse
	58, // 106: backend.proto.customer.v1.CustomerService.ListViews:output_type -> backend.proto.customer.v1.ListViewsResponse
	90, // 107: backend.proto.customer.v1.CustomerService.DeleteView:output_type -> google.protobuf.Empty
	77, // [77:108] is the sub-list for method output_type
	46, // [46:77] is the sub-list for method input_type
	46, // [46:46] is the sub-list for extension type_name
	46, // [46:46] is the sub-list for extension extendee
	0,  // [0:46] is the sub-list for field type_name
}

func init() { file_backend_proto_customer_v1_customer_service_proto_init() }
func file_backend_proto_customer_v1_customer_service_proto_init() {
	if File_backend_proto_customer_v1_customer_service_proto != nil {
		return
	}
	file_backend_proto_customer_v1_customer_proto_init()
	file_backend_proto_customer_v1_customer_view_models_proto_init()
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[4].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[12].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[14].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[16].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[18].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[20].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[22].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[32].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[50].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[52].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[55].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[56].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[57].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[58].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[59].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[60].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[61].OneofWrappers = []any{}
	file_backend_proto_customer_v1_customer_service_proto_msgTypes[62].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_customer_v1_customer_service_proto_rawDesc), len(file_backend_proto_customer_v1_customer_service_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   63,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_customer_v1_customer_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_customer_v1_customer_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_customer_v1_customer_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_customer_v1_customer_service_proto_msgTypes,
	}.Build()
	File_backend_proto_customer_v1_customer_service_proto = out.File
	file_backend_proto_customer_v1_customer_service_proto_goTypes = nil
	file_backend_proto_customer_v1_customer_service_proto_depIdxs = nil
}
