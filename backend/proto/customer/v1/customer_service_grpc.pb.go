// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0148::human-names=disabled
//     aip.dev/not-precedent: 使用first_name和last_name作为字段名 --)
// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用_name后缀作为字段名 --)
// (-- api-linter: core::0132::request-field-types=disabled
//     aip.dev/not-precedent: 使用自定义过滤器类型 --)
// (-- api-linter: core::0132::request-parent-required=disabled
//     aip.dev/not-precedent: 使用customer_id作为标识符 --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用自定义响应字段 --)
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: 使用自定义请求字段结构 --)
// (-- api-linter: core::0122::name-suffix=disabled
//     aip.dev/not-precedent: 使用name后缀 --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/customer/v1/customer_service.proto

package customerpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CustomerService_CreateCustomer_FullMethodName            = "/backend.proto.customer.v1.CustomerService/CreateCustomer"
	CustomerService_UpdateCustomer_FullMethodName            = "/backend.proto.customer.v1.CustomerService/UpdateCustomer"
	CustomerService_ListCustomers_FullMethodName             = "/backend.proto.customer.v1.CustomerService/ListCustomers"
	CustomerService_GetCustomer_FullMethodName               = "/backend.proto.customer.v1.CustomerService/GetCustomer"
	CustomerService_DeleteCustomer_FullMethodName            = "/backend.proto.customer.v1.CustomerService/DeleteCustomer"
	CustomerService_SyncCustomerSearch_FullMethodName        = "/backend.proto.customer.v1.CustomerService/SyncCustomerSearch"
	CustomerService_CreateCustomerHistoryLog_FullMethodName  = "/backend.proto.customer.v1.CustomerService/CreateCustomerHistoryLog"
	CustomerService_UpdateCustomerHistoryLog_FullMethodName  = "/backend.proto.customer.v1.CustomerService/UpdateCustomerHistoryLog"
	CustomerService_ListCustomerHistoryLogs_FullMethodName   = "/backend.proto.customer.v1.CustomerService/ListCustomerHistoryLogs"
	CustomerService_ConvertCustomer_FullMethodName           = "/backend.proto.customer.v1.CustomerService/ConvertCustomer"
	CustomerService_ConvertCustomersAttribute_FullMethodName = "/backend.proto.customer.v1.CustomerService/ConvertCustomersAttribute"
	CustomerService_CreateCustomerTask_FullMethodName        = "/backend.proto.customer.v1.CustomerService/CreateCustomerTask"
	CustomerService_UpdateCustomerTask_FullMethodName        = "/backend.proto.customer.v1.CustomerService/UpdateCustomerTask"
	CustomerService_ListCustomerTasks_FullMethodName         = "/backend.proto.customer.v1.CustomerService/ListCustomerTasks"
	CustomerService_DeleteCustomerTask_FullMethodName        = "/backend.proto.customer.v1.CustomerService/DeleteCustomerTask"
	CustomerService_CreateAddress_FullMethodName             = "/backend.proto.customer.v1.CustomerService/CreateAddress"
	CustomerService_UpdateAddress_FullMethodName             = "/backend.proto.customer.v1.CustomerService/UpdateAddress"
	CustomerService_DeleteAddress_FullMethodName             = "/backend.proto.customer.v1.CustomerService/DeleteAddress"
	CustomerService_ListAddresses_FullMethodName             = "/backend.proto.customer.v1.CustomerService/ListAddresses"
	CustomerService_CreateLifeCycle_FullMethodName           = "/backend.proto.customer.v1.CustomerService/CreateLifeCycle"
	CustomerService_UpdateLifeCycles_FullMethodName          = "/backend.proto.customer.v1.CustomerService/UpdateLifeCycles"
	CustomerService_ListLifeCycles_FullMethodName            = "/backend.proto.customer.v1.CustomerService/ListLifeCycles"
	CustomerService_DeleteLifeCycle_FullMethodName           = "/backend.proto.customer.v1.CustomerService/DeleteLifeCycle"
	CustomerService_CreateActionState_FullMethodName         = "/backend.proto.customer.v1.CustomerService/CreateActionState"
	CustomerService_UpdateActionStates_FullMethodName        = "/backend.proto.customer.v1.CustomerService/UpdateActionStates"
	CustomerService_ListActionStates_FullMethodName          = "/backend.proto.customer.v1.CustomerService/ListActionStates"
	CustomerService_DeleteActionState_FullMethodName         = "/backend.proto.customer.v1.CustomerService/DeleteActionState"
	CustomerService_CreateView_FullMethodName                = "/backend.proto.customer.v1.CustomerService/CreateView"
	CustomerService_UpdateView_FullMethodName                = "/backend.proto.customer.v1.CustomerService/UpdateView"
	CustomerService_ListViews_FullMethodName                 = "/backend.proto.customer.v1.CustomerService/ListViews"
	CustomerService_DeleteView_FullMethodName                = "/backend.proto.customer.v1.CustomerService/DeleteView"
)

// CustomerServiceClient is the client API for CustomerService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CustomerService 提供客户管理相关的服务
type CustomerServiceClient interface {
	// CreateCustomer 创建新客户
	CreateCustomer(ctx context.Context, in *CreateCustomerRequest, opts ...grpc.CallOption) (*CreateCustomerResponse, error)
	// UpdateCustomer 更新客户信息
	UpdateCustomer(ctx context.Context, in *UpdateCustomerRequest, opts ...grpc.CallOption) (*UpdateCustomerResponse, error)
	// ListCustomers 获取客户列表
	ListCustomers(ctx context.Context, in *ListCustomersRequest, opts ...grpc.CallOption) (*ListCustomersResponse, error)
	// GetCustomer 获取客户详情
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetCustomer(ctx context.Context, in *GetCustomerRequest, opts ...grpc.CallOption) (*Customer, error)
	// deleted 删除客户
	DeleteCustomer(ctx context.Context, in *DeleteCustomerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// SyncCustomerSearch 同步客户数据到搜索
	SyncCustomerSearch(ctx context.Context, in *SyncCustomerSearchRequest, opts ...grpc.CallOption) (*SyncCustomerSearchResponse, error)
	// CreateCustomerHistoryLog 创建客户活动日志
	CreateCustomerHistoryLog(ctx context.Context, in *CreateCustomerHistoryLogRequest, opts ...grpc.CallOption) (*CreateCustomerHistoryLogResponse, error)
	// ListCustomerHistoryLogs 获取客户历史记录
	UpdateCustomerHistoryLog(ctx context.Context, in *UpdateCustomerHistoryLogRequest, opts ...grpc.CallOption) (*UpdateCustomerHistoryLogResponse, error)
	// ListCustomerHistoryLogs 获取客户历史记录
	ListCustomerHistoryLogs(ctx context.Context, in *ListCustomerHistoryLogsRequest, opts ...grpc.CallOption) (*ListCustomerHistoryLogsResponse, error)
	// ConvertCustomer 转换客户状态
	ConvertCustomer(ctx context.Context, in *ConvertCustomerRequest, opts ...grpc.CallOption) (*ConvertCustomerResponse, error)
	// ConvertCustomersAttribute 批量转换客户属性
	ConvertCustomersAttribute(ctx context.Context, in *ConvertCustomersAttributeRequest, opts ...grpc.CallOption) (*ConvertCustomersAttributeResponse, error)
	// CreateCustomerTask 创建客户任务
	CreateCustomerTask(ctx context.Context, in *CreateCustomerTaskRequest, opts ...grpc.CallOption) (*CreateCustomerTaskResponse, error)
	// UpdateCustomerTask 更新客户任务
	UpdateCustomerTask(ctx context.Context, in *UpdateCustomerTaskRequest, opts ...grpc.CallOption) (*UpdateCustomerTaskResponse, error)
	// ListCustomerTasks 获取客户任务列表
	ListCustomerTasks(ctx context.Context, in *ListCustomerTasksRequest, opts ...grpc.CallOption) (*ListCustomerTasksResponse, error)
	// DeleteCustomerTask 删除客户任务列表
	DeleteCustomerTask(ctx context.Context, in *DeleteCustomerTaskRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// Address
	// CreateAddress 创建地址
	CreateAddress(ctx context.Context, in *CreateAddressRequest, opts ...grpc.CallOption) (*CreateAddressResponse, error)
	// UpdateAddress 更新地址
	UpdateAddress(ctx context.Context, in *UpdateAddressRequest, opts ...grpc.CallOption) (*UpdateAddressResponse, error)
	// DeleteAddress 删除地址
	DeleteAddress(ctx context.Context, in *DeleteAddressRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// ListAddresses 获取地址列表
	ListAddresses(ctx context.Context, in *ListAddressesRequest, opts ...grpc.CallOption) (*ListAddressesResponse, error)
	// LifeCycle
	// CreateLifeCycle 创建生命周期
	CreateLifeCycle(ctx context.Context, in *CreateLifeCycleRequest, opts ...grpc.CallOption) (*CreateLifeCycleResponse, error)
	// UpdateLifeCycle 更新生命周期
	UpdateLifeCycles(ctx context.Context, in *UpdateLifeCyclesRequest, opts ...grpc.CallOption) (*UpdateLifeCyclesResponse, error)
	// ListLifeCycles 获取生命周期列表
	ListLifeCycles(ctx context.Context, in *ListLifeCyclesRequest, opts ...grpc.CallOption) (*ListLifeCyclesResponse, error)
	// DeleteLifeCycle 删除生命周期
	DeleteLifeCycle(ctx context.Context, in *DeleteLifeCycleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// ActionState
	// CreateActionState 创建行动状态
	CreateActionState(ctx context.Context, in *CreateActionStateRequest, opts ...grpc.CallOption) (*CreateActionStateResponse, error)
	// UpdateActionState 更新行动状态
	UpdateActionStates(ctx context.Context, in *UpdateActionStatesRequest, opts ...grpc.CallOption) (*UpdateActionsStatesResponse, error)
	// ListActionStates 获取行动状态列表
	ListActionStates(ctx context.Context, in *ListActionStatesRequest, opts ...grpc.CallOption) (*ListActionStatesResponse, error)
	// DeleteActionState 删除行动状态
	DeleteActionState(ctx context.Context, in *DeleteActionStateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// View
	// CreateView 创建用户视图
	CreateView(ctx context.Context, in *CreateViewRequest, opts ...grpc.CallOption) (*CreateViewResponse, error)
	// UpdateView 更新用户视图
	UpdateView(ctx context.Context, in *UpdateViewRequest, opts ...grpc.CallOption) (*UpdateViewResponse, error)
	// ListViews 获取用户视图列表
	ListViews(ctx context.Context, in *ListViewsRequest, opts ...grpc.CallOption) (*ListViewsResponse, error)
	// DeleteView 删除用户视图
	DeleteView(ctx context.Context, in *DeleteViewRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type customerServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCustomerServiceClient(cc grpc.ClientConnInterface) CustomerServiceClient {
	return &customerServiceClient{cc}
}

func (c *customerServiceClient) CreateCustomer(ctx context.Context, in *CreateCustomerRequest, opts ...grpc.CallOption) (*CreateCustomerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCustomerResponse)
	err := c.cc.Invoke(ctx, CustomerService_CreateCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) UpdateCustomer(ctx context.Context, in *UpdateCustomerRequest, opts ...grpc.CallOption) (*UpdateCustomerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCustomerResponse)
	err := c.cc.Invoke(ctx, CustomerService_UpdateCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) ListCustomers(ctx context.Context, in *ListCustomersRequest, opts ...grpc.CallOption) (*ListCustomersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomersResponse)
	err := c.cc.Invoke(ctx, CustomerService_ListCustomers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) GetCustomer(ctx context.Context, in *GetCustomerRequest, opts ...grpc.CallOption) (*Customer, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Customer)
	err := c.cc.Invoke(ctx, CustomerService_GetCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) DeleteCustomer(ctx context.Context, in *DeleteCustomerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CustomerService_DeleteCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) SyncCustomerSearch(ctx context.Context, in *SyncCustomerSearchRequest, opts ...grpc.CallOption) (*SyncCustomerSearchResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncCustomerSearchResponse)
	err := c.cc.Invoke(ctx, CustomerService_SyncCustomerSearch_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) CreateCustomerHistoryLog(ctx context.Context, in *CreateCustomerHistoryLogRequest, opts ...grpc.CallOption) (*CreateCustomerHistoryLogResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCustomerHistoryLogResponse)
	err := c.cc.Invoke(ctx, CustomerService_CreateCustomerHistoryLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) UpdateCustomerHistoryLog(ctx context.Context, in *UpdateCustomerHistoryLogRequest, opts ...grpc.CallOption) (*UpdateCustomerHistoryLogResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCustomerHistoryLogResponse)
	err := c.cc.Invoke(ctx, CustomerService_UpdateCustomerHistoryLog_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) ListCustomerHistoryLogs(ctx context.Context, in *ListCustomerHistoryLogsRequest, opts ...grpc.CallOption) (*ListCustomerHistoryLogsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomerHistoryLogsResponse)
	err := c.cc.Invoke(ctx, CustomerService_ListCustomerHistoryLogs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) ConvertCustomer(ctx context.Context, in *ConvertCustomerRequest, opts ...grpc.CallOption) (*ConvertCustomerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConvertCustomerResponse)
	err := c.cc.Invoke(ctx, CustomerService_ConvertCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) ConvertCustomersAttribute(ctx context.Context, in *ConvertCustomersAttributeRequest, opts ...grpc.CallOption) (*ConvertCustomersAttributeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ConvertCustomersAttributeResponse)
	err := c.cc.Invoke(ctx, CustomerService_ConvertCustomersAttribute_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) CreateCustomerTask(ctx context.Context, in *CreateCustomerTaskRequest, opts ...grpc.CallOption) (*CreateCustomerTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateCustomerTaskResponse)
	err := c.cc.Invoke(ctx, CustomerService_CreateCustomerTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) UpdateCustomerTask(ctx context.Context, in *UpdateCustomerTaskRequest, opts ...grpc.CallOption) (*UpdateCustomerTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateCustomerTaskResponse)
	err := c.cc.Invoke(ctx, CustomerService_UpdateCustomerTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) ListCustomerTasks(ctx context.Context, in *ListCustomerTasksRequest, opts ...grpc.CallOption) (*ListCustomerTasksResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCustomerTasksResponse)
	err := c.cc.Invoke(ctx, CustomerService_ListCustomerTasks_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) DeleteCustomerTask(ctx context.Context, in *DeleteCustomerTaskRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CustomerService_DeleteCustomerTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) CreateAddress(ctx context.Context, in *CreateAddressRequest, opts ...grpc.CallOption) (*CreateAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateAddressResponse)
	err := c.cc.Invoke(ctx, CustomerService_CreateAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) UpdateAddress(ctx context.Context, in *UpdateAddressRequest, opts ...grpc.CallOption) (*UpdateAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateAddressResponse)
	err := c.cc.Invoke(ctx, CustomerService_UpdateAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) DeleteAddress(ctx context.Context, in *DeleteAddressRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CustomerService_DeleteAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) ListAddresses(ctx context.Context, in *ListAddressesRequest, opts ...grpc.CallOption) (*ListAddressesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListAddressesResponse)
	err := c.cc.Invoke(ctx, CustomerService_ListAddresses_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) CreateLifeCycle(ctx context.Context, in *CreateLifeCycleRequest, opts ...grpc.CallOption) (*CreateLifeCycleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateLifeCycleResponse)
	err := c.cc.Invoke(ctx, CustomerService_CreateLifeCycle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) UpdateLifeCycles(ctx context.Context, in *UpdateLifeCyclesRequest, opts ...grpc.CallOption) (*UpdateLifeCyclesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateLifeCyclesResponse)
	err := c.cc.Invoke(ctx, CustomerService_UpdateLifeCycles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) ListLifeCycles(ctx context.Context, in *ListLifeCyclesRequest, opts ...grpc.CallOption) (*ListLifeCyclesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListLifeCyclesResponse)
	err := c.cc.Invoke(ctx, CustomerService_ListLifeCycles_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) DeleteLifeCycle(ctx context.Context, in *DeleteLifeCycleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CustomerService_DeleteLifeCycle_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) CreateActionState(ctx context.Context, in *CreateActionStateRequest, opts ...grpc.CallOption) (*CreateActionStateResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateActionStateResponse)
	err := c.cc.Invoke(ctx, CustomerService_CreateActionState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) UpdateActionStates(ctx context.Context, in *UpdateActionStatesRequest, opts ...grpc.CallOption) (*UpdateActionsStatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateActionsStatesResponse)
	err := c.cc.Invoke(ctx, CustomerService_UpdateActionStates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) ListActionStates(ctx context.Context, in *ListActionStatesRequest, opts ...grpc.CallOption) (*ListActionStatesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListActionStatesResponse)
	err := c.cc.Invoke(ctx, CustomerService_ListActionStates_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) DeleteActionState(ctx context.Context, in *DeleteActionStateRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CustomerService_DeleteActionState_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) CreateView(ctx context.Context, in *CreateViewRequest, opts ...grpc.CallOption) (*CreateViewResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateViewResponse)
	err := c.cc.Invoke(ctx, CustomerService_CreateView_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) UpdateView(ctx context.Context, in *UpdateViewRequest, opts ...grpc.CallOption) (*UpdateViewResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateViewResponse)
	err := c.cc.Invoke(ctx, CustomerService_UpdateView_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) ListViews(ctx context.Context, in *ListViewsRequest, opts ...grpc.CallOption) (*ListViewsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListViewsResponse)
	err := c.cc.Invoke(ctx, CustomerService_ListViews_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *customerServiceClient) DeleteView(ctx context.Context, in *DeleteViewRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, CustomerService_DeleteView_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CustomerServiceServer is the server API for CustomerService service.
// All implementations must embed UnimplementedCustomerServiceServer
// for forward compatibility.
//
// CustomerService 提供客户管理相关的服务
type CustomerServiceServer interface {
	// CreateCustomer 创建新客户
	CreateCustomer(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error)
	// UpdateCustomer 更新客户信息
	UpdateCustomer(context.Context, *UpdateCustomerRequest) (*UpdateCustomerResponse, error)
	// ListCustomers 获取客户列表
	ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error)
	// GetCustomer 获取客户详情
	// buf:lint:ignore RPC_RESPONSE_STANDARD_NAME
	GetCustomer(context.Context, *GetCustomerRequest) (*Customer, error)
	// deleted 删除客户
	DeleteCustomer(context.Context, *DeleteCustomerRequest) (*emptypb.Empty, error)
	// SyncCustomerSearch 同步客户数据到搜索
	SyncCustomerSearch(context.Context, *SyncCustomerSearchRequest) (*SyncCustomerSearchResponse, error)
	// CreateCustomerHistoryLog 创建客户活动日志
	CreateCustomerHistoryLog(context.Context, *CreateCustomerHistoryLogRequest) (*CreateCustomerHistoryLogResponse, error)
	// ListCustomerHistoryLogs 获取客户历史记录
	UpdateCustomerHistoryLog(context.Context, *UpdateCustomerHistoryLogRequest) (*UpdateCustomerHistoryLogResponse, error)
	// ListCustomerHistoryLogs 获取客户历史记录
	ListCustomerHistoryLogs(context.Context, *ListCustomerHistoryLogsRequest) (*ListCustomerHistoryLogsResponse, error)
	// ConvertCustomer 转换客户状态
	ConvertCustomer(context.Context, *ConvertCustomerRequest) (*ConvertCustomerResponse, error)
	// ConvertCustomersAttribute 批量转换客户属性
	ConvertCustomersAttribute(context.Context, *ConvertCustomersAttributeRequest) (*ConvertCustomersAttributeResponse, error)
	// CreateCustomerTask 创建客户任务
	CreateCustomerTask(context.Context, *CreateCustomerTaskRequest) (*CreateCustomerTaskResponse, error)
	// UpdateCustomerTask 更新客户任务
	UpdateCustomerTask(context.Context, *UpdateCustomerTaskRequest) (*UpdateCustomerTaskResponse, error)
	// ListCustomerTasks 获取客户任务列表
	ListCustomerTasks(context.Context, *ListCustomerTasksRequest) (*ListCustomerTasksResponse, error)
	// DeleteCustomerTask 删除客户任务列表
	DeleteCustomerTask(context.Context, *DeleteCustomerTaskRequest) (*emptypb.Empty, error)
	// Address
	// CreateAddress 创建地址
	CreateAddress(context.Context, *CreateAddressRequest) (*CreateAddressResponse, error)
	// UpdateAddress 更新地址
	UpdateAddress(context.Context, *UpdateAddressRequest) (*UpdateAddressResponse, error)
	// DeleteAddress 删除地址
	DeleteAddress(context.Context, *DeleteAddressRequest) (*emptypb.Empty, error)
	// ListAddresses 获取地址列表
	ListAddresses(context.Context, *ListAddressesRequest) (*ListAddressesResponse, error)
	// LifeCycle
	// CreateLifeCycle 创建生命周期
	CreateLifeCycle(context.Context, *CreateLifeCycleRequest) (*CreateLifeCycleResponse, error)
	// UpdateLifeCycle 更新生命周期
	UpdateLifeCycles(context.Context, *UpdateLifeCyclesRequest) (*UpdateLifeCyclesResponse, error)
	// ListLifeCycles 获取生命周期列表
	ListLifeCycles(context.Context, *ListLifeCyclesRequest) (*ListLifeCyclesResponse, error)
	// DeleteLifeCycle 删除生命周期
	DeleteLifeCycle(context.Context, *DeleteLifeCycleRequest) (*emptypb.Empty, error)
	// ActionState
	// CreateActionState 创建行动状态
	CreateActionState(context.Context, *CreateActionStateRequest) (*CreateActionStateResponse, error)
	// UpdateActionState 更新行动状态
	UpdateActionStates(context.Context, *UpdateActionStatesRequest) (*UpdateActionsStatesResponse, error)
	// ListActionStates 获取行动状态列表
	ListActionStates(context.Context, *ListActionStatesRequest) (*ListActionStatesResponse, error)
	// DeleteActionState 删除行动状态
	DeleteActionState(context.Context, *DeleteActionStateRequest) (*emptypb.Empty, error)
	// View
	// CreateView 创建用户视图
	CreateView(context.Context, *CreateViewRequest) (*CreateViewResponse, error)
	// UpdateView 更新用户视图
	UpdateView(context.Context, *UpdateViewRequest) (*UpdateViewResponse, error)
	// ListViews 获取用户视图列表
	ListViews(context.Context, *ListViewsRequest) (*ListViewsResponse, error)
	// DeleteView 删除用户视图
	DeleteView(context.Context, *DeleteViewRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedCustomerServiceServer()
}

// UnimplementedCustomerServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCustomerServiceServer struct{}

func (UnimplementedCustomerServiceServer) CreateCustomer(context.Context, *CreateCustomerRequest) (*CreateCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomer not implemented")
}
func (UnimplementedCustomerServiceServer) UpdateCustomer(context.Context, *UpdateCustomerRequest) (*UpdateCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomer not implemented")
}
func (UnimplementedCustomerServiceServer) ListCustomers(context.Context, *ListCustomersRequest) (*ListCustomersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomers not implemented")
}
func (UnimplementedCustomerServiceServer) GetCustomer(context.Context, *GetCustomerRequest) (*Customer, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomer not implemented")
}
func (UnimplementedCustomerServiceServer) DeleteCustomer(context.Context, *DeleteCustomerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomer not implemented")
}
func (UnimplementedCustomerServiceServer) SyncCustomerSearch(context.Context, *SyncCustomerSearchRequest) (*SyncCustomerSearchResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncCustomerSearch not implemented")
}
func (UnimplementedCustomerServiceServer) CreateCustomerHistoryLog(context.Context, *CreateCustomerHistoryLogRequest) (*CreateCustomerHistoryLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomerHistoryLog not implemented")
}
func (UnimplementedCustomerServiceServer) UpdateCustomerHistoryLog(context.Context, *UpdateCustomerHistoryLogRequest) (*UpdateCustomerHistoryLogResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomerHistoryLog not implemented")
}
func (UnimplementedCustomerServiceServer) ListCustomerHistoryLogs(context.Context, *ListCustomerHistoryLogsRequest) (*ListCustomerHistoryLogsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerHistoryLogs not implemented")
}
func (UnimplementedCustomerServiceServer) ConvertCustomer(context.Context, *ConvertCustomerRequest) (*ConvertCustomerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertCustomer not implemented")
}
func (UnimplementedCustomerServiceServer) ConvertCustomersAttribute(context.Context, *ConvertCustomersAttributeRequest) (*ConvertCustomersAttributeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ConvertCustomersAttribute not implemented")
}
func (UnimplementedCustomerServiceServer) CreateCustomerTask(context.Context, *CreateCustomerTaskRequest) (*CreateCustomerTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateCustomerTask not implemented")
}
func (UnimplementedCustomerServiceServer) UpdateCustomerTask(context.Context, *UpdateCustomerTaskRequest) (*UpdateCustomerTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateCustomerTask not implemented")
}
func (UnimplementedCustomerServiceServer) ListCustomerTasks(context.Context, *ListCustomerTasksRequest) (*ListCustomerTasksResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCustomerTasks not implemented")
}
func (UnimplementedCustomerServiceServer) DeleteCustomerTask(context.Context, *DeleteCustomerTaskRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteCustomerTask not implemented")
}
func (UnimplementedCustomerServiceServer) CreateAddress(context.Context, *CreateAddressRequest) (*CreateAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAddress not implemented")
}
func (UnimplementedCustomerServiceServer) UpdateAddress(context.Context, *UpdateAddressRequest) (*UpdateAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAddress not implemented")
}
func (UnimplementedCustomerServiceServer) DeleteAddress(context.Context, *DeleteAddressRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAddress not implemented")
}
func (UnimplementedCustomerServiceServer) ListAddresses(context.Context, *ListAddressesRequest) (*ListAddressesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListAddresses not implemented")
}
func (UnimplementedCustomerServiceServer) CreateLifeCycle(context.Context, *CreateLifeCycleRequest) (*CreateLifeCycleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateLifeCycle not implemented")
}
func (UnimplementedCustomerServiceServer) UpdateLifeCycles(context.Context, *UpdateLifeCyclesRequest) (*UpdateLifeCyclesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateLifeCycles not implemented")
}
func (UnimplementedCustomerServiceServer) ListLifeCycles(context.Context, *ListLifeCyclesRequest) (*ListLifeCyclesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListLifeCycles not implemented")
}
func (UnimplementedCustomerServiceServer) DeleteLifeCycle(context.Context, *DeleteLifeCycleRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteLifeCycle not implemented")
}
func (UnimplementedCustomerServiceServer) CreateActionState(context.Context, *CreateActionStateRequest) (*CreateActionStateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateActionState not implemented")
}
func (UnimplementedCustomerServiceServer) UpdateActionStates(context.Context, *UpdateActionStatesRequest) (*UpdateActionsStatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateActionStates not implemented")
}
func (UnimplementedCustomerServiceServer) ListActionStates(context.Context, *ListActionStatesRequest) (*ListActionStatesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListActionStates not implemented")
}
func (UnimplementedCustomerServiceServer) DeleteActionState(context.Context, *DeleteActionStateRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteActionState not implemented")
}
func (UnimplementedCustomerServiceServer) CreateView(context.Context, *CreateViewRequest) (*CreateViewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateView not implemented")
}
func (UnimplementedCustomerServiceServer) UpdateView(context.Context, *UpdateViewRequest) (*UpdateViewResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateView not implemented")
}
func (UnimplementedCustomerServiceServer) ListViews(context.Context, *ListViewsRequest) (*ListViewsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListViews not implemented")
}
func (UnimplementedCustomerServiceServer) DeleteView(context.Context, *DeleteViewRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteView not implemented")
}
func (UnimplementedCustomerServiceServer) mustEmbedUnimplementedCustomerServiceServer() {}
func (UnimplementedCustomerServiceServer) testEmbeddedByValue()                         {}

// UnsafeCustomerServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CustomerServiceServer will
// result in compilation errors.
type UnsafeCustomerServiceServer interface {
	mustEmbedUnimplementedCustomerServiceServer()
}

func RegisterCustomerServiceServer(s grpc.ServiceRegistrar, srv CustomerServiceServer) {
	// If the following call pancis, it indicates UnimplementedCustomerServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CustomerService_ServiceDesc, srv)
}

func _CustomerService_CreateCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).CreateCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_CreateCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).CreateCustomer(ctx, req.(*CreateCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_UpdateCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).UpdateCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_UpdateCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).UpdateCustomer(ctx, req.(*UpdateCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_ListCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).ListCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_ListCustomers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).ListCustomers(ctx, req.(*ListCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_GetCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).GetCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_GetCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).GetCustomer(ctx, req.(*GetCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_DeleteCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).DeleteCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_DeleteCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).DeleteCustomer(ctx, req.(*DeleteCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_SyncCustomerSearch_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncCustomerSearchRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).SyncCustomerSearch(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_SyncCustomerSearch_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).SyncCustomerSearch(ctx, req.(*SyncCustomerSearchRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_CreateCustomerHistoryLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerHistoryLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).CreateCustomerHistoryLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_CreateCustomerHistoryLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).CreateCustomerHistoryLog(ctx, req.(*CreateCustomerHistoryLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_UpdateCustomerHistoryLog_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerHistoryLogRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).UpdateCustomerHistoryLog(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_UpdateCustomerHistoryLog_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).UpdateCustomerHistoryLog(ctx, req.(*UpdateCustomerHistoryLogRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_ListCustomerHistoryLogs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerHistoryLogsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).ListCustomerHistoryLogs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_ListCustomerHistoryLogs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).ListCustomerHistoryLogs(ctx, req.(*ListCustomerHistoryLogsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_ConvertCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).ConvertCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_ConvertCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).ConvertCustomer(ctx, req.(*ConvertCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_ConvertCustomersAttribute_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ConvertCustomersAttributeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).ConvertCustomersAttribute(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_ConvertCustomersAttribute_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).ConvertCustomersAttribute(ctx, req.(*ConvertCustomersAttributeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_CreateCustomerTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateCustomerTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).CreateCustomerTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_CreateCustomerTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).CreateCustomerTask(ctx, req.(*CreateCustomerTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_UpdateCustomerTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateCustomerTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).UpdateCustomerTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_UpdateCustomerTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).UpdateCustomerTask(ctx, req.(*UpdateCustomerTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_ListCustomerTasks_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCustomerTasksRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).ListCustomerTasks(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_ListCustomerTasks_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).ListCustomerTasks(ctx, req.(*ListCustomerTasksRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_DeleteCustomerTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteCustomerTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).DeleteCustomerTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_DeleteCustomerTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).DeleteCustomerTask(ctx, req.(*DeleteCustomerTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_CreateAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).CreateAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_CreateAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).CreateAddress(ctx, req.(*CreateAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_UpdateAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).UpdateAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_UpdateAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).UpdateAddress(ctx, req.(*UpdateAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_DeleteAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).DeleteAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_DeleteAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).DeleteAddress(ctx, req.(*DeleteAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_ListAddresses_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListAddressesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).ListAddresses(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_ListAddresses_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).ListAddresses(ctx, req.(*ListAddressesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_CreateLifeCycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateLifeCycleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).CreateLifeCycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_CreateLifeCycle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).CreateLifeCycle(ctx, req.(*CreateLifeCycleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_UpdateLifeCycles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateLifeCyclesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).UpdateLifeCycles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_UpdateLifeCycles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).UpdateLifeCycles(ctx, req.(*UpdateLifeCyclesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_ListLifeCycles_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListLifeCyclesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).ListLifeCycles(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_ListLifeCycles_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).ListLifeCycles(ctx, req.(*ListLifeCyclesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_DeleteLifeCycle_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteLifeCycleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).DeleteLifeCycle(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_DeleteLifeCycle_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).DeleteLifeCycle(ctx, req.(*DeleteLifeCycleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_CreateActionState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateActionStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).CreateActionState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_CreateActionState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).CreateActionState(ctx, req.(*CreateActionStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_UpdateActionStates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateActionStatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).UpdateActionStates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_UpdateActionStates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).UpdateActionStates(ctx, req.(*UpdateActionStatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_ListActionStates_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListActionStatesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).ListActionStates(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_ListActionStates_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).ListActionStates(ctx, req.(*ListActionStatesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_DeleteActionState_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteActionStateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).DeleteActionState(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_DeleteActionState_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).DeleteActionState(ctx, req.(*DeleteActionStateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_CreateView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateViewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).CreateView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_CreateView_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).CreateView(ctx, req.(*CreateViewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_UpdateView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateViewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).UpdateView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_UpdateView_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).UpdateView(ctx, req.(*UpdateViewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_ListViews_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListViewsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).ListViews(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_ListViews_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).ListViews(ctx, req.(*ListViewsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CustomerService_DeleteView_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteViewRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CustomerServiceServer).DeleteView(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CustomerService_DeleteView_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CustomerServiceServer).DeleteView(ctx, req.(*DeleteViewRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CustomerService_ServiceDesc is the grpc.ServiceDesc for CustomerService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CustomerService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.customer.v1.CustomerService",
	HandlerType: (*CustomerServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateCustomer",
			Handler:    _CustomerService_CreateCustomer_Handler,
		},
		{
			MethodName: "UpdateCustomer",
			Handler:    _CustomerService_UpdateCustomer_Handler,
		},
		{
			MethodName: "ListCustomers",
			Handler:    _CustomerService_ListCustomers_Handler,
		},
		{
			MethodName: "GetCustomer",
			Handler:    _CustomerService_GetCustomer_Handler,
		},
		{
			MethodName: "DeleteCustomer",
			Handler:    _CustomerService_DeleteCustomer_Handler,
		},
		{
			MethodName: "SyncCustomerSearch",
			Handler:    _CustomerService_SyncCustomerSearch_Handler,
		},
		{
			MethodName: "CreateCustomerHistoryLog",
			Handler:    _CustomerService_CreateCustomerHistoryLog_Handler,
		},
		{
			MethodName: "UpdateCustomerHistoryLog",
			Handler:    _CustomerService_UpdateCustomerHistoryLog_Handler,
		},
		{
			MethodName: "ListCustomerHistoryLogs",
			Handler:    _CustomerService_ListCustomerHistoryLogs_Handler,
		},
		{
			MethodName: "ConvertCustomer",
			Handler:    _CustomerService_ConvertCustomer_Handler,
		},
		{
			MethodName: "ConvertCustomersAttribute",
			Handler:    _CustomerService_ConvertCustomersAttribute_Handler,
		},
		{
			MethodName: "CreateCustomerTask",
			Handler:    _CustomerService_CreateCustomerTask_Handler,
		},
		{
			MethodName: "UpdateCustomerTask",
			Handler:    _CustomerService_UpdateCustomerTask_Handler,
		},
		{
			MethodName: "ListCustomerTasks",
			Handler:    _CustomerService_ListCustomerTasks_Handler,
		},
		{
			MethodName: "DeleteCustomerTask",
			Handler:    _CustomerService_DeleteCustomerTask_Handler,
		},
		{
			MethodName: "CreateAddress",
			Handler:    _CustomerService_CreateAddress_Handler,
		},
		{
			MethodName: "UpdateAddress",
			Handler:    _CustomerService_UpdateAddress_Handler,
		},
		{
			MethodName: "DeleteAddress",
			Handler:    _CustomerService_DeleteAddress_Handler,
		},
		{
			MethodName: "ListAddresses",
			Handler:    _CustomerService_ListAddresses_Handler,
		},
		{
			MethodName: "CreateLifeCycle",
			Handler:    _CustomerService_CreateLifeCycle_Handler,
		},
		{
			MethodName: "UpdateLifeCycles",
			Handler:    _CustomerService_UpdateLifeCycles_Handler,
		},
		{
			MethodName: "ListLifeCycles",
			Handler:    _CustomerService_ListLifeCycles_Handler,
		},
		{
			MethodName: "DeleteLifeCycle",
			Handler:    _CustomerService_DeleteLifeCycle_Handler,
		},
		{
			MethodName: "CreateActionState",
			Handler:    _CustomerService_CreateActionState_Handler,
		},
		{
			MethodName: "UpdateActionStates",
			Handler:    _CustomerService_UpdateActionStates_Handler,
		},
		{
			MethodName: "ListActionStates",
			Handler:    _CustomerService_ListActionStates_Handler,
		},
		{
			MethodName: "DeleteActionState",
			Handler:    _CustomerService_DeleteActionState_Handler,
		},
		{
			MethodName: "CreateView",
			Handler:    _CustomerService_CreateView_Handler,
		},
		{
			MethodName: "UpdateView",
			Handler:    _CustomerService_UpdateView_Handler,
		},
		{
			MethodName: "ListViews",
			Handler:    _CustomerService_ListViews_Handler,
		},
		{
			MethodName: "DeleteView",
			Handler:    _CustomerService_DeleteView_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/customer/v1/customer_service.proto",
}
