// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cluster resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cluster resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cluster resources. --)

syntax = "proto3";

package backend.proto.tools.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "backend/proto/tools/v1/resource_models.proto";
import "google/protobuf/struct.proto";
import "buf/validate/validate.proto";

// ClusterService
service ClusterService {

    // list clusters
    rpc ListClusters(ListClustersRequest) returns (ListClustersResponse);

    // get a cluster
    rpc GetCluster(GetClusterRequest) returns (Cluster);

    // list nodegroups
    rpc ListNodeGroups(ListNodeGroupsRequest) returns (ListNodeGroupsResponse);

    // get a nodegroup
    rpc GetNodeGroup(GetNodeGroupRequest) returns (NodeGroup);

    // list nodes
    rpc ListNodes(ListNodesRequest) returns (ListNodesResponse);

    // get a node
    // (-- api-linter: core::0131::response-message-name=disabled
    //     aip.dev/not-precedent: We need to do this because the fields of the resource may be dynamic. --)
    rpc GetNode(GetNodeRequest) returns (google.protobuf.Struct);

    // list namespaces
    rpc ListNamespaces(ListNamespacesRequest) returns (ListNamespacesResponse);

    // get a namespace
    // (-- api-linter: core::0131::response-message-name=disabled
    //     aip.dev/not-precedent: We need to do this because the fields of the resource may be dynamic. --)
    rpc GetNamespace(GetNamespaceRequest) returns (google.protobuf.Struct);

    // list resources
    rpc ListResources(ListResourcesRequest) returns (ListResourcesResponse);

    // get resource
    // (-- api-linter: core::0131::response-message-name=disabled
    //     aip.dev/not-precedent: We need to do this because the fields of the resource may be dynamic. --)
    rpc GetResource(GetResourceRequest) returns (google.protobuf.Struct);

    // reset nodegroup
    rpc ResetNodeGroups(ResetNodeGroupsRequest) returns (ResetNodeGroupsResponse);

    // reset resource
    rpc ResetResources(ResetResourcesRequest) returns (ResetResourcesResponse);

    // restart workloads
    rpc RestartWorkloads(RestartWorkloadsRequest) returns (RestartWorkloadsResponse);

    // deploy apps
    rpc DeployApplications(DeployApplicationsRequest) returns (DeployApplicationsResponse);

    // rpc ResetDatabaseSecret(ResetDatabaseSecretRequest) returns (ResetDatabaseSecretResponse);

    // rpc FlushSecrets(FlushSecretsRequest) returns (FlushSecretsResponse);

}


// ListClustersRequest
message ListClustersRequest {
    // the platform where the cluster is running
    optional string platform = 1 [(buf.validate.field).string.max_len = 128];
}

// ListClustersResponse
message ListClustersResponse {
    // list of clusters
    repeated Cluster clusters = 1;
}

// GetClusterRequest
message GetClusterRequest {
    // cluster identifier
    PlatformIdentifier identifier = 1;
}

// ListNodeGroupsRequest
message ListNodeGroupsRequest {
    // platform
    string platform = 1 [(buf.validate.field) = { string: { max_len: 128 } }];
    // cluster
    string cluster = 2 [(buf.validate.field) = { string: { max_len: 63 } }];
}

// ListNodeGroupsResponse
message ListNodeGroupsResponse {
    // list of node groups
    repeated NodeGroup node_groups = 1;
}

// GetNodeGroupRequest
message GetNodeGroupRequest {
    // nodegroup identifier
    NodeGroup.Identifier identifier = 1;
}

// ListNodesRequest
message ListNodesRequest {
    // platform
    string platform = 1 [(buf.validate.field) = { string: { max_len: 128 } }];
    // cluster
    string cluster = 2 [(buf.validate.field) = { string: { max_len: 63 } }];
}

// ListNodesResponse
message ListNodesResponse {
    // list of nodes
    repeated google.protobuf.Struct nodes = 1;
}

// GetNamespaceRequest
message GetNodeRequest {
    // cluster where node in
    string cluster = 1 [(buf.validate.field).string.max_len = 63];
    // node name
    string name = 2;
}

// ListNamespacesRequest
message ListNamespacesRequest {
    // platform
    string platform = 1 [(buf.validate.field) = { string: { max_len: 128 } }];
    // cluster
    string cluster = 2 [(buf.validate.field) = { string: { max_len: 63 } }];
}

// ListNamespacesResponse
message ListNamespacesResponse {
    // list of namespaces
    repeated google.protobuf.Struct namespaces = 1;
}

// GetNamespaceRequest
message GetNamespaceRequest {
    // namespace environment
    optional EnvironmentIdentifier environment = 1;
}

// ListResourcesRequest
message ListResourcesRequest {
    // cluster environment
    EnvironmentIdentifier environment = 1;
    // resource api version
    optional string api_version = 2;
    // resource kind
    string kind = 3;
    // page size
    int32 page_size = 4;
    // page token
    string page_token = 5;
}

// ListResourcesResponse
message ListResourcesResponse {
    // list of resources
    repeated google.protobuf.Struct resources = 1;
    // next page
    string next_page_token = 2;
}

// GetResourceRequest
message GetResourceRequest {
    // namespace environment
    optional EnvironmentIdentifier environment = 1;
    // resource api version
    optional string api_version = 2;
    // resource kind
    string kind = 3;
    // resource name
    string name = 4;
}

// ResetNodeGroupRequest
message ResetNodeGroupRequest {
    // nodegroup source identifier
    optional NodeGroup.Identifier source = 1;
    // nodegroup target identifier
    optional NodeGroup.Identifier target = 2;
    // The instance class of the node group
    optional string instance_class = 3;
    // The image used by the node group
    optional string image = 4;
    // Minimum number of nodes in a node group
    optional int32 min_size = 5;
    // Maximum number of nodes in a node group
    optional int32 max_size = 6;
    // Desired number of nodes in a node group
    optional int32 desired_size = 7;
    // disk size for instance
    optional int32 disk_size = 8;
    // The labels attached to the node group
    map<string, string> labels = 13 [(buf.validate.field) = { map: {
        max_pairs: 256,
        keys: {
            string: { max_len: 255 }
        },
        values: {
            string: { max_len: 63 }
        }
    } }];

    // extra info
    optional google.protobuf.Struct extra = 16;
}

// ResetNodeGroupsRequest
message ResetNodeGroupsRequest {
    // list of node groups
    repeated ResetNodeGroupRequest node_groups = 1;
}

// ResetNodeGroupsResponse
message ResetNodeGroupsResponse {
    // node groups
    repeated NodeGroup node_groups = 1;
}

// ResetResourcesRequest
message ResetResourcesRequest {
    // reset resource from source environment
    optional EnvironmentIdentifier source_environment = 1;
    // reset resource to source environment
    optional EnvironmentIdentifier target_environment = 2;
    // Specify the list of kubernetes resources to reset
    repeated ResourceItems resources = 3;

    // ResourceItems
    message ResourceItems {
        // resource api version
        optional string api_version = 1;
        // resource kind
        string kind = 2;
        // list of resources
        repeated google.protobuf.Struct resources = 3;
    }
}

// ResetResourcesResponse
message ResetResourcesResponse {
    // list of resources
    repeated google.protobuf.Struct resources = 1;
}

// RestartWorkloadsRequest
message RestartWorkloadsRequest {
    // reset resource from source environment
    EnvironmentIdentifier environment = 1;
    // list of workloads
    repeated ResourceIdentifier identifiers = 2;
}

// RestartWorkloadsResponse
message RestartWorkloadsResponse {
    // list of resources identifiers
    repeated ResourceIdentifier identifiers = 1;
}

// DeployApplicationsRequest
message DeployApplicationsRequest {
    // environment to deploy
    EnvironmentIdentifier environment = 1;

    // list of applications to deploy
    repeated DeployContext applications = 2;

    // DeployContext
    message DeployContext {
        // app name
        string app = 1;
        // image name
        optional string image = 2;
        // image tag
        optional string tag = 3;
        // repository name
        optional string repo = 4;
        // code branch
        optional string branch = 5;
        // app version
        optional string version = 6;
    }
}

// DeployApplicationsResponse
message DeployApplicationsResponse {
    // environment to deploy
    EnvironmentIdentifier environment = 1;

    // applications status
    repeated google.protobuf.Struct applications = 2;
}
