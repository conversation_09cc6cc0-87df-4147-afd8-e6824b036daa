syntax = "proto3";

package backend.proto.tools.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

// TestingService
service TestingService {

    // clean apps
    rpc CleanApps(CleanAppsRequest) returns (CleanAppsResponse);

    // celan Services
    rpc CleanServices(CleanServicesRequest) returns (CleanServicesResponse);

    // clean EnvoyFilters
    rpc CleanEnvoyFilters(CleanEnvoyFiltersRequest) returns (CleanEnvoyFiltersResponse);

    // clean PodDisruptionBudgets
    rpc CleanPodDisruptionBudgets(CleanPodDisruptionBudgetsRequest) returns (CleanPodDisruptionBudgetsResponse);

}

// CleanAppsRequest
message CleanAppsRequest {
    // Specifies whether to perform a dry run. If true, the cleanup action will not be performed. default false.
    optional bool validate_only = 1;
    // Default retention period
    optional google.protobuf.Duration duration = 2;
    // Specify a list of policies to clean up
    repeated Policy policies = 3;

    // expire rules
    message Policy {
        // app or repo name
        optional string name = 1;
        // branch regular expressions
        optional string branch = 2;
        // validity period
        ValidityPeriod validity_period = 3;
    }

    // ValidityPeriod
    message ValidityPeriod {
        // duration
        optional google.protobuf.Duration duration = 1;
        // expiry time
        optional google.protobuf.Timestamp expiry_time = 2;
    }
}

// CleanAppsResponse
message CleanAppsResponse {
    // list of apps
    repeated string apps = 1;
}

// CleanServicesRequest
message CleanServicesRequest {
    // Specifies whether to perform a dry run. If true, the cleanup action will not be performed. default false.
    optional bool validate_only = 1;
}

// CleanServicesResponse
message CleanServicesResponse {
    // list of services
    repeated string services = 1;
}

// CleanIngressesRequest
message CleanEnvoyFiltersRequest {
    // Specifies whether to perform a dry run. If true, the cleanup action will not be performed. default false.
    optional bool validate_only = 1;
}

// CleanEnvoyFiltersResponse
message CleanEnvoyFiltersResponse {
    // list of envoy filters
    repeated string envoy_filters = 1;
}

// CleanPodDisruptionBudgetsRequest
message CleanPodDisruptionBudgetsRequest {
    // Specifies whether to perform a dry run. If true, the cleanup action will not be performed. default false.
    optional bool validate_only = 1;
}

// CleanPodDisruptionBudgetsResponse
message CleanPodDisruptionBudgetsResponse {
    // list of pod disruption budgets
    repeated string pod_disruption_budgets = 1;
}
