syntax = "proto3";

package backend.proto.tools.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "backend/proto/tools/v1/resource_models.proto";

// StagingService
service StagingService {

    // restore databases
    rpc RestoreDatabases(RestoreDatabasesRequest) returns (RestoreDatabasesResponse);

    // reset databases
    rpc ResetDatabase(ResetDatabaseRequest) returns (ResetDatabaseResponse);

    // flush secrets
    rpc FlushSecrets(FlushSecretsRequest) returns (FlushSecretsResponse);

    // flush routes
    rpc FlushRoutes(FlushRoutesRequest) returns (FlushRoutesResponse);

    // flush databases
    rpc FlushDatabases(FlushDatabasesRequest) returns (FlushDatabasesResponse);

}

// RestoreDatabasesRequest
message RestoreDatabasesRequest {

}

// RestoreDatabasesResponse
message RestoreDatabasesResponse {
    // restored databases
    repeated Database databases = 1;
}

// ResetDatabaseRequest
message ResetDatabaseRequest {
    // database identifier
    PlatformIdentifier identifier = 1;
    // database username
    optional string username = 2;
    // database username
    optional string password = 3;
    // list of sql requests
    repeated SqlRequest sql_requests = 8;
    
    // SqlRequest
    message SqlRequest {
        // database to execute sql
        string database = 1;
        // list of sql statements
        repeated string sql = 2;
    }

}

// ResetDatabaseResponse
message ResetDatabaseResponse {
    // executed sql
    repeated string sql = 1;
}

// FlushSecretsRequest
message FlushSecretsRequest {
    // secrets
    repeated Secret secrets = 1;

    // Secret
    message Secret {
        // secret name
        string name = 1;
        // secret values
        map<string, string> secrets = 2;
    }
}

// FlushSecretsResponse
message FlushSecretsResponse {
    // changed secrets key count
    map<string, int32> changed = 1;
}

// FlushRoutesRequest
message FlushRoutesRequest {

}

// FlushRoutesResponse
message FlushRoutesResponse {
    // flushed route names
    repeated string names = 1;
}

// FlushDatabasesRequest
message FlushDatabasesRequest {
    // database identifiers
    repeated PlatformIdentifier databases = 1;
}

// FlushDatabasesResponse
message FlushDatabasesResponse {
    // deleted database count
    int32 deleted = 1;
    // inserted database count
    int32 inserted = 2;
}
