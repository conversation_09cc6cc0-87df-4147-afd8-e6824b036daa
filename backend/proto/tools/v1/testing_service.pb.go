// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/tools/v1/testing_service.proto

package toolspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CleanAppsRequest
type CleanAppsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Specifies whether to perform a dry run. If true, the cleanup action will not be performed. default false.
	ValidateOnly *bool `protobuf:"varint,1,opt,name=validate_only,json=validateOnly,proto3,oneof" json:"validate_only,omitempty"`
	// Default retention period
	Duration *durationpb.Duration `protobuf:"bytes,2,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// Specify a list of policies to clean up
	Policies      []*CleanAppsRequest_Policy `protobuf:"bytes,3,rep,name=policies,proto3" json:"policies,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanAppsRequest) Reset() {
	*x = CleanAppsRequest{}
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanAppsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanAppsRequest) ProtoMessage() {}

func (x *CleanAppsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanAppsRequest.ProtoReflect.Descriptor instead.
func (*CleanAppsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP(), []int{0}
}

func (x *CleanAppsRequest) GetValidateOnly() bool {
	if x != nil && x.ValidateOnly != nil {
		return *x.ValidateOnly
	}
	return false
}

func (x *CleanAppsRequest) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *CleanAppsRequest) GetPolicies() []*CleanAppsRequest_Policy {
	if x != nil {
		return x.Policies
	}
	return nil
}

// CleanAppsResponse
type CleanAppsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of apps
	Apps          []string `protobuf:"bytes,1,rep,name=apps,proto3" json:"apps,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanAppsResponse) Reset() {
	*x = CleanAppsResponse{}
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanAppsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanAppsResponse) ProtoMessage() {}

func (x *CleanAppsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanAppsResponse.ProtoReflect.Descriptor instead.
func (*CleanAppsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP(), []int{1}
}

func (x *CleanAppsResponse) GetApps() []string {
	if x != nil {
		return x.Apps
	}
	return nil
}

// CleanServicesRequest
type CleanServicesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Specifies whether to perform a dry run. If true, the cleanup action will not be performed. default false.
	ValidateOnly  *bool `protobuf:"varint,1,opt,name=validate_only,json=validateOnly,proto3,oneof" json:"validate_only,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanServicesRequest) Reset() {
	*x = CleanServicesRequest{}
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanServicesRequest) ProtoMessage() {}

func (x *CleanServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanServicesRequest.ProtoReflect.Descriptor instead.
func (*CleanServicesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP(), []int{2}
}

func (x *CleanServicesRequest) GetValidateOnly() bool {
	if x != nil && x.ValidateOnly != nil {
		return *x.ValidateOnly
	}
	return false
}

// CleanServicesResponse
type CleanServicesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of services
	Services      []string `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanServicesResponse) Reset() {
	*x = CleanServicesResponse{}
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanServicesResponse) ProtoMessage() {}

func (x *CleanServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanServicesResponse.ProtoReflect.Descriptor instead.
func (*CleanServicesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP(), []int{3}
}

func (x *CleanServicesResponse) GetServices() []string {
	if x != nil {
		return x.Services
	}
	return nil
}

// CleanIngressesRequest
type CleanEnvoyFiltersRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Specifies whether to perform a dry run. If true, the cleanup action will not be performed. default false.
	ValidateOnly  *bool `protobuf:"varint,1,opt,name=validate_only,json=validateOnly,proto3,oneof" json:"validate_only,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanEnvoyFiltersRequest) Reset() {
	*x = CleanEnvoyFiltersRequest{}
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanEnvoyFiltersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanEnvoyFiltersRequest) ProtoMessage() {}

func (x *CleanEnvoyFiltersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanEnvoyFiltersRequest.ProtoReflect.Descriptor instead.
func (*CleanEnvoyFiltersRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP(), []int{4}
}

func (x *CleanEnvoyFiltersRequest) GetValidateOnly() bool {
	if x != nil && x.ValidateOnly != nil {
		return *x.ValidateOnly
	}
	return false
}

// CleanEnvoyFiltersResponse
type CleanEnvoyFiltersResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of envoy filters
	EnvoyFilters  []string `protobuf:"bytes,1,rep,name=envoy_filters,json=envoyFilters,proto3" json:"envoy_filters,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanEnvoyFiltersResponse) Reset() {
	*x = CleanEnvoyFiltersResponse{}
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanEnvoyFiltersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanEnvoyFiltersResponse) ProtoMessage() {}

func (x *CleanEnvoyFiltersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanEnvoyFiltersResponse.ProtoReflect.Descriptor instead.
func (*CleanEnvoyFiltersResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP(), []int{5}
}

func (x *CleanEnvoyFiltersResponse) GetEnvoyFilters() []string {
	if x != nil {
		return x.EnvoyFilters
	}
	return nil
}

// CleanPodDisruptionBudgetsRequest
type CleanPodDisruptionBudgetsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Specifies whether to perform a dry run. If true, the cleanup action will not be performed. default false.
	ValidateOnly  *bool `protobuf:"varint,1,opt,name=validate_only,json=validateOnly,proto3,oneof" json:"validate_only,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanPodDisruptionBudgetsRequest) Reset() {
	*x = CleanPodDisruptionBudgetsRequest{}
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanPodDisruptionBudgetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanPodDisruptionBudgetsRequest) ProtoMessage() {}

func (x *CleanPodDisruptionBudgetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanPodDisruptionBudgetsRequest.ProtoReflect.Descriptor instead.
func (*CleanPodDisruptionBudgetsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP(), []int{6}
}

func (x *CleanPodDisruptionBudgetsRequest) GetValidateOnly() bool {
	if x != nil && x.ValidateOnly != nil {
		return *x.ValidateOnly
	}
	return false
}

// CleanPodDisruptionBudgetsResponse
type CleanPodDisruptionBudgetsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of pod disruption budgets
	PodDisruptionBudgets []string `protobuf:"bytes,1,rep,name=pod_disruption_budgets,json=podDisruptionBudgets,proto3" json:"pod_disruption_budgets,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CleanPodDisruptionBudgetsResponse) Reset() {
	*x = CleanPodDisruptionBudgetsResponse{}
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanPodDisruptionBudgetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanPodDisruptionBudgetsResponse) ProtoMessage() {}

func (x *CleanPodDisruptionBudgetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanPodDisruptionBudgetsResponse.ProtoReflect.Descriptor instead.
func (*CleanPodDisruptionBudgetsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP(), []int{7}
}

func (x *CleanPodDisruptionBudgetsResponse) GetPodDisruptionBudgets() []string {
	if x != nil {
		return x.PodDisruptionBudgets
	}
	return nil
}

// expire rules
type CleanAppsRequest_Policy struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// app or repo name
	Name *string `protobuf:"bytes,1,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// branch regular expressions
	Branch *string `protobuf:"bytes,2,opt,name=branch,proto3,oneof" json:"branch,omitempty"`
	// validity period
	ValidityPeriod *CleanAppsRequest_ValidityPeriod `protobuf:"bytes,3,opt,name=validity_period,json=validityPeriod,proto3" json:"validity_period,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CleanAppsRequest_Policy) Reset() {
	*x = CleanAppsRequest_Policy{}
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanAppsRequest_Policy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanAppsRequest_Policy) ProtoMessage() {}

func (x *CleanAppsRequest_Policy) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanAppsRequest_Policy.ProtoReflect.Descriptor instead.
func (*CleanAppsRequest_Policy) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *CleanAppsRequest_Policy) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *CleanAppsRequest_Policy) GetBranch() string {
	if x != nil && x.Branch != nil {
		return *x.Branch
	}
	return ""
}

func (x *CleanAppsRequest_Policy) GetValidityPeriod() *CleanAppsRequest_ValidityPeriod {
	if x != nil {
		return x.ValidityPeriod
	}
	return nil
}

// ValidityPeriod
type CleanAppsRequest_ValidityPeriod struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// duration
	Duration *durationpb.Duration `protobuf:"bytes,1,opt,name=duration,proto3,oneof" json:"duration,omitempty"`
	// expiry time
	ExpiryTime    *timestamppb.Timestamp `protobuf:"bytes,2,opt,name=expiry_time,json=expiryTime,proto3,oneof" json:"expiry_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanAppsRequest_ValidityPeriod) Reset() {
	*x = CleanAppsRequest_ValidityPeriod{}
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanAppsRequest_ValidityPeriod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanAppsRequest_ValidityPeriod) ProtoMessage() {}

func (x *CleanAppsRequest_ValidityPeriod) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_testing_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanAppsRequest_ValidityPeriod.ProtoReflect.Descriptor instead.
func (*CleanAppsRequest_ValidityPeriod) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP(), []int{0, 1}
}

func (x *CleanAppsRequest_ValidityPeriod) GetDuration() *durationpb.Duration {
	if x != nil {
		return x.Duration
	}
	return nil
}

func (x *CleanAppsRequest_ValidityPeriod) GetExpiryTime() *timestamppb.Timestamp {
	if x != nil {
		return x.ExpiryTime
	}
	return nil
}

var File_backend_proto_tools_v1_testing_service_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_testing_service_proto_rawDesc = "" +
	"\n" +
	",backend/proto/tools/v1/testing_service.proto\x12\x16backend.proto.tools.v1\x1a\x1egoogle/protobuf/duration.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xc9\x04\n" +
	"\x10CleanAppsRequest\x12(\n" +
	"\rvalidate_only\x18\x01 \x01(\bH\x00R\fvalidateOnly\x88\x01\x01\x12:\n" +
	"\bduration\x18\x02 \x01(\v2\x19.google.protobuf.DurationH\x01R\bduration\x88\x01\x01\x12K\n" +
	"\bpolicies\x18\x03 \x03(\v2/.backend.proto.tools.v1.CleanAppsRequest.PolicyR\bpolicies\x1a\xb4\x01\n" +
	"\x06Policy\x12\x17\n" +
	"\x04name\x18\x01 \x01(\tH\x00R\x04name\x88\x01\x01\x12\x1b\n" +
	"\x06branch\x18\x02 \x01(\tH\x01R\x06branch\x88\x01\x01\x12`\n" +
	"\x0fvalidity_period\x18\x03 \x01(\v27.backend.proto.tools.v1.CleanAppsRequest.ValidityPeriodR\x0evalidityPeriodB\a\n" +
	"\x05_nameB\t\n" +
	"\a_branch\x1a\xab\x01\n" +
	"\x0eValidityPeriod\x12:\n" +
	"\bduration\x18\x01 \x01(\v2\x19.google.protobuf.DurationH\x00R\bduration\x88\x01\x01\x12@\n" +
	"\vexpiry_time\x18\x02 \x01(\v2\x1a.google.protobuf.TimestampH\x01R\n" +
	"expiryTime\x88\x01\x01B\v\n" +
	"\t_durationB\x0e\n" +
	"\f_expiry_timeB\x10\n" +
	"\x0e_validate_onlyB\v\n" +
	"\t_duration\"'\n" +
	"\x11CleanAppsResponse\x12\x12\n" +
	"\x04apps\x18\x01 \x03(\tR\x04apps\"R\n" +
	"\x14CleanServicesRequest\x12(\n" +
	"\rvalidate_only\x18\x01 \x01(\bH\x00R\fvalidateOnly\x88\x01\x01B\x10\n" +
	"\x0e_validate_only\"3\n" +
	"\x15CleanServicesResponse\x12\x1a\n" +
	"\bservices\x18\x01 \x03(\tR\bservices\"V\n" +
	"\x18CleanEnvoyFiltersRequest\x12(\n" +
	"\rvalidate_only\x18\x01 \x01(\bH\x00R\fvalidateOnly\x88\x01\x01B\x10\n" +
	"\x0e_validate_only\"@\n" +
	"\x19CleanEnvoyFiltersResponse\x12#\n" +
	"\renvoy_filters\x18\x01 \x03(\tR\fenvoyFilters\"^\n" +
	" CleanPodDisruptionBudgetsRequest\x12(\n" +
	"\rvalidate_only\x18\x01 \x01(\bH\x00R\fvalidateOnly\x88\x01\x01B\x10\n" +
	"\x0e_validate_only\"Y\n" +
	"!CleanPodDisruptionBudgetsResponse\x124\n" +
	"\x16pod_disruption_budgets\x18\x01 \x03(\tR\x14podDisruptionBudgets2\xed\x03\n" +
	"\x0eTestingService\x12`\n" +
	"\tCleanApps\x12(.backend.proto.tools.v1.CleanAppsRequest\x1a).backend.proto.tools.v1.CleanAppsResponse\x12l\n" +
	"\rCleanServices\x12,.backend.proto.tools.v1.CleanServicesRequest\x1a-.backend.proto.tools.v1.CleanServicesResponse\x12x\n" +
	"\x11CleanEnvoyFilters\x120.backend.proto.tools.v1.CleanEnvoyFiltersRequest\x1a1.backend.proto.tools.v1.CleanEnvoyFiltersResponse\x12\x90\x01\n" +
	"\x19CleanPodDisruptionBudgets\x128.backend.proto.tools.v1.CleanPodDisruptionBudgetsRequest\x1a9.backend.proto.tools.v1.CleanPodDisruptionBudgetsResponseBb\n" +
	" com.moego.backend.proto.tools.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_testing_service_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_testing_service_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_testing_service_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_testing_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_testing_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_testing_service_proto_rawDesc), len(file_backend_proto_tools_v1_testing_service_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_testing_service_proto_rawDescData
}

var file_backend_proto_tools_v1_testing_service_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_backend_proto_tools_v1_testing_service_proto_goTypes = []any{
	(*CleanAppsRequest)(nil),                  // 0: backend.proto.tools.v1.CleanAppsRequest
	(*CleanAppsResponse)(nil),                 // 1: backend.proto.tools.v1.CleanAppsResponse
	(*CleanServicesRequest)(nil),              // 2: backend.proto.tools.v1.CleanServicesRequest
	(*CleanServicesResponse)(nil),             // 3: backend.proto.tools.v1.CleanServicesResponse
	(*CleanEnvoyFiltersRequest)(nil),          // 4: backend.proto.tools.v1.CleanEnvoyFiltersRequest
	(*CleanEnvoyFiltersResponse)(nil),         // 5: backend.proto.tools.v1.CleanEnvoyFiltersResponse
	(*CleanPodDisruptionBudgetsRequest)(nil),  // 6: backend.proto.tools.v1.CleanPodDisruptionBudgetsRequest
	(*CleanPodDisruptionBudgetsResponse)(nil), // 7: backend.proto.tools.v1.CleanPodDisruptionBudgetsResponse
	(*CleanAppsRequest_Policy)(nil),           // 8: backend.proto.tools.v1.CleanAppsRequest.Policy
	(*CleanAppsRequest_ValidityPeriod)(nil),   // 9: backend.proto.tools.v1.CleanAppsRequest.ValidityPeriod
	(*durationpb.Duration)(nil),               // 10: google.protobuf.Duration
	(*timestamppb.Timestamp)(nil),             // 11: google.protobuf.Timestamp
}
var file_backend_proto_tools_v1_testing_service_proto_depIdxs = []int32{
	10, // 0: backend.proto.tools.v1.CleanAppsRequest.duration:type_name -> google.protobuf.Duration
	8,  // 1: backend.proto.tools.v1.CleanAppsRequest.policies:type_name -> backend.proto.tools.v1.CleanAppsRequest.Policy
	9,  // 2: backend.proto.tools.v1.CleanAppsRequest.Policy.validity_period:type_name -> backend.proto.tools.v1.CleanAppsRequest.ValidityPeriod
	10, // 3: backend.proto.tools.v1.CleanAppsRequest.ValidityPeriod.duration:type_name -> google.protobuf.Duration
	11, // 4: backend.proto.tools.v1.CleanAppsRequest.ValidityPeriod.expiry_time:type_name -> google.protobuf.Timestamp
	0,  // 5: backend.proto.tools.v1.TestingService.CleanApps:input_type -> backend.proto.tools.v1.CleanAppsRequest
	2,  // 6: backend.proto.tools.v1.TestingService.CleanServices:input_type -> backend.proto.tools.v1.CleanServicesRequest
	4,  // 7: backend.proto.tools.v1.TestingService.CleanEnvoyFilters:input_type -> backend.proto.tools.v1.CleanEnvoyFiltersRequest
	6,  // 8: backend.proto.tools.v1.TestingService.CleanPodDisruptionBudgets:input_type -> backend.proto.tools.v1.CleanPodDisruptionBudgetsRequest
	1,  // 9: backend.proto.tools.v1.TestingService.CleanApps:output_type -> backend.proto.tools.v1.CleanAppsResponse
	3,  // 10: backend.proto.tools.v1.TestingService.CleanServices:output_type -> backend.proto.tools.v1.CleanServicesResponse
	5,  // 11: backend.proto.tools.v1.TestingService.CleanEnvoyFilters:output_type -> backend.proto.tools.v1.CleanEnvoyFiltersResponse
	7,  // 12: backend.proto.tools.v1.TestingService.CleanPodDisruptionBudgets:output_type -> backend.proto.tools.v1.CleanPodDisruptionBudgetsResponse
	9,  // [9:13] is the sub-list for method output_type
	5,  // [5:9] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_testing_service_proto_init() }
func file_backend_proto_tools_v1_testing_service_proto_init() {
	if File_backend_proto_tools_v1_testing_service_proto != nil {
		return
	}
	file_backend_proto_tools_v1_testing_service_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_tools_v1_testing_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_tools_v1_testing_service_proto_msgTypes[4].OneofWrappers = []any{}
	file_backend_proto_tools_v1_testing_service_proto_msgTypes[6].OneofWrappers = []any{}
	file_backend_proto_tools_v1_testing_service_proto_msgTypes[8].OneofWrappers = []any{}
	file_backend_proto_tools_v1_testing_service_proto_msgTypes[9].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_testing_service_proto_rawDesc), len(file_backend_proto_tools_v1_testing_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_tools_v1_testing_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_testing_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_tools_v1_testing_service_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_testing_service_proto = out.File
	file_backend_proto_tools_v1_testing_service_proto_goTypes = nil
	file_backend_proto_tools_v1_testing_service_proto_depIdxs = nil
}
