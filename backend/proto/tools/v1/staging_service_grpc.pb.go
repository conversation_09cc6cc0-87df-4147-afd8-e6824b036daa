// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/tools/v1/staging_service.proto

package toolspb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	StagingService_RestoreDatabases_FullMethodName = "/backend.proto.tools.v1.StagingService/RestoreDatabases"
	StagingService_ResetDatabase_FullMethodName    = "/backend.proto.tools.v1.StagingService/ResetDatabase"
	StagingService_FlushSecrets_FullMethodName     = "/backend.proto.tools.v1.StagingService/FlushSecrets"
	StagingService_FlushRoutes_FullMethodName      = "/backend.proto.tools.v1.StagingService/FlushRoutes"
	StagingService_FlushDatabases_FullMethodName   = "/backend.proto.tools.v1.StagingService/FlushDatabases"
)

// StagingServiceClient is the client API for StagingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// StagingService
type StagingServiceClient interface {
	// restore databases
	RestoreDatabases(ctx context.Context, in *RestoreDatabasesRequest, opts ...grpc.CallOption) (*RestoreDatabasesResponse, error)
	// reset databases
	ResetDatabase(ctx context.Context, in *ResetDatabaseRequest, opts ...grpc.CallOption) (*ResetDatabaseResponse, error)
	// flush secrets
	FlushSecrets(ctx context.Context, in *FlushSecretsRequest, opts ...grpc.CallOption) (*FlushSecretsResponse, error)
	// flush routes
	FlushRoutes(ctx context.Context, in *FlushRoutesRequest, opts ...grpc.CallOption) (*FlushRoutesResponse, error)
	// flush databases
	FlushDatabases(ctx context.Context, in *FlushDatabasesRequest, opts ...grpc.CallOption) (*FlushDatabasesResponse, error)
}

type stagingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewStagingServiceClient(cc grpc.ClientConnInterface) StagingServiceClient {
	return &stagingServiceClient{cc}
}

func (c *stagingServiceClient) RestoreDatabases(ctx context.Context, in *RestoreDatabasesRequest, opts ...grpc.CallOption) (*RestoreDatabasesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RestoreDatabasesResponse)
	err := c.cc.Invoke(ctx, StagingService_RestoreDatabases_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stagingServiceClient) ResetDatabase(ctx context.Context, in *ResetDatabaseRequest, opts ...grpc.CallOption) (*ResetDatabaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResetDatabaseResponse)
	err := c.cc.Invoke(ctx, StagingService_ResetDatabase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stagingServiceClient) FlushSecrets(ctx context.Context, in *FlushSecretsRequest, opts ...grpc.CallOption) (*FlushSecretsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FlushSecretsResponse)
	err := c.cc.Invoke(ctx, StagingService_FlushSecrets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stagingServiceClient) FlushRoutes(ctx context.Context, in *FlushRoutesRequest, opts ...grpc.CallOption) (*FlushRoutesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FlushRoutesResponse)
	err := c.cc.Invoke(ctx, StagingService_FlushRoutes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *stagingServiceClient) FlushDatabases(ctx context.Context, in *FlushDatabasesRequest, opts ...grpc.CallOption) (*FlushDatabasesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FlushDatabasesResponse)
	err := c.cc.Invoke(ctx, StagingService_FlushDatabases_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// StagingServiceServer is the server API for StagingService service.
// All implementations must embed UnimplementedStagingServiceServer
// for forward compatibility.
//
// StagingService
type StagingServiceServer interface {
	// restore databases
	RestoreDatabases(context.Context, *RestoreDatabasesRequest) (*RestoreDatabasesResponse, error)
	// reset databases
	ResetDatabase(context.Context, *ResetDatabaseRequest) (*ResetDatabaseResponse, error)
	// flush secrets
	FlushSecrets(context.Context, *FlushSecretsRequest) (*FlushSecretsResponse, error)
	// flush routes
	FlushRoutes(context.Context, *FlushRoutesRequest) (*FlushRoutesResponse, error)
	// flush databases
	FlushDatabases(context.Context, *FlushDatabasesRequest) (*FlushDatabasesResponse, error)
	mustEmbedUnimplementedStagingServiceServer()
}

// UnimplementedStagingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedStagingServiceServer struct{}

func (UnimplementedStagingServiceServer) RestoreDatabases(context.Context, *RestoreDatabasesRequest) (*RestoreDatabasesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestoreDatabases not implemented")
}
func (UnimplementedStagingServiceServer) ResetDatabase(context.Context, *ResetDatabaseRequest) (*ResetDatabaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetDatabase not implemented")
}
func (UnimplementedStagingServiceServer) FlushSecrets(context.Context, *FlushSecretsRequest) (*FlushSecretsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FlushSecrets not implemented")
}
func (UnimplementedStagingServiceServer) FlushRoutes(context.Context, *FlushRoutesRequest) (*FlushRoutesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FlushRoutes not implemented")
}
func (UnimplementedStagingServiceServer) FlushDatabases(context.Context, *FlushDatabasesRequest) (*FlushDatabasesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FlushDatabases not implemented")
}
func (UnimplementedStagingServiceServer) mustEmbedUnimplementedStagingServiceServer() {}
func (UnimplementedStagingServiceServer) testEmbeddedByValue()                        {}

// UnsafeStagingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to StagingServiceServer will
// result in compilation errors.
type UnsafeStagingServiceServer interface {
	mustEmbedUnimplementedStagingServiceServer()
}

func RegisterStagingServiceServer(s grpc.ServiceRegistrar, srv StagingServiceServer) {
	// If the following call pancis, it indicates UnimplementedStagingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&StagingService_ServiceDesc, srv)
}

func _StagingService_RestoreDatabases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RestoreDatabasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StagingServiceServer).RestoreDatabases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StagingService_RestoreDatabases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StagingServiceServer).RestoreDatabases(ctx, req.(*RestoreDatabasesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StagingService_ResetDatabase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetDatabaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StagingServiceServer).ResetDatabase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StagingService_ResetDatabase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StagingServiceServer).ResetDatabase(ctx, req.(*ResetDatabaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StagingService_FlushSecrets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlushSecretsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StagingServiceServer).FlushSecrets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StagingService_FlushSecrets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StagingServiceServer).FlushSecrets(ctx, req.(*FlushSecretsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StagingService_FlushRoutes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlushRoutesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StagingServiceServer).FlushRoutes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StagingService_FlushRoutes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StagingServiceServer).FlushRoutes(ctx, req.(*FlushRoutesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _StagingService_FlushDatabases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FlushDatabasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(StagingServiceServer).FlushDatabases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: StagingService_FlushDatabases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(StagingServiceServer).FlushDatabases(ctx, req.(*FlushDatabasesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// StagingService_ServiceDesc is the grpc.ServiceDesc for StagingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var StagingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.tools.v1.StagingService",
	HandlerType: (*StagingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RestoreDatabases",
			Handler:    _StagingService_RestoreDatabases_Handler,
		},
		{
			MethodName: "ResetDatabase",
			Handler:    _StagingService_ResetDatabase_Handler,
		},
		{
			MethodName: "FlushSecrets",
			Handler:    _StagingService_FlushSecrets_Handler,
		},
		{
			MethodName: "FlushRoutes",
			Handler:    _StagingService_FlushRoutes_Handler,
		},
		{
			MethodName: "FlushDatabases",
			Handler:    _StagingService_FlushDatabases_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/tools/v1/staging_service.proto",
}
