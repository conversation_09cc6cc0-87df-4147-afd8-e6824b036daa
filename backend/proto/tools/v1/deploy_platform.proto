syntax = "proto3";

package backend.proto.tools.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb"; // This package name should not be changed
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/timestamp.proto";

// Service to configure deployments
service DeployPlatformService {

  // Skip Canary
  rpc SkipDeployTask(SkipDeployTaskRequest) returns (SkipDeployTaskResponse);

  // Rollback (Canary Or ArgoCD)
  // (-- api-linter: core::0162::rollback-response-message-name=disabled
  //     aip.dev/not-precedent: We need to do this because reasons. --)
  rpc RollbackDeployTask(RollbackDeployTaskRequest) returns (RollbackDeployTaskResponse);

  // Handle Canary Event webhook event
  rpc HandleCanaryEventWebhookEvent(HandleCanaryEventWebhookEventRequest) returns (HandleCanaryEventWebhookEventResponse);

  // Handle Canary webhook event: rollout/rollback/skip/promotion
  rpc HandleCanaryWebhookEvent(HandleCanaryWebhookEventRequest) returns (HandleCanaryWebhookEventResponse);

  // Handle Slack Interactions
  rpc HandleSlackInteractionsEvent(HandleSlackInteractionsEventRequest) returns (HandleSlackInteractionsEventResponse);
}

// Request message for SkipCanary
message SkipDeployTaskRequest {
  // Name of the deployment task
  string name = 1 [(google.api.field_behavior) = REQUIRED];
  // Reason for skipping the canary
  string reason = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for SkipCanary
message SkipDeployTaskResponse {
}

// Request message for Rollback
message RollbackDeployTaskRequest {
  // Name of the deployment task
  // (-- api-linter: core::0162::rollback-request-name-reference=disabled
  //     aip.dev/not-precedent: We need to do this because reasons. --)
  string name = 1 [(google.api.field_behavior) = REQUIRED];
  // Revision ID of the deployment
  string revision_id = 2 [(google.api.field_behavior) = REQUIRED];
  // Reason for rolling back the deployment
  string reason = 3 [(google.api.field_behavior) = REQUIRED];
}

// Response message for Rollback
message RollbackDeployTaskResponse {
}


// Request message for HandleCanaryEventWebhookEvent
message HandleCanaryEventWebhookEventRequest {
  // Name of the canary.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Namespace of the canary.
  string namespace = 2 [(google.api.field_behavior) = REQUIRED];

  // Phase of the canary analysis.
  string phase = 3 [(google.api.field_behavior) = REQUIRED];

  // Hash from the TrackedConfigs and LastAppliedSpec of the Canary.
  // Can be used to identify a Canary for a specific configuration of the deployed resources.
  string checksum = 4 [(google.api.field_behavior) = REQUIRED];

  // BuildId of the Workload.
  string build_id = 5 [(google.api.field_behavior) = REQUIRED];

  // Type of the event.
  optional string type = 6 [(google.api.field_behavior) = OPTIONAL];

  // FailedChecks
  optional int32  failed_checks = 7 [(google.api.field_behavior) = OPTIONAL];

  // CanaryWeight
  optional int32 canary_weight = 8 [(google.api.field_behavior) = OPTIONAL];

  // Iterations
  optional int32 iterations = 9 [(google.api.field_behavior) = OPTIONAL];

  // RemainingTime
  optional int64 remaining = 10 [(google.api.field_behavior) = OPTIONAL];

  // Metadata (key-value pairs) for this webhook (optional).
  map<string, string> metadata = 11 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for HandleCanaryEventWebhookEvent
message HandleCanaryEventWebhookEventResponse {
}

// Request message for HandleCanaryWebhookEvent
message HandleCanaryWebhookEventRequest {
  // Name of the canary.
  string name = 1 [(google.api.field_behavior) = REQUIRED];

  // Namespace of the canary.
  string namespace = 2 [(google.api.field_behavior) = REQUIRED];

  // Phase of the canary analysis.
  string phase = 3 [(google.api.field_behavior) = REQUIRED];

  // Hash from the TrackedConfigs and LastAppliedSpec of the Canary.
  // Can be used to identify a Canary for a specific configuration of the deployed resources.
  string checksum = 4 [(google.api.field_behavior) = REQUIRED];

  // BuildId of the Workload.
  string build_id = 5 [(google.api.field_behavior) = REQUIRED];

  // Type of the event.
  string type = 7 [(google.api.field_behavior) = REQUIRED];

  // Metadata (key-value pairs) for this webhook (optional).
  map<string, string> metadata = 6 [(google.api.field_behavior) = OPTIONAL];
}

// Response message for HandleCanaryWebhookEvent
message HandleCanaryWebhookEventResponse {
}


// DeployTask represents a deployment task
// (-- api-linter: core::0123::resource-annotation=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
message DeployTask {
  // Unique identifier
  int64 id = 1 [(google.api.field_behavior) = REQUIRED];
  // Task key
  string name = 2 [(google.api.field_behavior) = REQUIRED];
  // Task title
  string title = 3 [(google.api.field_behavior) = REQUIRED];
  // Project key
  string description = 4 [(google.api.field_behavior) = REQUIRED];
  // Creator of the task
  string creator = 5 [(google.api.field_behavior) = REQUIRED];
  // Creation timestamp
  google.protobuf.Timestamp create_time = 6 [(google.api.field_behavior) = OPTIONAL];
  // Last update timestamp
  google.protobuf.Timestamp update_time = 7 [(google.api.field_behavior) = OPTIONAL];
  // Current state of the task
  string state = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // Current phase of the task
  string current_phase = 9 [(google.api.field_behavior) = OPTIONAL];
  // Parameters for the task
  string parameters = 10 [(google.api.field_behavior) = OPTIONAL];

  // List of deployment phases
  repeated DeployPhase phases = 11 [(google.api.field_behavior) = OPTIONAL];
  // List of deployment logs
  repeated DeployLog logs = 12 [(google.api.field_behavior) = OPTIONAL];

  // DeployPhase represents a phase of a deployment task
  message DeployPhase {
    // Unique identifier
    int64 id = 1 [(google.api.field_behavior) = OPTIONAL];
    // ID of the associated deployment task
    int64 task_id = 2 [(google.api.field_behavior) = OPTIONAL];
    // Name of the phase
    string name = 3 [(google.api.field_behavior) = OPTIONAL];
    // type of the phase
    string type = 4 [(google.api.field_behavior) = OPTIONAL];
    // Current state of the phase
    string state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
    // Parameters for the phase
    string parameters = 6 [(google.api.field_behavior) = OPTIONAL];
    // Start timestamp of the phase
    google.protobuf.Timestamp start_time = 7 [(google.api.field_behavior) = OPTIONAL];
    // End timestamp of the phase (nullable)
    optional google.protobuf.Timestamp end_time = 8 [(google.api.field_behavior) = OPTIONAL];
    // Reason for the modification
    optional string reason = 9 [(google.api.field_behavior) = OPTIONAL];
  }

  // DeployLog represents a log entry for a deployment task
  message DeployLog {
    // Unique identifier
    int64 id = 1 [(google.api.field_behavior) = OPTIONAL];
    // ID of the associated deployment task
    int64 task_id = 2 [(google.api.field_behavior) = OPTIONAL];
    // Log timestamp
    google.protobuf.Timestamp log_time = 3 [(google.api.field_behavior) = OPTIONAL];
    // Type of the log entry
    string type = 4 [(google.api.field_behavior) = OPTIONAL];
    // State of the deployment task at the time of the log entry
    string state = 5 [(google.api.field_behavior) = OUTPUT_ONLY];
    // Phase of the deployment task at the time of the log entry
    string phase = 6 [(google.api.field_behavior) = OPTIONAL];
    // Message associated with the log entry
    string message = 7 [(google.api.field_behavior) = OPTIONAL];
  }
}

// Request message for HandleSlackInteractionsEvent
message HandleSlackInteractionsEventRequest {
  // Payload of the Slack event
  string payload = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for HandleSlackInteractionsEvent
message HandleSlackInteractionsEventResponse {
}
