// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cache resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cache resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cache resources. --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/tools/v1/cache_service.proto

package toolspb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	CacheService_RegisterCache_FullMethodName       = "/backend.proto.tools.v1.CacheService/RegisterCache"
	CacheService_ListCaches_FullMethodName          = "/backend.proto.tools.v1.CacheService/ListCaches"
	CacheService_GetCache_FullMethodName            = "/backend.proto.tools.v1.CacheService/GetCache"
	CacheService_ExecuteCacheCommand_FullMethodName = "/backend.proto.tools.v1.CacheService/ExecuteCacheCommand"
)

// CacheServiceClient is the client API for CacheService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// CacheService
type CacheServiceClient interface {
	// register a cache
	RegisterCache(ctx context.Context, in *RegisterCacheRequest, opts ...grpc.CallOption) (*RegisterCacheResponse, error)
	// list caches
	ListCaches(ctx context.Context, in *ListCachesRequest, opts ...grpc.CallOption) (*ListCachesResponse, error)
	// get a cache
	GetCache(ctx context.Context, in *GetCacheRequest, opts ...grpc.CallOption) (*Cache, error)
	// execute command in cache
	ExecuteCacheCommand(ctx context.Context, in *ExecuteCacheCommandRequest, opts ...grpc.CallOption) (*ExecuteCacheCommandResponse, error)
}

type cacheServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewCacheServiceClient(cc grpc.ClientConnInterface) CacheServiceClient {
	return &cacheServiceClient{cc}
}

func (c *cacheServiceClient) RegisterCache(ctx context.Context, in *RegisterCacheRequest, opts ...grpc.CallOption) (*RegisterCacheResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterCacheResponse)
	err := c.cc.Invoke(ctx, CacheService_RegisterCache_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cacheServiceClient) ListCaches(ctx context.Context, in *ListCachesRequest, opts ...grpc.CallOption) (*ListCachesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListCachesResponse)
	err := c.cc.Invoke(ctx, CacheService_ListCaches_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cacheServiceClient) GetCache(ctx context.Context, in *GetCacheRequest, opts ...grpc.CallOption) (*Cache, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Cache)
	err := c.cc.Invoke(ctx, CacheService_GetCache_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cacheServiceClient) ExecuteCacheCommand(ctx context.Context, in *ExecuteCacheCommandRequest, opts ...grpc.CallOption) (*ExecuteCacheCommandResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ExecuteCacheCommandResponse)
	err := c.cc.Invoke(ctx, CacheService_ExecuteCacheCommand_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CacheServiceServer is the server API for CacheService service.
// All implementations must embed UnimplementedCacheServiceServer
// for forward compatibility.
//
// CacheService
type CacheServiceServer interface {
	// register a cache
	RegisterCache(context.Context, *RegisterCacheRequest) (*RegisterCacheResponse, error)
	// list caches
	ListCaches(context.Context, *ListCachesRequest) (*ListCachesResponse, error)
	// get a cache
	GetCache(context.Context, *GetCacheRequest) (*Cache, error)
	// execute command in cache
	ExecuteCacheCommand(context.Context, *ExecuteCacheCommandRequest) (*ExecuteCacheCommandResponse, error)
	mustEmbedUnimplementedCacheServiceServer()
}

// UnimplementedCacheServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedCacheServiceServer struct{}

func (UnimplementedCacheServiceServer) RegisterCache(context.Context, *RegisterCacheRequest) (*RegisterCacheResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterCache not implemented")
}
func (UnimplementedCacheServiceServer) ListCaches(context.Context, *ListCachesRequest) (*ListCachesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListCaches not implemented")
}
func (UnimplementedCacheServiceServer) GetCache(context.Context, *GetCacheRequest) (*Cache, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCache not implemented")
}
func (UnimplementedCacheServiceServer) ExecuteCacheCommand(context.Context, *ExecuteCacheCommandRequest) (*ExecuteCacheCommandResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExecuteCacheCommand not implemented")
}
func (UnimplementedCacheServiceServer) mustEmbedUnimplementedCacheServiceServer() {}
func (UnimplementedCacheServiceServer) testEmbeddedByValue()                      {}

// UnsafeCacheServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CacheServiceServer will
// result in compilation errors.
type UnsafeCacheServiceServer interface {
	mustEmbedUnimplementedCacheServiceServer()
}

func RegisterCacheServiceServer(s grpc.ServiceRegistrar, srv CacheServiceServer) {
	// If the following call pancis, it indicates UnimplementedCacheServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&CacheService_ServiceDesc, srv)
}

func _CacheService_RegisterCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CacheServiceServer).RegisterCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CacheService_RegisterCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CacheServiceServer).RegisterCache(ctx, req.(*RegisterCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CacheService_ListCaches_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListCachesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CacheServiceServer).ListCaches(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CacheService_ListCaches_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CacheServiceServer).ListCaches(ctx, req.(*ListCachesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CacheService_GetCache_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCacheRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CacheServiceServer).GetCache(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CacheService_GetCache_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CacheServiceServer).GetCache(ctx, req.(*GetCacheRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CacheService_ExecuteCacheCommand_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExecuteCacheCommandRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CacheServiceServer).ExecuteCacheCommand(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: CacheService_ExecuteCacheCommand_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CacheServiceServer).ExecuteCacheCommand(ctx, req.(*ExecuteCacheCommandRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// CacheService_ServiceDesc is the grpc.ServiceDesc for CacheService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var CacheService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.tools.v1.CacheService",
	HandlerType: (*CacheServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RegisterCache",
			Handler:    _CacheService_RegisterCache_Handler,
		},
		{
			MethodName: "ListCaches",
			Handler:    _CacheService_ListCaches_Handler,
		},
		{
			MethodName: "GetCache",
			Handler:    _CacheService_GetCache_Handler,
		},
		{
			MethodName: "ExecuteCacheCommand",
			Handler:    _CacheService_ExecuteCacheCommand_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/tools/v1/cache_service.proto",
}
