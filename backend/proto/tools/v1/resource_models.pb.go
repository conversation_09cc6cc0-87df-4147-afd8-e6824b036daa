// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/tools/v1/resource_models.proto

package toolspb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// PlatformType
type PlatformType int32

const (
	// unspecified
	PlatformType_PLATFORM_TYPE_UNSPECIFIED PlatformType = 0
	// self-hosted
	PlatformType_SELF_HOSTED PlatformType = 1
	// AWS Elastic Kubernetes Service
	PlatformType_AWS_EKS PlatformType = 11
	// AWS Elastic Container Registry
	PlatformType_AWS_ECR PlatformType = 12
	// AWS Relation Database Service
	PlatformType_AWS_RDS PlatformType = 13
	// AWS Managed Streaming for Apache Kafka
	PlatformType_AWS_MSK PlatformType = 14
	// AWS Simple Storage Service
	PlatformType_AWS_S3 PlatformType = 15
	// AWS Elastic Cache
	PlatformType_AWS_ELASTIC_CACHE PlatformType = 16
)

// Enum value maps for PlatformType.
var (
	PlatformType_name = map[int32]string{
		0:  "PLATFORM_TYPE_UNSPECIFIED",
		1:  "SELF_HOSTED",
		11: "AWS_EKS",
		12: "AWS_ECR",
		13: "AWS_RDS",
		14: "AWS_MSK",
		15: "AWS_S3",
		16: "AWS_ELASTIC_CACHE",
	}
	PlatformType_value = map[string]int32{
		"PLATFORM_TYPE_UNSPECIFIED": 0,
		"SELF_HOSTED":               1,
		"AWS_EKS":                   11,
		"AWS_ECR":                   12,
		"AWS_RDS":                   13,
		"AWS_MSK":                   14,
		"AWS_S3":                    15,
		"AWS_ELASTIC_CACHE":         16,
	}
)

func (x PlatformType) Enum() *PlatformType {
	p := new(PlatformType)
	*p = x
	return p
}

func (x PlatformType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PlatformType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_tools_v1_resource_models_proto_enumTypes[0].Descriptor()
}

func (PlatformType) Type() protoreflect.EnumType {
	return &file_backend_proto_tools_v1_resource_models_proto_enumTypes[0]
}

func (x PlatformType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PlatformType.Descriptor instead.
func (PlatformType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{0}
}

// PlatformIdentifier
type PlatformIdentifier struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// resource platform
	Platform string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	// resource identifier
	Identifier    string `protobuf:"bytes,2,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PlatformIdentifier) Reset() {
	*x = PlatformIdentifier{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PlatformIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PlatformIdentifier) ProtoMessage() {}

func (x *PlatformIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PlatformIdentifier.ProtoReflect.Descriptor instead.
func (*PlatformIdentifier) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{0}
}

func (x *PlatformIdentifier) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *PlatformIdentifier) GetIdentifier() string {
	if x != nil {
		return x.Identifier
	}
	return ""
}

// EnvironmentIdentifier: unique identifier for an environment
type EnvironmentIdentifier struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// kubernetes cluster
	Cluster string `protobuf:"bytes,1,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// kubernetes namespace
	Namespace     string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnvironmentIdentifier) Reset() {
	*x = EnvironmentIdentifier{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnvironmentIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvironmentIdentifier) ProtoMessage() {}

func (x *EnvironmentIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvironmentIdentifier.ProtoReflect.Descriptor instead.
func (*EnvironmentIdentifier) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{1}
}

func (x *EnvironmentIdentifier) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *EnvironmentIdentifier) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

// ResourceIdentifier
type ResourceIdentifier struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// api group/version
	ApiVersion *string `protobuf:"bytes,1,opt,name=api_version,json=apiVersion,proto3,oneof" json:"api_version,omitempty"`
	// resource kind
	Kind string `protobuf:"bytes,2,opt,name=kind,proto3" json:"kind,omitempty"`
	// resource name
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceIdentifier) Reset() {
	*x = ResourceIdentifier{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceIdentifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceIdentifier) ProtoMessage() {}

func (x *ResourceIdentifier) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceIdentifier.ProtoReflect.Descriptor instead.
func (*ResourceIdentifier) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{2}
}

func (x *ResourceIdentifier) GetApiVersion() string {
	if x != nil && x.ApiVersion != nil {
		return *x.ApiVersion
	}
	return ""
}

func (x *ResourceIdentifier) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

func (x *ResourceIdentifier) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// Endpoint
type Endpoint struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// address
	Address string `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	// port
	Port          int32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Endpoint) Reset() {
	*x = Endpoint{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Endpoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Endpoint) ProtoMessage() {}

func (x *Endpoint) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Endpoint.ProtoReflect.Descriptor instead.
func (*Endpoint) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{3}
}

func (x *Endpoint) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

func (x *Endpoint) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

// Environment
type Environment struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// environment identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// environment status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// environment is managed?
	IsManaged bool `protobuf:"varint,3,opt,name=is_managed,json=isManaged,proto3" json:"is_managed,omitempty"`
	// environment name, use to display
	Name *string `protobuf:"bytes,4,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// environment description
	Description *string `protobuf:"bytes,5,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// list of databases
	Databases []*PlatformIdentifier `protobuf:"bytes,6,rep,name=databases,proto3" json:"databases,omitempty"`
	// list of caches
	Caches []*PlatformIdentifier `protobuf:"bytes,7,rep,name=caches,proto3" json:"caches,omitempty"`
	// list of message queues
	MessageQueues []*PlatformIdentifier `protobuf:"bytes,8,rep,name=message_queues,json=messageQueues,proto3" json:"message_queues,omitempty"`
	// extra info
	Extra *structpb.Struct `protobuf:"bytes,13,opt,name=extra,proto3,oneof" json:"extra,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,14,opt,name=create_time,json=createTime,proto3,oneof" json:"create_time,omitempty"`
	// update time
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3,oneof" json:"update_time,omitempty"`
	// delete time
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Environment) Reset() {
	*x = Environment{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Environment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Environment) ProtoMessage() {}

func (x *Environment) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Environment.ProtoReflect.Descriptor instead.
func (*Environment) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{4}
}

func (x *Environment) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *Environment) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Environment) GetIsManaged() bool {
	if x != nil {
		return x.IsManaged
	}
	return false
}

func (x *Environment) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *Environment) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *Environment) GetDatabases() []*PlatformIdentifier {
	if x != nil {
		return x.Databases
	}
	return nil
}

func (x *Environment) GetCaches() []*PlatformIdentifier {
	if x != nil {
		return x.Caches
	}
	return nil
}

func (x *Environment) GetMessageQueues() []*PlatformIdentifier {
	if x != nil {
		return x.MessageQueues
	}
	return nil
}

func (x *Environment) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Environment) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Environment) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *Environment) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// Cluster
type Cluster struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cluster identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// cluster version
	Version string `protobuf:"bytes,2,opt,name=version,proto3" json:"version,omitempty"`
	// cluster endpoint
	Endpoint string `protobuf:"bytes,3,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	// cluster status
	Status string `protobuf:"bytes,4,opt,name=status,proto3" json:"status,omitempty"`
	// cluster labels
	Labels map[string]string `protobuf:"bytes,6,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// create time
	CreateTime    *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Cluster) Reset() {
	*x = Cluster{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Cluster) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cluster) ProtoMessage() {}

func (x *Cluster) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cluster.ProtoReflect.Descriptor instead.
func (*Cluster) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{5}
}

func (x *Cluster) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *Cluster) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Cluster) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *Cluster) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Cluster) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Cluster) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

// NodeGroup
type NodeGroup struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// nodegroup identifier
	Identifier *NodeGroup_Identifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// node group status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// The instance class of the node group
	InstanceClass string `protobuf:"bytes,3,opt,name=instance_class,json=instanceClass,proto3" json:"instance_class,omitempty"`
	// The image used by the node group
	Image string `protobuf:"bytes,4,opt,name=image,proto3" json:"image,omitempty"`
	// Minimum number of nodes in a node group
	MinSize *int32 `protobuf:"varint,5,opt,name=min_size,json=minSize,proto3,oneof" json:"min_size,omitempty"`
	// Maximum number of nodes in a node group
	MaxSize *int32 `protobuf:"varint,6,opt,name=max_size,json=maxSize,proto3,oneof" json:"max_size,omitempty"`
	// Desired number of nodes in a node group
	DesiredSize *int32 `protobuf:"varint,7,opt,name=desired_size,json=desiredSize,proto3,oneof" json:"desired_size,omitempty"`
	// disk size for instance
	DiskSize *int32 `protobuf:"varint,8,opt,name=disk_size,json=diskSize,proto3,oneof" json:"disk_size,omitempty"`
	// The labels attached to the node group
	Labels map[string]string `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// extra info
	Extra *structpb.Struct `protobuf:"bytes,14,opt,name=extra,proto3" json:"extra,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeGroup) Reset() {
	*x = NodeGroup{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeGroup) ProtoMessage() {}

func (x *NodeGroup) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeGroup.ProtoReflect.Descriptor instead.
func (*NodeGroup) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{6}
}

func (x *NodeGroup) GetIdentifier() *NodeGroup_Identifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *NodeGroup) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *NodeGroup) GetInstanceClass() string {
	if x != nil {
		return x.InstanceClass
	}
	return ""
}

func (x *NodeGroup) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *NodeGroup) GetMinSize() int32 {
	if x != nil && x.MinSize != nil {
		return *x.MinSize
	}
	return 0
}

func (x *NodeGroup) GetMaxSize() int32 {
	if x != nil && x.MaxSize != nil {
		return *x.MaxSize
	}
	return 0
}

func (x *NodeGroup) GetDesiredSize() int32 {
	if x != nil && x.DesiredSize != nil {
		return *x.DesiredSize
	}
	return 0
}

func (x *NodeGroup) GetDiskSize() int32 {
	if x != nil && x.DiskSize != nil {
		return *x.DiskSize
	}
	return 0
}

func (x *NodeGroup) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *NodeGroup) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *NodeGroup) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *NodeGroup) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// Database
type Database struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// database identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// database status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// database engine
	Engine string `protobuf:"bytes,3,opt,name=engine,proto3" json:"engine,omitempty"`
	// database engine version
	Version string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	// database endpoints
	Endpoints []*Endpoint `protobuf:"bytes,5,rep,name=endpoints,proto3" json:"endpoints,omitempty"`
	// database port
	Port int32 `protobuf:"varint,6,opt,name=port,proto3" json:"port,omitempty"`
	// database instance class
	InstanceClass string `protobuf:"bytes,7,opt,name=instance_class,json=instanceClass,proto3" json:"instance_class,omitempty"`
	// database storage size
	AllocatedSize int32 `protobuf:"varint,8,opt,name=allocated_size,json=allocatedSize,proto3" json:"allocated_size,omitempty"`
	// Whether the database is publicly accessible
	PubliclyAccessible bool `protobuf:"varint,9,opt,name=publicly_accessible,json=publiclyAccessible,proto3" json:"publicly_accessible,omitempty"`
	// The labels attached to the database
	Labels map[string]string `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// extra info
	Extra *structpb.Struct `protobuf:"bytes,14,opt,name=extra,proto3" json:"extra,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Database) Reset() {
	*x = Database{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Database) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Database) ProtoMessage() {}

func (x *Database) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Database.ProtoReflect.Descriptor instead.
func (*Database) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{7}
}

func (x *Database) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *Database) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Database) GetEngine() string {
	if x != nil {
		return x.Engine
	}
	return ""
}

func (x *Database) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Database) GetEndpoints() []*Endpoint {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *Database) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Database) GetInstanceClass() string {
	if x != nil {
		return x.InstanceClass
	}
	return ""
}

func (x *Database) GetAllocatedSize() int32 {
	if x != nil {
		return x.AllocatedSize
	}
	return 0
}

func (x *Database) GetPubliclyAccessible() bool {
	if x != nil {
		return x.PubliclyAccessible
	}
	return false
}

func (x *Database) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Database) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Database) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Database) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// Cache
type Cache struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cache identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// cache status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// cache engine
	Engine string `protobuf:"bytes,3,opt,name=engine,proto3" json:"engine,omitempty"`
	// cache engine version
	Version string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	// cache endpoints
	Endpoints []*Endpoint `protobuf:"bytes,5,rep,name=endpoints,proto3" json:"endpoints,omitempty"`
	// cache engine version
	Port int32 `protobuf:"varint,6,opt,name=port,proto3" json:"port,omitempty"`
	// cache instance class
	InstanceClass string `protobuf:"bytes,7,opt,name=instance_class,json=instanceClass,proto3" json:"instance_class,omitempty"`
	// cache storage size
	AllocatedSize int32 `protobuf:"varint,8,opt,name=allocated_size,json=allocatedSize,proto3" json:"allocated_size,omitempty"`
	// whether the cache is publicly accessible
	PubliclyAccessible bool `protobuf:"varint,9,opt,name=publicly_accessible,json=publiclyAccessible,proto3" json:"publicly_accessible,omitempty"`
	// whether the cache is enable tls
	Tls bool `protobuf:"varint,10,opt,name=tls,proto3" json:"tls,omitempty"`
	// The labels attached to the cache
	Labels map[string]string `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// extra info
	Extra *structpb.Struct `protobuf:"bytes,14,opt,name=extra,proto3" json:"extra,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Cache) Reset() {
	*x = Cache{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Cache) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Cache) ProtoMessage() {}

func (x *Cache) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Cache.ProtoReflect.Descriptor instead.
func (*Cache) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{8}
}

func (x *Cache) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *Cache) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *Cache) GetEngine() string {
	if x != nil {
		return x.Engine
	}
	return ""
}

func (x *Cache) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *Cache) GetEndpoints() []*Endpoint {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *Cache) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *Cache) GetInstanceClass() string {
	if x != nil {
		return x.InstanceClass
	}
	return ""
}

func (x *Cache) GetAllocatedSize() int32 {
	if x != nil {
		return x.AllocatedSize
	}
	return 0
}

func (x *Cache) GetPubliclyAccessible() bool {
	if x != nil {
		return x.PubliclyAccessible
	}
	return false
}

func (x *Cache) GetTls() bool {
	if x != nil {
		return x.Tls
	}
	return false
}

func (x *Cache) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Cache) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *Cache) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Cache) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// MessageQueue
type MessageQueue struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// MQ identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// MQ status
	Status string `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
	// MQ engine
	Engine string `protobuf:"bytes,3,opt,name=engine,proto3" json:"engine,omitempty"`
	// MQ engine version
	Version string `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	// MQ endpoints
	Endpoints []*Endpoint `protobuf:"bytes,5,rep,name=endpoints,proto3" json:"endpoints,omitempty"`
	// MQ replicas
	Replicas int32 `protobuf:"varint,6,opt,name=replicas,proto3" json:"replicas,omitempty"`
	// MQ storage size
	AllocatedSize int32 `protobuf:"varint,7,opt,name=allocated_size,json=allocatedSize,proto3" json:"allocated_size,omitempty"`
	// MQ instance class
	InstanceClass string `protobuf:"bytes,8,opt,name=instance_class,json=instanceClass,proto3" json:"instance_class,omitempty"`
	// Whether the MQ has deletion protection enabled
	DeletionProtection bool `protobuf:"varint,9,opt,name=deletion_protection,json=deletionProtection,proto3" json:"deletion_protection,omitempty"`
	// Whether the MQ is publicly accessible
	PubliclyAccessible bool `protobuf:"varint,10,opt,name=publicly_accessible,json=publiclyAccessible,proto3" json:"publicly_accessible,omitempty"`
	// The labels attached to the MQ
	Labels map[string]string `protobuf:"bytes,13,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// MQ extra info
	Extra *structpb.Struct `protobuf:"bytes,14,opt,name=extra,proto3" json:"extra,omitempty"`
	// create time
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// update time
	UpdateTime    *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageQueue) Reset() {
	*x = MessageQueue{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageQueue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageQueue) ProtoMessage() {}

func (x *MessageQueue) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageQueue.ProtoReflect.Descriptor instead.
func (*MessageQueue) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{9}
}

func (x *MessageQueue) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *MessageQueue) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *MessageQueue) GetEngine() string {
	if x != nil {
		return x.Engine
	}
	return ""
}

func (x *MessageQueue) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *MessageQueue) GetEndpoints() []*Endpoint {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *MessageQueue) GetReplicas() int32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *MessageQueue) GetAllocatedSize() int32 {
	if x != nil {
		return x.AllocatedSize
	}
	return 0
}

func (x *MessageQueue) GetInstanceClass() string {
	if x != nil {
		return x.InstanceClass
	}
	return ""
}

func (x *MessageQueue) GetDeletionProtection() bool {
	if x != nil {
		return x.DeletionProtection
	}
	return false
}

func (x *MessageQueue) GetPubliclyAccessible() bool {
	if x != nil {
		return x.PubliclyAccessible
	}
	return false
}

func (x *MessageQueue) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *MessageQueue) GetExtra() *structpb.Struct {
	if x != nil {
		return x.Extra
	}
	return nil
}

func (x *MessageQueue) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *MessageQueue) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// Topic
type Topic struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// topic id
	Id *string `protobuf:"bytes,1,opt,name=id,proto3,oneof" json:"id,omitempty"`
	// topic name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// topic engine
	Engine string `protobuf:"bytes,3,opt,name=engine,proto3" json:"engine,omitempty"`
	// topic tenant
	Tenant *string `protobuf:"bytes,4,opt,name=tenant,proto3,oneof" json:"tenant,omitempty"`
	// topic namespace
	Namespace *string `protobuf:"bytes,5,opt,name=namespace,proto3,oneof" json:"namespace,omitempty"`
	// is internal topic?
	Internal *bool `protobuf:"varint,6,opt,name=internal,proto3,oneof" json:"internal,omitempty"`
	// partition number
	PartitionNumber *int32 `protobuf:"varint,7,opt,name=partition_number,json=partitionNumber,proto3,oneof" json:"partition_number,omitempty"`
	// replication factor
	ReplicationFactor *int32 `protobuf:"varint,8,opt,name=replication_factor,json=replicationFactor,proto3,oneof" json:"replication_factor,omitempty"`
	// extra config
	Config        *structpb.Struct `protobuf:"bytes,9,opt,name=config,proto3,oneof" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Topic) Reset() {
	*x = Topic{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Topic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Topic) ProtoMessage() {}

func (x *Topic) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Topic.ProtoReflect.Descriptor instead.
func (*Topic) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{10}
}

func (x *Topic) GetId() string {
	if x != nil && x.Id != nil {
		return *x.Id
	}
	return ""
}

func (x *Topic) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Topic) GetEngine() string {
	if x != nil {
		return x.Engine
	}
	return ""
}

func (x *Topic) GetTenant() string {
	if x != nil && x.Tenant != nil {
		return *x.Tenant
	}
	return ""
}

func (x *Topic) GetNamespace() string {
	if x != nil && x.Namespace != nil {
		return *x.Namespace
	}
	return ""
}

func (x *Topic) GetInternal() bool {
	if x != nil && x.Internal != nil {
		return *x.Internal
	}
	return false
}

func (x *Topic) GetPartitionNumber() int32 {
	if x != nil && x.PartitionNumber != nil {
		return *x.PartitionNumber
	}
	return 0
}

func (x *Topic) GetReplicationFactor() int32 {
	if x != nil && x.ReplicationFactor != nil {
		return *x.ReplicationFactor
	}
	return 0
}

func (x *Topic) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

// Identifier
type NodeGroup_Identifier struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The platform where the node group is located
	Platform string `protobuf:"bytes,1,opt,name=platform,proto3" json:"platform,omitempty"`
	// The cluster where the node group is located
	Cluster string `protobuf:"bytes,2,opt,name=cluster,proto3" json:"cluster,omitempty"`
	// node group name
	Name          string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeGroup_Identifier) Reset() {
	*x = NodeGroup_Identifier{}
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeGroup_Identifier) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeGroup_Identifier) ProtoMessage() {}

func (x *NodeGroup_Identifier) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_resource_models_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeGroup_Identifier.ProtoReflect.Descriptor instead.
func (*NodeGroup_Identifier) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP(), []int{6, 1}
}

func (x *NodeGroup_Identifier) GetPlatform() string {
	if x != nil {
		return x.Platform
	}
	return ""
}

func (x *NodeGroup_Identifier) GetCluster() string {
	if x != nil {
		return x.Cluster
	}
	return ""
}

func (x *NodeGroup_Identifier) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_backend_proto_tools_v1_resource_models_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_resource_models_proto_rawDesc = "" +
	"\n" +
	",backend/proto/tools/v1/resource_models.proto\x12\x16backend.proto.tools.v1\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\"d\n" +
	"\x12PlatformIdentifier\x12$\n" +
	"\bplatform\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01R\bplatform\x12(\n" +
	"\n" +
	"identifier\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x18\x80\bR\n" +
	"identifier\"a\n" +
	"\x15EnvironmentIdentifier\x12!\n" +
	"\acluster\x18\x01 \x01(\tB\a\xbaH\x04r\x02\x18?R\acluster\x12%\n" +
	"\tnamespace\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x18?R\tnamespace\"r\n" +
	"\x12ResourceIdentifier\x12$\n" +
	"\vapi_version\x18\x01 \x01(\tH\x00R\n" +
	"apiVersion\x88\x01\x01\x12\x12\n" +
	"\x04kind\x18\x02 \x01(\tR\x04kind\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04nameB\x0e\n" +
	"\f_api_version\"M\n" +
	"\bEndpoint\x12\"\n" +
	"\aaddress\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x18\x80\bR\aaddress\x12\x1d\n" +
	"\x04port\x18\x02 \x01(\x05B\t\xbaH\x06\x1a\x04\x10\x80\x80\x04R\x04port\"\x91\x06\n" +
	"\vEnvironment\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x1d\n" +
	"\n" +
	"is_managed\x18\x03 \x01(\bR\tisManaged\x12 \n" +
	"\x04name\x18\x04 \x01(\tB\a\xbaH\x04r\x02\x18@H\x00R\x04name\x88\x01\x01\x12/\n" +
	"\vdescription\x18\x05 \x01(\tB\b\xbaH\x05r\x03\x18\x80\bH\x01R\vdescription\x88\x01\x01\x12H\n" +
	"\tdatabases\x18\x06 \x03(\v2*.backend.proto.tools.v1.PlatformIdentifierR\tdatabases\x12B\n" +
	"\x06caches\x18\a \x03(\v2*.backend.proto.tools.v1.PlatformIdentifierR\x06caches\x12Q\n" +
	"\x0emessage_queues\x18\b \x03(\v2*.backend.proto.tools.v1.PlatformIdentifierR\rmessageQueues\x122\n" +
	"\x05extra\x18\r \x01(\v2\x17.google.protobuf.StructH\x02R\x05extra\x88\x01\x01\x12@\n" +
	"\vcreate_time\x18\x0e \x01(\v2\x1a.google.protobuf.TimestampH\x03R\n" +
	"createTime\x88\x01\x01\x12@\n" +
	"\vupdate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampH\x04R\n" +
	"updateTime\x88\x01\x01\x12@\n" +
	"\vdelete_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampH\x05R\n" +
	"deleteTime\x88\x01\x01B\a\n" +
	"\x05_nameB\x0e\n" +
	"\f_descriptionB\b\n" +
	"\x06_extraB\x0e\n" +
	"\f_create_timeB\x0e\n" +
	"\f_update_timeB\x0e\n" +
	"\f_delete_time\"\xe0\x02\n" +
	"\aCluster\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12\x18\n" +
	"\aversion\x18\x02 \x01(\tR\aversion\x12\x1a\n" +
	"\bendpoint\x18\x03 \x01(\tR\bendpoint\x12\x16\n" +
	"\x06status\x18\x04 \x01(\tR\x06status\x12C\n" +
	"\x06labels\x18\x06 \x03(\v2+.backend.proto.tools.v1.Cluster.LabelsEntryR\x06labels\x12;\n" +
	"\vcreate_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xa8\x06\n" +
	"\tNodeGroup\x12L\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2,.backend.proto.tools.v1.NodeGroup.IdentifierR\n" +
	"identifier\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12%\n" +
	"\x0einstance_class\x18\x03 \x01(\tR\rinstanceClass\x12\x14\n" +
	"\x05image\x18\x04 \x01(\tR\x05image\x12\x1e\n" +
	"\bmin_size\x18\x05 \x01(\x05H\x00R\aminSize\x88\x01\x01\x12\x1e\n" +
	"\bmax_size\x18\x06 \x01(\x05H\x01R\amaxSize\x88\x01\x01\x12&\n" +
	"\fdesired_size\x18\a \x01(\x05H\x02R\vdesiredSize\x88\x01\x01\x12 \n" +
	"\tdisk_size\x18\b \x01(\x05H\x03R\bdiskSize\x88\x01\x01\x12]\n" +
	"\x06labels\x18\r \x03(\v2-.backend.proto.tools.v1.NodeGroup.LabelsEntryB\x16\xbaH\x13\x9a\x01\x10\x10\x80\x02\"\x05r\x03\x18\xff\x01*\x04r\x02\x18?R\x06labels\x12-\n" +
	"\x05extra\x18\x0e \x01(\v2\x17.google.protobuf.StructR\x05extra\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\x1ar\n" +
	"\n" +
	"Identifier\x12$\n" +
	"\bplatform\x18\x01 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01R\bplatform\x12!\n" +
	"\acluster\x18\x02 \x01(\tB\a\xbaH\x04r\x02\x18?R\acluster\x12\x1b\n" +
	"\x04name\x18\x03 \x01(\tB\a\xbaH\x04r\x02\x18?R\x04nameB\v\n" +
	"\t_min_sizeB\v\n" +
	"\t_max_sizeB\x0f\n" +
	"\r_desired_sizeB\f\n" +
	"\n" +
	"_disk_size\"\xb5\x05\n" +
	"\bDatabase\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x16\n" +
	"\x06engine\x18\x03 \x01(\tR\x06engine\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x12>\n" +
	"\tendpoints\x18\x05 \x03(\v2 .backend.proto.tools.v1.EndpointR\tendpoints\x12\x12\n" +
	"\x04port\x18\x06 \x01(\x05R\x04port\x12%\n" +
	"\x0einstance_class\x18\a \x01(\tR\rinstanceClass\x12%\n" +
	"\x0eallocated_size\x18\b \x01(\x05R\rallocatedSize\x12/\n" +
	"\x13publicly_accessible\x18\t \x01(\bR\x12publiclyAccessible\x12\\\n" +
	"\x06labels\x18\r \x03(\v2,.backend.proto.tools.v1.Database.LabelsEntryB\x16\xbaH\x13\x9a\x01\x10\x10\x80\x02\"\x05r\x03\x18\xff\x01*\x04r\x02\x18?R\x06labels\x12-\n" +
	"\x05extra\x18\x0e \x01(\v2\x17.google.protobuf.StructR\x05extra\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xc1\x05\n" +
	"\x05Cache\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x16\n" +
	"\x06engine\x18\x03 \x01(\tR\x06engine\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x12>\n" +
	"\tendpoints\x18\x05 \x03(\v2 .backend.proto.tools.v1.EndpointR\tendpoints\x12\x12\n" +
	"\x04port\x18\x06 \x01(\x05R\x04port\x12%\n" +
	"\x0einstance_class\x18\a \x01(\tR\rinstanceClass\x12%\n" +
	"\x0eallocated_size\x18\b \x01(\x05R\rallocatedSize\x12/\n" +
	"\x13publicly_accessible\x18\t \x01(\bR\x12publiclyAccessible\x12\x10\n" +
	"\x03tls\x18\n" +
	" \x01(\bR\x03tls\x12Y\n" +
	"\x06labels\x18\r \x03(\v2).backend.proto.tools.v1.Cache.LabelsEntryB\x16\xbaH\x13\x9a\x01\x10\x10\x80\x02\"\x05r\x03\x18\xff\x01*\x04r\x02\x18?R\x06labels\x12-\n" +
	"\x05extra\x18\x0e \x01(\v2\x17.google.protobuf.StructR\x05extra\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xf6\x05\n" +
	"\fMessageQueue\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12\x16\n" +
	"\x06status\x18\x02 \x01(\tR\x06status\x12\x16\n" +
	"\x06engine\x18\x03 \x01(\tR\x06engine\x12\x18\n" +
	"\aversion\x18\x04 \x01(\tR\aversion\x12>\n" +
	"\tendpoints\x18\x05 \x03(\v2 .backend.proto.tools.v1.EndpointR\tendpoints\x12\x1a\n" +
	"\breplicas\x18\x06 \x01(\x05R\breplicas\x12%\n" +
	"\x0eallocated_size\x18\a \x01(\x05R\rallocatedSize\x12%\n" +
	"\x0einstance_class\x18\b \x01(\tR\rinstanceClass\x12/\n" +
	"\x13deletion_protection\x18\t \x01(\bR\x12deletionProtection\x12/\n" +
	"\x13publicly_accessible\x18\n" +
	" \x01(\bR\x12publiclyAccessible\x12`\n" +
	"\x06labels\x18\r \x03(\v20.backend.proto.tools.v1.MessageQueue.LabelsEntryB\x16\xbaH\x13\x9a\x01\x10\x10\x80\x02\"\x05r\x03\x18\xff\x01*\x04r\x02\x18?R\x06labels\x12-\n" +
	"\x05extra\x18\x0e \x01(\v2\x17.google.protobuf.StructR\x05extra\x12;\n" +
	"\vcreate_time\x18\x0f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x10 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01\"\xa7\x03\n" +
	"\x05Topic\x12\x13\n" +
	"\x02id\x18\x01 \x01(\tH\x00R\x02id\x88\x01\x01\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06engine\x18\x03 \x01(\tR\x06engine\x12\x1b\n" +
	"\x06tenant\x18\x04 \x01(\tH\x01R\x06tenant\x88\x01\x01\x12!\n" +
	"\tnamespace\x18\x05 \x01(\tH\x02R\tnamespace\x88\x01\x01\x12\x1f\n" +
	"\binternal\x18\x06 \x01(\bH\x03R\binternal\x88\x01\x01\x12.\n" +
	"\x10partition_number\x18\a \x01(\x05H\x04R\x0fpartitionNumber\x88\x01\x01\x122\n" +
	"\x12replication_factor\x18\b \x01(\x05H\x05R\x11replicationFactor\x88\x01\x01\x124\n" +
	"\x06config\x18\t \x01(\v2\x17.google.protobuf.StructH\x06R\x06config\x88\x01\x01B\x05\n" +
	"\x03_idB\t\n" +
	"\a_tenantB\f\n" +
	"\n" +
	"_namespaceB\v\n" +
	"\t_internalB\x13\n" +
	"\x11_partition_numberB\x15\n" +
	"\x13_replication_factorB\t\n" +
	"\a_config*\x95\x01\n" +
	"\fPlatformType\x12\x1d\n" +
	"\x19PLATFORM_TYPE_UNSPECIFIED\x10\x00\x12\x0f\n" +
	"\vSELF_HOSTED\x10\x01\x12\v\n" +
	"\aAWS_EKS\x10\v\x12\v\n" +
	"\aAWS_ECR\x10\f\x12\v\n" +
	"\aAWS_RDS\x10\r\x12\v\n" +
	"\aAWS_MSK\x10\x0e\x12\n" +
	"\n" +
	"\x06AWS_S3\x10\x0f\x12\x15\n" +
	"\x11AWS_ELASTIC_CACHE\x10\x10Bb\n" +
	" com.moego.backend.proto.tools.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_resource_models_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_resource_models_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_resource_models_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_resource_models_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_resource_models_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_resource_models_proto_rawDesc), len(file_backend_proto_tools_v1_resource_models_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_resource_models_proto_rawDescData
}

var file_backend_proto_tools_v1_resource_models_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_tools_v1_resource_models_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_backend_proto_tools_v1_resource_models_proto_goTypes = []any{
	(PlatformType)(0),             // 0: backend.proto.tools.v1.PlatformType
	(*PlatformIdentifier)(nil),    // 1: backend.proto.tools.v1.PlatformIdentifier
	(*EnvironmentIdentifier)(nil), // 2: backend.proto.tools.v1.EnvironmentIdentifier
	(*ResourceIdentifier)(nil),    // 3: backend.proto.tools.v1.ResourceIdentifier
	(*Endpoint)(nil),              // 4: backend.proto.tools.v1.Endpoint
	(*Environment)(nil),           // 5: backend.proto.tools.v1.Environment
	(*Cluster)(nil),               // 6: backend.proto.tools.v1.Cluster
	(*NodeGroup)(nil),             // 7: backend.proto.tools.v1.NodeGroup
	(*Database)(nil),              // 8: backend.proto.tools.v1.Database
	(*Cache)(nil),                 // 9: backend.proto.tools.v1.Cache
	(*MessageQueue)(nil),          // 10: backend.proto.tools.v1.MessageQueue
	(*Topic)(nil),                 // 11: backend.proto.tools.v1.Topic
	nil,                           // 12: backend.proto.tools.v1.Cluster.LabelsEntry
	nil,                           // 13: backend.proto.tools.v1.NodeGroup.LabelsEntry
	(*NodeGroup_Identifier)(nil),  // 14: backend.proto.tools.v1.NodeGroup.Identifier
	nil,                           // 15: backend.proto.tools.v1.Database.LabelsEntry
	nil,                           // 16: backend.proto.tools.v1.Cache.LabelsEntry
	nil,                           // 17: backend.proto.tools.v1.MessageQueue.LabelsEntry
	(*structpb.Struct)(nil),       // 18: google.protobuf.Struct
	(*timestamppb.Timestamp)(nil), // 19: google.protobuf.Timestamp
}
var file_backend_proto_tools_v1_resource_models_proto_depIdxs = []int32{
	1,  // 0: backend.proto.tools.v1.Environment.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	1,  // 1: backend.proto.tools.v1.Environment.databases:type_name -> backend.proto.tools.v1.PlatformIdentifier
	1,  // 2: backend.proto.tools.v1.Environment.caches:type_name -> backend.proto.tools.v1.PlatformIdentifier
	1,  // 3: backend.proto.tools.v1.Environment.message_queues:type_name -> backend.proto.tools.v1.PlatformIdentifier
	18, // 4: backend.proto.tools.v1.Environment.extra:type_name -> google.protobuf.Struct
	19, // 5: backend.proto.tools.v1.Environment.create_time:type_name -> google.protobuf.Timestamp
	19, // 6: backend.proto.tools.v1.Environment.update_time:type_name -> google.protobuf.Timestamp
	19, // 7: backend.proto.tools.v1.Environment.delete_time:type_name -> google.protobuf.Timestamp
	1,  // 8: backend.proto.tools.v1.Cluster.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	12, // 9: backend.proto.tools.v1.Cluster.labels:type_name -> backend.proto.tools.v1.Cluster.LabelsEntry
	19, // 10: backend.proto.tools.v1.Cluster.create_time:type_name -> google.protobuf.Timestamp
	14, // 11: backend.proto.tools.v1.NodeGroup.identifier:type_name -> backend.proto.tools.v1.NodeGroup.Identifier
	13, // 12: backend.proto.tools.v1.NodeGroup.labels:type_name -> backend.proto.tools.v1.NodeGroup.LabelsEntry
	18, // 13: backend.proto.tools.v1.NodeGroup.extra:type_name -> google.protobuf.Struct
	19, // 14: backend.proto.tools.v1.NodeGroup.create_time:type_name -> google.protobuf.Timestamp
	19, // 15: backend.proto.tools.v1.NodeGroup.update_time:type_name -> google.protobuf.Timestamp
	1,  // 16: backend.proto.tools.v1.Database.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	4,  // 17: backend.proto.tools.v1.Database.endpoints:type_name -> backend.proto.tools.v1.Endpoint
	15, // 18: backend.proto.tools.v1.Database.labels:type_name -> backend.proto.tools.v1.Database.LabelsEntry
	18, // 19: backend.proto.tools.v1.Database.extra:type_name -> google.protobuf.Struct
	19, // 20: backend.proto.tools.v1.Database.create_time:type_name -> google.protobuf.Timestamp
	19, // 21: backend.proto.tools.v1.Database.update_time:type_name -> google.protobuf.Timestamp
	1,  // 22: backend.proto.tools.v1.Cache.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	4,  // 23: backend.proto.tools.v1.Cache.endpoints:type_name -> backend.proto.tools.v1.Endpoint
	16, // 24: backend.proto.tools.v1.Cache.labels:type_name -> backend.proto.tools.v1.Cache.LabelsEntry
	18, // 25: backend.proto.tools.v1.Cache.extra:type_name -> google.protobuf.Struct
	19, // 26: backend.proto.tools.v1.Cache.create_time:type_name -> google.protobuf.Timestamp
	19, // 27: backend.proto.tools.v1.Cache.update_time:type_name -> google.protobuf.Timestamp
	1,  // 28: backend.proto.tools.v1.MessageQueue.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	4,  // 29: backend.proto.tools.v1.MessageQueue.endpoints:type_name -> backend.proto.tools.v1.Endpoint
	17, // 30: backend.proto.tools.v1.MessageQueue.labels:type_name -> backend.proto.tools.v1.MessageQueue.LabelsEntry
	18, // 31: backend.proto.tools.v1.MessageQueue.extra:type_name -> google.protobuf.Struct
	19, // 32: backend.proto.tools.v1.MessageQueue.create_time:type_name -> google.protobuf.Timestamp
	19, // 33: backend.proto.tools.v1.MessageQueue.update_time:type_name -> google.protobuf.Timestamp
	18, // 34: backend.proto.tools.v1.Topic.config:type_name -> google.protobuf.Struct
	35, // [35:35] is the sub-list for method output_type
	35, // [35:35] is the sub-list for method input_type
	35, // [35:35] is the sub-list for extension type_name
	35, // [35:35] is the sub-list for extension extendee
	0,  // [0:35] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_resource_models_proto_init() }
func file_backend_proto_tools_v1_resource_models_proto_init() {
	if File_backend_proto_tools_v1_resource_models_proto != nil {
		return
	}
	file_backend_proto_tools_v1_resource_models_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_tools_v1_resource_models_proto_msgTypes[4].OneofWrappers = []any{}
	file_backend_proto_tools_v1_resource_models_proto_msgTypes[6].OneofWrappers = []any{}
	file_backend_proto_tools_v1_resource_models_proto_msgTypes[10].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_resource_models_proto_rawDesc), len(file_backend_proto_tools_v1_resource_models_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_tools_v1_resource_models_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_resource_models_proto_depIdxs,
		EnumInfos:         file_backend_proto_tools_v1_resource_models_proto_enumTypes,
		MessageInfos:      file_backend_proto_tools_v1_resource_models_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_resource_models_proto = out.File
	file_backend_proto_tools_v1_resource_models_proto_goTypes = nil
	file_backend_proto_tools_v1_resource_models_proto_depIdxs = nil
}
