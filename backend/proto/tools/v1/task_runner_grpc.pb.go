// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-package=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-multiple-files=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/tools/v1/task_runner.proto

package toolspb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TaskRunner_RunTask_FullMethodName = "/backend.proto.tools.v1.TaskRunner/RunTask"
)

// TaskRunnerClient is the client API for TaskRunner service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// TaskRunner 定义了任务运行器服务的接口。
type TaskRunnerClient interface {
	// RunTask executes a specific task by name
	RunTask(ctx context.Context, in *RunTaskRequest, opts ...grpc.CallOption) (*DefaultResponse, error)
}

type taskRunnerClient struct {
	cc grpc.ClientConnInterface
}

func NewTaskRunnerClient(cc grpc.ClientConnInterface) TaskRunnerClient {
	return &taskRunnerClient{cc}
}

func (c *taskRunnerClient) RunTask(ctx context.Context, in *RunTaskRequest, opts ...grpc.CallOption) (*DefaultResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DefaultResponse)
	err := c.cc.Invoke(ctx, TaskRunner_RunTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TaskRunnerServer is the server API for TaskRunner service.
// All implementations must embed UnimplementedTaskRunnerServer
// for forward compatibility.
//
// TaskRunner 定义了任务运行器服务的接口。
type TaskRunnerServer interface {
	// RunTask executes a specific task by name
	RunTask(context.Context, *RunTaskRequest) (*DefaultResponse, error)
	mustEmbedUnimplementedTaskRunnerServer()
}

// UnimplementedTaskRunnerServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTaskRunnerServer struct{}

func (UnimplementedTaskRunnerServer) RunTask(context.Context, *RunTaskRequest) (*DefaultResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RunTask not implemented")
}
func (UnimplementedTaskRunnerServer) mustEmbedUnimplementedTaskRunnerServer() {}
func (UnimplementedTaskRunnerServer) testEmbeddedByValue()                    {}

// UnsafeTaskRunnerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TaskRunnerServer will
// result in compilation errors.
type UnsafeTaskRunnerServer interface {
	mustEmbedUnimplementedTaskRunnerServer()
}

func RegisterTaskRunnerServer(s grpc.ServiceRegistrar, srv TaskRunnerServer) {
	// If the following call pancis, it indicates UnimplementedTaskRunnerServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TaskRunner_ServiceDesc, srv)
}

func _TaskRunner_RunTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RunTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TaskRunnerServer).RunTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TaskRunner_RunTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TaskRunnerServer).RunTask(ctx, req.(*RunTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TaskRunner_ServiceDesc is the grpc.ServiceDesc for TaskRunner service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TaskRunner_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.tools.v1.TaskRunner",
	HandlerType: (*TaskRunnerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RunTask",
			Handler:    _TaskRunner_RunTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/tools/v1/task_runner.proto",
}
