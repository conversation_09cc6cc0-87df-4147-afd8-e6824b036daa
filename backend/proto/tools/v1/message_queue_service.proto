// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many MQ resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many MQ resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many MQ resources. --)

syntax = "proto3";

package backend.proto.tools.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "backend/proto/tools/v1/resource_models.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "buf/validate/validate.proto";

// MessageQueueService
service MessageQueueService {

    // register MQ
    rpc RegisterMessageQueue(RegisterMessageQueueRequest) returns (RegisterMessageQueueResponse);

    // list MQ
    rpc ListMessageQueues(ListMessageQueuesRequest) returns (ListMessageQueuesResponse);

    // get a MQ
    rpc GetMessageQueue(GetMessageQueueRequest) returns (MessageQueue);

    // list topics
    rpc ListTopics(ListTopicsRequest) returns (ListTopicsResponse);

    // create topics
    // (-- api-linter: core::0133::response-message-name=disabled
    //     aip.dev/not-precedent: We need to do this because this is create multiple topics. --)
    rpc CreateTopics(CreateTopicsRequest) returns (CreateTopicsResponse);

    // delete topics
    rpc DeleteTopics(DeleteTopicsRequest) returns (google.protobuf.Empty);

    // update topic
    rpc UpdateTopic(UpdateTopicRequest) returns (Topic);

}

// RegisterMessageQueueRequest
message RegisterMessageQueueRequest {
    // MQ identifier
    PlatformIdentifier identifier = 1;
}

// RegisterMessageQueueResponse
message RegisterMessageQueueResponse {
    // message queue
    optional MessageQueue message_queue = 1;
}

// ListMessageQueuesRequest
message ListMessageQueuesRequest {
    // the platform where the message queue is running
    optional string platform = 2 [(buf.validate.field) = { string: { max_len: 128 } }];
    // MQ engine
    optional string engine = 3 [(buf.validate.field) = { string: { max_len: 128 } }];
    // MQ labels
    map<string, string> labels = 4 [(buf.validate.field) = { map: {
        max_pairs: 256,
        keys: {
            string: { max_len: 255 }
        },
        values: {
            string: { max_len: 63 }
        }
    } }];
}

// ListMessageQueuesResponse
message ListMessageQueuesResponse {
    // list of message queues
    repeated MessageQueue message_queues = 1;
}

// GetMessageQueueRequest
message GetMessageQueueRequest {
    // message queue identifier
    PlatformIdentifier identifier = 1;
}

// ListTopicsRequest
message ListTopicsRequest {
    // message queue identifier
    PlatformIdentifier identifier = 1;
    // topic tenant
    optional string tenant = 2;
    // topic namespace
    optional string namespace = 3;
    // whether should list internal topics.
    optional bool internal = 4;
}

// ListTopicsResponse
message ListTopicsResponse {
    // list of topics
    repeated Topic topics = 1;
}

// CreateTopicsRequest
// (-- api-linter: core::0133::request-resource-field=disabled
//     aip.dev/not-precedent: We need to do this because this is create multiple topics. --)
message CreateTopicsRequest {
    // message queue identifier
    PlatformIdentifier identifier = 1;
    // list of topic details
    repeated Topic topics = 2;
}

// CreateTopicsResponse
message CreateTopicsResponse {
    // list of topic names
    repeated Topic topics = 1;
}

// DeleteTopicsRequest
message DeleteTopicsRequest {
    // message queue identifier
    PlatformIdentifier identifier = 1;
    // list of topic names
    repeated string topics = 2;
}

// UpdateTopicRequest
message UpdateTopicRequest {
    // message queue identifier
    PlatformIdentifier identifier = 1;
    // topic name
    string name = 2;
    // partition number
    optional int32 partition_number = 3;
    // replication factor
    optional int32 replication_factor = 4;
    // extra config
    optional google.protobuf.Struct config = 5;
}
