// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are are only three environments. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are are only three environments. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are are only three environments. --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/tools/v1/environment_service.proto

package toolspb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	EnvironmentService_RegisterEnvironments_FullMethodName = "/backend.proto.tools.v1.EnvironmentService/RegisterEnvironments"
	EnvironmentService_ListEnvironments_FullMethodName     = "/backend.proto.tools.v1.EnvironmentService/ListEnvironments"
	EnvironmentService_GetEnvironment_FullMethodName       = "/backend.proto.tools.v1.EnvironmentService/GetEnvironment"
	EnvironmentService_UpdateEnvironment_FullMethodName    = "/backend.proto.tools.v1.EnvironmentService/UpdateEnvironment"
	EnvironmentService_CheckEnvironment_FullMethodName     = "/backend.proto.tools.v1.EnvironmentService/CheckEnvironment"
)

// EnvironmentServiceClient is the client API for EnvironmentService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// EnvironmentService
type EnvironmentServiceClient interface {
	// register environments
	RegisterEnvironments(ctx context.Context, in *RegisterEnvironmentsRequest, opts ...grpc.CallOption) (*RegisterEnvironmentsResponse, error)
	// list environments
	ListEnvironments(ctx context.Context, in *ListEnvironmentsRequest, opts ...grpc.CallOption) (*ListEnvironmentsResponse, error)
	// get a environment
	GetEnvironment(ctx context.Context, in *GetEnvironmentRequest, opts ...grpc.CallOption) (*Environment, error)
	// update environment
	UpdateEnvironment(ctx context.Context, in *UpdateEnvironmentRequest, opts ...grpc.CallOption) (*Environment, error)
	// check environment
	CheckEnvironment(ctx context.Context, in *CheckEnvironmentRequest, opts ...grpc.CallOption) (*CheckEnvironmentResponse, error)
}

type environmentServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEnvironmentServiceClient(cc grpc.ClientConnInterface) EnvironmentServiceClient {
	return &environmentServiceClient{cc}
}

func (c *environmentServiceClient) RegisterEnvironments(ctx context.Context, in *RegisterEnvironmentsRequest, opts ...grpc.CallOption) (*RegisterEnvironmentsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterEnvironmentsResponse)
	err := c.cc.Invoke(ctx, EnvironmentService_RegisterEnvironments_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *environmentServiceClient) ListEnvironments(ctx context.Context, in *ListEnvironmentsRequest, opts ...grpc.CallOption) (*ListEnvironmentsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListEnvironmentsResponse)
	err := c.cc.Invoke(ctx, EnvironmentService_ListEnvironments_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *environmentServiceClient) GetEnvironment(ctx context.Context, in *GetEnvironmentRequest, opts ...grpc.CallOption) (*Environment, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Environment)
	err := c.cc.Invoke(ctx, EnvironmentService_GetEnvironment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *environmentServiceClient) UpdateEnvironment(ctx context.Context, in *UpdateEnvironmentRequest, opts ...grpc.CallOption) (*Environment, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Environment)
	err := c.cc.Invoke(ctx, EnvironmentService_UpdateEnvironment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *environmentServiceClient) CheckEnvironment(ctx context.Context, in *CheckEnvironmentRequest, opts ...grpc.CallOption) (*CheckEnvironmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckEnvironmentResponse)
	err := c.cc.Invoke(ctx, EnvironmentService_CheckEnvironment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EnvironmentServiceServer is the server API for EnvironmentService service.
// All implementations must embed UnimplementedEnvironmentServiceServer
// for forward compatibility.
//
// EnvironmentService
type EnvironmentServiceServer interface {
	// register environments
	RegisterEnvironments(context.Context, *RegisterEnvironmentsRequest) (*RegisterEnvironmentsResponse, error)
	// list environments
	ListEnvironments(context.Context, *ListEnvironmentsRequest) (*ListEnvironmentsResponse, error)
	// get a environment
	GetEnvironment(context.Context, *GetEnvironmentRequest) (*Environment, error)
	// update environment
	UpdateEnvironment(context.Context, *UpdateEnvironmentRequest) (*Environment, error)
	// check environment
	CheckEnvironment(context.Context, *CheckEnvironmentRequest) (*CheckEnvironmentResponse, error)
	mustEmbedUnimplementedEnvironmentServiceServer()
}

// UnimplementedEnvironmentServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedEnvironmentServiceServer struct{}

func (UnimplementedEnvironmentServiceServer) RegisterEnvironments(context.Context, *RegisterEnvironmentsRequest) (*RegisterEnvironmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterEnvironments not implemented")
}
func (UnimplementedEnvironmentServiceServer) ListEnvironments(context.Context, *ListEnvironmentsRequest) (*ListEnvironmentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEnvironments not implemented")
}
func (UnimplementedEnvironmentServiceServer) GetEnvironment(context.Context, *GetEnvironmentRequest) (*Environment, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEnvironment not implemented")
}
func (UnimplementedEnvironmentServiceServer) UpdateEnvironment(context.Context, *UpdateEnvironmentRequest) (*Environment, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEnvironment not implemented")
}
func (UnimplementedEnvironmentServiceServer) CheckEnvironment(context.Context, *CheckEnvironmentRequest) (*CheckEnvironmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckEnvironment not implemented")
}
func (UnimplementedEnvironmentServiceServer) mustEmbedUnimplementedEnvironmentServiceServer() {}
func (UnimplementedEnvironmentServiceServer) testEmbeddedByValue()                            {}

// UnsafeEnvironmentServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EnvironmentServiceServer will
// result in compilation errors.
type UnsafeEnvironmentServiceServer interface {
	mustEmbedUnimplementedEnvironmentServiceServer()
}

func RegisterEnvironmentServiceServer(s grpc.ServiceRegistrar, srv EnvironmentServiceServer) {
	// If the following call pancis, it indicates UnimplementedEnvironmentServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&EnvironmentService_ServiceDesc, srv)
}

func _EnvironmentService_RegisterEnvironments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterEnvironmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnvironmentServiceServer).RegisterEnvironments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EnvironmentService_RegisterEnvironments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnvironmentServiceServer).RegisterEnvironments(ctx, req.(*RegisterEnvironmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnvironmentService_ListEnvironments_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEnvironmentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnvironmentServiceServer).ListEnvironments(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EnvironmentService_ListEnvironments_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnvironmentServiceServer).ListEnvironments(ctx, req.(*ListEnvironmentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnvironmentService_GetEnvironment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEnvironmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnvironmentServiceServer).GetEnvironment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EnvironmentService_GetEnvironment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnvironmentServiceServer).GetEnvironment(ctx, req.(*GetEnvironmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnvironmentService_UpdateEnvironment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEnvironmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnvironmentServiceServer).UpdateEnvironment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EnvironmentService_UpdateEnvironment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnvironmentServiceServer).UpdateEnvironment(ctx, req.(*UpdateEnvironmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EnvironmentService_CheckEnvironment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckEnvironmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EnvironmentServiceServer).CheckEnvironment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: EnvironmentService_CheckEnvironment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EnvironmentServiceServer).CheckEnvironment(ctx, req.(*CheckEnvironmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EnvironmentService_ServiceDesc is the grpc.ServiceDesc for EnvironmentService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EnvironmentService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.tools.v1.EnvironmentService",
	HandlerType: (*EnvironmentServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RegisterEnvironments",
			Handler:    _EnvironmentService_RegisterEnvironments_Handler,
		},
		{
			MethodName: "ListEnvironments",
			Handler:    _EnvironmentService_ListEnvironments_Handler,
		},
		{
			MethodName: "GetEnvironment",
			Handler:    _EnvironmentService_GetEnvironment_Handler,
		},
		{
			MethodName: "UpdateEnvironment",
			Handler:    _EnvironmentService_UpdateEnvironment_Handler,
		},
		{
			MethodName: "CheckEnvironment",
			Handler:    _EnvironmentService_CheckEnvironment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/tools/v1/environment_service.proto",
}
