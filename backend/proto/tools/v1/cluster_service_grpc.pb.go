// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cluster resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cluster resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many cluster resources. --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/tools/v1/cluster_service.proto

package toolspb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	structpb "google.golang.org/protobuf/types/known/structpb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ClusterService_ListClusters_FullMethodName       = "/backend.proto.tools.v1.ClusterService/ListClusters"
	ClusterService_GetCluster_FullMethodName         = "/backend.proto.tools.v1.ClusterService/GetCluster"
	ClusterService_ListNodeGroups_FullMethodName     = "/backend.proto.tools.v1.ClusterService/ListNodeGroups"
	ClusterService_GetNodeGroup_FullMethodName       = "/backend.proto.tools.v1.ClusterService/GetNodeGroup"
	ClusterService_ListNodes_FullMethodName          = "/backend.proto.tools.v1.ClusterService/ListNodes"
	ClusterService_GetNode_FullMethodName            = "/backend.proto.tools.v1.ClusterService/GetNode"
	ClusterService_ListNamespaces_FullMethodName     = "/backend.proto.tools.v1.ClusterService/ListNamespaces"
	ClusterService_GetNamespace_FullMethodName       = "/backend.proto.tools.v1.ClusterService/GetNamespace"
	ClusterService_ListResources_FullMethodName      = "/backend.proto.tools.v1.ClusterService/ListResources"
	ClusterService_GetResource_FullMethodName        = "/backend.proto.tools.v1.ClusterService/GetResource"
	ClusterService_ResetNodeGroups_FullMethodName    = "/backend.proto.tools.v1.ClusterService/ResetNodeGroups"
	ClusterService_ResetResources_FullMethodName     = "/backend.proto.tools.v1.ClusterService/ResetResources"
	ClusterService_RestartWorkloads_FullMethodName   = "/backend.proto.tools.v1.ClusterService/RestartWorkloads"
	ClusterService_DeployApplications_FullMethodName = "/backend.proto.tools.v1.ClusterService/DeployApplications"
)

// ClusterServiceClient is the client API for ClusterService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// ClusterService
type ClusterServiceClient interface {
	// list clusters
	ListClusters(ctx context.Context, in *ListClustersRequest, opts ...grpc.CallOption) (*ListClustersResponse, error)
	// get a cluster
	GetCluster(ctx context.Context, in *GetClusterRequest, opts ...grpc.CallOption) (*Cluster, error)
	// list nodegroups
	ListNodeGroups(ctx context.Context, in *ListNodeGroupsRequest, opts ...grpc.CallOption) (*ListNodeGroupsResponse, error)
	// get a nodegroup
	GetNodeGroup(ctx context.Context, in *GetNodeGroupRequest, opts ...grpc.CallOption) (*NodeGroup, error)
	// list nodes
	ListNodes(ctx context.Context, in *ListNodesRequest, opts ...grpc.CallOption) (*ListNodesResponse, error)
	// get a node
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: We need to do this because the fields of the resource may be dynamic. --)
	GetNode(ctx context.Context, in *GetNodeRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// list namespaces
	ListNamespaces(ctx context.Context, in *ListNamespacesRequest, opts ...grpc.CallOption) (*ListNamespacesResponse, error)
	// get a namespace
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: We need to do this because the fields of the resource may be dynamic. --)
	GetNamespace(ctx context.Context, in *GetNamespaceRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// list resources
	ListResources(ctx context.Context, in *ListResourcesRequest, opts ...grpc.CallOption) (*ListResourcesResponse, error)
	// get resource
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: We need to do this because the fields of the resource may be dynamic. --)
	GetResource(ctx context.Context, in *GetResourceRequest, opts ...grpc.CallOption) (*structpb.Struct, error)
	// reset nodegroup
	ResetNodeGroups(ctx context.Context, in *ResetNodeGroupsRequest, opts ...grpc.CallOption) (*ResetNodeGroupsResponse, error)
	// reset resource
	ResetResources(ctx context.Context, in *ResetResourcesRequest, opts ...grpc.CallOption) (*ResetResourcesResponse, error)
	// restart workloads
	RestartWorkloads(ctx context.Context, in *RestartWorkloadsRequest, opts ...grpc.CallOption) (*RestartWorkloadsResponse, error)
	// deploy apps
	DeployApplications(ctx context.Context, in *DeployApplicationsRequest, opts ...grpc.CallOption) (*DeployApplicationsResponse, error)
}

type clusterServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewClusterServiceClient(cc grpc.ClientConnInterface) ClusterServiceClient {
	return &clusterServiceClient{cc}
}

func (c *clusterServiceClient) ListClusters(ctx context.Context, in *ListClustersRequest, opts ...grpc.CallOption) (*ListClustersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListClustersResponse)
	err := c.cc.Invoke(ctx, ClusterService_ListClusters_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) GetCluster(ctx context.Context, in *GetClusterRequest, opts ...grpc.CallOption) (*Cluster, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Cluster)
	err := c.cc.Invoke(ctx, ClusterService_GetCluster_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) ListNodeGroups(ctx context.Context, in *ListNodeGroupsRequest, opts ...grpc.CallOption) (*ListNodeGroupsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListNodeGroupsResponse)
	err := c.cc.Invoke(ctx, ClusterService_ListNodeGroups_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) GetNodeGroup(ctx context.Context, in *GetNodeGroupRequest, opts ...grpc.CallOption) (*NodeGroup, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(NodeGroup)
	err := c.cc.Invoke(ctx, ClusterService_GetNodeGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) ListNodes(ctx context.Context, in *ListNodesRequest, opts ...grpc.CallOption) (*ListNodesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListNodesResponse)
	err := c.cc.Invoke(ctx, ClusterService_ListNodes_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) GetNode(ctx context.Context, in *GetNodeRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, ClusterService_GetNode_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) ListNamespaces(ctx context.Context, in *ListNamespacesRequest, opts ...grpc.CallOption) (*ListNamespacesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListNamespacesResponse)
	err := c.cc.Invoke(ctx, ClusterService_ListNamespaces_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) GetNamespace(ctx context.Context, in *GetNamespaceRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, ClusterService_GetNamespace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) ListResources(ctx context.Context, in *ListResourcesRequest, opts ...grpc.CallOption) (*ListResourcesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListResourcesResponse)
	err := c.cc.Invoke(ctx, ClusterService_ListResources_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) GetResource(ctx context.Context, in *GetResourceRequest, opts ...grpc.CallOption) (*structpb.Struct, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(structpb.Struct)
	err := c.cc.Invoke(ctx, ClusterService_GetResource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) ResetNodeGroups(ctx context.Context, in *ResetNodeGroupsRequest, opts ...grpc.CallOption) (*ResetNodeGroupsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResetNodeGroupsResponse)
	err := c.cc.Invoke(ctx, ClusterService_ResetNodeGroups_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) ResetResources(ctx context.Context, in *ResetResourcesRequest, opts ...grpc.CallOption) (*ResetResourcesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResetResourcesResponse)
	err := c.cc.Invoke(ctx, ClusterService_ResetResources_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) RestartWorkloads(ctx context.Context, in *RestartWorkloadsRequest, opts ...grpc.CallOption) (*RestartWorkloadsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RestartWorkloadsResponse)
	err := c.cc.Invoke(ctx, ClusterService_RestartWorkloads_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *clusterServiceClient) DeployApplications(ctx context.Context, in *DeployApplicationsRequest, opts ...grpc.CallOption) (*DeployApplicationsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeployApplicationsResponse)
	err := c.cc.Invoke(ctx, ClusterService_DeployApplications_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ClusterServiceServer is the server API for ClusterService service.
// All implementations must embed UnimplementedClusterServiceServer
// for forward compatibility.
//
// ClusterService
type ClusterServiceServer interface {
	// list clusters
	ListClusters(context.Context, *ListClustersRequest) (*ListClustersResponse, error)
	// get a cluster
	GetCluster(context.Context, *GetClusterRequest) (*Cluster, error)
	// list nodegroups
	ListNodeGroups(context.Context, *ListNodeGroupsRequest) (*ListNodeGroupsResponse, error)
	// get a nodegroup
	GetNodeGroup(context.Context, *GetNodeGroupRequest) (*NodeGroup, error)
	// list nodes
	ListNodes(context.Context, *ListNodesRequest) (*ListNodesResponse, error)
	// get a node
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: We need to do this because the fields of the resource may be dynamic. --)
	GetNode(context.Context, *GetNodeRequest) (*structpb.Struct, error)
	// list namespaces
	ListNamespaces(context.Context, *ListNamespacesRequest) (*ListNamespacesResponse, error)
	// get a namespace
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: We need to do this because the fields of the resource may be dynamic. --)
	GetNamespace(context.Context, *GetNamespaceRequest) (*structpb.Struct, error)
	// list resources
	ListResources(context.Context, *ListResourcesRequest) (*ListResourcesResponse, error)
	// get resource
	// (-- api-linter: core::0131::response-message-name=disabled
	//
	//	aip.dev/not-precedent: We need to do this because the fields of the resource may be dynamic. --)
	GetResource(context.Context, *GetResourceRequest) (*structpb.Struct, error)
	// reset nodegroup
	ResetNodeGroups(context.Context, *ResetNodeGroupsRequest) (*ResetNodeGroupsResponse, error)
	// reset resource
	ResetResources(context.Context, *ResetResourcesRequest) (*ResetResourcesResponse, error)
	// restart workloads
	RestartWorkloads(context.Context, *RestartWorkloadsRequest) (*RestartWorkloadsResponse, error)
	// deploy apps
	DeployApplications(context.Context, *DeployApplicationsRequest) (*DeployApplicationsResponse, error)
	mustEmbedUnimplementedClusterServiceServer()
}

// UnimplementedClusterServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedClusterServiceServer struct{}

func (UnimplementedClusterServiceServer) ListClusters(context.Context, *ListClustersRequest) (*ListClustersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListClusters not implemented")
}
func (UnimplementedClusterServiceServer) GetCluster(context.Context, *GetClusterRequest) (*Cluster, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCluster not implemented")
}
func (UnimplementedClusterServiceServer) ListNodeGroups(context.Context, *ListNodeGroupsRequest) (*ListNodeGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNodeGroups not implemented")
}
func (UnimplementedClusterServiceServer) GetNodeGroup(context.Context, *GetNodeGroupRequest) (*NodeGroup, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNodeGroup not implemented")
}
func (UnimplementedClusterServiceServer) ListNodes(context.Context, *ListNodesRequest) (*ListNodesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNodes not implemented")
}
func (UnimplementedClusterServiceServer) GetNode(context.Context, *GetNodeRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNode not implemented")
}
func (UnimplementedClusterServiceServer) ListNamespaces(context.Context, *ListNamespacesRequest) (*ListNamespacesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNamespaces not implemented")
}
func (UnimplementedClusterServiceServer) GetNamespace(context.Context, *GetNamespaceRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNamespace not implemented")
}
func (UnimplementedClusterServiceServer) ListResources(context.Context, *ListResourcesRequest) (*ListResourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListResources not implemented")
}
func (UnimplementedClusterServiceServer) GetResource(context.Context, *GetResourceRequest) (*structpb.Struct, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetResource not implemented")
}
func (UnimplementedClusterServiceServer) ResetNodeGroups(context.Context, *ResetNodeGroupsRequest) (*ResetNodeGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetNodeGroups not implemented")
}
func (UnimplementedClusterServiceServer) ResetResources(context.Context, *ResetResourcesRequest) (*ResetResourcesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetResources not implemented")
}
func (UnimplementedClusterServiceServer) RestartWorkloads(context.Context, *RestartWorkloadsRequest) (*RestartWorkloadsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestartWorkloads not implemented")
}
func (UnimplementedClusterServiceServer) DeployApplications(context.Context, *DeployApplicationsRequest) (*DeployApplicationsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeployApplications not implemented")
}
func (UnimplementedClusterServiceServer) mustEmbedUnimplementedClusterServiceServer() {}
func (UnimplementedClusterServiceServer) testEmbeddedByValue()                        {}

// UnsafeClusterServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ClusterServiceServer will
// result in compilation errors.
type UnsafeClusterServiceServer interface {
	mustEmbedUnimplementedClusterServiceServer()
}

func RegisterClusterServiceServer(s grpc.ServiceRegistrar, srv ClusterServiceServer) {
	// If the following call pancis, it indicates UnimplementedClusterServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ClusterService_ServiceDesc, srv)
}

func _ClusterService_ListClusters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListClustersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).ListClusters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_ListClusters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).ListClusters(ctx, req.(*ListClustersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_GetCluster_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClusterRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).GetCluster(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_GetCluster_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).GetCluster(ctx, req.(*GetClusterRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_ListNodeGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodeGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).ListNodeGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_ListNodeGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).ListNodeGroups(ctx, req.(*ListNodeGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_GetNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).GetNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_GetNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).GetNodeGroup(ctx, req.(*GetNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_ListNodes_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNodesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).ListNodes(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_ListNodes_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).ListNodes(ctx, req.(*ListNodesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_GetNode_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNodeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).GetNode(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_GetNode_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).GetNode(ctx, req.(*GetNodeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_ListNamespaces_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNamespacesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).ListNamespaces(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_ListNamespaces_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).ListNamespaces(ctx, req.(*ListNamespacesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_GetNamespace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNamespaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).GetNamespace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_GetNamespace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).GetNamespace(ctx, req.(*GetNamespaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_ListResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListResourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).ListResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_ListResources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).ListResources(ctx, req.(*ListResourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_GetResource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetResourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).GetResource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_GetResource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).GetResource(ctx, req.(*GetResourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_ResetNodeGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetNodeGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).ResetNodeGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_ResetNodeGroups_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).ResetNodeGroups(ctx, req.(*ResetNodeGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_ResetResources_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetResourcesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).ResetResources(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_ResetResources_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).ResetResources(ctx, req.(*ResetResourcesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_RestartWorkloads_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RestartWorkloadsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).RestartWorkloads(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_RestartWorkloads_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).RestartWorkloads(ctx, req.(*RestartWorkloadsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ClusterService_DeployApplications_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeployApplicationsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ClusterServiceServer).DeployApplications(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ClusterService_DeployApplications_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ClusterServiceServer).DeployApplications(ctx, req.(*DeployApplicationsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ClusterService_ServiceDesc is the grpc.ServiceDesc for ClusterService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ClusterService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.tools.v1.ClusterService",
	HandlerType: (*ClusterServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListClusters",
			Handler:    _ClusterService_ListClusters_Handler,
		},
		{
			MethodName: "GetCluster",
			Handler:    _ClusterService_GetCluster_Handler,
		},
		{
			MethodName: "ListNodeGroups",
			Handler:    _ClusterService_ListNodeGroups_Handler,
		},
		{
			MethodName: "GetNodeGroup",
			Handler:    _ClusterService_GetNodeGroup_Handler,
		},
		{
			MethodName: "ListNodes",
			Handler:    _ClusterService_ListNodes_Handler,
		},
		{
			MethodName: "GetNode",
			Handler:    _ClusterService_GetNode_Handler,
		},
		{
			MethodName: "ListNamespaces",
			Handler:    _ClusterService_ListNamespaces_Handler,
		},
		{
			MethodName: "GetNamespace",
			Handler:    _ClusterService_GetNamespace_Handler,
		},
		{
			MethodName: "ListResources",
			Handler:    _ClusterService_ListResources_Handler,
		},
		{
			MethodName: "GetResource",
			Handler:    _ClusterService_GetResource_Handler,
		},
		{
			MethodName: "ResetNodeGroups",
			Handler:    _ClusterService_ResetNodeGroups_Handler,
		},
		{
			MethodName: "ResetResources",
			Handler:    _ClusterService_ResetResources_Handler,
		},
		{
			MethodName: "RestartWorkloads",
			Handler:    _ClusterService_RestartWorkloads_Handler,
		},
		{
			MethodName: "DeployApplications",
			Handler:    _ClusterService_DeployApplications_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/tools/v1/cluster_service.proto",
}
