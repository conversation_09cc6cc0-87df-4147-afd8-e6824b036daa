

syntax = "proto3";

package backend.proto.tools.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb"; // This package name should not be changed
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

// Service for Adminer management operations
service AdminerManagementService {
  // AddUser ...
  rpc AddUser(AddUserRequest) returns (AddUserResponse) {}
  
  // GetUser ...
  rpc GetUser(GetUserRequest) returns (User) {}
}

// Request message for AddUser
message AddUserRequest {
  // username ...
  string username = 1 [(google.api.field_behavior) = REQUIRED];
  // password ...
  string password = 2 [(google.api.field_behavior) = REQUIRED];
  // email ...
  string email = 3 [(google.api.field_behavior) = REQUIRED];
  // env ...
  string env = 4 [(google.api.field_behavior) = REQUIRED];
}

// Response message for AddUser
message AddUserResponse {
    // success ...
    bool success = 1;
}

// Request message for GetUser
message GetUserRequest {
    // The credential of the user to retrieve.
    // (-- api-linter: core::0131::request-required-fields=disabled
    //     aip.dev/not-precedent: We need to do this because reasons. --)
    string user_credential = 1 [(google.api.field_behavior) = REQUIRED];
    // env is the adminer server
    // (-- api-linter: core::0131::request-required-fields=disabled
    //     aip.dev/not-precedent: We need to do this because reasons. --)
    string env = 2 [(google.api.field_behavior) = REQUIRED];
}

// Response message for GetUser
message User {
    // username ...
    string username = 1;
    // password ...
    string password = 2;
    // email ...
    string email = 3;
}