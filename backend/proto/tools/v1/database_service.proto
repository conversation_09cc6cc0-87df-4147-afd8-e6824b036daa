// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many database resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many database resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many database resources. --)

syntax = "proto3";

package backend.proto.tools.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.tools.v1";

import "backend/proto/tools/v1/resource_models.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/struct.proto";
import "buf/validate/validate.proto";

// DatabaseService
service DatabaseService {

    // Register a cloud database information to the devops console
    rpc RegisterDatabase(RegisterDatabaseRequest) returns (RegisterDatabaseResponse);

    // list databases
    rpc ListDatabases(ListDatabasesRequest) returns (ListDatabasesResponse);

    // get a database
    rpc GetDatabase(GetDatabaseRequest) returns (Database);

    // watch a cloud database status
    rpc WatchDatabase(WatchDatabaseRequest) returns (WatchDatabaseResponse);

    // delete a database information from the devops console
    rpc DeleteDatabase(DeleteDatabaseRequest) returns (google.protobuf.Empty);

    // restore database from snapshot
    rpc RestoreDatabase(RestoreDatabaseRequest) returns (RestoreDatabaseResponse);

    // execute sql on database
    rpc ExecuteSql(ExecuteSqlRequest) returns (ExecuteSqlResponse);

}

// RegisterDatabaseRequest
message RegisterDatabaseRequest {
    // database identifier
    PlatformIdentifier identifier = 1;
    // database admin user
    optional string admin_user = 2;
    // database default user
    optional string default_user = 3;
}

// RegisterDatabaseResponse
message RegisterDatabaseResponse {
    // database
    optional Database database = 1;
}

// ListDatabasesRequest
message ListDatabasesRequest {
    // the platform where the database is running
    optional string platform = 2 [(buf.validate.field).string.max_len = 128];
    // database engine
    optional string engine = 3 [(buf.validate.field).string.max_len = 128];
    // database labels
    map<string, string> labels = 4 [(buf.validate.field) = { map: {
        max_pairs: 256,
        keys: { string: { max_len: 255 } },
        values: { string: { max_len: 63 } }
    } }];
}

// ListDatabasesResponse
message ListDatabasesResponse {
    // list of database
    repeated Database databases = 1;
}

// GetDatabaseRequest
message GetDatabaseRequest {
    // database identifier
    PlatformIdentifier identifier = 1;
}

// GetDatabaseResponse
message GetDatabaseResponse {
    // database
    optional Database database = 1;
}

// WatchDatabaseRequest
message WatchDatabaseRequest {
    // database identifier
    PlatformIdentifier identifier = 1;
}

// WatchDatabaseResponse
message WatchDatabaseResponse {
    // database
    Database database = 1;
}

// DeleteDatabaseRequest
message DeleteDatabaseRequest {
    // database identifier
    PlatformIdentifier identifier = 1;
}

// DeleteDatabaseResponse
message DeleteDatabaseResponse {
    // database
    Database database = 1;
}

// RestoreDatabaseRequest
message RestoreDatabaseRequest {
    // Specifies the snapshot source from which the target database should be restored
    // If not specified, the database needs to be created directly.
    optional PlatformIdentifier source_identifier = 1;
    // database name
    PlatformIdentifier target_identifier = 2;
    // database instance names
    repeated string instance_names = 3;
    // Whether the database has deletion protection enabled
    optional bool deletion_protection = 4;
    // Whether the database is publicly accessible
    optional bool publicly_accessible = 5;
    // database params group
    optional string params_group = 6;
    // aurora serverlessV2 min ACU
    optional double min_capacity = 7;
    // aurora serverlessV2 max ACU
    optional double max_capacity = 8;
    // The labels attached to the database
    map<string, string> labels = 14 [(buf.validate.field) = { map: {
        max_pairs: 256,
        keys: { string: { max_len: 255 } },
        values: { string: { max_len: 63 } }
    } }];
}

// RestoreDatabaseResponse
message RestoreDatabaseResponse {
    // database
    Database database = 1;
}

// ExecuteSqlRequest
message ExecuteSqlRequest {
    // database identifier
    PlatformIdentifier identifier = 1;
    // actions for create database
    repeated DatabaseAction actions = 2;

    // DatabaseAction
    message DatabaseAction {
        // action type
        oneof action_type {
            // action for create database
            CreateDatabaseAction create_database = 1;
            // action for execute sql
            ExecuteSqlAction execute_sql = 2;
        }

        // CreateDatabaseAction
        message CreateDatabaseAction {
            // database name to create
            string database = 1;
            // Specify whether a user needs to be created
            // If `create_user` is true but the `username` is not specified, a username is automatically generated
            optional bool create_user = 2;
            // Specify the owner of the new database to be created.
            // If `username` is specified:
            //  - If the specified user already exists, the `create_user` parameter is ignored and the user will be directly authorized after the database is created.
            //  - If the specified user does not exist, the `create_user` parameter is either not specified or specified as true,
            //      the user will be created and the corresponding permissions will be granted, otherwise it is considered an illegal parameter.
            // If not specified `username`:
            //  - If the `create_user` parameter is explicitly specified as true, an account will be automatically generated and authorized after the database is created.
            //  - Otherwise, the default account will be authorized after the database is created.
            //  - If the default account does not exist, only the database will be created without authorizing any users.
            // There are two special tags to specify specific users:
            //  - ${db.user.admin}   specifies the administrator user
            //  - ${db.user.default} specifies the default user, If this flag is specified explicitly and the default user does not exist, an error is returned.
            optional string username = 3;
        }

        // ExecuteSqlAction
        message ExecuteSqlAction {
            // sql in database
            string database = 1;
            // sql content
            string sql = 2 [(buf.validate.field).string.max_len = 65536];
            // Specifies the user to execute sql.
            // If not specified, the appropriate user will be selected by default based on the database, such as the owner of the database or the default user.
            // There are two special tags to specify specific users:
            //  - ${db.user.admin}   specifies the administrator user
            //  - ${db.user.default} specifies the default user, If this flag is specified explicitly and the default user does not exist, an error is returned.
            optional string username = 3;
        }
    }
}

// ExecuteSqlResponse
message ExecuteSqlResponse {
    // database identifier
    PlatformIdentifier identifier = 1;
    // action outputs
    repeated ActionOutput outputs = 2;

    // ActionOutput
    message ActionOutput {
        // sql in database
        optional string database = 1;
        // result output
        optional string output = 2;
        // extra info
        optional google.protobuf.Struct extra = 3;
    }
}
