// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/tools/v1/deploy_platform.proto

package toolspb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	DeployPlatformService_SkipDeployTask_FullMethodName                = "/backend.proto.tools.v1.DeployPlatformService/SkipDeployTask"
	DeployPlatformService_RollbackDeployTask_FullMethodName            = "/backend.proto.tools.v1.DeployPlatformService/RollbackDeployTask"
	DeployPlatformService_HandleCanaryEventWebhookEvent_FullMethodName = "/backend.proto.tools.v1.DeployPlatformService/HandleCanaryEventWebhookEvent"
	DeployPlatformService_HandleCanaryWebhookEvent_FullMethodName      = "/backend.proto.tools.v1.DeployPlatformService/HandleCanaryWebhookEvent"
	DeployPlatformService_HandleSlackInteractionsEvent_FullMethodName  = "/backend.proto.tools.v1.DeployPlatformService/HandleSlackInteractionsEvent"
)

// DeployPlatformServiceClient is the client API for DeployPlatformService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Service to configure deployments
type DeployPlatformServiceClient interface {
	// Skip Canary
	SkipDeployTask(ctx context.Context, in *SkipDeployTaskRequest, opts ...grpc.CallOption) (*SkipDeployTaskResponse, error)
	// Rollback (Canary Or ArgoCD)
	// (-- api-linter: core::0162::rollback-response-message-name=disabled
	//
	//	aip.dev/not-precedent: We need to do this because reasons. --)
	RollbackDeployTask(ctx context.Context, in *RollbackDeployTaskRequest, opts ...grpc.CallOption) (*RollbackDeployTaskResponse, error)
	// Handle Canary Event webhook event
	HandleCanaryEventWebhookEvent(ctx context.Context, in *HandleCanaryEventWebhookEventRequest, opts ...grpc.CallOption) (*HandleCanaryEventWebhookEventResponse, error)
	// Handle Canary webhook event: rollout/rollback/skip/promotion
	HandleCanaryWebhookEvent(ctx context.Context, in *HandleCanaryWebhookEventRequest, opts ...grpc.CallOption) (*HandleCanaryWebhookEventResponse, error)
	// Handle Slack Interactions
	HandleSlackInteractionsEvent(ctx context.Context, in *HandleSlackInteractionsEventRequest, opts ...grpc.CallOption) (*HandleSlackInteractionsEventResponse, error)
}

type deployPlatformServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewDeployPlatformServiceClient(cc grpc.ClientConnInterface) DeployPlatformServiceClient {
	return &deployPlatformServiceClient{cc}
}

func (c *deployPlatformServiceClient) SkipDeployTask(ctx context.Context, in *SkipDeployTaskRequest, opts ...grpc.CallOption) (*SkipDeployTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SkipDeployTaskResponse)
	err := c.cc.Invoke(ctx, DeployPlatformService_SkipDeployTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deployPlatformServiceClient) RollbackDeployTask(ctx context.Context, in *RollbackDeployTaskRequest, opts ...grpc.CallOption) (*RollbackDeployTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RollbackDeployTaskResponse)
	err := c.cc.Invoke(ctx, DeployPlatformService_RollbackDeployTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deployPlatformServiceClient) HandleCanaryEventWebhookEvent(ctx context.Context, in *HandleCanaryEventWebhookEventRequest, opts ...grpc.CallOption) (*HandleCanaryEventWebhookEventResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleCanaryEventWebhookEventResponse)
	err := c.cc.Invoke(ctx, DeployPlatformService_HandleCanaryEventWebhookEvent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deployPlatformServiceClient) HandleCanaryWebhookEvent(ctx context.Context, in *HandleCanaryWebhookEventRequest, opts ...grpc.CallOption) (*HandleCanaryWebhookEventResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleCanaryWebhookEventResponse)
	err := c.cc.Invoke(ctx, DeployPlatformService_HandleCanaryWebhookEvent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *deployPlatformServiceClient) HandleSlackInteractionsEvent(ctx context.Context, in *HandleSlackInteractionsEventRequest, opts ...grpc.CallOption) (*HandleSlackInteractionsEventResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(HandleSlackInteractionsEventResponse)
	err := c.cc.Invoke(ctx, DeployPlatformService_HandleSlackInteractionsEvent_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DeployPlatformServiceServer is the server API for DeployPlatformService service.
// All implementations must embed UnimplementedDeployPlatformServiceServer
// for forward compatibility.
//
// Service to configure deployments
type DeployPlatformServiceServer interface {
	// Skip Canary
	SkipDeployTask(context.Context, *SkipDeployTaskRequest) (*SkipDeployTaskResponse, error)
	// Rollback (Canary Or ArgoCD)
	// (-- api-linter: core::0162::rollback-response-message-name=disabled
	//
	//	aip.dev/not-precedent: We need to do this because reasons. --)
	RollbackDeployTask(context.Context, *RollbackDeployTaskRequest) (*RollbackDeployTaskResponse, error)
	// Handle Canary Event webhook event
	HandleCanaryEventWebhookEvent(context.Context, *HandleCanaryEventWebhookEventRequest) (*HandleCanaryEventWebhookEventResponse, error)
	// Handle Canary webhook event: rollout/rollback/skip/promotion
	HandleCanaryWebhookEvent(context.Context, *HandleCanaryWebhookEventRequest) (*HandleCanaryWebhookEventResponse, error)
	// Handle Slack Interactions
	HandleSlackInteractionsEvent(context.Context, *HandleSlackInteractionsEventRequest) (*HandleSlackInteractionsEventResponse, error)
	mustEmbedUnimplementedDeployPlatformServiceServer()
}

// UnimplementedDeployPlatformServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedDeployPlatformServiceServer struct{}

func (UnimplementedDeployPlatformServiceServer) SkipDeployTask(context.Context, *SkipDeployTaskRequest) (*SkipDeployTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SkipDeployTask not implemented")
}
func (UnimplementedDeployPlatformServiceServer) RollbackDeployTask(context.Context, *RollbackDeployTaskRequest) (*RollbackDeployTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RollbackDeployTask not implemented")
}
func (UnimplementedDeployPlatformServiceServer) HandleCanaryEventWebhookEvent(context.Context, *HandleCanaryEventWebhookEventRequest) (*HandleCanaryEventWebhookEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleCanaryEventWebhookEvent not implemented")
}
func (UnimplementedDeployPlatformServiceServer) HandleCanaryWebhookEvent(context.Context, *HandleCanaryWebhookEventRequest) (*HandleCanaryWebhookEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleCanaryWebhookEvent not implemented")
}
func (UnimplementedDeployPlatformServiceServer) HandleSlackInteractionsEvent(context.Context, *HandleSlackInteractionsEventRequest) (*HandleSlackInteractionsEventResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method HandleSlackInteractionsEvent not implemented")
}
func (UnimplementedDeployPlatformServiceServer) mustEmbedUnimplementedDeployPlatformServiceServer() {}
func (UnimplementedDeployPlatformServiceServer) testEmbeddedByValue()                               {}

// UnsafeDeployPlatformServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DeployPlatformServiceServer will
// result in compilation errors.
type UnsafeDeployPlatformServiceServer interface {
	mustEmbedUnimplementedDeployPlatformServiceServer()
}

func RegisterDeployPlatformServiceServer(s grpc.ServiceRegistrar, srv DeployPlatformServiceServer) {
	// If the following call pancis, it indicates UnimplementedDeployPlatformServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&DeployPlatformService_ServiceDesc, srv)
}

func _DeployPlatformService_SkipDeployTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SkipDeployTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeployPlatformServiceServer).SkipDeployTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeployPlatformService_SkipDeployTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeployPlatformServiceServer).SkipDeployTask(ctx, req.(*SkipDeployTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeployPlatformService_RollbackDeployTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackDeployTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeployPlatformServiceServer).RollbackDeployTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeployPlatformService_RollbackDeployTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeployPlatformServiceServer).RollbackDeployTask(ctx, req.(*RollbackDeployTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeployPlatformService_HandleCanaryEventWebhookEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleCanaryEventWebhookEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeployPlatformServiceServer).HandleCanaryEventWebhookEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeployPlatformService_HandleCanaryEventWebhookEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeployPlatformServiceServer).HandleCanaryEventWebhookEvent(ctx, req.(*HandleCanaryEventWebhookEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeployPlatformService_HandleCanaryWebhookEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleCanaryWebhookEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeployPlatformServiceServer).HandleCanaryWebhookEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeployPlatformService_HandleCanaryWebhookEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeployPlatformServiceServer).HandleCanaryWebhookEvent(ctx, req.(*HandleCanaryWebhookEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DeployPlatformService_HandleSlackInteractionsEvent_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(HandleSlackInteractionsEventRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DeployPlatformServiceServer).HandleSlackInteractionsEvent(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: DeployPlatformService_HandleSlackInteractionsEvent_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DeployPlatformServiceServer).HandleSlackInteractionsEvent(ctx, req.(*HandleSlackInteractionsEventRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DeployPlatformService_ServiceDesc is the grpc.ServiceDesc for DeployPlatformService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DeployPlatformService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.tools.v1.DeployPlatformService",
	HandlerType: (*DeployPlatformServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SkipDeployTask",
			Handler:    _DeployPlatformService_SkipDeployTask_Handler,
		},
		{
			MethodName: "RollbackDeployTask",
			Handler:    _DeployPlatformService_RollbackDeployTask_Handler,
		},
		{
			MethodName: "HandleCanaryEventWebhookEvent",
			Handler:    _DeployPlatformService_HandleCanaryEventWebhookEvent_Handler,
		},
		{
			MethodName: "HandleCanaryWebhookEvent",
			Handler:    _DeployPlatformService_HandleCanaryWebhookEvent_Handler,
		},
		{
			MethodName: "HandleSlackInteractionsEvent",
			Handler:    _DeployPlatformService_HandleSlackInteractionsEvent_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/tools/v1/deploy_platform.proto",
}
