// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many MQ resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many MQ resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many MQ resources. --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/tools/v1/message_queue_service.proto

package toolspb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	structpb "google.golang.org/protobuf/types/known/structpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RegisterMessageQueueRequest
type RegisterMessageQueueRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// MQ identifier
	Identifier    *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterMessageQueueRequest) Reset() {
	*x = RegisterMessageQueueRequest{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterMessageQueueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterMessageQueueRequest) ProtoMessage() {}

func (x *RegisterMessageQueueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterMessageQueueRequest.ProtoReflect.Descriptor instead.
func (*RegisterMessageQueueRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{0}
}

func (x *RegisterMessageQueueRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// RegisterMessageQueueResponse
type RegisterMessageQueueResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message queue
	MessageQueue  *MessageQueue `protobuf:"bytes,1,opt,name=message_queue,json=messageQueue,proto3,oneof" json:"message_queue,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RegisterMessageQueueResponse) Reset() {
	*x = RegisterMessageQueueResponse{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RegisterMessageQueueResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RegisterMessageQueueResponse) ProtoMessage() {}

func (x *RegisterMessageQueueResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RegisterMessageQueueResponse.ProtoReflect.Descriptor instead.
func (*RegisterMessageQueueResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{1}
}

func (x *RegisterMessageQueueResponse) GetMessageQueue() *MessageQueue {
	if x != nil {
		return x.MessageQueue
	}
	return nil
}

// ListMessageQueuesRequest
type ListMessageQueuesRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the platform where the message queue is running
	Platform *string `protobuf:"bytes,2,opt,name=platform,proto3,oneof" json:"platform,omitempty"`
	// MQ engine
	Engine *string `protobuf:"bytes,3,opt,name=engine,proto3,oneof" json:"engine,omitempty"`
	// MQ labels
	Labels        map[string]string `protobuf:"bytes,4,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMessageQueuesRequest) Reset() {
	*x = ListMessageQueuesRequest{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMessageQueuesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMessageQueuesRequest) ProtoMessage() {}

func (x *ListMessageQueuesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMessageQueuesRequest.ProtoReflect.Descriptor instead.
func (*ListMessageQueuesRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{2}
}

func (x *ListMessageQueuesRequest) GetPlatform() string {
	if x != nil && x.Platform != nil {
		return *x.Platform
	}
	return ""
}

func (x *ListMessageQueuesRequest) GetEngine() string {
	if x != nil && x.Engine != nil {
		return *x.Engine
	}
	return ""
}

func (x *ListMessageQueuesRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// ListMessageQueuesResponse
type ListMessageQueuesResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of message queues
	MessageQueues []*MessageQueue `protobuf:"bytes,1,rep,name=message_queues,json=messageQueues,proto3" json:"message_queues,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListMessageQueuesResponse) Reset() {
	*x = ListMessageQueuesResponse{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListMessageQueuesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListMessageQueuesResponse) ProtoMessage() {}

func (x *ListMessageQueuesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListMessageQueuesResponse.ProtoReflect.Descriptor instead.
func (*ListMessageQueuesResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{3}
}

func (x *ListMessageQueuesResponse) GetMessageQueues() []*MessageQueue {
	if x != nil {
		return x.MessageQueues
	}
	return nil
}

// GetMessageQueueRequest
type GetMessageQueueRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message queue identifier
	Identifier    *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetMessageQueueRequest) Reset() {
	*x = GetMessageQueueRequest{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetMessageQueueRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetMessageQueueRequest) ProtoMessage() {}

func (x *GetMessageQueueRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetMessageQueueRequest.ProtoReflect.Descriptor instead.
func (*GetMessageQueueRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetMessageQueueRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

// ListTopicsRequest
type ListTopicsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message queue identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// topic tenant
	Tenant *string `protobuf:"bytes,2,opt,name=tenant,proto3,oneof" json:"tenant,omitempty"`
	// topic namespace
	Namespace *string `protobuf:"bytes,3,opt,name=namespace,proto3,oneof" json:"namespace,omitempty"`
	// whether should list internal topics.
	Internal      *bool `protobuf:"varint,4,opt,name=internal,proto3,oneof" json:"internal,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTopicsRequest) Reset() {
	*x = ListTopicsRequest{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTopicsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTopicsRequest) ProtoMessage() {}

func (x *ListTopicsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTopicsRequest.ProtoReflect.Descriptor instead.
func (*ListTopicsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListTopicsRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *ListTopicsRequest) GetTenant() string {
	if x != nil && x.Tenant != nil {
		return *x.Tenant
	}
	return ""
}

func (x *ListTopicsRequest) GetNamespace() string {
	if x != nil && x.Namespace != nil {
		return *x.Namespace
	}
	return ""
}

func (x *ListTopicsRequest) GetInternal() bool {
	if x != nil && x.Internal != nil {
		return *x.Internal
	}
	return false
}

// ListTopicsResponse
type ListTopicsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of topics
	Topics        []*Topic `protobuf:"bytes,1,rep,name=topics,proto3" json:"topics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTopicsResponse) Reset() {
	*x = ListTopicsResponse{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTopicsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTopicsResponse) ProtoMessage() {}

func (x *ListTopicsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTopicsResponse.ProtoReflect.Descriptor instead.
func (*ListTopicsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListTopicsResponse) GetTopics() []*Topic {
	if x != nil {
		return x.Topics
	}
	return nil
}

// CreateTopicsRequest
// (-- api-linter: core::0133::request-resource-field=disabled
//
//	aip.dev/not-precedent: We need to do this because this is create multiple topics. --)
type CreateTopicsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message queue identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// list of topic details
	Topics        []*Topic `protobuf:"bytes,2,rep,name=topics,proto3" json:"topics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTopicsRequest) Reset() {
	*x = CreateTopicsRequest{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTopicsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTopicsRequest) ProtoMessage() {}

func (x *CreateTopicsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTopicsRequest.ProtoReflect.Descriptor instead.
func (*CreateTopicsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{7}
}

func (x *CreateTopicsRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *CreateTopicsRequest) GetTopics() []*Topic {
	if x != nil {
		return x.Topics
	}
	return nil
}

// CreateTopicsResponse
type CreateTopicsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// list of topic names
	Topics        []*Topic `protobuf:"bytes,1,rep,name=topics,proto3" json:"topics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTopicsResponse) Reset() {
	*x = CreateTopicsResponse{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTopicsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTopicsResponse) ProtoMessage() {}

func (x *CreateTopicsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTopicsResponse.ProtoReflect.Descriptor instead.
func (*CreateTopicsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{8}
}

func (x *CreateTopicsResponse) GetTopics() []*Topic {
	if x != nil {
		return x.Topics
	}
	return nil
}

// DeleteTopicsRequest
type DeleteTopicsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message queue identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// list of topic names
	Topics        []string `protobuf:"bytes,2,rep,name=topics,proto3" json:"topics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTopicsRequest) Reset() {
	*x = DeleteTopicsRequest{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTopicsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTopicsRequest) ProtoMessage() {}

func (x *DeleteTopicsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTopicsRequest.ProtoReflect.Descriptor instead.
func (*DeleteTopicsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteTopicsRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *DeleteTopicsRequest) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

// UpdateTopicRequest
type UpdateTopicRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message queue identifier
	Identifier *PlatformIdentifier `protobuf:"bytes,1,opt,name=identifier,proto3" json:"identifier,omitempty"`
	// topic name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// partition number
	PartitionNumber *int32 `protobuf:"varint,3,opt,name=partition_number,json=partitionNumber,proto3,oneof" json:"partition_number,omitempty"`
	// replication factor
	ReplicationFactor *int32 `protobuf:"varint,4,opt,name=replication_factor,json=replicationFactor,proto3,oneof" json:"replication_factor,omitempty"`
	// extra config
	Config        *structpb.Struct `protobuf:"bytes,5,opt,name=config,proto3,oneof" json:"config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTopicRequest) Reset() {
	*x = UpdateTopicRequest{}
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTopicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTopicRequest) ProtoMessage() {}

func (x *UpdateTopicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTopicRequest.ProtoReflect.Descriptor instead.
func (*UpdateTopicRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP(), []int{10}
}

func (x *UpdateTopicRequest) GetIdentifier() *PlatformIdentifier {
	if x != nil {
		return x.Identifier
	}
	return nil
}

func (x *UpdateTopicRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateTopicRequest) GetPartitionNumber() int32 {
	if x != nil && x.PartitionNumber != nil {
		return *x.PartitionNumber
	}
	return 0
}

func (x *UpdateTopicRequest) GetReplicationFactor() int32 {
	if x != nil && x.ReplicationFactor != nil {
		return *x.ReplicationFactor
	}
	return 0
}

func (x *UpdateTopicRequest) GetConfig() *structpb.Struct {
	if x != nil {
		return x.Config
	}
	return nil
}

var File_backend_proto_tools_v1_message_queue_service_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_message_queue_service_proto_rawDesc = "" +
	"\n" +
	"2backend/proto/tools/v1/message_queue_service.proto\x12\x16backend.proto.tools.v1\x1a,backend/proto/tools/v1/resource_models.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1bbuf/validate/validate.proto\"i\n" +
	"\x1bRegisterMessageQueueRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\"\x80\x01\n" +
	"\x1cRegisterMessageQueueResponse\x12N\n" +
	"\rmessage_queue\x18\x01 \x01(\v2$.backend.proto.tools.v1.MessageQueueH\x00R\fmessageQueue\x88\x01\x01B\x10\n" +
	"\x0e_message_queue\"\xad\x02\n" +
	"\x18ListMessageQueuesRequest\x12)\n" +
	"\bplatform\x18\x02 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01H\x00R\bplatform\x88\x01\x01\x12%\n" +
	"\x06engine\x18\x03 \x01(\tB\b\xbaH\x05r\x03\x18\x80\x01H\x01R\x06engine\x88\x01\x01\x12l\n" +
	"\x06labels\x18\x04 \x03(\v2<.backend.proto.tools.v1.ListMessageQueuesRequest.LabelsEntryB\x16\xbaH\x13\x9a\x01\x10\x10\x80\x02\"\x05r\x03\x18\xff\x01*\x04r\x02\x18?R\x06labels\x1a9\n" +
	"\vLabelsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x01B\v\n" +
	"\t_platformB\t\n" +
	"\a_engine\"h\n" +
	"\x19ListMessageQueuesResponse\x12K\n" +
	"\x0emessage_queues\x18\x01 \x03(\v2$.backend.proto.tools.v1.MessageQueueR\rmessageQueues\"d\n" +
	"\x16GetMessageQueueRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\"\xe6\x01\n" +
	"\x11ListTopicsRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12\x1b\n" +
	"\x06tenant\x18\x02 \x01(\tH\x00R\x06tenant\x88\x01\x01\x12!\n" +
	"\tnamespace\x18\x03 \x01(\tH\x01R\tnamespace\x88\x01\x01\x12\x1f\n" +
	"\binternal\x18\x04 \x01(\bH\x02R\binternal\x88\x01\x01B\t\n" +
	"\a_tenantB\f\n" +
	"\n" +
	"_namespaceB\v\n" +
	"\t_internal\"K\n" +
	"\x12ListTopicsResponse\x125\n" +
	"\x06topics\x18\x01 \x03(\v2\x1d.backend.proto.tools.v1.TopicR\x06topics\"\x98\x01\n" +
	"\x13CreateTopicsRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x125\n" +
	"\x06topics\x18\x02 \x03(\v2\x1d.backend.proto.tools.v1.TopicR\x06topics\"M\n" +
	"\x14CreateTopicsResponse\x125\n" +
	"\x06topics\x18\x01 \x03(\v2\x1d.backend.proto.tools.v1.TopicR\x06topics\"y\n" +
	"\x13DeleteTopicsRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12\x16\n" +
	"\x06topics\x18\x02 \x03(\tR\x06topics\"\xc5\x02\n" +
	"\x12UpdateTopicRequest\x12J\n" +
	"\n" +
	"identifier\x18\x01 \x01(\v2*.backend.proto.tools.v1.PlatformIdentifierR\n" +
	"identifier\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12.\n" +
	"\x10partition_number\x18\x03 \x01(\x05H\x00R\x0fpartitionNumber\x88\x01\x01\x122\n" +
	"\x12replication_factor\x18\x04 \x01(\x05H\x01R\x11replicationFactor\x88\x01\x01\x124\n" +
	"\x06config\x18\x05 \x01(\v2\x17.google.protobuf.StructH\x02R\x06config\x88\x01\x01B\x13\n" +
	"\x11_partition_numberB\x15\n" +
	"\x13_replication_factorB\t\n" +
	"\a_config2\xfb\x05\n" +
	"\x13MessageQueueService\x12\x81\x01\n" +
	"\x14RegisterMessageQueue\x123.backend.proto.tools.v1.RegisterMessageQueueRequest\x1a4.backend.proto.tools.v1.RegisterMessageQueueResponse\x12x\n" +
	"\x11ListMessageQueues\x120.backend.proto.tools.v1.ListMessageQueuesRequest\x1a1.backend.proto.tools.v1.ListMessageQueuesResponse\x12g\n" +
	"\x0fGetMessageQueue\x12..backend.proto.tools.v1.GetMessageQueueRequest\x1a$.backend.proto.tools.v1.MessageQueue\x12c\n" +
	"\n" +
	"ListTopics\x12).backend.proto.tools.v1.ListTopicsRequest\x1a*.backend.proto.tools.v1.ListTopicsResponse\x12i\n" +
	"\fCreateTopics\x12+.backend.proto.tools.v1.CreateTopicsRequest\x1a,.backend.proto.tools.v1.CreateTopicsResponse\x12S\n" +
	"\fDeleteTopics\x12+.backend.proto.tools.v1.DeleteTopicsRequest\x1a\x16.google.protobuf.Empty\x12X\n" +
	"\vUpdateTopic\x12*.backend.proto.tools.v1.UpdateTopicRequest\x1a\x1d.backend.proto.tools.v1.TopicBb\n" +
	" com.moego.backend.proto.tools.v1P\x01Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_message_queue_service_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_message_queue_service_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_message_queue_service_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_message_queue_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_message_queue_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_message_queue_service_proto_rawDesc), len(file_backend_proto_tools_v1_message_queue_service_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_message_queue_service_proto_rawDescData
}

var file_backend_proto_tools_v1_message_queue_service_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_backend_proto_tools_v1_message_queue_service_proto_goTypes = []any{
	(*RegisterMessageQueueRequest)(nil),  // 0: backend.proto.tools.v1.RegisterMessageQueueRequest
	(*RegisterMessageQueueResponse)(nil), // 1: backend.proto.tools.v1.RegisterMessageQueueResponse
	(*ListMessageQueuesRequest)(nil),     // 2: backend.proto.tools.v1.ListMessageQueuesRequest
	(*ListMessageQueuesResponse)(nil),    // 3: backend.proto.tools.v1.ListMessageQueuesResponse
	(*GetMessageQueueRequest)(nil),       // 4: backend.proto.tools.v1.GetMessageQueueRequest
	(*ListTopicsRequest)(nil),            // 5: backend.proto.tools.v1.ListTopicsRequest
	(*ListTopicsResponse)(nil),           // 6: backend.proto.tools.v1.ListTopicsResponse
	(*CreateTopicsRequest)(nil),          // 7: backend.proto.tools.v1.CreateTopicsRequest
	(*CreateTopicsResponse)(nil),         // 8: backend.proto.tools.v1.CreateTopicsResponse
	(*DeleteTopicsRequest)(nil),          // 9: backend.proto.tools.v1.DeleteTopicsRequest
	(*UpdateTopicRequest)(nil),           // 10: backend.proto.tools.v1.UpdateTopicRequest
	nil,                                  // 11: backend.proto.tools.v1.ListMessageQueuesRequest.LabelsEntry
	(*PlatformIdentifier)(nil),           // 12: backend.proto.tools.v1.PlatformIdentifier
	(*MessageQueue)(nil),                 // 13: backend.proto.tools.v1.MessageQueue
	(*Topic)(nil),                        // 14: backend.proto.tools.v1.Topic
	(*structpb.Struct)(nil),              // 15: google.protobuf.Struct
	(*emptypb.Empty)(nil),                // 16: google.protobuf.Empty
}
var file_backend_proto_tools_v1_message_queue_service_proto_depIdxs = []int32{
	12, // 0: backend.proto.tools.v1.RegisterMessageQueueRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	13, // 1: backend.proto.tools.v1.RegisterMessageQueueResponse.message_queue:type_name -> backend.proto.tools.v1.MessageQueue
	11, // 2: backend.proto.tools.v1.ListMessageQueuesRequest.labels:type_name -> backend.proto.tools.v1.ListMessageQueuesRequest.LabelsEntry
	13, // 3: backend.proto.tools.v1.ListMessageQueuesResponse.message_queues:type_name -> backend.proto.tools.v1.MessageQueue
	12, // 4: backend.proto.tools.v1.GetMessageQueueRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	12, // 5: backend.proto.tools.v1.ListTopicsRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	14, // 6: backend.proto.tools.v1.ListTopicsResponse.topics:type_name -> backend.proto.tools.v1.Topic
	12, // 7: backend.proto.tools.v1.CreateTopicsRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	14, // 8: backend.proto.tools.v1.CreateTopicsRequest.topics:type_name -> backend.proto.tools.v1.Topic
	14, // 9: backend.proto.tools.v1.CreateTopicsResponse.topics:type_name -> backend.proto.tools.v1.Topic
	12, // 10: backend.proto.tools.v1.DeleteTopicsRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	12, // 11: backend.proto.tools.v1.UpdateTopicRequest.identifier:type_name -> backend.proto.tools.v1.PlatformIdentifier
	15, // 12: backend.proto.tools.v1.UpdateTopicRequest.config:type_name -> google.protobuf.Struct
	0,  // 13: backend.proto.tools.v1.MessageQueueService.RegisterMessageQueue:input_type -> backend.proto.tools.v1.RegisterMessageQueueRequest
	2,  // 14: backend.proto.tools.v1.MessageQueueService.ListMessageQueues:input_type -> backend.proto.tools.v1.ListMessageQueuesRequest
	4,  // 15: backend.proto.tools.v1.MessageQueueService.GetMessageQueue:input_type -> backend.proto.tools.v1.GetMessageQueueRequest
	5,  // 16: backend.proto.tools.v1.MessageQueueService.ListTopics:input_type -> backend.proto.tools.v1.ListTopicsRequest
	7,  // 17: backend.proto.tools.v1.MessageQueueService.CreateTopics:input_type -> backend.proto.tools.v1.CreateTopicsRequest
	9,  // 18: backend.proto.tools.v1.MessageQueueService.DeleteTopics:input_type -> backend.proto.tools.v1.DeleteTopicsRequest
	10, // 19: backend.proto.tools.v1.MessageQueueService.UpdateTopic:input_type -> backend.proto.tools.v1.UpdateTopicRequest
	1,  // 20: backend.proto.tools.v1.MessageQueueService.RegisterMessageQueue:output_type -> backend.proto.tools.v1.RegisterMessageQueueResponse
	3,  // 21: backend.proto.tools.v1.MessageQueueService.ListMessageQueues:output_type -> backend.proto.tools.v1.ListMessageQueuesResponse
	13, // 22: backend.proto.tools.v1.MessageQueueService.GetMessageQueue:output_type -> backend.proto.tools.v1.MessageQueue
	6,  // 23: backend.proto.tools.v1.MessageQueueService.ListTopics:output_type -> backend.proto.tools.v1.ListTopicsResponse
	8,  // 24: backend.proto.tools.v1.MessageQueueService.CreateTopics:output_type -> backend.proto.tools.v1.CreateTopicsResponse
	16, // 25: backend.proto.tools.v1.MessageQueueService.DeleteTopics:output_type -> google.protobuf.Empty
	14, // 26: backend.proto.tools.v1.MessageQueueService.UpdateTopic:output_type -> backend.proto.tools.v1.Topic
	20, // [20:27] is the sub-list for method output_type
	13, // [13:20] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_message_queue_service_proto_init() }
func file_backend_proto_tools_v1_message_queue_service_proto_init() {
	if File_backend_proto_tools_v1_message_queue_service_proto != nil {
		return
	}
	file_backend_proto_tools_v1_resource_models_proto_init()
	file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[1].OneofWrappers = []any{}
	file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[5].OneofWrappers = []any{}
	file_backend_proto_tools_v1_message_queue_service_proto_msgTypes[10].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_message_queue_service_proto_rawDesc), len(file_backend_proto_tools_v1_message_queue_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_tools_v1_message_queue_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_message_queue_service_proto_depIdxs,
		MessageInfos:      file_backend_proto_tools_v1_message_queue_service_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_message_queue_service_proto = out.File
	file_backend_proto_tools_v1_message_queue_service_proto_goTypes = nil
	file_backend_proto_tools_v1_message_queue_service_proto_depIdxs = nil
}
