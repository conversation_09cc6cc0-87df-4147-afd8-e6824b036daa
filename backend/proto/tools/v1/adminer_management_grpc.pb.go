// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/tools/v1/adminer_management.proto

package toolspb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AdminerManagementService_AddUser_FullMethodName = "/backend.proto.tools.v1.AdminerManagementService/AddUser"
	AdminerManagementService_GetUser_FullMethodName = "/backend.proto.tools.v1.AdminerManagementService/GetUser"
)

// AdminerManagementServiceClient is the client API for AdminerManagementService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// Service for Adminer management operations
type AdminerManagementServiceClient interface {
	// AddUser ...
	AddUser(ctx context.Context, in *AddUserRequest, opts ...grpc.CallOption) (*AddUserResponse, error)
	// GetUser ...
	GetUser(ctx context.Context, in *GetUserRequest, opts ...grpc.CallOption) (*User, error)
}

type adminerManagementServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewAdminerManagementServiceClient(cc grpc.ClientConnInterface) AdminerManagementServiceClient {
	return &adminerManagementServiceClient{cc}
}

func (c *adminerManagementServiceClient) AddUser(ctx context.Context, in *AddUserRequest, opts ...grpc.CallOption) (*AddUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AddUserResponse)
	err := c.cc.Invoke(ctx, AdminerManagementService_AddUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *adminerManagementServiceClient) GetUser(ctx context.Context, in *GetUserRequest, opts ...grpc.CallOption) (*User, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(User)
	err := c.cc.Invoke(ctx, AdminerManagementService_GetUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AdminerManagementServiceServer is the server API for AdminerManagementService service.
// All implementations must embed UnimplementedAdminerManagementServiceServer
// for forward compatibility.
//
// Service for Adminer management operations
type AdminerManagementServiceServer interface {
	// AddUser ...
	AddUser(context.Context, *AddUserRequest) (*AddUserResponse, error)
	// GetUser ...
	GetUser(context.Context, *GetUserRequest) (*User, error)
	mustEmbedUnimplementedAdminerManagementServiceServer()
}

// UnimplementedAdminerManagementServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAdminerManagementServiceServer struct{}

func (UnimplementedAdminerManagementServiceServer) AddUser(context.Context, *AddUserRequest) (*AddUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUser not implemented")
}
func (UnimplementedAdminerManagementServiceServer) GetUser(context.Context, *GetUserRequest) (*User, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUser not implemented")
}
func (UnimplementedAdminerManagementServiceServer) mustEmbedUnimplementedAdminerManagementServiceServer() {
}
func (UnimplementedAdminerManagementServiceServer) testEmbeddedByValue() {}

// UnsafeAdminerManagementServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AdminerManagementServiceServer will
// result in compilation errors.
type UnsafeAdminerManagementServiceServer interface {
	mustEmbedUnimplementedAdminerManagementServiceServer()
}

func RegisterAdminerManagementServiceServer(s grpc.ServiceRegistrar, srv AdminerManagementServiceServer) {
	// If the following call pancis, it indicates UnimplementedAdminerManagementServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AdminerManagementService_ServiceDesc, srv)
}

func _AdminerManagementService_AddUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AddUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminerManagementServiceServer).AddUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminerManagementService_AddUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminerManagementServiceServer).AddUser(ctx, req.(*AddUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AdminerManagementService_GetUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AdminerManagementServiceServer).GetUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AdminerManagementService_GetUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AdminerManagementServiceServer).GetUser(ctx, req.(*GetUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AdminerManagementService_ServiceDesc is the grpc.ServiceDesc for AdminerManagementService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AdminerManagementService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.tools.v1.AdminerManagementService",
	HandlerType: (*AdminerManagementServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddUser",
			Handler:    _AdminerManagementService_AddUser_Handler,
		},
		{
			MethodName: "GetUser",
			Handler:    _AdminerManagementService_GetUser_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/tools/v1/adminer_management.proto",
}
