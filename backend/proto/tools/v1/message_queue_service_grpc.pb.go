// (-- api-linter: core::0158::request-page-size-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many MQ resources. --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many MQ resources. --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: We need to do this because there are not many MQ resources. --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/tools/v1/message_queue_service.proto

package toolspb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	MessageQueueService_RegisterMessageQueue_FullMethodName = "/backend.proto.tools.v1.MessageQueueService/RegisterMessageQueue"
	MessageQueueService_ListMessageQueues_FullMethodName    = "/backend.proto.tools.v1.MessageQueueService/ListMessageQueues"
	MessageQueueService_GetMessageQueue_FullMethodName      = "/backend.proto.tools.v1.MessageQueueService/GetMessageQueue"
	MessageQueueService_ListTopics_FullMethodName           = "/backend.proto.tools.v1.MessageQueueService/ListTopics"
	MessageQueueService_CreateTopics_FullMethodName         = "/backend.proto.tools.v1.MessageQueueService/CreateTopics"
	MessageQueueService_DeleteTopics_FullMethodName         = "/backend.proto.tools.v1.MessageQueueService/DeleteTopics"
	MessageQueueService_UpdateTopic_FullMethodName          = "/backend.proto.tools.v1.MessageQueueService/UpdateTopic"
)

// MessageQueueServiceClient is the client API for MessageQueueService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// MessageQueueService
type MessageQueueServiceClient interface {
	// register MQ
	RegisterMessageQueue(ctx context.Context, in *RegisterMessageQueueRequest, opts ...grpc.CallOption) (*RegisterMessageQueueResponse, error)
	// list MQ
	ListMessageQueues(ctx context.Context, in *ListMessageQueuesRequest, opts ...grpc.CallOption) (*ListMessageQueuesResponse, error)
	// get a MQ
	GetMessageQueue(ctx context.Context, in *GetMessageQueueRequest, opts ...grpc.CallOption) (*MessageQueue, error)
	// list topics
	ListTopics(ctx context.Context, in *ListTopicsRequest, opts ...grpc.CallOption) (*ListTopicsResponse, error)
	// create topics
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: We need to do this because this is create multiple topics. --)
	CreateTopics(ctx context.Context, in *CreateTopicsRequest, opts ...grpc.CallOption) (*CreateTopicsResponse, error)
	// delete topics
	DeleteTopics(ctx context.Context, in *DeleteTopicsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// update topic
	UpdateTopic(ctx context.Context, in *UpdateTopicRequest, opts ...grpc.CallOption) (*Topic, error)
}

type messageQueueServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewMessageQueueServiceClient(cc grpc.ClientConnInterface) MessageQueueServiceClient {
	return &messageQueueServiceClient{cc}
}

func (c *messageQueueServiceClient) RegisterMessageQueue(ctx context.Context, in *RegisterMessageQueueRequest, opts ...grpc.CallOption) (*RegisterMessageQueueResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RegisterMessageQueueResponse)
	err := c.cc.Invoke(ctx, MessageQueueService_RegisterMessageQueue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageQueueServiceClient) ListMessageQueues(ctx context.Context, in *ListMessageQueuesRequest, opts ...grpc.CallOption) (*ListMessageQueuesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListMessageQueuesResponse)
	err := c.cc.Invoke(ctx, MessageQueueService_ListMessageQueues_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageQueueServiceClient) GetMessageQueue(ctx context.Context, in *GetMessageQueueRequest, opts ...grpc.CallOption) (*MessageQueue, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MessageQueue)
	err := c.cc.Invoke(ctx, MessageQueueService_GetMessageQueue_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageQueueServiceClient) ListTopics(ctx context.Context, in *ListTopicsRequest, opts ...grpc.CallOption) (*ListTopicsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTopicsResponse)
	err := c.cc.Invoke(ctx, MessageQueueService_ListTopics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageQueueServiceClient) CreateTopics(ctx context.Context, in *CreateTopicsRequest, opts ...grpc.CallOption) (*CreateTopicsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateTopicsResponse)
	err := c.cc.Invoke(ctx, MessageQueueService_CreateTopics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageQueueServiceClient) DeleteTopics(ctx context.Context, in *DeleteTopicsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, MessageQueueService_DeleteTopics_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *messageQueueServiceClient) UpdateTopic(ctx context.Context, in *UpdateTopicRequest, opts ...grpc.CallOption) (*Topic, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(Topic)
	err := c.cc.Invoke(ctx, MessageQueueService_UpdateTopic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// MessageQueueServiceServer is the server API for MessageQueueService service.
// All implementations must embed UnimplementedMessageQueueServiceServer
// for forward compatibility.
//
// MessageQueueService
type MessageQueueServiceServer interface {
	// register MQ
	RegisterMessageQueue(context.Context, *RegisterMessageQueueRequest) (*RegisterMessageQueueResponse, error)
	// list MQ
	ListMessageQueues(context.Context, *ListMessageQueuesRequest) (*ListMessageQueuesResponse, error)
	// get a MQ
	GetMessageQueue(context.Context, *GetMessageQueueRequest) (*MessageQueue, error)
	// list topics
	ListTopics(context.Context, *ListTopicsRequest) (*ListTopicsResponse, error)
	// create topics
	// (-- api-linter: core::0133::response-message-name=disabled
	//
	//	aip.dev/not-precedent: We need to do this because this is create multiple topics. --)
	CreateTopics(context.Context, *CreateTopicsRequest) (*CreateTopicsResponse, error)
	// delete topics
	DeleteTopics(context.Context, *DeleteTopicsRequest) (*emptypb.Empty, error)
	// update topic
	UpdateTopic(context.Context, *UpdateTopicRequest) (*Topic, error)
	mustEmbedUnimplementedMessageQueueServiceServer()
}

// UnimplementedMessageQueueServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedMessageQueueServiceServer struct{}

func (UnimplementedMessageQueueServiceServer) RegisterMessageQueue(context.Context, *RegisterMessageQueueRequest) (*RegisterMessageQueueResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RegisterMessageQueue not implemented")
}
func (UnimplementedMessageQueueServiceServer) ListMessageQueues(context.Context, *ListMessageQueuesRequest) (*ListMessageQueuesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMessageQueues not implemented")
}
func (UnimplementedMessageQueueServiceServer) GetMessageQueue(context.Context, *GetMessageQueueRequest) (*MessageQueue, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetMessageQueue not implemented")
}
func (UnimplementedMessageQueueServiceServer) ListTopics(context.Context, *ListTopicsRequest) (*ListTopicsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTopics not implemented")
}
func (UnimplementedMessageQueueServiceServer) CreateTopics(context.Context, *CreateTopicsRequest) (*CreateTopicsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTopics not implemented")
}
func (UnimplementedMessageQueueServiceServer) DeleteTopics(context.Context, *DeleteTopicsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTopics not implemented")
}
func (UnimplementedMessageQueueServiceServer) UpdateTopic(context.Context, *UpdateTopicRequest) (*Topic, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTopic not implemented")
}
func (UnimplementedMessageQueueServiceServer) mustEmbedUnimplementedMessageQueueServiceServer() {}
func (UnimplementedMessageQueueServiceServer) testEmbeddedByValue()                             {}

// UnsafeMessageQueueServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to MessageQueueServiceServer will
// result in compilation errors.
type UnsafeMessageQueueServiceServer interface {
	mustEmbedUnimplementedMessageQueueServiceServer()
}

func RegisterMessageQueueServiceServer(s grpc.ServiceRegistrar, srv MessageQueueServiceServer) {
	// If the following call pancis, it indicates UnimplementedMessageQueueServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&MessageQueueService_ServiceDesc, srv)
}

func _MessageQueueService_RegisterMessageQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RegisterMessageQueueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageQueueServiceServer).RegisterMessageQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageQueueService_RegisterMessageQueue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageQueueServiceServer).RegisterMessageQueue(ctx, req.(*RegisterMessageQueueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageQueueService_ListMessageQueues_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMessageQueuesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageQueueServiceServer).ListMessageQueues(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageQueueService_ListMessageQueues_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageQueueServiceServer).ListMessageQueues(ctx, req.(*ListMessageQueuesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageQueueService_GetMessageQueue_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetMessageQueueRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageQueueServiceServer).GetMessageQueue(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageQueueService_GetMessageQueue_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageQueueServiceServer).GetMessageQueue(ctx, req.(*GetMessageQueueRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageQueueService_ListTopics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTopicsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageQueueServiceServer).ListTopics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageQueueService_ListTopics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageQueueServiceServer).ListTopics(ctx, req.(*ListTopicsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageQueueService_CreateTopics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTopicsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageQueueServiceServer).CreateTopics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageQueueService_CreateTopics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageQueueServiceServer).CreateTopics(ctx, req.(*CreateTopicsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageQueueService_DeleteTopics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTopicsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageQueueServiceServer).DeleteTopics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageQueueService_DeleteTopics_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageQueueServiceServer).DeleteTopics(ctx, req.(*DeleteTopicsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _MessageQueueService_UpdateTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTopicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(MessageQueueServiceServer).UpdateTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: MessageQueueService_UpdateTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(MessageQueueServiceServer).UpdateTopic(ctx, req.(*UpdateTopicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// MessageQueueService_ServiceDesc is the grpc.ServiceDesc for MessageQueueService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var MessageQueueService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.tools.v1.MessageQueueService",
	HandlerType: (*MessageQueueServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RegisterMessageQueue",
			Handler:    _MessageQueueService_RegisterMessageQueue_Handler,
		},
		{
			MethodName: "ListMessageQueues",
			Handler:    _MessageQueueService_ListMessageQueues_Handler,
		},
		{
			MethodName: "GetMessageQueue",
			Handler:    _MessageQueueService_GetMessageQueue_Handler,
		},
		{
			MethodName: "ListTopics",
			Handler:    _MessageQueueService_ListTopics_Handler,
		},
		{
			MethodName: "CreateTopics",
			Handler:    _MessageQueueService_CreateTopics_Handler,
		},
		{
			MethodName: "DeleteTopics",
			Handler:    _MessageQueueService_DeleteTopics_Handler,
		},
		{
			MethodName: "UpdateTopic",
			Handler:    _MessageQueueService_UpdateTopic_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/tools/v1/message_queue_service.proto",
}
