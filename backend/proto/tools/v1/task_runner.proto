// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-package=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-multiple-files=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
syntax = "proto3";

package backend.proto.tools.v1;

option go_package = "github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspb";

// TaskRunner 定义了任务运行器服务的接口。
service TaskRunner {
    // RunTask executes a specific task by name
    rpc RunTask(RunTaskRequest) returns (DefaultResponse);
}

// DefaultResponse is DefaultResponse
message DefaultResponse{
    // message is the message
    string message=1;
    // error_code is error_code
    int32 error_code=2;
}

// RunTaskRequest is the request for RunTask
message RunTaskRequest{
    // task is the name of the task to run
    string task=1;
    // task_type is the task type, related to the global.TaskType
    string task_type=2;
    // params is the params
    // 透传额外字段，如 import_sheet_url, priority
    map<string, string> params = 3;
}