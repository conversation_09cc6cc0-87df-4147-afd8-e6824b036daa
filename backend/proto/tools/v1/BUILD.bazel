load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "toolspb_proto",
    srcs = [
        "adminer_management.proto",
        "cache_service.proto",
        "cluster_service.proto",
        "database_service.proto",
        "deploy_platform.proto",
        "environment_service.proto",
        "message_queue_service.proto",
        "resource_models.proto",
        "staging_service.proto",
        "task_runner.proto",
        "testing_service.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto",
        "@com_google_protobuf//:duration_proto",
        "@com_google_protobuf//:empty_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@googleapis//google/api:annotations_proto",
        "@googleapis//google/api:field_behavior_proto",
    ],
)

go_proto_library(
    name = "toolspb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/tools/v1",
    proto = ":toolspb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library",
        "@googleapis//google/api:annotations_go_proto",
        "@googleapis//google/api:field_behavior_go_proto",
    ],
)

go_library(
    name = "tools",
    embed = [":toolspb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/tools/v1",
    visibility = ["//visibility:public"],
)
