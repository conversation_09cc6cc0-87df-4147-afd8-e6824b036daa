// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-package=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
// (-- api-linter: core::0191::java-multiple-files=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/tools/v1/task_runner.proto

package toolspb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// DefaultResponse is DefaultResponse
type DefaultResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// message is the message
	Message string `protobuf:"bytes,1,opt,name=message,proto3" json:"message,omitempty"`
	// error_code is error_code
	ErrorCode     int32 `protobuf:"varint,2,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DefaultResponse) Reset() {
	*x = DefaultResponse{}
	mi := &file_backend_proto_tools_v1_task_runner_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DefaultResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DefaultResponse) ProtoMessage() {}

func (x *DefaultResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_task_runner_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DefaultResponse.ProtoReflect.Descriptor instead.
func (*DefaultResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_task_runner_proto_rawDescGZIP(), []int{0}
}

func (x *DefaultResponse) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

func (x *DefaultResponse) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

// RunTaskRequest is the request for RunTask
type RunTaskRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// task is the name of the task to run
	Task string `protobuf:"bytes,1,opt,name=task,proto3" json:"task,omitempty"`
	// task_type is the task type, related to the global.TaskType
	TaskType string `protobuf:"bytes,2,opt,name=task_type,json=taskType,proto3" json:"task_type,omitempty"`
	// params is the params
	// 透传额外字段，如 import_sheet_url, priority
	Params        map[string]string `protobuf:"bytes,3,rep,name=params,proto3" json:"params,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RunTaskRequest) Reset() {
	*x = RunTaskRequest{}
	mi := &file_backend_proto_tools_v1_task_runner_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RunTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RunTaskRequest) ProtoMessage() {}

func (x *RunTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_tools_v1_task_runner_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RunTaskRequest.ProtoReflect.Descriptor instead.
func (*RunTaskRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_tools_v1_task_runner_proto_rawDescGZIP(), []int{1}
}

func (x *RunTaskRequest) GetTask() string {
	if x != nil {
		return x.Task
	}
	return ""
}

func (x *RunTaskRequest) GetTaskType() string {
	if x != nil {
		return x.TaskType
	}
	return ""
}

func (x *RunTaskRequest) GetParams() map[string]string {
	if x != nil {
		return x.Params
	}
	return nil
}

var File_backend_proto_tools_v1_task_runner_proto protoreflect.FileDescriptor

const file_backend_proto_tools_v1_task_runner_proto_rawDesc = "" +
	"\n" +
	"(backend/proto/tools/v1/task_runner.proto\x12\x16backend.proto.tools.v1\"J\n" +
	"\x0fDefaultResponse\x12\x18\n" +
	"\amessage\x18\x01 \x01(\tR\amessage\x12\x1d\n" +
	"\n" +
	"error_code\x18\x02 \x01(\x05R\terrorCode\"\xc8\x01\n" +
	"\x0eRunTaskRequest\x12\x12\n" +
	"\x04task\x18\x01 \x01(\tR\x04task\x12\x1b\n" +
	"\ttask_type\x18\x02 \x01(\tR\btaskType\x12J\n" +
	"\x06params\x18\x03 \x03(\v22.backend.proto.tools.v1.RunTaskRequest.ParamsEntryR\x06params\x1a9\n" +
	"\vParamsEntry\x12\x10\n" +
	"\x03key\x18\x01 \x01(\tR\x03key\x12\x14\n" +
	"\x05value\x18\x02 \x01(\tR\x05value:\x028\x012h\n" +
	"\n" +
	"TaskRunner\x12Z\n" +
	"\aRunTask\x12&.backend.proto.tools.v1.RunTaskRequest\x1a'.backend.proto.tools.v1.DefaultResponseB>Z<github.com/MoeGolibrary/moego/backend/proto/tools/v1;toolspbb\x06proto3"

var (
	file_backend_proto_tools_v1_task_runner_proto_rawDescOnce sync.Once
	file_backend_proto_tools_v1_task_runner_proto_rawDescData []byte
)

func file_backend_proto_tools_v1_task_runner_proto_rawDescGZIP() []byte {
	file_backend_proto_tools_v1_task_runner_proto_rawDescOnce.Do(func() {
		file_backend_proto_tools_v1_task_runner_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_task_runner_proto_rawDesc), len(file_backend_proto_tools_v1_task_runner_proto_rawDesc)))
	})
	return file_backend_proto_tools_v1_task_runner_proto_rawDescData
}

var file_backend_proto_tools_v1_task_runner_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_backend_proto_tools_v1_task_runner_proto_goTypes = []any{
	(*DefaultResponse)(nil), // 0: backend.proto.tools.v1.DefaultResponse
	(*RunTaskRequest)(nil),  // 1: backend.proto.tools.v1.RunTaskRequest
	nil,                     // 2: backend.proto.tools.v1.RunTaskRequest.ParamsEntry
}
var file_backend_proto_tools_v1_task_runner_proto_depIdxs = []int32{
	2, // 0: backend.proto.tools.v1.RunTaskRequest.params:type_name -> backend.proto.tools.v1.RunTaskRequest.ParamsEntry
	1, // 1: backend.proto.tools.v1.TaskRunner.RunTask:input_type -> backend.proto.tools.v1.RunTaskRequest
	0, // 2: backend.proto.tools.v1.TaskRunner.RunTask:output_type -> backend.proto.tools.v1.DefaultResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_backend_proto_tools_v1_task_runner_proto_init() }
func file_backend_proto_tools_v1_task_runner_proto_init() {
	if File_backend_proto_tools_v1_task_runner_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_tools_v1_task_runner_proto_rawDesc), len(file_backend_proto_tools_v1_task_runner_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_tools_v1_task_runner_proto_goTypes,
		DependencyIndexes: file_backend_proto_tools_v1_task_runner_proto_depIdxs,
		MessageInfos:      file_backend_proto_tools_v1_task_runner_proto_msgTypes,
	}.Build()
	File_backend_proto_tools_v1_task_runner_proto = out.File
	file_backend_proto_tools_v1_task_runner_proto_goTypes = nil
	file_backend_proto_tools_v1_task_runner_proto_depIdxs = nil
}
