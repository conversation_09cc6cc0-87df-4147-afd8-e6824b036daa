// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/tools/v1/testing_service.proto

package toolspb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TestingService_CleanApps_FullMethodName                 = "/backend.proto.tools.v1.TestingService/CleanApps"
	TestingService_CleanServices_FullMethodName             = "/backend.proto.tools.v1.TestingService/CleanServices"
	TestingService_CleanEnvoyFilters_FullMethodName         = "/backend.proto.tools.v1.TestingService/CleanEnvoyFilters"
	TestingService_CleanPodDisruptionBudgets_FullMethodName = "/backend.proto.tools.v1.TestingService/CleanPodDisruptionBudgets"
)

// TestingServiceClient is the client API for TestingService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// TestingService
type TestingServiceClient interface {
	// clean apps
	CleanApps(ctx context.Context, in *CleanAppsRequest, opts ...grpc.CallOption) (*CleanAppsResponse, error)
	// celan Services
	CleanServices(ctx context.Context, in *CleanServicesRequest, opts ...grpc.CallOption) (*CleanServicesResponse, error)
	// clean EnvoyFilters
	CleanEnvoyFilters(ctx context.Context, in *CleanEnvoyFiltersRequest, opts ...grpc.CallOption) (*CleanEnvoyFiltersResponse, error)
	// clean PodDisruptionBudgets
	CleanPodDisruptionBudgets(ctx context.Context, in *CleanPodDisruptionBudgetsRequest, opts ...grpc.CallOption) (*CleanPodDisruptionBudgetsResponse, error)
}

type testingServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewTestingServiceClient(cc grpc.ClientConnInterface) TestingServiceClient {
	return &testingServiceClient{cc}
}

func (c *testingServiceClient) CleanApps(ctx context.Context, in *CleanAppsRequest, opts ...grpc.CallOption) (*CleanAppsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CleanAppsResponse)
	err := c.cc.Invoke(ctx, TestingService_CleanApps_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testingServiceClient) CleanServices(ctx context.Context, in *CleanServicesRequest, opts ...grpc.CallOption) (*CleanServicesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CleanServicesResponse)
	err := c.cc.Invoke(ctx, TestingService_CleanServices_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testingServiceClient) CleanEnvoyFilters(ctx context.Context, in *CleanEnvoyFiltersRequest, opts ...grpc.CallOption) (*CleanEnvoyFiltersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CleanEnvoyFiltersResponse)
	err := c.cc.Invoke(ctx, TestingService_CleanEnvoyFilters_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *testingServiceClient) CleanPodDisruptionBudgets(ctx context.Context, in *CleanPodDisruptionBudgetsRequest, opts ...grpc.CallOption) (*CleanPodDisruptionBudgetsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CleanPodDisruptionBudgetsResponse)
	err := c.cc.Invoke(ctx, TestingService_CleanPodDisruptionBudgets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TestingServiceServer is the server API for TestingService service.
// All implementations must embed UnimplementedTestingServiceServer
// for forward compatibility.
//
// TestingService
type TestingServiceServer interface {
	// clean apps
	CleanApps(context.Context, *CleanAppsRequest) (*CleanAppsResponse, error)
	// celan Services
	CleanServices(context.Context, *CleanServicesRequest) (*CleanServicesResponse, error)
	// clean EnvoyFilters
	CleanEnvoyFilters(context.Context, *CleanEnvoyFiltersRequest) (*CleanEnvoyFiltersResponse, error)
	// clean PodDisruptionBudgets
	CleanPodDisruptionBudgets(context.Context, *CleanPodDisruptionBudgetsRequest) (*CleanPodDisruptionBudgetsResponse, error)
	mustEmbedUnimplementedTestingServiceServer()
}

// UnimplementedTestingServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTestingServiceServer struct{}

func (UnimplementedTestingServiceServer) CleanApps(context.Context, *CleanAppsRequest) (*CleanAppsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanApps not implemented")
}
func (UnimplementedTestingServiceServer) CleanServices(context.Context, *CleanServicesRequest) (*CleanServicesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanServices not implemented")
}
func (UnimplementedTestingServiceServer) CleanEnvoyFilters(context.Context, *CleanEnvoyFiltersRequest) (*CleanEnvoyFiltersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanEnvoyFilters not implemented")
}
func (UnimplementedTestingServiceServer) CleanPodDisruptionBudgets(context.Context, *CleanPodDisruptionBudgetsRequest) (*CleanPodDisruptionBudgetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CleanPodDisruptionBudgets not implemented")
}
func (UnimplementedTestingServiceServer) mustEmbedUnimplementedTestingServiceServer() {}
func (UnimplementedTestingServiceServer) testEmbeddedByValue()                        {}

// UnsafeTestingServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TestingServiceServer will
// result in compilation errors.
type UnsafeTestingServiceServer interface {
	mustEmbedUnimplementedTestingServiceServer()
}

func RegisterTestingServiceServer(s grpc.ServiceRegistrar, srv TestingServiceServer) {
	// If the following call pancis, it indicates UnimplementedTestingServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TestingService_ServiceDesc, srv)
}

func _TestingService_CleanApps_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanAppsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestingServiceServer).CleanApps(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestingService_CleanApps_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestingServiceServer).CleanApps(ctx, req.(*CleanAppsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestingService_CleanServices_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanServicesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestingServiceServer).CleanServices(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestingService_CleanServices_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestingServiceServer).CleanServices(ctx, req.(*CleanServicesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestingService_CleanEnvoyFilters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanEnvoyFiltersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestingServiceServer).CleanEnvoyFilters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestingService_CleanEnvoyFilters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestingServiceServer).CleanEnvoyFilters(ctx, req.(*CleanEnvoyFiltersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TestingService_CleanPodDisruptionBudgets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CleanPodDisruptionBudgetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TestingServiceServer).CleanPodDisruptionBudgets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TestingService_CleanPodDisruptionBudgets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TestingServiceServer).CleanPodDisruptionBudgets(ctx, req.(*CleanPodDisruptionBudgetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TestingService_ServiceDesc is the grpc.ServiceDesc for TestingService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TestingService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.tools.v1.TestingService",
	HandlerType: (*TestingServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CleanApps",
			Handler:    _TestingService_CleanApps_Handler,
		},
		{
			MethodName: "CleanServices",
			Handler:    _TestingService_CleanServices_Handler,
		},
		{
			MethodName: "CleanEnvoyFilters",
			Handler:    _TestingService_CleanEnvoyFilters_Handler,
		},
		{
			MethodName: "CleanPodDisruptionBudgets",
			Handler:    _TestingService_CleanPodDisruptionBudgets_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/tools/v1/testing_service.proto",
}
