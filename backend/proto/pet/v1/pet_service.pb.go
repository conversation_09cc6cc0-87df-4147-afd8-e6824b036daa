// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0132::request-field-types=disabled
//     aip.dev/not-precedent: 使用自定义过滤器类型 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用自定义的响应字段结构 --)
// (-- api-linter: core::0132::request-parent-field=disabled
//     aip.dev/not-precedent: 使用int64类型的parent字段表示customer id --)
// (-- api-linter: core::0132::request-parent-behavior=disabled
//     aip.dev/not-precedent: 不需要behavior --)
// (-- api-linter: core::0132::request-parent-reference=disabled
//     aip.dev/not-precedent: 不需要reference，业务不支持 --)

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/pet/v1/pet_service.proto

package petpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ErrCode 定义错误码枚举
//
// 以下两行枚举不要改动，从下面开始累加最后两位数新增错误码，前四位不能改
// 如果单个服务新增错误码超过100个, 建议入土
// 如果是老服务重构的新服务可删除以上错误码继续沿用原有的错误码
// 注意：pb enum 的规范是：统一前缀，全大写，下划线
// 业务代码优先使用通用错误码 /backend/common/proto/rpc/code.proto ，再定义自己的错误码
type ErrCode int32

const (
	// (-- api-linter: core::0126::unspecified=disabled
	//
	//	aip.dev/not-precedent: We need to do this because
	//	the content of the error code is automatically generated by
	//	the script and is exclusive to each service.
	//	Please do not turn off this linter for the rest of the enum --)
	//
	// 成功
	ErrCode_ERR_CODE_OK ErrCode = 0
	// 本服务自动分配的全局唯一的起始错误码
	ErrCode_ERR_CODE_UNSPECIFIED ErrCode = 118800
)

// Enum value maps for ErrCode.
var (
	ErrCode_name = map[int32]string{
		0:      "ERR_CODE_OK",
		118800: "ERR_CODE_UNSPECIFIED",
	}
	ErrCode_value = map[string]int32{
		"ERR_CODE_OK":          0,
		"ERR_CODE_UNSPECIFIED": 118800,
	}
)

func (x ErrCode) Enum() *ErrCode {
	p := new(ErrCode)
	*p = x
	return p
}

func (x ErrCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ErrCode) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_pet_v1_pet_service_proto_enumTypes[0].Descriptor()
}

func (ErrCode) Type() protoreflect.EnumType {
	return &file_backend_proto_pet_v1_pet_service_proto_enumTypes[0]
}

func (x ErrCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ErrCode.Descriptor instead.
func (ErrCode) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{0}
}

// (-- api-linter: core::0158::request-page-token-field=disabled
//
//	aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
//
// SearchPetRequest 搜索宠物文档请求
type SearchPetRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 搜索条件，使用 oneof 实现可扩展的搜索条件
	//
	// Types that are valid to be assigned to SearchCriteria:
	//
	//	*SearchPetRequest_Term
	//	*SearchPetRequest_PetFilter
	SearchCriteria isSearchPetRequest_SearchCriteria `protobuf_oneof:"search_criteria"`
	// company id
	CompanyId *int64 `protobuf:"varint,3,opt,name=company_id,json=companyId,proto3,oneof" json:"company_id,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,10,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 分页游标
	PageToken     *string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3,oneof" json:"page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPetRequest) Reset() {
	*x = SearchPetRequest{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetRequest) ProtoMessage() {}

func (x *SearchPetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetRequest.ProtoReflect.Descriptor instead.
func (*SearchPetRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{0}
}

func (x *SearchPetRequest) GetSearchCriteria() isSearchPetRequest_SearchCriteria {
	if x != nil {
		return x.SearchCriteria
	}
	return nil
}

func (x *SearchPetRequest) GetTerm() string {
	if x != nil {
		if x, ok := x.SearchCriteria.(*SearchPetRequest_Term); ok {
			return x.Term
		}
	}
	return ""
}

func (x *SearchPetRequest) GetPetFilter() *SearchPetRequest_Filter {
	if x != nil {
		if x, ok := x.SearchCriteria.(*SearchPetRequest_PetFilter); ok {
			return x.PetFilter
		}
	}
	return nil
}

func (x *SearchPetRequest) GetCompanyId() int64 {
	if x != nil && x.CompanyId != nil {
		return *x.CompanyId
	}
	return 0
}

func (x *SearchPetRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *SearchPetRequest) GetPageToken() string {
	if x != nil && x.PageToken != nil {
		return *x.PageToken
	}
	return ""
}

type isSearchPetRequest_SearchCriteria interface {
	isSearchPetRequest_SearchCriteria()
}

type SearchPetRequest_Term struct {
	// 搜索词（用于搜索宠物名称或客户名称）
	Term string `protobuf:"bytes,1,opt,name=term,proto3,oneof"`
}

type SearchPetRequest_PetFilter struct {
	// Filter pet_filter = 2;
	PetFilter *SearchPetRequest_Filter `protobuf:"bytes,2,opt,name=pet_filter,json=petFilter,proto3,oneof"`
}

func (*SearchPetRequest_Term) isSearchPetRequest_SearchCriteria() {}

func (*SearchPetRequest_PetFilter) isSearchPetRequest_SearchCriteria() {}

// SearchPetResponse 搜索宠物文档响应
type SearchPetResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物列表
	Pets []*SearchPetResponse_Pet `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
	// next page token
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPetResponse) Reset() {
	*x = SearchPetResponse{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetResponse) ProtoMessage() {}

func (x *SearchPetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetResponse.ProtoReflect.Descriptor instead.
func (*SearchPetResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{1}
}

func (x *SearchPetResponse) GetPets() []*SearchPetResponse_Pet {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *SearchPetResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

// IndexPetDocumentRequest
// 同步数据到ES的请求
type IndexPetDocumentRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// flags
	//
	// Types that are valid to be assigned to Flags:
	//
	//	*IndexPetDocumentRequest_Company_
	//	*IndexPetDocumentRequest_Customer_
	//	*IndexPetDocumentRequest_Pet_
	Flags         isIndexPetDocumentRequest_Flags `protobuf_oneof:"flags"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IndexPetDocumentRequest) Reset() {
	*x = IndexPetDocumentRequest{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IndexPetDocumentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexPetDocumentRequest) ProtoMessage() {}

func (x *IndexPetDocumentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexPetDocumentRequest.ProtoReflect.Descriptor instead.
func (*IndexPetDocumentRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{2}
}

func (x *IndexPetDocumentRequest) GetFlags() isIndexPetDocumentRequest_Flags {
	if x != nil {
		return x.Flags
	}
	return nil
}

func (x *IndexPetDocumentRequest) GetCompany() *IndexPetDocumentRequest_Company {
	if x != nil {
		if x, ok := x.Flags.(*IndexPetDocumentRequest_Company_); ok {
			return x.Company
		}
	}
	return nil
}

func (x *IndexPetDocumentRequest) GetCustomer() *IndexPetDocumentRequest_Customer {
	if x != nil {
		if x, ok := x.Flags.(*IndexPetDocumentRequest_Customer_); ok {
			return x.Customer
		}
	}
	return nil
}

func (x *IndexPetDocumentRequest) GetPet() *IndexPetDocumentRequest_Pet {
	if x != nil {
		if x, ok := x.Flags.(*IndexPetDocumentRequest_Pet_); ok {
			return x.Pet
		}
	}
	return nil
}

type isIndexPetDocumentRequest_Flags interface {
	isIndexPetDocumentRequest_Flags()
}

type IndexPetDocumentRequest_Company_ struct {
	// company 维度
	Company *IndexPetDocumentRequest_Company `protobuf:"bytes,1,opt,name=company,proto3,oneof"`
}

type IndexPetDocumentRequest_Customer_ struct {
	// customer 维度
	Customer *IndexPetDocumentRequest_Customer `protobuf:"bytes,2,opt,name=customer,proto3,oneof"`
}

type IndexPetDocumentRequest_Pet_ struct {
	// pet 维度
	Pet *IndexPetDocumentRequest_Pet `protobuf:"bytes,3,opt,name=pet,proto3,oneof"`
}

func (*IndexPetDocumentRequest_Company_) isIndexPetDocumentRequest_Flags() {}

func (*IndexPetDocumentRequest_Customer_) isIndexPetDocumentRequest_Flags() {}

func (*IndexPetDocumentRequest_Pet_) isIndexPetDocumentRequest_Flags() {}

// IndexPetDocumentResponse
// 同步数据到ES的响应
type IndexPetDocumentResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 成功索引的ID
	SuccessIds []int64 `protobuf:"varint,1,rep,packed,name=success_ids,json=successIds,proto3" json:"success_ids,omitempty"`
	// 错误的ID
	Errors        []*IndexPetDocumentResponse_Error `protobuf:"bytes,2,rep,name=errors,proto3" json:"errors,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IndexPetDocumentResponse) Reset() {
	*x = IndexPetDocumentResponse{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IndexPetDocumentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexPetDocumentResponse) ProtoMessage() {}

func (x *IndexPetDocumentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexPetDocumentResponse.ProtoReflect.Descriptor instead.
func (*IndexPetDocumentResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{3}
}

func (x *IndexPetDocumentResponse) GetSuccessIds() []int64 {
	if x != nil {
		return x.SuccessIds
	}
	return nil
}

func (x *IndexPetDocumentResponse) GetErrors() []*IndexPetDocumentResponse_Error {
	if x != nil {
		return x.Errors
	}
	return nil
}

// CreatePetRequest
type CreatePetRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物信息
	Pet           *Pet `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePetRequest) Reset() {
	*x = CreatePetRequest{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetRequest) ProtoMessage() {}

func (x *CreatePetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetRequest.ProtoReflect.Descriptor instead.
func (*CreatePetRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{4}
}

func (x *CreatePetRequest) GetPet() *Pet {
	if x != nil {
		return x.Pet
	}
	return nil
}

// CreatePetResponse
type CreatePetResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物信息
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePetResponse) Reset() {
	*x = CreatePetResponse{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePetResponse) ProtoMessage() {}

func (x *CreatePetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePetResponse.ProtoReflect.Descriptor instead.
func (*CreatePetResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{5}
}

func (x *CreatePetResponse) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// BatchCreatePetRequest
type BatchCreatePetsRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物信息
	Requests []*CreatePetRequest `protobuf:"bytes,1,rep,name=requests,proto3" json:"requests,omitempty"`
	// 客户ID (customer_id)
	// (-- api-linter: core::0233::request-parent-reference=disabled
	//
	//	aip.dev/not-precedent: 不需要reference，业务不支持 --)
	Parent        int64 `protobuf:"varint,2,opt,name=parent,proto3" json:"parent,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreatePetsRequest) Reset() {
	*x = BatchCreatePetsRequest{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreatePetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreatePetsRequest) ProtoMessage() {}

func (x *BatchCreatePetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreatePetsRequest.ProtoReflect.Descriptor instead.
func (*BatchCreatePetsRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{6}
}

func (x *BatchCreatePetsRequest) GetRequests() []*CreatePetRequest {
	if x != nil {
		return x.Requests
	}
	return nil
}

func (x *BatchCreatePetsRequest) GetParent() int64 {
	if x != nil {
		return x.Parent
	}
	return 0
}

// BatchCreatePetResponse
type BatchCreatePetsResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物信息
	Pets          []*Pet `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BatchCreatePetsResponse) Reset() {
	*x = BatchCreatePetsResponse{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BatchCreatePetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BatchCreatePetsResponse) ProtoMessage() {}

func (x *BatchCreatePetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BatchCreatePetsResponse.ProtoReflect.Descriptor instead.
func (*BatchCreatePetsResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{7}
}

func (x *BatchCreatePetsResponse) GetPets() []*Pet {
	if x != nil {
		return x.Pets
	}
	return nil
}

// UpdatePetRequest
type UpdatePetRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// update pet info
	Pet           *UpdatePetRequest_UpdatePet `protobuf:"bytes,1,opt,name=pet,proto3" json:"pet,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePetRequest) Reset() {
	*x = UpdatePetRequest{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetRequest) ProtoMessage() {}

func (x *UpdatePetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetRequest.ProtoReflect.Descriptor instead.
func (*UpdatePetRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpdatePetRequest) GetPet() *UpdatePetRequest_UpdatePet {
	if x != nil {
		return x.Pet
	}
	return nil
}

// ListPetRequest
type ListPetRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 过滤条件
	Filter *ListPetRequest_Filter `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// parent, 这里指的是customer id, 必填
	Parent int64 `protobuf:"varint,2,opt,name=parent,proto3" json:"parent,omitempty"`
	// 分页大小
	PageSize int32 `protobuf:"varint,100,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// 排序字段
	OrderBy *string `protobuf:"bytes,101,opt,name=order_by,json=orderBy,proto3,oneof" json:"order_by,omitempty"`
	// 分页参数，使用 oneof 实现不同分页方式
	//
	// Types that are valid to be assigned to Pagination:
	//
	//	*ListPetRequest_PageToken
	//	*ListPetRequest_Page
	Pagination    isListPetRequest_Pagination `protobuf_oneof:"pagination"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPetRequest) Reset() {
	*x = ListPetRequest{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetRequest) ProtoMessage() {}

func (x *ListPetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetRequest.ProtoReflect.Descriptor instead.
func (*ListPetRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{9}
}

func (x *ListPetRequest) GetFilter() *ListPetRequest_Filter {
	if x != nil {
		return x.Filter
	}
	return nil
}

func (x *ListPetRequest) GetParent() int64 {
	if x != nil {
		return x.Parent
	}
	return 0
}

func (x *ListPetRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListPetRequest) GetOrderBy() string {
	if x != nil && x.OrderBy != nil {
		return *x.OrderBy
	}
	return ""
}

func (x *ListPetRequest) GetPagination() isListPetRequest_Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListPetRequest) GetPageToken() string {
	if x != nil {
		if x, ok := x.Pagination.(*ListPetRequest_PageToken); ok {
			return x.PageToken
		}
	}
	return ""
}

func (x *ListPetRequest) GetPage() int32 {
	if x != nil {
		if x, ok := x.Pagination.(*ListPetRequest_Page); ok {
			return x.Page
		}
	}
	return 0
}

type isListPetRequest_Pagination interface {
	isListPetRequest_Pagination()
}

type ListPetRequest_PageToken struct {
	// 分页令牌，用于基于游标的分页（无限滚动）
	PageToken string `protobuf:"bytes,102,opt,name=page_token,json=pageToken,proto3,oneof"`
}

type ListPetRequest_Page struct {
	// 页码，用于基于偏移量的分页（深分页）
	Page int32 `protobuf:"varint,103,opt,name=page,proto3,oneof"`
}

func (*ListPetRequest_PageToken) isListPetRequest_Pagination() {}

func (*ListPetRequest_Page) isListPetRequest_Pagination() {}

// ListPetResponse
type ListPetResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物列表
	Pets []*Pet `protobuf:"bytes,1,rep,name=pets,proto3" json:"pets,omitempty"`
	// 分页信息
	//
	// Types that are valid to be assigned to Pagination:
	//
	//	*ListPetResponse_Total
	//	*ListPetResponse_NextPageToken
	Pagination    isListPetResponse_Pagination `protobuf_oneof:"pagination"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPetResponse) Reset() {
	*x = ListPetResponse{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetResponse) ProtoMessage() {}

func (x *ListPetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetResponse.ProtoReflect.Descriptor instead.
func (*ListPetResponse) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{10}
}

func (x *ListPetResponse) GetPets() []*Pet {
	if x != nil {
		return x.Pets
	}
	return nil
}

func (x *ListPetResponse) GetPagination() isListPetResponse_Pagination {
	if x != nil {
		return x.Pagination
	}
	return nil
}

func (x *ListPetResponse) GetTotal() int32 {
	if x != nil {
		if x, ok := x.Pagination.(*ListPetResponse_Total); ok {
			return x.Total
		}
	}
	return 0
}

func (x *ListPetResponse) GetNextPageToken() string {
	if x != nil {
		if x, ok := x.Pagination.(*ListPetResponse_NextPageToken); ok {
			return x.NextPageToken
		}
	}
	return ""
}

type isListPetResponse_Pagination interface {
	isListPetResponse_Pagination()
}

type ListPetResponse_Total struct {
	// total, 只有深分页时才会有内容
	Total int32 `protobuf:"varint,2,opt,name=total,proto3,oneof"`
}

type ListPetResponse_NextPageToken struct {
	// next page token, 用于无线流
	NextPageToken string `protobuf:"bytes,3,opt,name=next_page_token,json=nextPageToken,proto3,oneof"`
}

func (*ListPetResponse_Total) isListPetResponse_Pagination() {}

func (*ListPetResponse_NextPageToken) isListPetResponse_Pagination() {}

// DeletePetRequest
type DeletePetRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet id
	Id            int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePetRequest) Reset() {
	*x = DeletePetRequest{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePetRequest) ProtoMessage() {}

func (x *DeletePetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePetRequest.ProtoReflect.Descriptor instead.
func (*DeletePetRequest) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{11}
}

func (x *DeletePetRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 搜索条件
type SearchPetRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 按宠物ID搜索
	PetIds        []int64 `protobuf:"varint,2,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPetRequest_Filter) Reset() {
	*x = SearchPetRequest_Filter{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPetRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetRequest_Filter) ProtoMessage() {}

func (x *SearchPetRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetRequest_Filter.ProtoReflect.Descriptor instead.
func (*SearchPetRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{0, 0}
}

func (x *SearchPetRequest_Filter) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

// pet 文档
type SearchPetResponse_Pet struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 宠物ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 客户ID
	CustomerId int64 `protobuf:"varint,2,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	// 宠物名称
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// 宠物头像路径
	AvatarPath string `protobuf:"bytes,4,opt,name=avatar_path,json=avatarPath,proto3" json:"avatar_path,omitempty"`
	// 宠物类型
	PetType int32 `protobuf:"varint,5,opt,name=pet_type,json=petType,proto3" json:"pet_type,omitempty"`
	// 宠物品种
	Breed string `protobuf:"bytes,6,opt,name=breed,proto3" json:"breed,omitempty"`
	// 宠物体重
	Weight string `protobuf:"bytes,7,opt,name=weight,proto3" json:"weight,omitempty"`
	// 宠物毛发类型
	CoatType string `protobuf:"bytes,8,opt,name=coat_type,json=coatType,proto3" json:"coat_type,omitempty"`
	// 关联的客户信息
	Client        *SearchPetResponse_Pet_Client `protobuf:"bytes,9,opt,name=client,proto3" json:"client,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPetResponse_Pet) Reset() {
	*x = SearchPetResponse_Pet{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPetResponse_Pet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetResponse_Pet) ProtoMessage() {}

func (x *SearchPetResponse_Pet) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetResponse_Pet.ProtoReflect.Descriptor instead.
func (*SearchPetResponse_Pet) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{1, 0}
}

func (x *SearchPetResponse_Pet) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchPetResponse_Pet) GetCustomerId() int64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *SearchPetResponse_Pet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SearchPetResponse_Pet) GetAvatarPath() string {
	if x != nil {
		return x.AvatarPath
	}
	return ""
}

func (x *SearchPetResponse_Pet) GetPetType() int32 {
	if x != nil {
		return x.PetType
	}
	return 0
}

func (x *SearchPetResponse_Pet) GetBreed() string {
	if x != nil {
		return x.Breed
	}
	return ""
}

func (x *SearchPetResponse_Pet) GetWeight() string {
	if x != nil {
		return x.Weight
	}
	return ""
}

func (x *SearchPetResponse_Pet) GetCoatType() string {
	if x != nil {
		return x.CoatType
	}
	return ""
}

func (x *SearchPetResponse_Pet) GetClient() *SearchPetResponse_Pet_Client {
	if x != nil {
		return x.Client
	}
	return nil
}

// 客户信息
type SearchPetResponse_Pet_Client struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 客户ID
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 客户名
	GivenName string `protobuf:"bytes,2,opt,name=given_name,json=givenName,proto3" json:"given_name,omitempty"`
	// 客户姓
	FamilyName string `protobuf:"bytes,3,opt,name=family_name,json=familyName,proto3" json:"family_name,omitempty"`
	// 客户全名
	Name          string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SearchPetResponse_Pet_Client) Reset() {
	*x = SearchPetResponse_Pet_Client{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SearchPetResponse_Pet_Client) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SearchPetResponse_Pet_Client) ProtoMessage() {}

func (x *SearchPetResponse_Pet_Client) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SearchPetResponse_Pet_Client.ProtoReflect.Descriptor instead.
func (*SearchPetResponse_Pet_Client) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{1, 0, 0}
}

func (x *SearchPetResponse_Pet_Client) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *SearchPetResponse_Pet_Client) GetGivenName() string {
	if x != nil {
		return x.GivenName
	}
	return ""
}

func (x *SearchPetResponse_Pet_Client) GetFamilyName() string {
	if x != nil {
		return x.FamilyName
	}
	return ""
}

func (x *SearchPetResponse_Pet_Client) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

// 同步数据到ES的flag
type IndexPetDocumentRequest_Company struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// company ids, 如果用company id同步, 会找到所有与该company 关联的客户, 然后同步其信息与pet 信息
	CompanyIds    []int64 `protobuf:"varint,1,rep,packed,name=company_ids,json=companyIds,proto3" json:"company_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IndexPetDocumentRequest_Company) Reset() {
	*x = IndexPetDocumentRequest_Company{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IndexPetDocumentRequest_Company) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexPetDocumentRequest_Company) ProtoMessage() {}

func (x *IndexPetDocumentRequest_Company) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexPetDocumentRequest_Company.ProtoReflect.Descriptor instead.
func (*IndexPetDocumentRequest_Company) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{2, 0}
}

func (x *IndexPetDocumentRequest_Company) GetCompanyIds() []int64 {
	if x != nil {
		return x.CompanyIds
	}
	return nil
}

// 客户信息
type IndexPetDocumentRequest_Customer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// customer ids(client ids), 根据customer id 同步会一起更新该customer相关的pet信息到es
	CustomerIds   []int64 `protobuf:"varint,1,rep,packed,name=customer_ids,json=customerIds,proto3" json:"customer_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IndexPetDocumentRequest_Customer) Reset() {
	*x = IndexPetDocumentRequest_Customer{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IndexPetDocumentRequest_Customer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexPetDocumentRequest_Customer) ProtoMessage() {}

func (x *IndexPetDocumentRequest_Customer) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexPetDocumentRequest_Customer.ProtoReflect.Descriptor instead.
func (*IndexPetDocumentRequest_Customer) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{2, 1}
}

func (x *IndexPetDocumentRequest_Customer) GetCustomerIds() []int64 {
	if x != nil {
		return x.CustomerIds
	}
	return nil
}

// pet
type IndexPetDocumentRequest_Pet struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet ids, 同步pet的同时会更新该pet 关联的customer信息到es
	PetIds        []int64 `protobuf:"varint,1,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IndexPetDocumentRequest_Pet) Reset() {
	*x = IndexPetDocumentRequest_Pet{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IndexPetDocumentRequest_Pet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexPetDocumentRequest_Pet) ProtoMessage() {}

func (x *IndexPetDocumentRequest_Pet) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexPetDocumentRequest_Pet.ProtoReflect.Descriptor instead.
func (*IndexPetDocumentRequest_Pet) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{2, 2}
}

func (x *IndexPetDocumentRequest_Pet) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

// 错误信息, 表示索引失败的宠物ID
type IndexPetDocumentResponse_Error struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 错误类型，例如：
	// - document_missing_exception: 文档不存在
	// - version_conflict_engine_exception: 版本冲突
	// - mapper_parsing_exception: 字段映射错误
	// - 更多错误类型请参考 OpenSearch 文档
	ErrorType string `protobuf:"bytes,1,opt,name=error_type,json=errorType,proto3" json:"error_type,omitempty"`
	// 错误具体原因描述
	ErrorReason string `protobuf:"bytes,2,opt,name=error_reason,json=errorReason,proto3" json:"error_reason,omitempty"`
	// error id
	Id            int64 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IndexPetDocumentResponse_Error) Reset() {
	*x = IndexPetDocumentResponse_Error{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IndexPetDocumentResponse_Error) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IndexPetDocumentResponse_Error) ProtoMessage() {}

func (x *IndexPetDocumentResponse_Error) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IndexPetDocumentResponse_Error.ProtoReflect.Descriptor instead.
func (*IndexPetDocumentResponse_Error) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{3, 0}
}

func (x *IndexPetDocumentResponse_Error) GetErrorType() string {
	if x != nil {
		return x.ErrorType
	}
	return ""
}

func (x *IndexPetDocumentResponse_Error) GetErrorReason() string {
	if x != nil {
		return x.ErrorReason
	}
	return ""
}

func (x *IndexPetDocumentResponse_Error) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// pet
type UpdatePetRequest_UpdatePet struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// pet id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// pet name
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// pet type
	PetType *Pet_PetType `protobuf:"varint,3,opt,name=pet_type,json=petType,proto3,enum=backend.proto.pet.v1.Pet_PetType,oneof" json:"pet_type,omitempty"`
	// pet gender
	PetGender *Pet_PetGender `protobuf:"varint,4,opt,name=pet_gender,json=petGender,proto3,enum=backend.proto.pet.v1.Pet_PetGender,oneof" json:"pet_gender,omitempty"`
	// pet breed
	Breed *string `protobuf:"bytes,5,opt,name=breed,proto3,oneof" json:"breed,omitempty"`
	// mixed
	Mixed         *bool `protobuf:"varint,6,opt,name=mixed,proto3,oneof" json:"mixed,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePetRequest_UpdatePet) Reset() {
	*x = UpdatePetRequest_UpdatePet{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePetRequest_UpdatePet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePetRequest_UpdatePet) ProtoMessage() {}

func (x *UpdatePetRequest_UpdatePet) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePetRequest_UpdatePet.ProtoReflect.Descriptor instead.
func (*UpdatePetRequest_UpdatePet) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{8, 0}
}

func (x *UpdatePetRequest_UpdatePet) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdatePetRequest_UpdatePet) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdatePetRequest_UpdatePet) GetPetType() Pet_PetType {
	if x != nil && x.PetType != nil {
		return *x.PetType
	}
	return Pet_PET_TYPE_UNSPECIFIED
}

func (x *UpdatePetRequest_UpdatePet) GetPetGender() Pet_PetGender {
	if x != nil && x.PetGender != nil {
		return *x.PetGender
	}
	return Pet_PET_GENDER_UNSPECIFIED
}

func (x *UpdatePetRequest_UpdatePet) GetBreed() string {
	if x != nil && x.Breed != nil {
		return *x.Breed
	}
	return ""
}

func (x *UpdatePetRequest_UpdatePet) GetMixed() bool {
	if x != nil && x.Mixed != nil {
		return *x.Mixed
	}
	return false
}

// 过滤条件
type ListPetRequest_Filter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// (-- api-linter: core::0132::request-field-types=disabled
	//
	//	aip.dev/not-precedent: 使用自定义过滤器类型 --)
	//
	// 按宠物ID列表过滤
	PetIds []int64 `protobuf:"varint,1,rep,packed,name=pet_ids,json=petIds,proto3" json:"pet_ids,omitempty"`
	// pet type
	PetType       *int32 `protobuf:"varint,2,opt,name=pet_type,json=petType,proto3,oneof" json:"pet_type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListPetRequest_Filter) Reset() {
	*x = ListPetRequest_Filter{}
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListPetRequest_Filter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListPetRequest_Filter) ProtoMessage() {}

func (x *ListPetRequest_Filter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_pet_v1_pet_service_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListPetRequest_Filter.ProtoReflect.Descriptor instead.
func (*ListPetRequest_Filter) Descriptor() ([]byte, []int) {
	return file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ListPetRequest_Filter) GetPetIds() []int64 {
	if x != nil {
		return x.PetIds
	}
	return nil
}

func (x *ListPetRequest_Filter) GetPetType() int32 {
	if x != nil && x.PetType != nil {
		return *x.PetType
	}
	return 0
}

var File_backend_proto_pet_v1_pet_service_proto protoreflect.FileDescriptor

const file_backend_proto_pet_v1_pet_service_proto_rawDesc = "" +
	"\n" +
	"&backend/proto/pet/v1/pet_service.proto\x12\x14backend.proto.pet.v1\x1a\x1bbuf/validate/validate.proto\x1a\x1fgoogle/api/field_behavior.proto\x1a\x1ebackend/proto/pet/v1/pet.proto\x1a\x1bgoogle/protobuf/empty.proto\"\xda\x02\n" +
	"\x10SearchPetRequest\x12\x1f\n" +
	"\x04term\x18\x01 \x01(\tB\t\xbaH\x06r\x04\x10\x01\x18dH\x00R\x04term\x12N\n" +
	"\n" +
	"pet_filter\x18\x02 \x01(\v2-.backend.proto.pet.v1.SearchPetRequest.FilterH\x00R\tpetFilter\x12\"\n" +
	"\n" +
	"company_id\x18\x03 \x01(\x03H\x01R\tcompanyId\x88\x01\x01\x12*\n" +
	"\tpage_size\x18\n" +
	" \x01(\x05B\r\xe0A\x01\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\"\n" +
	"\n" +
	"page_token\x18\x04 \x01(\tH\x02R\tpageToken\x88\x01\x01\x1a0\n" +
	"\x06Filter\x12&\n" +
	"\apet_ids\x18\x02 \x03(\x03B\r\xe0A\x02\xbaH\a\x92\x01\x04\b\x01\x18\x01R\x06petIdsB\x11\n" +
	"\x0fsearch_criteriaB\r\n" +
	"\v_company_idB\r\n" +
	"\v_page_token\"\x94\x04\n" +
	"\x11SearchPetResponse\x12D\n" +
	"\x04pets\x18\x01 \x03(\v2+.backend.proto.pet.v1.SearchPetResponse.PetB\x03\xe0A\x03R\x04pets\x12+\n" +
	"\x0fnext_page_token\x18\x02 \x01(\tB\x03\xe0A\x03R\rnextPageToken\x1a\x8b\x03\n" +
	"\x03Pet\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1f\n" +
	"\vcustomer_id\x18\x02 \x01(\x03R\n" +
	"customerId\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1f\n" +
	"\vavatar_path\x18\x04 \x01(\tR\n" +
	"avatarPath\x12\x19\n" +
	"\bpet_type\x18\x05 \x01(\x05R\apetType\x12\x14\n" +
	"\x05breed\x18\x06 \x01(\tR\x05breed\x12\x16\n" +
	"\x06weight\x18\a \x01(\tR\x06weight\x12\x1b\n" +
	"\tcoat_type\x18\b \x01(\tR\bcoatType\x12J\n" +
	"\x06client\x18\t \x01(\v22.backend.proto.pet.v1.SearchPetResponse.Pet.ClientR\x06client\x1al\n" +
	"\x06Client\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x1d\n" +
	"\n" +
	"given_name\x18\x02 \x01(\tR\tgivenName\x12\x1f\n" +
	"\vfamily_name\x18\x03 \x01(\tR\n" +
	"familyName\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\"\x9c\x03\n" +
	"\x17IndexPetDocumentRequest\x12Q\n" +
	"\acompany\x18\x01 \x01(\v25.backend.proto.pet.v1.IndexPetDocumentRequest.CompanyH\x00R\acompany\x12T\n" +
	"\bcustomer\x18\x02 \x01(\v26.backend.proto.pet.v1.IndexPetDocumentRequest.CustomerH\x00R\bcustomer\x12E\n" +
	"\x03pet\x18\x03 \x01(\v21.backend.proto.pet.v1.IndexPetDocumentRequest.PetH\x00R\x03pet\x1a/\n" +
	"\aCompany\x12$\n" +
	"\vcompany_ids\x18\x01 \x03(\x03B\x03\xe0A\x02R\n" +
	"companyIds\x1a2\n" +
	"\bCustomer\x12&\n" +
	"\fcustomer_ids\x18\x01 \x03(\x03B\x03\xe0A\x02R\vcustomerIds\x1a#\n" +
	"\x03Pet\x12\x1c\n" +
	"\apet_ids\x18\x01 \x03(\x03B\x03\xe0A\x02R\x06petIdsB\a\n" +
	"\x05flags\"\xee\x01\n" +
	"\x18IndexPetDocumentResponse\x12$\n" +
	"\vsuccess_ids\x18\x01 \x03(\x03B\x03\xe0A\x03R\n" +
	"successIds\x12Q\n" +
	"\x06errors\x18\x02 \x03(\v24.backend.proto.pet.v1.IndexPetDocumentResponse.ErrorB\x03\xe0A\x03R\x06errors\x1aY\n" +
	"\x05Error\x12\x1d\n" +
	"\n" +
	"error_type\x18\x01 \x01(\tR\terrorType\x12!\n" +
	"\ferror_reason\x18\x02 \x01(\tR\verrorReason\x12\x0e\n" +
	"\x02id\x18\x03 \x01(\x03R\x02id\"?\n" +
	"\x10CreatePetRequest\x12+\n" +
	"\x03pet\x18\x01 \x01(\v2\x19.backend.proto.pet.v1.PetR\x03pet\"#\n" +
	"\x11CreatePetResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"~\n" +
	"\x16BatchCreatePetsRequest\x12G\n" +
	"\brequests\x18\x01 \x03(\v2&.backend.proto.pet.v1.CreatePetRequestB\x03\xe0A\x02R\brequests\x12\x1b\n" +
	"\x06parent\x18\x02 \x01(\x03B\x03\xe0A\x02R\x06parent\"H\n" +
	"\x17BatchCreatePetsResponse\x12-\n" +
	"\x04pets\x18\x01 \x03(\v2\x19.backend.proto.pet.v1.PetR\x04pets\"\x88\x03\n" +
	"\x10UpdatePetRequest\x12B\n" +
	"\x03pet\x18\x01 \x01(\v20.backend.proto.pet.v1.UpdatePetRequest.UpdatePetR\x03pet\x1a\xaf\x02\n" +
	"\tUpdatePet\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\x04name\x18\x02 \x01(\tH\x00R\x04name\x88\x01\x01\x12A\n" +
	"\bpet_type\x18\x03 \x01(\x0e2!.backend.proto.pet.v1.Pet.PetTypeH\x01R\apetType\x88\x01\x01\x12G\n" +
	"\n" +
	"pet_gender\x18\x04 \x01(\x0e2#.backend.proto.pet.v1.Pet.PetGenderH\x02R\tpetGender\x88\x01\x01\x12\x19\n" +
	"\x05breed\x18\x05 \x01(\tH\x03R\x05breed\x88\x01\x01\x12\x19\n" +
	"\x05mixed\x18\x06 \x01(\bH\x04R\x05mixed\x88\x01\x01B\a\n" +
	"\x05_nameB\v\n" +
	"\t_pet_typeB\r\n" +
	"\v_pet_genderB\b\n" +
	"\x06_breedB\b\n" +
	"\x06_mixed\"\xf7\x02\n" +
	"\x0eListPetRequest\x12C\n" +
	"\x06filter\x18\x01 \x01(\v2+.backend.proto.pet.v1.ListPetRequest.FilterR\x06filter\x12\x1f\n" +
	"\x06parent\x18\x02 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x06parent\x12*\n" +
	"\tpage_size\x18d \x01(\x05B\r\xe0A\x01\xbaH\a\x1a\x05\x18\xe8\a(\x01R\bpageSize\x12\x1e\n" +
	"\border_by\x18e \x01(\tH\x01R\aorderBy\x88\x01\x01\x12\x1f\n" +
	"\n" +
	"page_token\x18f \x01(\tH\x00R\tpageToken\x12\x1d\n" +
	"\x04page\x18g \x01(\x05B\a\xbaH\x04\x1a\x02(\x01H\x00R\x04page\x1aX\n" +
	"\x06Filter\x12!\n" +
	"\apet_ids\x18\x01 \x03(\x03B\b\xbaH\x05\x92\x01\x02\x18\x01R\x06petIds\x12\x1e\n" +
	"\bpet_type\x18\x02 \x01(\x05H\x00R\apetType\x88\x01\x01B\v\n" +
	"\t_pet_typeB\f\n" +
	"\n" +
	"paginationB\v\n" +
	"\t_order_by\"\x90\x01\n" +
	"\x0fListPetResponse\x12-\n" +
	"\x04pets\x18\x01 \x03(\v2\x19.backend.proto.pet.v1.PetR\x04pets\x12\x16\n" +
	"\x05total\x18\x02 \x01(\x05H\x00R\x05total\x12(\n" +
	"\x0fnext_page_token\x18\x03 \x01(\tH\x00R\rnextPageTokenB\f\n" +
	"\n" +
	"pagination\"+\n" +
	"\x10DeletePetRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00R\x02id*6\n" +
	"\aErrCode\x12\x0f\n" +
	"\vERR_CODE_OK\x10\x00\x12\x1a\n" +
	"\x14ERR_CODE_UNSPECIFIED\x10\x90\xa0\a2\x9d\x05\n" +
	"\n" +
	"PetService\x12\\\n" +
	"\tSearchPet\x12&.backend.proto.pet.v1.SearchPetRequest\x1a'.backend.proto.pet.v1.SearchPetResponse\x12q\n" +
	"\x10IndexPetDocument\x12-.backend.proto.pet.v1.IndexPetDocumentRequest\x1a..backend.proto.pet.v1.IndexPetDocumentResponse\x12\\\n" +
	"\tCreatePet\x12&.backend.proto.pet.v1.CreatePetRequest\x1a'.backend.proto.pet.v1.CreatePetResponse\x12n\n" +
	"\x0fBatchCreatePets\x12,.backend.proto.pet.v1.BatchCreatePetsRequest\x1a-.backend.proto.pet.v1.BatchCreatePetsResponse\x12K\n" +
	"\tUpdatePet\x12&.backend.proto.pet.v1.UpdatePetRequest\x1a\x16.google.protobuf.Empty\x12V\n" +
	"\aListPet\x12$.backend.proto.pet.v1.ListPetRequest\x1a%.backend.proto.pet.v1.ListPetResponse\x12K\n" +
	"\tDeletePet\x12&.backend.proto.pet.v1.DeletePetRequest\x1a\x16.google.protobuf.EmptyB\\\n" +
	"\x1ecom.moego.backend.proto.pet.v1P\x01Z8github.com/MoeGolibrary/moego/backend/proto/pet/v1;petpbb\x06proto3"

var (
	file_backend_proto_pet_v1_pet_service_proto_rawDescOnce sync.Once
	file_backend_proto_pet_v1_pet_service_proto_rawDescData []byte
)

func file_backend_proto_pet_v1_pet_service_proto_rawDescGZIP() []byte {
	file_backend_proto_pet_v1_pet_service_proto_rawDescOnce.Do(func() {
		file_backend_proto_pet_v1_pet_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_pet_v1_pet_service_proto_rawDesc), len(file_backend_proto_pet_v1_pet_service_proto_rawDesc)))
	})
	return file_backend_proto_pet_v1_pet_service_proto_rawDescData
}

var file_backend_proto_pet_v1_pet_service_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_pet_v1_pet_service_proto_msgTypes = make([]protoimpl.MessageInfo, 21)
var file_backend_proto_pet_v1_pet_service_proto_goTypes = []any{
	(ErrCode)(0),                             // 0: backend.proto.pet.v1.ErrCode
	(*SearchPetRequest)(nil),                 // 1: backend.proto.pet.v1.SearchPetRequest
	(*SearchPetResponse)(nil),                // 2: backend.proto.pet.v1.SearchPetResponse
	(*IndexPetDocumentRequest)(nil),          // 3: backend.proto.pet.v1.IndexPetDocumentRequest
	(*IndexPetDocumentResponse)(nil),         // 4: backend.proto.pet.v1.IndexPetDocumentResponse
	(*CreatePetRequest)(nil),                 // 5: backend.proto.pet.v1.CreatePetRequest
	(*CreatePetResponse)(nil),                // 6: backend.proto.pet.v1.CreatePetResponse
	(*BatchCreatePetsRequest)(nil),           // 7: backend.proto.pet.v1.BatchCreatePetsRequest
	(*BatchCreatePetsResponse)(nil),          // 8: backend.proto.pet.v1.BatchCreatePetsResponse
	(*UpdatePetRequest)(nil),                 // 9: backend.proto.pet.v1.UpdatePetRequest
	(*ListPetRequest)(nil),                   // 10: backend.proto.pet.v1.ListPetRequest
	(*ListPetResponse)(nil),                  // 11: backend.proto.pet.v1.ListPetResponse
	(*DeletePetRequest)(nil),                 // 12: backend.proto.pet.v1.DeletePetRequest
	(*SearchPetRequest_Filter)(nil),          // 13: backend.proto.pet.v1.SearchPetRequest.Filter
	(*SearchPetResponse_Pet)(nil),            // 14: backend.proto.pet.v1.SearchPetResponse.Pet
	(*SearchPetResponse_Pet_Client)(nil),     // 15: backend.proto.pet.v1.SearchPetResponse.Pet.Client
	(*IndexPetDocumentRequest_Company)(nil),  // 16: backend.proto.pet.v1.IndexPetDocumentRequest.Company
	(*IndexPetDocumentRequest_Customer)(nil), // 17: backend.proto.pet.v1.IndexPetDocumentRequest.Customer
	(*IndexPetDocumentRequest_Pet)(nil),      // 18: backend.proto.pet.v1.IndexPetDocumentRequest.Pet
	(*IndexPetDocumentResponse_Error)(nil),   // 19: backend.proto.pet.v1.IndexPetDocumentResponse.Error
	(*UpdatePetRequest_UpdatePet)(nil),       // 20: backend.proto.pet.v1.UpdatePetRequest.UpdatePet
	(*ListPetRequest_Filter)(nil),            // 21: backend.proto.pet.v1.ListPetRequest.Filter
	(*Pet)(nil),                              // 22: backend.proto.pet.v1.Pet
	(Pet_PetType)(0),                         // 23: backend.proto.pet.v1.Pet.PetType
	(Pet_PetGender)(0),                       // 24: backend.proto.pet.v1.Pet.PetGender
	(*emptypb.Empty)(nil),                    // 25: google.protobuf.Empty
}
var file_backend_proto_pet_v1_pet_service_proto_depIdxs = []int32{
	13, // 0: backend.proto.pet.v1.SearchPetRequest.pet_filter:type_name -> backend.proto.pet.v1.SearchPetRequest.Filter
	14, // 1: backend.proto.pet.v1.SearchPetResponse.pets:type_name -> backend.proto.pet.v1.SearchPetResponse.Pet
	16, // 2: backend.proto.pet.v1.IndexPetDocumentRequest.company:type_name -> backend.proto.pet.v1.IndexPetDocumentRequest.Company
	17, // 3: backend.proto.pet.v1.IndexPetDocumentRequest.customer:type_name -> backend.proto.pet.v1.IndexPetDocumentRequest.Customer
	18, // 4: backend.proto.pet.v1.IndexPetDocumentRequest.pet:type_name -> backend.proto.pet.v1.IndexPetDocumentRequest.Pet
	19, // 5: backend.proto.pet.v1.IndexPetDocumentResponse.errors:type_name -> backend.proto.pet.v1.IndexPetDocumentResponse.Error
	22, // 6: backend.proto.pet.v1.CreatePetRequest.pet:type_name -> backend.proto.pet.v1.Pet
	5,  // 7: backend.proto.pet.v1.BatchCreatePetsRequest.requests:type_name -> backend.proto.pet.v1.CreatePetRequest
	22, // 8: backend.proto.pet.v1.BatchCreatePetsResponse.pets:type_name -> backend.proto.pet.v1.Pet
	20, // 9: backend.proto.pet.v1.UpdatePetRequest.pet:type_name -> backend.proto.pet.v1.UpdatePetRequest.UpdatePet
	21, // 10: backend.proto.pet.v1.ListPetRequest.filter:type_name -> backend.proto.pet.v1.ListPetRequest.Filter
	22, // 11: backend.proto.pet.v1.ListPetResponse.pets:type_name -> backend.proto.pet.v1.Pet
	15, // 12: backend.proto.pet.v1.SearchPetResponse.Pet.client:type_name -> backend.proto.pet.v1.SearchPetResponse.Pet.Client
	23, // 13: backend.proto.pet.v1.UpdatePetRequest.UpdatePet.pet_type:type_name -> backend.proto.pet.v1.Pet.PetType
	24, // 14: backend.proto.pet.v1.UpdatePetRequest.UpdatePet.pet_gender:type_name -> backend.proto.pet.v1.Pet.PetGender
	1,  // 15: backend.proto.pet.v1.PetService.SearchPet:input_type -> backend.proto.pet.v1.SearchPetRequest
	3,  // 16: backend.proto.pet.v1.PetService.IndexPetDocument:input_type -> backend.proto.pet.v1.IndexPetDocumentRequest
	5,  // 17: backend.proto.pet.v1.PetService.CreatePet:input_type -> backend.proto.pet.v1.CreatePetRequest
	7,  // 18: backend.proto.pet.v1.PetService.BatchCreatePets:input_type -> backend.proto.pet.v1.BatchCreatePetsRequest
	9,  // 19: backend.proto.pet.v1.PetService.UpdatePet:input_type -> backend.proto.pet.v1.UpdatePetRequest
	10, // 20: backend.proto.pet.v1.PetService.ListPet:input_type -> backend.proto.pet.v1.ListPetRequest
	12, // 21: backend.proto.pet.v1.PetService.DeletePet:input_type -> backend.proto.pet.v1.DeletePetRequest
	2,  // 22: backend.proto.pet.v1.PetService.SearchPet:output_type -> backend.proto.pet.v1.SearchPetResponse
	4,  // 23: backend.proto.pet.v1.PetService.IndexPetDocument:output_type -> backend.proto.pet.v1.IndexPetDocumentResponse
	6,  // 24: backend.proto.pet.v1.PetService.CreatePet:output_type -> backend.proto.pet.v1.CreatePetResponse
	8,  // 25: backend.proto.pet.v1.PetService.BatchCreatePets:output_type -> backend.proto.pet.v1.BatchCreatePetsResponse
	25, // 26: backend.proto.pet.v1.PetService.UpdatePet:output_type -> google.protobuf.Empty
	11, // 27: backend.proto.pet.v1.PetService.ListPet:output_type -> backend.proto.pet.v1.ListPetResponse
	25, // 28: backend.proto.pet.v1.PetService.DeletePet:output_type -> google.protobuf.Empty
	22, // [22:29] is the sub-list for method output_type
	15, // [15:22] is the sub-list for method input_type
	15, // [15:15] is the sub-list for extension type_name
	15, // [15:15] is the sub-list for extension extendee
	0,  // [0:15] is the sub-list for field type_name
}

func init() { file_backend_proto_pet_v1_pet_service_proto_init() }
func file_backend_proto_pet_v1_pet_service_proto_init() {
	if File_backend_proto_pet_v1_pet_service_proto != nil {
		return
	}
	file_backend_proto_pet_v1_pet_proto_init()
	file_backend_proto_pet_v1_pet_service_proto_msgTypes[0].OneofWrappers = []any{
		(*SearchPetRequest_Term)(nil),
		(*SearchPetRequest_PetFilter)(nil),
	}
	file_backend_proto_pet_v1_pet_service_proto_msgTypes[2].OneofWrappers = []any{
		(*IndexPetDocumentRequest_Company_)(nil),
		(*IndexPetDocumentRequest_Customer_)(nil),
		(*IndexPetDocumentRequest_Pet_)(nil),
	}
	file_backend_proto_pet_v1_pet_service_proto_msgTypes[9].OneofWrappers = []any{
		(*ListPetRequest_PageToken)(nil),
		(*ListPetRequest_Page)(nil),
	}
	file_backend_proto_pet_v1_pet_service_proto_msgTypes[10].OneofWrappers = []any{
		(*ListPetResponse_Total)(nil),
		(*ListPetResponse_NextPageToken)(nil),
	}
	file_backend_proto_pet_v1_pet_service_proto_msgTypes[19].OneofWrappers = []any{}
	file_backend_proto_pet_v1_pet_service_proto_msgTypes[20].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_pet_v1_pet_service_proto_rawDesc), len(file_backend_proto_pet_v1_pet_service_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   21,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_backend_proto_pet_v1_pet_service_proto_goTypes,
		DependencyIndexes: file_backend_proto_pet_v1_pet_service_proto_depIdxs,
		EnumInfos:         file_backend_proto_pet_v1_pet_service_proto_enumTypes,
		MessageInfos:      file_backend_proto_pet_v1_pet_service_proto_msgTypes,
	}.Build()
	File_backend_proto_pet_v1_pet_service_proto = out.File
	file_backend_proto_pet_v1_pet_service_proto_goTypes = nil
	file_backend_proto_pet_v1_pet_service_proto_depIdxs = nil
}
