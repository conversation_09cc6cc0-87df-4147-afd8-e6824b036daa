syntax = "proto3";

package backend.proto.pet.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/pet/v1;petpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.pet.v1";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";

// Pet 表示宠物信息
message Pet {

  // pet type
  enum PetType {
    // unspecified
    PET_TYPE_UNSPECIFIED = 0;
    // dog
    DOG = 1;
    // cat
    CAT = 2;
    // bird
    BIRD = 3;
    // rabbit
    RABBIT = 4;
    // guinea pig
    GUINEA_PIG = 5;
    // horse
    HORSE = 6;
    // rat
    RAT = 7;
    // mouse
    MOUSE = 8;
    // hamster
    HAMSTER = 9;
    // chinchilla
    CHINCHILLA = 10;
    // other
    OTHER = 11;
  }

  // gender
  enum PetGender {
    // unspecified
    PET_GENDER_UNSPECIFIED = 0;
    // male
    MALE = 1;
    // female
    FEMALE = 2;
    // unknown
    UNKNOWN = 3;
  }

  // pet status 
  enum State {
    // unspecified
    STATE_UNSPECIFIED = 0;
    // active
    ACTIVE = 1;
    // inactive
    INACTIVE = 2;
  }

  // pet life status
  enum LifeState {
    // unspecified
    LIFE_STATE_UNSPECIFIED = 0;
    // active
    LIFE_STATE_ACTIVE = 1;
    // pass away
    LIFE_STATE_DIE = 2;
  }

  // pet id
  int64 id = 1;
  // company id
  int64 company_id = 2;
  // customer id
  int64 customer_id = 3;
  // business id
  int64 business_id = 4;
  // pet name
  string name = 5;
  // pet type
  PetType pet_type = 6;
  // breed
  string breed = 7;
  // gender
  PetGender gender = 8;
  // mixed
  bool mixed = 9;
  // pet state
  State state = 10 [(google.api.field_behavior) = OUTPUT_ONLY];
}