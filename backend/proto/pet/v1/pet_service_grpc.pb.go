// (-- api-linter: core::0131::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0133::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0134::response-message-name=disabled
//     aip.dev/not-precedent: 使用自定义响应消息 --)
// (-- api-linter: core::0132::request-field-types=disabled
//     aip.dev/not-precedent: 使用自定义过滤器类型 --)
// (-- api-linter: core::0158::request-page-token-field=disabled
//     aip.dev/not-precedent: 搜索场景下没有分页的需求, 所以没有必要设置page_token --)
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: 使用自定义分页方式 --)
// (-- api-linter: core::0132::response-unknown-fields=disabled
//     aip.dev/not-precedent: 使用自定义的响应字段结构 --)
// (-- api-linter: core::0132::request-parent-field=disabled
//     aip.dev/not-precedent: 使用int64类型的parent字段表示customer id --)
// (-- api-linter: core::0132::request-parent-behavior=disabled
//     aip.dev/not-precedent: 不需要behavior --)
// (-- api-linter: core::0132::request-parent-reference=disabled
//     aip.dev/not-precedent: 不需要reference，业务不支持 --)

// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: backend/proto/pet/v1/pet_service.proto

package petpb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PetService_SearchPet_FullMethodName        = "/backend.proto.pet.v1.PetService/SearchPet"
	PetService_IndexPetDocument_FullMethodName = "/backend.proto.pet.v1.PetService/IndexPetDocument"
	PetService_CreatePet_FullMethodName        = "/backend.proto.pet.v1.PetService/CreatePet"
	PetService_BatchCreatePets_FullMethodName  = "/backend.proto.pet.v1.PetService/BatchCreatePets"
	PetService_UpdatePet_FullMethodName        = "/backend.proto.pet.v1.PetService/UpdatePet"
	PetService_ListPet_FullMethodName          = "/backend.proto.pet.v1.PetService/ListPet"
	PetService_DeletePet_FullMethodName        = "/backend.proto.pet.v1.PetService/DeletePet"
)

// PetServiceClient is the client API for PetService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// PetService 宠物服务
// 提供宠物相关的操作，包括搜索、索引、创建、更新和列表查询
type PetServiceClient interface {
	// pet document in elasticsearch
	//
	// search pet by term (pet name/customer name)
	SearchPet(ctx context.Context, in *SearchPetRequest, opts ...grpc.CallOption) (*SearchPetResponse, error)
	// 同步客户和宠物数据到Elasticsearch
	// 目前仅用于Airflow调度任务
	IndexPetDocument(ctx context.Context, in *IndexPetDocumentRequest, opts ...grpc.CallOption) (*IndexPetDocumentResponse, error)
	// pet metadata operations
	//
	// create pet in database, this operation not sync to elasticsearch
	CreatePet(ctx context.Context, in *CreatePetRequest, opts ...grpc.CallOption) (*CreatePetResponse, error)
	// batch create pet in database, this operation not sync to elasticsearch
	BatchCreatePets(ctx context.Context, in *BatchCreatePetsRequest, opts ...grpc.CallOption) (*BatchCreatePetsResponse, error)
	// 更新宠物信息
	UpdatePet(ctx context.Context, in *UpdatePetRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 列出宠物，数据源为数据库
	// 不提供单独的get方法，可以通过List单个数据实现
	ListPet(ctx context.Context, in *ListPetRequest, opts ...grpc.CallOption) (*ListPetResponse, error)
	// 删除宠物，数据源为数据库
	DeletePet(ctx context.Context, in *DeletePetRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type petServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewPetServiceClient(cc grpc.ClientConnInterface) PetServiceClient {
	return &petServiceClient{cc}
}

func (c *petServiceClient) SearchPet(ctx context.Context, in *SearchPetRequest, opts ...grpc.CallOption) (*SearchPetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SearchPetResponse)
	err := c.cc.Invoke(ctx, PetService_SearchPet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) IndexPetDocument(ctx context.Context, in *IndexPetDocumentRequest, opts ...grpc.CallOption) (*IndexPetDocumentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(IndexPetDocumentResponse)
	err := c.cc.Invoke(ctx, PetService_IndexPetDocument_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) CreatePet(ctx context.Context, in *CreatePetRequest, opts ...grpc.CallOption) (*CreatePetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePetResponse)
	err := c.cc.Invoke(ctx, PetService_CreatePet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) BatchCreatePets(ctx context.Context, in *BatchCreatePetsRequest, opts ...grpc.CallOption) (*BatchCreatePetsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BatchCreatePetsResponse)
	err := c.cc.Invoke(ctx, PetService_BatchCreatePets_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) UpdatePet(ctx context.Context, in *UpdatePetRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PetService_UpdatePet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) ListPet(ctx context.Context, in *ListPetRequest, opts ...grpc.CallOption) (*ListPetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListPetResponse)
	err := c.cc.Invoke(ctx, PetService_ListPet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *petServiceClient) DeletePet(ctx context.Context, in *DeletePetRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, PetService_DeletePet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PetServiceServer is the server API for PetService service.
// All implementations must embed UnimplementedPetServiceServer
// for forward compatibility.
//
// PetService 宠物服务
// 提供宠物相关的操作，包括搜索、索引、创建、更新和列表查询
type PetServiceServer interface {
	// pet document in elasticsearch
	//
	// search pet by term (pet name/customer name)
	SearchPet(context.Context, *SearchPetRequest) (*SearchPetResponse, error)
	// 同步客户和宠物数据到Elasticsearch
	// 目前仅用于Airflow调度任务
	IndexPetDocument(context.Context, *IndexPetDocumentRequest) (*IndexPetDocumentResponse, error)
	// pet metadata operations
	//
	// create pet in database, this operation not sync to elasticsearch
	CreatePet(context.Context, *CreatePetRequest) (*CreatePetResponse, error)
	// batch create pet in database, this operation not sync to elasticsearch
	BatchCreatePets(context.Context, *BatchCreatePetsRequest) (*BatchCreatePetsResponse, error)
	// 更新宠物信息
	UpdatePet(context.Context, *UpdatePetRequest) (*emptypb.Empty, error)
	// 列出宠物，数据源为数据库
	// 不提供单独的get方法，可以通过List单个数据实现
	ListPet(context.Context, *ListPetRequest) (*ListPetResponse, error)
	// 删除宠物，数据源为数据库
	DeletePet(context.Context, *DeletePetRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedPetServiceServer()
}

// UnimplementedPetServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPetServiceServer struct{}

func (UnimplementedPetServiceServer) SearchPet(context.Context, *SearchPetRequest) (*SearchPetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SearchPet not implemented")
}
func (UnimplementedPetServiceServer) IndexPetDocument(context.Context, *IndexPetDocumentRequest) (*IndexPetDocumentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IndexPetDocument not implemented")
}
func (UnimplementedPetServiceServer) CreatePet(context.Context, *CreatePetRequest) (*CreatePetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePet not implemented")
}
func (UnimplementedPetServiceServer) BatchCreatePets(context.Context, *BatchCreatePetsRequest) (*BatchCreatePetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BatchCreatePets not implemented")
}
func (UnimplementedPetServiceServer) UpdatePet(context.Context, *UpdatePetRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePet not implemented")
}
func (UnimplementedPetServiceServer) ListPet(context.Context, *ListPetRequest) (*ListPetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListPet not implemented")
}
func (UnimplementedPetServiceServer) DeletePet(context.Context, *DeletePetRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePet not implemented")
}
func (UnimplementedPetServiceServer) mustEmbedUnimplementedPetServiceServer() {}
func (UnimplementedPetServiceServer) testEmbeddedByValue()                    {}

// UnsafePetServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PetServiceServer will
// result in compilation errors.
type UnsafePetServiceServer interface {
	mustEmbedUnimplementedPetServiceServer()
}

func RegisterPetServiceServer(s grpc.ServiceRegistrar, srv PetServiceServer) {
	// If the following call pancis, it indicates UnimplementedPetServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PetService_ServiceDesc, srv)
}

func _PetService_SearchPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SearchPetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).SearchPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PetService_SearchPet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).SearchPet(ctx, req.(*SearchPetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_IndexPetDocument_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IndexPetDocumentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).IndexPetDocument(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PetService_IndexPetDocument_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).IndexPetDocument(ctx, req.(*IndexPetDocumentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_CreatePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).CreatePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PetService_CreatePet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).CreatePet(ctx, req.(*CreatePetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_BatchCreatePets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BatchCreatePetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).BatchCreatePets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PetService_BatchCreatePets_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).BatchCreatePets(ctx, req.(*BatchCreatePetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_UpdatePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).UpdatePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PetService_UpdatePet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).UpdatePet(ctx, req.(*UpdatePetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_ListPet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListPetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).ListPet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PetService_ListPet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).ListPet(ctx, req.(*ListPetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PetService_DeletePet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PetServiceServer).DeletePet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PetService_DeletePet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PetServiceServer).DeletePet(ctx, req.(*DeletePetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PetService_ServiceDesc is the grpc.ServiceDesc for PetService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PetService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "backend.proto.pet.v1.PetService",
	HandlerType: (*PetServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SearchPet",
			Handler:    _PetService_SearchPet_Handler,
		},
		{
			MethodName: "IndexPetDocument",
			Handler:    _PetService_IndexPetDocument_Handler,
		},
		{
			MethodName: "CreatePet",
			Handler:    _PetService_CreatePet_Handler,
		},
		{
			MethodName: "BatchCreatePets",
			Handler:    _PetService_BatchCreatePets_Handler,
		},
		{
			MethodName: "UpdatePet",
			Handler:    _PetService_UpdatePet_Handler,
		},
		{
			MethodName: "ListPet",
			Handler:    _PetService_ListPet_Handler,
		},
		{
			MethodName: "DeletePet",
			Handler:    _PetService_DeletePet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend/proto/pet/v1/pet_service.proto",
}
