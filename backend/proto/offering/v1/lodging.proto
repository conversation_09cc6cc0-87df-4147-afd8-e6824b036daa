syntax = "proto3";

package backend.proto.offering.v1;
import "google/protobuf/timestamp.proto";
import "buf/validate/validate.proto";
import "backend/proto/offering/v1/service.proto";
import "backend/proto/offering/v1/common.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// LodgingType 住宿类型信息
message LodgingType {
    // id of the lodging type
  int64 id = 1;
  // name of the lodging type
  string name = 2;
  // description of the lodging type
  string description = 3;
  // images of this lodging type
  repeated string photo_list = 4;
  // max pet number of this lodging type
  int32 max_pet_num = 5;
  // max pet total weight of this lodging type
  int32 max_pet_total_weight = 6;
  // available for all pet size
  bool all_pet_sizes = 7;
  // available pet size (only if is_available_for_all_pet_size is false)
  // moe_pet_size.id list
  repeated int64 pet_size_ids = 8;
  // lodging unit type in this lodging type
  LodgingUnitType lodging_unit_type = 9;
  // whether the lodging type is available for all pet size
  bool pet_size_filter = 10;
  // Sort for the lodging type
  int32 sort = 11;
  // source
  OfferingSource source = 12;
}

// LodgingTypeFilter 住宿单元过滤条件
message LodgingTypeFilter {
    // lodging type id
    repeated int64 lodging_type_ids = 1;
}

// LodgingUnit 住宿单元信息
message LodgingUnit {
    // lodging unit id
    int64 id = 1;
    // lodging unit name
    string name = 2;
    // business id
    int64 business_id = 3;
    // lodging type id
    int64 lodging_type_id = 4;
    // camera id
    int64 camera_id = 5;
    // sort
    int32 sort = 6;
}

// LodgingUnitType 住宿单元类型
enum LodgingUnitType {
  // Unspecified lodging unit type
  LODGING_UNIT_TYPE_UNSPECIFIED = 0;
  // Room/kennel type
  ROOM = 1;
  // Area type
  AREA = 2;
}
