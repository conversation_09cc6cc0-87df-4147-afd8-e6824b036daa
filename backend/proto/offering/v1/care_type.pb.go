// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/offering/v1/care_type.proto

package offeringpb

import (
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	structpb "google.golang.org/protobuf/types/known/structpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 护理类别，包含系统预设和自定义
type CareCategory int32

const (
	// 未知护理类型
	CareCategory_CARE_CATEGORY_UNSPECIFIED CareCategory = 0
	// GROOMING类型
	CareCategory_GROOMING CareCategory = 1
	// BOARDING类型
	CareCategory_BOARDING CareCategory = 2
	// DAYCARE类型
	CareCategory_DAYCARE CareCategory = 3
	// EVALUATION类型
	CareCategory_EVALUATION CareCategory = 4
	// DOG_WALKING类型
	CareCategory_DOG_WALKING CareCategory = 5
	// GROUP_CLASS类型
	CareCategory_GROUP_CLASS CareCategory = 6
	// 自定义类型
	CareCategory_CUSTOM CareCategory = 99
)

// Enum value maps for CareCategory.
var (
	CareCategory_name = map[int32]string{
		0:  "CARE_CATEGORY_UNSPECIFIED",
		1:  "GROOMING",
		2:  "BOARDING",
		3:  "DAYCARE",
		4:  "EVALUATION",
		5:  "DOG_WALKING",
		6:  "GROUP_CLASS",
		99: "CUSTOM",
	}
	CareCategory_value = map[string]int32{
		"CARE_CATEGORY_UNSPECIFIED": 0,
		"GROOMING":                  1,
		"BOARDING":                  2,
		"DAYCARE":                   3,
		"EVALUATION":                4,
		"DOG_WALKING":               5,
		"GROUP_CLASS":               6,
		"CUSTOM":                    99,
	}
)

func (x CareCategory) Enum() *CareCategory {
	p := new(CareCategory)
	*p = x
	return p
}

func (x CareCategory) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CareCategory) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_care_type_proto_enumTypes[0].Descriptor()
}

func (CareCategory) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_care_type_proto_enumTypes[0]
}

func (x CareCategory) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CareCategory.Descriptor instead.
func (CareCategory) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_proto_rawDescGZIP(), []int{0}
}

// Defines the unique keys for all supported attributes in the system.
// This enum is the single source of truth for what attributes are available.
type AttributeKey int32

const (
	// Unspecified attribute key.
	AttributeKey_ATTRIBUTE_KEY_UNSPECIFIED AttributeKey = 0
	// Duration in minutes. for grooming/dog_walking services
	AttributeKey_DURATION AttributeKey = 1
	// Max stay duration in minutes. for daycare services
	AttributeKey_MAX_DURATION AttributeKey = 2
	// A list of prerequisite service IDs. for boarding/daycare/group_class services
	AttributeKey_PREREQUISITE_SERVICE AttributeKey = 3
	// Indicates if staff can be auto-assigned.
	AttributeKey_STAFF_AUTO_ASSIGN AttributeKey = 4
	// Indicates if the service is resettable. for evaluation services
	AttributeKey_RESULT_RESETTABLE AttributeKey = 5
	// Indicates if staff binding is supported.
	AttributeKey_AVAILABLE_STAFF AttributeKey = 6
	// Indicates if lodging type binding is supported.
	AttributeKey_AVAILABLE_LODGING_TYPE AttributeKey = 7
	// Indicates if auto-rollover is supported.
	AttributeKey_AUTO_ROLLOVER AttributeKey = 8
	// Service name in online booking. for evaluation services
	AttributeKey_ONLINE_BOOKING_ALIAS AttributeKey = 9
	// Indicates if the addon requires staff (only for ADD_ON type services)
	AttributeKey_IS_REQUIRED_STAFF AttributeKey = 10
	// Price unit.
	AttributeKey_PRICE_UNIT AttributeKey = 12
	// Default service for single-day services
	AttributeKey_DEFAULT_SERVICE AttributeKey = 13
	// Default service for conditional cross-day services
	AttributeKey_CONDITIONAL_DEFAULT_SERVICE AttributeKey = 14
)

// Enum value maps for AttributeKey.
var (
	AttributeKey_name = map[int32]string{
		0:  "ATTRIBUTE_KEY_UNSPECIFIED",
		1:  "DURATION",
		2:  "MAX_DURATION",
		3:  "PREREQUISITE_SERVICE",
		4:  "STAFF_AUTO_ASSIGN",
		5:  "RESULT_RESETTABLE",
		6:  "AVAILABLE_STAFF",
		7:  "AVAILABLE_LODGING_TYPE",
		8:  "AUTO_ROLLOVER",
		9:  "ONLINE_BOOKING_ALIAS",
		10: "IS_REQUIRED_STAFF",
		12: "PRICE_UNIT",
		13: "DEFAULT_SERVICE",
		14: "CONDITIONAL_DEFAULT_SERVICE",
	}
	AttributeKey_value = map[string]int32{
		"ATTRIBUTE_KEY_UNSPECIFIED":   0,
		"DURATION":                    1,
		"MAX_DURATION":                2,
		"PREREQUISITE_SERVICE":        3,
		"STAFF_AUTO_ASSIGN":           4,
		"RESULT_RESETTABLE":           5,
		"AVAILABLE_STAFF":             6,
		"AVAILABLE_LODGING_TYPE":      7,
		"AUTO_ROLLOVER":               8,
		"ONLINE_BOOKING_ALIAS":        9,
		"IS_REQUIRED_STAFF":           10,
		"PRICE_UNIT":                  12,
		"DEFAULT_SERVICE":             13,
		"CONDITIONAL_DEFAULT_SERVICE": 14,
	}
)

func (x AttributeKey) Enum() *AttributeKey {
	p := new(AttributeKey)
	*p = x
	return p
}

func (x AttributeKey) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AttributeKey) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_care_type_proto_enumTypes[1].Descriptor()
}

func (AttributeKey) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_care_type_proto_enumTypes[1]
}

func (x AttributeKey) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AttributeKey.Descriptor instead.
func (AttributeKey) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_proto_rawDescGZIP(), []int{1}
}

// ValueType defines the data type of an attribute's value, for frontend rendering.
type ValueType int32

const (
	// Unspecified value type.
	ValueType_VALUE_TYPE_UNSPECIFIED ValueType = 0
	// A single-line string.
	ValueType_STRING ValueType = 1
	// A multi-line text.
	ValueType_TEXT ValueType = 2
	// A numerical value (integer or decimal).
	ValueType_NUMBER ValueType = 3
	// A boolean value (true or false).
	ValueType_BOOLEAN ValueType = 4
	// A single selection from a list of options.
	ValueType_SINGLE_SELECT ValueType = 5
	// Multiple selections from a list of options.
	ValueType_MULTI_SELECT ValueType = 6
	// A date value.
	ValueType_DATE ValueType = 7
	// A date and time value.
	ValueType_DATETIME ValueType = 8
	// A JSON object.
	ValueType_JSON ValueType = 9
	// A table configuration that requires special UI components and database tables.
	ValueType_TABLE ValueType = 10
)

// Enum value maps for ValueType.
var (
	ValueType_name = map[int32]string{
		0:  "VALUE_TYPE_UNSPECIFIED",
		1:  "STRING",
		2:  "TEXT",
		3:  "NUMBER",
		4:  "BOOLEAN",
		5:  "SINGLE_SELECT",
		6:  "MULTI_SELECT",
		7:  "DATE",
		8:  "DATETIME",
		9:  "JSON",
		10: "TABLE",
	}
	ValueType_value = map[string]int32{
		"VALUE_TYPE_UNSPECIFIED": 0,
		"STRING":                 1,
		"TEXT":                   2,
		"NUMBER":                 3,
		"BOOLEAN":                4,
		"SINGLE_SELECT":          5,
		"MULTI_SELECT":           6,
		"DATE":                   7,
		"DATETIME":               8,
		"JSON":                   9,
		"TABLE":                  10,
	}
)

func (x ValueType) Enum() *ValueType {
	p := new(ValueType)
	*p = x
	return p
}

func (x ValueType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ValueType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_care_type_proto_enumTypes[2].Descriptor()
}

func (ValueType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_care_type_proto_enumTypes[2]
}

func (x ValueType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ValueType.Descriptor instead.
func (ValueType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_proto_rawDescGZIP(), []int{2}
}

// Defines a Care Type Config.
type CareType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique ID of the care type.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The level of the organization (e.g., "enterprise", "company").
	OrganizationType v1.OrganizationType `protobuf:"varint,2,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization.
	OrganizationId int64 `protobuf:"varint,3,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// The name of the care type.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// The system code of the care type (e.g. "grooming", "daycare", "custom").
	CareCategory CareCategory `protobuf:"varint,5,opt,name=care_category,json=careCategory,proto3,enum=backend.proto.offering.v1.CareCategory" json:"care_category,omitempty"`
	// An optional description.
	Description *string `protobuf:"bytes,6,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// The time the care type was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time the care type was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The time the care type was deleted.
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CareType) Reset() {
	*x = CareType{}
	mi := &file_backend_proto_offering_v1_care_type_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CareType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CareType) ProtoMessage() {}

func (x *CareType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CareType.ProtoReflect.Descriptor instead.
func (*CareType) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_proto_rawDescGZIP(), []int{0}
}

func (x *CareType) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CareType) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *CareType) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *CareType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CareType) GetCareCategory() CareCategory {
	if x != nil {
		return x.CareCategory
	}
	return CareCategory_CARE_CATEGORY_UNSPECIFIED
}

func (x *CareType) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CareType) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CareType) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *CareType) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

// Defines an attribute for a care type.
type CareTypeAttribute struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The unique ID of the attribute.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The ID of the care type this attribute belongs to.
	CareTypeId int64 `protobuf:"varint,2,opt,name=care_type_id,json=careTypeId,proto3" json:"care_type_id,omitempty"`
	// The unique key of the attribute.
	AttributeKey AttributeKey `protobuf:"varint,3,opt,name=attribute_key,json=attributeKey,proto3,enum=backend.proto.offering.v1.AttributeKey" json:"attribute_key,omitempty"`
	// (-- api-linter: core::0122::name-suffix=disabled
	//
	//	aip.dev/not-precedent: field_name 更适合表示字段名. --)
	//
	// The field name of the attribute.
	FieldName string `protobuf:"bytes,4,opt,name=field_name,json=fieldName,proto3" json:"field_name,omitempty"`
	// The display label for the UI (e.g., "Pet Size").
	Label *string `protobuf:"bytes,5,opt,name=label,proto3,oneof" json:"label,omitempty"`
	// The value type of the attribute (e.g., STRING, NUMBER).
	ValueType ValueType `protobuf:"varint,6,opt,name=value_type,json=valueType,proto3,enum=backend.proto.offering.v1.ValueType" json:"value_type,omitempty"`
	// A list of options, used when value_type is ENUM.
	Options *structpb.Struct `protobuf:"bytes,7,opt,name=options,proto3,oneof" json:"options,omitempty"`
	// An optional description.
	Description *string `protobuf:"bytes,8,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// Whether this attribute is required.
	IsRequired bool `protobuf:"varint,9,opt,name=is_required,json=isRequired,proto3" json:"is_required,omitempty"`
	// The default value for this attribute.
	DefaultValue *structpb.Value `protobuf:"bytes,10,opt,name=default_value,json=defaultValue,proto3,oneof" json:"default_value,omitempty"`
	// The time the attribute was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time the attribute was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,12,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The time the attribute was deleted.
	DeleteTime    *timestamppb.Timestamp `protobuf:"bytes,13,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CareTypeAttribute) Reset() {
	*x = CareTypeAttribute{}
	mi := &file_backend_proto_offering_v1_care_type_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CareTypeAttribute) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CareTypeAttribute) ProtoMessage() {}

func (x *CareTypeAttribute) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_care_type_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CareTypeAttribute.ProtoReflect.Descriptor instead.
func (*CareTypeAttribute) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_care_type_proto_rawDescGZIP(), []int{1}
}

func (x *CareTypeAttribute) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CareTypeAttribute) GetCareTypeId() int64 {
	if x != nil {
		return x.CareTypeId
	}
	return 0
}

func (x *CareTypeAttribute) GetAttributeKey() AttributeKey {
	if x != nil {
		return x.AttributeKey
	}
	return AttributeKey_ATTRIBUTE_KEY_UNSPECIFIED
}

func (x *CareTypeAttribute) GetFieldName() string {
	if x != nil {
		return x.FieldName
	}
	return ""
}

func (x *CareTypeAttribute) GetLabel() string {
	if x != nil && x.Label != nil {
		return *x.Label
	}
	return ""
}

func (x *CareTypeAttribute) GetValueType() ValueType {
	if x != nil {
		return x.ValueType
	}
	return ValueType_VALUE_TYPE_UNSPECIFIED
}

func (x *CareTypeAttribute) GetOptions() *structpb.Struct {
	if x != nil {
		return x.Options
	}
	return nil
}

func (x *CareTypeAttribute) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *CareTypeAttribute) GetIsRequired() bool {
	if x != nil {
		return x.IsRequired
	}
	return false
}

func (x *CareTypeAttribute) GetDefaultValue() *structpb.Value {
	if x != nil {
		return x.DefaultValue
	}
	return nil
}

func (x *CareTypeAttribute) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *CareTypeAttribute) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *CareTypeAttribute) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

var File_backend_proto_offering_v1_care_type_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_care_type_proto_rawDesc = "" +
	"\n" +
	")backend/proto/offering/v1/care_type.proto\x12\x19backend.proto.offering.v1\x1a0backend/proto/organization/v1/organization.proto\x1a\x1cgoogle/protobuf/struct.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\x86\x04\n" +
	"\bCareType\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\\\n" +
	"\x11organization_type\x18\x02 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x03 \x01(\x03R\x0eorganizationId\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12L\n" +
	"\rcare_category\x18\x05 \x01(\x0e2'.backend.proto.offering.v1.CareCategoryR\fcareCategory\x12%\n" +
	"\vdescription\x18\x06 \x01(\tH\x00R\vdescription\x88\x01\x01\x12;\n" +
	"\vcreate_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12@\n" +
	"\vdelete_time\x18\t \x01(\v2\x1a.google.protobuf.TimestampH\x01R\n" +
	"deleteTime\x88\x01\x01B\x0e\n" +
	"\f_descriptionB\x0e\n" +
	"\f_delete_time\"\xd8\x05\n" +
	"\x11CareTypeAttribute\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12 \n" +
	"\fcare_type_id\x18\x02 \x01(\x03R\n" +
	"careTypeId\x12L\n" +
	"\rattribute_key\x18\x03 \x01(\x0e2'.backend.proto.offering.v1.AttributeKeyR\fattributeKey\x12\x1d\n" +
	"\n" +
	"field_name\x18\x04 \x01(\tR\tfieldName\x12\x19\n" +
	"\x05label\x18\x05 \x01(\tH\x00R\x05label\x88\x01\x01\x12C\n" +
	"\n" +
	"value_type\x18\x06 \x01(\x0e2$.backend.proto.offering.v1.ValueTypeR\tvalueType\x126\n" +
	"\aoptions\x18\a \x01(\v2\x17.google.protobuf.StructH\x01R\aoptions\x88\x01\x01\x12%\n" +
	"\vdescription\x18\b \x01(\tH\x02R\vdescription\x88\x01\x01\x12\x1f\n" +
	"\vis_required\x18\t \x01(\bR\n" +
	"isRequired\x12@\n" +
	"\rdefault_value\x18\n" +
	" \x01(\v2\x16.google.protobuf.ValueH\x03R\fdefaultValue\x88\x01\x01\x12;\n" +
	"\vcreate_time\x18\v \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\f \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12@\n" +
	"\vdelete_time\x18\r \x01(\v2\x1a.google.protobuf.TimestampH\x04R\n" +
	"deleteTime\x88\x01\x01B\b\n" +
	"\x06_labelB\n" +
	"\n" +
	"\b_optionsB\x0e\n" +
	"\f_descriptionB\x10\n" +
	"\x0e_default_valueB\x0e\n" +
	"\f_delete_time*\x94\x01\n" +
	"\fCareCategory\x12\x1d\n" +
	"\x19CARE_CATEGORY_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bGROOMING\x10\x01\x12\f\n" +
	"\bBOARDING\x10\x02\x12\v\n" +
	"\aDAYCARE\x10\x03\x12\x0e\n" +
	"\n" +
	"EVALUATION\x10\x04\x12\x0f\n" +
	"\vDOG_WALKING\x10\x05\x12\x0f\n" +
	"\vGROUP_CLASS\x10\x06\x12\n" +
	"\n" +
	"\x06CUSTOM\x10c*\xd0\x02\n" +
	"\fAttributeKey\x12\x1d\n" +
	"\x19ATTRIBUTE_KEY_UNSPECIFIED\x10\x00\x12\f\n" +
	"\bDURATION\x10\x01\x12\x10\n" +
	"\fMAX_DURATION\x10\x02\x12\x18\n" +
	"\x14PREREQUISITE_SERVICE\x10\x03\x12\x15\n" +
	"\x11STAFF_AUTO_ASSIGN\x10\x04\x12\x15\n" +
	"\x11RESULT_RESETTABLE\x10\x05\x12\x13\n" +
	"\x0fAVAILABLE_STAFF\x10\x06\x12\x1a\n" +
	"\x16AVAILABLE_LODGING_TYPE\x10\a\x12\x11\n" +
	"\rAUTO_ROLLOVER\x10\b\x12\x18\n" +
	"\x14ONLINE_BOOKING_ALIAS\x10\t\x12\x15\n" +
	"\x11IS_REQUIRED_STAFF\x10\n" +
	"\x12\x0e\n" +
	"\n" +
	"PRICE_UNIT\x10\f\x12\x13\n" +
	"\x0fDEFAULT_SERVICE\x10\r\x12\x1f\n" +
	"\x1bCONDITIONAL_DEFAULT_SERVICE\x10\x0e*\xa8\x01\n" +
	"\tValueType\x12\x1a\n" +
	"\x16VALUE_TYPE_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06STRING\x10\x01\x12\b\n" +
	"\x04TEXT\x10\x02\x12\n" +
	"\n" +
	"\x06NUMBER\x10\x03\x12\v\n" +
	"\aBOOLEAN\x10\x04\x12\x11\n" +
	"\rSINGLE_SELECT\x10\x05\x12\x10\n" +
	"\fMULTI_SELECT\x10\x06\x12\b\n" +
	"\x04DATE\x10\a\x12\f\n" +
	"\bDATETIME\x10\b\x12\b\n" +
	"\x04JSON\x10\t\x12\t\n" +
	"\x05TABLE\x10\n" +
	"Bk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_care_type_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_care_type_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_care_type_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_care_type_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_care_type_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_care_type_proto_rawDesc), len(file_backend_proto_offering_v1_care_type_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_care_type_proto_rawDescData
}

var file_backend_proto_offering_v1_care_type_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_backend_proto_offering_v1_care_type_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_backend_proto_offering_v1_care_type_proto_goTypes = []any{
	(CareCategory)(0),             // 0: backend.proto.offering.v1.CareCategory
	(AttributeKey)(0),             // 1: backend.proto.offering.v1.AttributeKey
	(ValueType)(0),                // 2: backend.proto.offering.v1.ValueType
	(*CareType)(nil),              // 3: backend.proto.offering.v1.CareType
	(*CareTypeAttribute)(nil),     // 4: backend.proto.offering.v1.CareTypeAttribute
	(v1.OrganizationType)(0),      // 5: backend.proto.organization.v1.OrganizationType
	(*timestamppb.Timestamp)(nil), // 6: google.protobuf.Timestamp
	(*structpb.Struct)(nil),       // 7: google.protobuf.Struct
	(*structpb.Value)(nil),        // 8: google.protobuf.Value
}
var file_backend_proto_offering_v1_care_type_proto_depIdxs = []int32{
	5,  // 0: backend.proto.offering.v1.CareType.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	0,  // 1: backend.proto.offering.v1.CareType.care_category:type_name -> backend.proto.offering.v1.CareCategory
	6,  // 2: backend.proto.offering.v1.CareType.create_time:type_name -> google.protobuf.Timestamp
	6,  // 3: backend.proto.offering.v1.CareType.update_time:type_name -> google.protobuf.Timestamp
	6,  // 4: backend.proto.offering.v1.CareType.delete_time:type_name -> google.protobuf.Timestamp
	1,  // 5: backend.proto.offering.v1.CareTypeAttribute.attribute_key:type_name -> backend.proto.offering.v1.AttributeKey
	2,  // 6: backend.proto.offering.v1.CareTypeAttribute.value_type:type_name -> backend.proto.offering.v1.ValueType
	7,  // 7: backend.proto.offering.v1.CareTypeAttribute.options:type_name -> google.protobuf.Struct
	8,  // 8: backend.proto.offering.v1.CareTypeAttribute.default_value:type_name -> google.protobuf.Value
	6,  // 9: backend.proto.offering.v1.CareTypeAttribute.create_time:type_name -> google.protobuf.Timestamp
	6,  // 10: backend.proto.offering.v1.CareTypeAttribute.update_time:type_name -> google.protobuf.Timestamp
	6,  // 11: backend.proto.offering.v1.CareTypeAttribute.delete_time:type_name -> google.protobuf.Timestamp
	12, // [12:12] is the sub-list for method output_type
	12, // [12:12] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_care_type_proto_init() }
func file_backend_proto_offering_v1_care_type_proto_init() {
	if File_backend_proto_offering_v1_care_type_proto != nil {
		return
	}
	file_backend_proto_offering_v1_care_type_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_offering_v1_care_type_proto_msgTypes[1].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_care_type_proto_rawDesc), len(file_backend_proto_offering_v1_care_type_proto_rawDesc)),
			NumEnums:      3,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_care_type_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_care_type_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_v1_care_type_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_v1_care_type_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_care_type_proto = out.File
	file_backend_proto_offering_v1_care_type_proto_goTypes = nil
	file_backend_proto_offering_v1_care_type_proto_depIdxs = nil
}
