syntax = "proto3";

package backend.proto.offering.v1;

import "google/protobuf/timestamp.proto";
import "buf/validate/validate.proto";
import "backend/proto/offering/v1/lodging.proto";

option go_package="github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// LodgingService 住宿服务
service LodgingService {
  // GetLodgingTypeList 获取住宿类型列表
  // (-- api-linter: core::0131::response-message-name=disabled
  //     aip.dev/not-precedent: GetLodgingTypeListResponse is appropriate for this use case --)
  // (-- api-linter: core::0136::prepositions=disabled
  //     aip.dev/not-precedent: company_id is clear and descriptive --)
  rpc GetLodgingTypeList(GetLodgingTypeListRequest) returns (GetLodgingTypeListResponse);

  // ListLodgingUnit 获取住宿单元列表
  rpc ListLodgingUnit(ListLodgingUnitRequest) returns (ListLodgingUnitResponse);
}

// GetLodgingTypeListRequest 获取住宿类型列表请求
message GetLodgingTypeListRequest {
  // 公司ID
  int64 company_id = 1 [(buf.validate.field).int64.gt = 0];
  // 过滤条件
  LodgingTypeFilter filter = 2;
}

// GetLodgingTypeListResponse 获取住宿类型列表响应
message GetLodgingTypeListResponse {
  // 住宿类型列表
  repeated LodgingType lodging_types = 1;
}

  // ListLodgingUnitRequest 获取住宿单元列表请求
  // (-- api-linter: core::0132::request-parent-required=disabled
  //     aip.dev/not-precedent: parent field is not needed for this use case --)
  // (-- api-linter: core::0158::request-page-size-field=disabled
  //     aip.dev/not-precedent: page_size field is not needed for this use case --)
  // (-- api-linter: core::0158::request-page-token-field=disabled
  //     aip.dev/not-precedent: page_token field is not needed for this use case --)
message ListLodgingUnitRequest {
  // 商家ID
  int64 business_id = 1 [(buf.validate.field).int64.gt = 0];
}

// ListLodgingUnitResponse 获取住宿单元列表响应
// (-- api-linter: core::0158::response-next-page-token-field=disabled
//     aip.dev/not-precedent: next_page_token field is not needed for this use case --)
message ListLodgingUnitResponse {
  // 住宿单元列表
  // (-- api-linter: core::0132::response-unknown-fields=disabled
  //     aip.dev/not-precedent: lodging_units field is necessary for this use case --)
  repeated LodgingUnit lodging_units = 1;
}
