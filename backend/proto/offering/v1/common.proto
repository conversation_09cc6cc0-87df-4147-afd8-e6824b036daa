syntax = "proto3";

package backend.proto.offering.v1;

option go_package="github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb"; // 这里的package名不要改
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";

// 分页信息
message PaginationRef {
  // 偏移量，默认0
  int32 offset = 1;
  // 每页数量，默认200
  int32 limit = 2;
}

// 数据来源
enum OfferingSource {
  // Unspecified.
  OFFERING_SOURCE_UNSPECIFIED = 0;
  // MoeGo platform.
  MOEGO = 1;
  // Enterprise Hub.
  ENTERPRISE = 2;
}