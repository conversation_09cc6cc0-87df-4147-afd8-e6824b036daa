syntax = "proto3";

package backend.proto.offering.v1;

import "backend/proto/offering/v1/common.proto";
import "buf/validate/validate.proto";
import "backend/proto/organization/v1/organization.proto";
import "google/protobuf/timestamp.proto";
import "backend/proto/offering/v1/service.proto";
import "google/type/money.proto";

option go_package = "github.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpb";
option java_multiple_files = true;
option java_package = "com.moego.backend.proto.offering.v1";
option java_outer_classname = "AddOnOuterClass";

// Defines the structure for an add on, which acts as a blueprint for creating specific add on instances.
message AddOn {
  // Primary key ID of the add on
  int64 id = 1;

  // The type of the organization (e.g., "enterprise", "company").
  backend.proto.organization.v1.OrganizationType organization_type = 2;

  // The ID of the organization.
  int64 organization_id = 3;

  // The flag of required staff.
  bool is_required_staff = 4;

  // The ID of the category this add on.
  optional int64 category_id = 5;

  // Name of the add on, unique within the same company
  string name = 6;

  // Optional description of the add on
  optional string description = 7;

  // A color code associated with the add on for UI purposes.
  string color_code = 8;

  // The sorting order of the add on.
  optional int64 sort = 9;

  // A list of image URLs for the add on.
  repeated string images = 10;

  // The offering source of the add on.
  OfferingSource source = 11;

  // The status of the add on.
  Status status = 12;

  // Is deleted
  bool is_deleted = 13;

  // The duration minutes of the add on.
  int32 duration = 14;

  // The price of the service.
  google.type.Money price = 15;

  // The related tax id of the service.
  int64 tax_id = 16;

  // The billing price unit of the service. default is PER_SESSION
  // PriceUnit price_unit = 17;
  // The timestamp when the add on was created.
  google.protobuf.Timestamp create_time = 20;
  
  // The timestamp when the add on was last updated.
  google.protobuf.Timestamp update_time = 21;
  
  // The timestamp when the add on was deleted.
  optional google.protobuf.Timestamp delete_time = 22;

  // The available business scope for this add on.
  AvailableBusiness available_business = 23;

  // The available service scope for this add on.
  ApplicableService applicable_service = 24;

  // The pet type and breed scope for this add on.
  AvailablePetTypeBreed available_type_breed = 25;

  // The pet size scope for this add on.
  AvailablePetSize available_pet_size = 26;

  // The pet coat type scope for this add on.
  AvailableCoatType available_coat_type = 27;

  // Available pet weight configuration
  optional AvailablePetWeight available_pet_weight = 28;

  // Business override configuration (price, duration, tax_id overrides for specific businesses)
  repeated BusinessOverride business_overrides = 29;

  // (-- api-linter: core::0216::synonyms=disabled
  //     aip.dev/not-precedent: 保持 status 命名设计. --)
  // The status of the add on.
  enum Status {
    // Unspecified.
    STATUS_UNSPECIFIED = 0;
    // Active.
    ACTIVE = 1;
    // Inactive.
    INACTIVE = 2;
  }
}

// Applicable service for add on
message ApplicableService {
  // Whether the add on is applicable to all services, including new services
  bool is_all = 1;

  // Specific care type and service ids
  repeated SpecificService specific_services = 2 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 100
  }];
}

// Specific service for add on
message SpecificService {
  // The ID of the care type
  optional int64 care_type_id = 1 [(buf.validate.field).int64.gt = 0];

  // Is all services under the care type
  bool is_all_services = 2;

  // Specific service ids
  repeated int64 service_ids = 3 [(buf.validate.field).repeated = {
    min_items: 0,
    max_items: 1000,
    items: {
      int64: {gt : 0}
    }
  }];
}

// Defines the structure for a add on category, used to organize add ons.
message AddOnCategory {
  // Primary key ID of the add on category.
  int64 id = 1;

  // The type of the organization (e.g., company, enterprise).
  backend.proto.organization.v1.OrganizationType organization_type = 2;

  // The ID of the organization this category belongs to.
  int64 organization_id = 3;

  // The name of the add on category, unique within the same organization.
  string name = 4;

  // The sorting order of the add on category.
  int64 sort = 5;

  // The timestamp when the category was created.
  google.protobuf.Timestamp create_time = 6;

  // The timestamp when the category was last updated.
  google.protobuf.Timestamp update_time = 7;

  // The deleted time of the add on category.
  optional google.protobuf.Timestamp delete_time = 8;

  // The timestamp when the category was soft-deleted. Null if not deleted.
  bool is_deleted = 10;
}