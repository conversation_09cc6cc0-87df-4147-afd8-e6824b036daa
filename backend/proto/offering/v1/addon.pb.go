// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/offering/v1/addon.proto

package offeringpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	v1 "github.com/MoeGolibrary/moego/backend/proto/organization/v1"
	money "google.golang.org/genproto/googleapis/type/money"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// (-- api-linter: core::0216::synonyms=disabled
//
//	aip.dev/not-precedent: 保持 status 命名设计. --)
//
// The status of the add on.
type AddOn_Status int32

const (
	// Unspecified.
	AddOn_STATUS_UNSPECIFIED AddOn_Status = 0
	// Active.
	AddOn_ACTIVE AddOn_Status = 1
	// Inactive.
	AddOn_INACTIVE AddOn_Status = 2
)

// Enum value maps for AddOn_Status.
var (
	AddOn_Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "ACTIVE",
		2: "INACTIVE",
	}
	AddOn_Status_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"ACTIVE":             1,
		"INACTIVE":           2,
	}
)

func (x AddOn_Status) Enum() *AddOn_Status {
	p := new(AddOn_Status)
	*p = x
	return p
}

func (x AddOn_Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (AddOn_Status) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_addon_proto_enumTypes[0].Descriptor()
}

func (AddOn_Status) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_addon_proto_enumTypes[0]
}

func (x AddOn_Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use AddOn_Status.Descriptor instead.
func (AddOn_Status) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_proto_rawDescGZIP(), []int{0, 0}
}

// Defines the structure for an add on, which acts as a blueprint for creating specific add on instances.
type AddOn struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Primary key ID of the add on
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The type of the organization (e.g., "enterprise", "company").
	OrganizationType v1.OrganizationType `protobuf:"varint,2,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization.
	OrganizationId int64 `protobuf:"varint,3,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// The flag of required staff.
	IsRequiredStaff bool `protobuf:"varint,4,opt,name=is_required_staff,json=isRequiredStaff,proto3" json:"is_required_staff,omitempty"`
	// The ID of the category this add on.
	CategoryId *int64 `protobuf:"varint,5,opt,name=category_id,json=categoryId,proto3,oneof" json:"category_id,omitempty"`
	// Name of the add on, unique within the same company
	Name string `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`
	// Optional description of the add on
	Description *string `protobuf:"bytes,7,opt,name=description,proto3,oneof" json:"description,omitempty"`
	// A color code associated with the add on for UI purposes.
	ColorCode string `protobuf:"bytes,8,opt,name=color_code,json=colorCode,proto3" json:"color_code,omitempty"`
	// The sorting order of the add on.
	Sort *int64 `protobuf:"varint,9,opt,name=sort,proto3,oneof" json:"sort,omitempty"`
	// A list of image URLs for the add on.
	Images []string `protobuf:"bytes,10,rep,name=images,proto3" json:"images,omitempty"`
	// The offering source of the add on.
	Source OfferingSource `protobuf:"varint,11,opt,name=source,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"source,omitempty"`
	// The status of the add on.
	Status AddOn_Status `protobuf:"varint,12,opt,name=status,proto3,enum=backend.proto.offering.v1.AddOn_Status" json:"status,omitempty"`
	// Is deleted
	IsDeleted bool `protobuf:"varint,13,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	// The duration minutes of the add on.
	Duration int32 `protobuf:"varint,14,opt,name=duration,proto3" json:"duration,omitempty"`
	// The price of the service.
	Price *money.Money `protobuf:"bytes,15,opt,name=price,proto3" json:"price,omitempty"`
	// The related tax id of the service.
	TaxId int64 `protobuf:"varint,16,opt,name=tax_id,json=taxId,proto3" json:"tax_id,omitempty"`
	// The billing price unit of the service. default is PER_SESSION
	// PriceUnit price_unit = 17;
	// The timestamp when the add on was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,20,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The timestamp when the add on was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,21,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The timestamp when the add on was deleted.
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,22,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`
	// The available business scope for this add on.
	AvailableBusiness *AvailableBusiness `protobuf:"bytes,23,opt,name=available_business,json=availableBusiness,proto3" json:"available_business,omitempty"`
	// The available service scope for this add on.
	ApplicableService *ApplicableService `protobuf:"bytes,24,opt,name=applicable_service,json=applicableService,proto3" json:"applicable_service,omitempty"`
	// The pet type and breed scope for this add on.
	AvailableTypeBreed *AvailablePetTypeBreed `protobuf:"bytes,25,opt,name=available_type_breed,json=availableTypeBreed,proto3" json:"available_type_breed,omitempty"`
	// The pet size scope for this add on.
	AvailablePetSize *AvailablePetSize `protobuf:"bytes,26,opt,name=available_pet_size,json=availablePetSize,proto3" json:"available_pet_size,omitempty"`
	// The pet coat type scope for this add on.
	AvailableCoatType *AvailableCoatType `protobuf:"bytes,27,opt,name=available_coat_type,json=availableCoatType,proto3" json:"available_coat_type,omitempty"`
	// Available pet weight configuration
	AvailablePetWeight *AvailablePetWeight `protobuf:"bytes,28,opt,name=available_pet_weight,json=availablePetWeight,proto3,oneof" json:"available_pet_weight,omitempty"`
	// Business override configuration (price, duration, tax_id overrides for specific businesses)
	BusinessOverrides []*BusinessOverride `protobuf:"bytes,29,rep,name=business_overrides,json=businessOverrides,proto3" json:"business_overrides,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *AddOn) Reset() {
	*x = AddOn{}
	mi := &file_backend_proto_offering_v1_addon_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddOn) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOn) ProtoMessage() {}

func (x *AddOn) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOn.ProtoReflect.Descriptor instead.
func (*AddOn) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_proto_rawDescGZIP(), []int{0}
}

func (x *AddOn) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AddOn) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *AddOn) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *AddOn) GetIsRequiredStaff() bool {
	if x != nil {
		return x.IsRequiredStaff
	}
	return false
}

func (x *AddOn) GetCategoryId() int64 {
	if x != nil && x.CategoryId != nil {
		return *x.CategoryId
	}
	return 0
}

func (x *AddOn) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddOn) GetDescription() string {
	if x != nil && x.Description != nil {
		return *x.Description
	}
	return ""
}

func (x *AddOn) GetColorCode() string {
	if x != nil {
		return x.ColorCode
	}
	return ""
}

func (x *AddOn) GetSort() int64 {
	if x != nil && x.Sort != nil {
		return *x.Sort
	}
	return 0
}

func (x *AddOn) GetImages() []string {
	if x != nil {
		return x.Images
	}
	return nil
}

func (x *AddOn) GetSource() OfferingSource {
	if x != nil {
		return x.Source
	}
	return OfferingSource_OFFERING_SOURCE_UNSPECIFIED
}

func (x *AddOn) GetStatus() AddOn_Status {
	if x != nil {
		return x.Status
	}
	return AddOn_STATUS_UNSPECIFIED
}

func (x *AddOn) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

func (x *AddOn) GetDuration() int32 {
	if x != nil {
		return x.Duration
	}
	return 0
}

func (x *AddOn) GetPrice() *money.Money {
	if x != nil {
		return x.Price
	}
	return nil
}

func (x *AddOn) GetTaxId() int64 {
	if x != nil {
		return x.TaxId
	}
	return 0
}

func (x *AddOn) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AddOn) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *AddOn) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *AddOn) GetAvailableBusiness() *AvailableBusiness {
	if x != nil {
		return x.AvailableBusiness
	}
	return nil
}

func (x *AddOn) GetApplicableService() *ApplicableService {
	if x != nil {
		return x.ApplicableService
	}
	return nil
}

func (x *AddOn) GetAvailableTypeBreed() *AvailablePetTypeBreed {
	if x != nil {
		return x.AvailableTypeBreed
	}
	return nil
}

func (x *AddOn) GetAvailablePetSize() *AvailablePetSize {
	if x != nil {
		return x.AvailablePetSize
	}
	return nil
}

func (x *AddOn) GetAvailableCoatType() *AvailableCoatType {
	if x != nil {
		return x.AvailableCoatType
	}
	return nil
}

func (x *AddOn) GetAvailablePetWeight() *AvailablePetWeight {
	if x != nil {
		return x.AvailablePetWeight
	}
	return nil
}

func (x *AddOn) GetBusinessOverrides() []*BusinessOverride {
	if x != nil {
		return x.BusinessOverrides
	}
	return nil
}

// Applicable service for add on
type ApplicableService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Whether the add on is applicable to all services, including new services
	IsAll bool `protobuf:"varint,1,opt,name=is_all,json=isAll,proto3" json:"is_all,omitempty"`
	// Specific care type and service ids
	SpecificServices []*SpecificService `protobuf:"bytes,2,rep,name=specific_services,json=specificServices,proto3" json:"specific_services,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ApplicableService) Reset() {
	*x = ApplicableService{}
	mi := &file_backend_proto_offering_v1_addon_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplicableService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplicableService) ProtoMessage() {}

func (x *ApplicableService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplicableService.ProtoReflect.Descriptor instead.
func (*ApplicableService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_proto_rawDescGZIP(), []int{1}
}

func (x *ApplicableService) GetIsAll() bool {
	if x != nil {
		return x.IsAll
	}
	return false
}

func (x *ApplicableService) GetSpecificServices() []*SpecificService {
	if x != nil {
		return x.SpecificServices
	}
	return nil
}

// Specific service for add on
type SpecificService struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The ID of the care type
	CareTypeId *int64 `protobuf:"varint,1,opt,name=care_type_id,json=careTypeId,proto3,oneof" json:"care_type_id,omitempty"`
	// Is all services under the care type
	IsAllServices bool `protobuf:"varint,2,opt,name=is_all_services,json=isAllServices,proto3" json:"is_all_services,omitempty"`
	// Specific service ids
	ServiceIds    []int64 `protobuf:"varint,3,rep,packed,name=service_ids,json=serviceIds,proto3" json:"service_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SpecificService) Reset() {
	*x = SpecificService{}
	mi := &file_backend_proto_offering_v1_addon_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpecificService) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecificService) ProtoMessage() {}

func (x *SpecificService) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecificService.ProtoReflect.Descriptor instead.
func (*SpecificService) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_proto_rawDescGZIP(), []int{2}
}

func (x *SpecificService) GetCareTypeId() int64 {
	if x != nil && x.CareTypeId != nil {
		return *x.CareTypeId
	}
	return 0
}

func (x *SpecificService) GetIsAllServices() bool {
	if x != nil {
		return x.IsAllServices
	}
	return false
}

func (x *SpecificService) GetServiceIds() []int64 {
	if x != nil {
		return x.ServiceIds
	}
	return nil
}

// Defines the structure for a add on category, used to organize add ons.
type AddOnCategory struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Primary key ID of the add on category.
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// The type of the organization (e.g., company, enterprise).
	OrganizationType v1.OrganizationType `protobuf:"varint,2,opt,name=organization_type,json=organizationType,proto3,enum=backend.proto.organization.v1.OrganizationType" json:"organization_type,omitempty"`
	// The ID of the organization this category belongs to.
	OrganizationId int64 `protobuf:"varint,3,opt,name=organization_id,json=organizationId,proto3" json:"organization_id,omitempty"`
	// The name of the add on category, unique within the same organization.
	Name string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	// The sorting order of the add on category.
	Sort int64 `protobuf:"varint,5,opt,name=sort,proto3" json:"sort,omitempty"`
	// The timestamp when the category was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The timestamp when the category was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,7,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	// The deleted time of the add on category.
	DeleteTime *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=delete_time,json=deleteTime,proto3,oneof" json:"delete_time,omitempty"`
	// The timestamp when the category was soft-deleted. Null if not deleted.
	IsDeleted     bool `protobuf:"varint,10,opt,name=is_deleted,json=isDeleted,proto3" json:"is_deleted,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddOnCategory) Reset() {
	*x = AddOnCategory{}
	mi := &file_backend_proto_offering_v1_addon_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddOnCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddOnCategory) ProtoMessage() {}

func (x *AddOnCategory) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_addon_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddOnCategory.ProtoReflect.Descriptor instead.
func (*AddOnCategory) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_addon_proto_rawDescGZIP(), []int{3}
}

func (x *AddOnCategory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *AddOnCategory) GetOrganizationType() v1.OrganizationType {
	if x != nil {
		return x.OrganizationType
	}
	return v1.OrganizationType(0)
}

func (x *AddOnCategory) GetOrganizationId() int64 {
	if x != nil {
		return x.OrganizationId
	}
	return 0
}

func (x *AddOnCategory) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *AddOnCategory) GetSort() int64 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *AddOnCategory) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *AddOnCategory) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

func (x *AddOnCategory) GetDeleteTime() *timestamppb.Timestamp {
	if x != nil {
		return x.DeleteTime
	}
	return nil
}

func (x *AddOnCategory) GetIsDeleted() bool {
	if x != nil {
		return x.IsDeleted
	}
	return false
}

var File_backend_proto_offering_v1_addon_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_addon_proto_rawDesc = "" +
	"\n" +
	"%backend/proto/offering/v1/addon.proto\x12\x19backend.proto.offering.v1\x1a&backend/proto/offering/v1/common.proto\x1a\x1bbuf/validate/validate.proto\x1a0backend/proto/organization/v1/organization.proto\x1a\x1fgoogle/protobuf/timestamp.proto\x1a'backend/proto/offering/v1/service.proto\x1a\x17google/type/money.proto\"\xde\f\n" +
	"\x05AddOn\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\\\n" +
	"\x11organization_type\x18\x02 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x03 \x01(\x03R\x0eorganizationId\x12*\n" +
	"\x11is_required_staff\x18\x04 \x01(\bR\x0fisRequiredStaff\x12$\n" +
	"\vcategory_id\x18\x05 \x01(\x03H\x00R\n" +
	"categoryId\x88\x01\x01\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12%\n" +
	"\vdescription\x18\a \x01(\tH\x01R\vdescription\x88\x01\x01\x12\x1d\n" +
	"\n" +
	"color_code\x18\b \x01(\tR\tcolorCode\x12\x17\n" +
	"\x04sort\x18\t \x01(\x03H\x02R\x04sort\x88\x01\x01\x12\x16\n" +
	"\x06images\x18\n" +
	" \x03(\tR\x06images\x12A\n" +
	"\x06source\x18\v \x01(\x0e2).backend.proto.offering.v1.OfferingSourceR\x06source\x12?\n" +
	"\x06status\x18\f \x01(\x0e2'.backend.proto.offering.v1.AddOn.StatusR\x06status\x12\x1d\n" +
	"\n" +
	"is_deleted\x18\r \x01(\bR\tisDeleted\x12\x1a\n" +
	"\bduration\x18\x0e \x01(\x05R\bduration\x12(\n" +
	"\x05price\x18\x0f \x01(\v2\x12.google.type.MoneyR\x05price\x12\x15\n" +
	"\x06tax_id\x18\x10 \x01(\x03R\x05taxId\x12;\n" +
	"\vcreate_time\x18\x14 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\x15 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12@\n" +
	"\vdelete_time\x18\x16 \x01(\v2\x1a.google.protobuf.TimestampH\x03R\n" +
	"deleteTime\x88\x01\x01\x12[\n" +
	"\x12available_business\x18\x17 \x01(\v2,.backend.proto.offering.v1.AvailableBusinessR\x11availableBusiness\x12[\n" +
	"\x12applicable_service\x18\x18 \x01(\v2,.backend.proto.offering.v1.ApplicableServiceR\x11applicableService\x12b\n" +
	"\x14available_type_breed\x18\x19 \x01(\v20.backend.proto.offering.v1.AvailablePetTypeBreedR\x12availableTypeBreed\x12Y\n" +
	"\x12available_pet_size\x18\x1a \x01(\v2+.backend.proto.offering.v1.AvailablePetSizeR\x10availablePetSize\x12\\\n" +
	"\x13available_coat_type\x18\x1b \x01(\v2,.backend.proto.offering.v1.AvailableCoatTypeR\x11availableCoatType\x12d\n" +
	"\x14available_pet_weight\x18\x1c \x01(\v2-.backend.proto.offering.v1.AvailablePetWeightH\x04R\x12availablePetWeight\x88\x01\x01\x12Z\n" +
	"\x12business_overrides\x18\x1d \x03(\v2+.backend.proto.offering.v1.BusinessOverrideR\x11businessOverrides\":\n" +
	"\x06Status\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\n" +
	"\n" +
	"\x06ACTIVE\x10\x01\x12\f\n" +
	"\bINACTIVE\x10\x02B\x0e\n" +
	"\f_category_idB\x0e\n" +
	"\f_descriptionB\a\n" +
	"\x05_sortB\x0e\n" +
	"\f_delete_timeB\x17\n" +
	"\x15_available_pet_weight\"\x8f\x01\n" +
	"\x11ApplicableService\x12\x15\n" +
	"\x06is_all\x18\x01 \x01(\bR\x05isAll\x12c\n" +
	"\x11specific_services\x18\x02 \x03(\v2*.backend.proto.offering.v1.SpecificServiceB\n" +
	"\xbaH\a\x92\x01\x04\b\x00\x10dR\x10specificServices\"\xae\x01\n" +
	"\x0fSpecificService\x12.\n" +
	"\fcare_type_id\x18\x01 \x01(\x03B\a\xbaH\x04\"\x02 \x00H\x00R\n" +
	"careTypeId\x88\x01\x01\x12&\n" +
	"\x0fis_all_services\x18\x02 \x01(\bR\risAllServices\x122\n" +
	"\vservice_ids\x18\x03 \x03(\x03B\x11\xbaH\x0e\x92\x01\v\b\x00\x10\xe8\a\"\x04\"\x02 \x00R\n" +
	"serviceIdsB\x0f\n" +
	"\r_care_type_id\"\xb9\x03\n" +
	"\rAddOnCategory\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\\\n" +
	"\x11organization_type\x18\x02 \x01(\x0e2/.backend.proto.organization.v1.OrganizationTypeR\x10organizationType\x12'\n" +
	"\x0forganization_id\x18\x03 \x01(\x03R\x0eorganizationId\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x12\n" +
	"\x04sort\x18\x05 \x01(\x03R\x04sort\x12;\n" +
	"\vcreate_time\x18\x06 \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"createTime\x12;\n" +
	"\vupdate_time\x18\a \x01(\v2\x1a.google.protobuf.TimestampR\n" +
	"updateTime\x12@\n" +
	"\vdelete_time\x18\b \x01(\v2\x1a.google.protobuf.TimestampH\x00R\n" +
	"deleteTime\x88\x01\x01\x12\x1d\n" +
	"\n" +
	"is_deleted\x18\n" +
	" \x01(\bR\tisDeletedB\x0e\n" +
	"\f_delete_timeB|\n" +
	"#com.moego.backend.proto.offering.v1B\x0fAddOnOuterClassP\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_addon_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_addon_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_addon_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_addon_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_addon_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_addon_proto_rawDesc), len(file_backend_proto_offering_v1_addon_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_addon_proto_rawDescData
}

var file_backend_proto_offering_v1_addon_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_offering_v1_addon_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_backend_proto_offering_v1_addon_proto_goTypes = []any{
	(AddOn_Status)(0),             // 0: backend.proto.offering.v1.AddOn.Status
	(*AddOn)(nil),                 // 1: backend.proto.offering.v1.AddOn
	(*ApplicableService)(nil),     // 2: backend.proto.offering.v1.ApplicableService
	(*SpecificService)(nil),       // 3: backend.proto.offering.v1.SpecificService
	(*AddOnCategory)(nil),         // 4: backend.proto.offering.v1.AddOnCategory
	(v1.OrganizationType)(0),      // 5: backend.proto.organization.v1.OrganizationType
	(OfferingSource)(0),           // 6: backend.proto.offering.v1.OfferingSource
	(*money.Money)(nil),           // 7: google.type.Money
	(*timestamppb.Timestamp)(nil), // 8: google.protobuf.Timestamp
	(*AvailableBusiness)(nil),     // 9: backend.proto.offering.v1.AvailableBusiness
	(*AvailablePetTypeBreed)(nil), // 10: backend.proto.offering.v1.AvailablePetTypeBreed
	(*AvailablePetSize)(nil),      // 11: backend.proto.offering.v1.AvailablePetSize
	(*AvailableCoatType)(nil),     // 12: backend.proto.offering.v1.AvailableCoatType
	(*AvailablePetWeight)(nil),    // 13: backend.proto.offering.v1.AvailablePetWeight
	(*BusinessOverride)(nil),      // 14: backend.proto.offering.v1.BusinessOverride
}
var file_backend_proto_offering_v1_addon_proto_depIdxs = []int32{
	5,  // 0: backend.proto.offering.v1.AddOn.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	6,  // 1: backend.proto.offering.v1.AddOn.source:type_name -> backend.proto.offering.v1.OfferingSource
	0,  // 2: backend.proto.offering.v1.AddOn.status:type_name -> backend.proto.offering.v1.AddOn.Status
	7,  // 3: backend.proto.offering.v1.AddOn.price:type_name -> google.type.Money
	8,  // 4: backend.proto.offering.v1.AddOn.create_time:type_name -> google.protobuf.Timestamp
	8,  // 5: backend.proto.offering.v1.AddOn.update_time:type_name -> google.protobuf.Timestamp
	8,  // 6: backend.proto.offering.v1.AddOn.delete_time:type_name -> google.protobuf.Timestamp
	9,  // 7: backend.proto.offering.v1.AddOn.available_business:type_name -> backend.proto.offering.v1.AvailableBusiness
	2,  // 8: backend.proto.offering.v1.AddOn.applicable_service:type_name -> backend.proto.offering.v1.ApplicableService
	10, // 9: backend.proto.offering.v1.AddOn.available_type_breed:type_name -> backend.proto.offering.v1.AvailablePetTypeBreed
	11, // 10: backend.proto.offering.v1.AddOn.available_pet_size:type_name -> backend.proto.offering.v1.AvailablePetSize
	12, // 11: backend.proto.offering.v1.AddOn.available_coat_type:type_name -> backend.proto.offering.v1.AvailableCoatType
	13, // 12: backend.proto.offering.v1.AddOn.available_pet_weight:type_name -> backend.proto.offering.v1.AvailablePetWeight
	14, // 13: backend.proto.offering.v1.AddOn.business_overrides:type_name -> backend.proto.offering.v1.BusinessOverride
	3,  // 14: backend.proto.offering.v1.ApplicableService.specific_services:type_name -> backend.proto.offering.v1.SpecificService
	5,  // 15: backend.proto.offering.v1.AddOnCategory.organization_type:type_name -> backend.proto.organization.v1.OrganizationType
	8,  // 16: backend.proto.offering.v1.AddOnCategory.create_time:type_name -> google.protobuf.Timestamp
	8,  // 17: backend.proto.offering.v1.AddOnCategory.update_time:type_name -> google.protobuf.Timestamp
	8,  // 18: backend.proto.offering.v1.AddOnCategory.delete_time:type_name -> google.protobuf.Timestamp
	19, // [19:19] is the sub-list for method output_type
	19, // [19:19] is the sub-list for method input_type
	19, // [19:19] is the sub-list for extension type_name
	19, // [19:19] is the sub-list for extension extendee
	0,  // [0:19] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_addon_proto_init() }
func file_backend_proto_offering_v1_addon_proto_init() {
	if File_backend_proto_offering_v1_addon_proto != nil {
		return
	}
	file_backend_proto_offering_v1_common_proto_init()
	file_backend_proto_offering_v1_service_proto_init()
	file_backend_proto_offering_v1_addon_proto_msgTypes[0].OneofWrappers = []any{}
	file_backend_proto_offering_v1_addon_proto_msgTypes[2].OneofWrappers = []any{}
	file_backend_proto_offering_v1_addon_proto_msgTypes[3].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_addon_proto_rawDesc), len(file_backend_proto_offering_v1_addon_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_addon_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_addon_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_v1_addon_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_v1_addon_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_addon_proto = out.File
	file_backend_proto_offering_v1_addon_proto_goTypes = nil
	file_backend_proto_offering_v1_addon_proto_depIdxs = nil
}
