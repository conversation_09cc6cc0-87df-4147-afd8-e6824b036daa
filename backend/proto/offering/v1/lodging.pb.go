// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.9
// 	protoc        (unknown)
// source: backend/proto/offering/v1/lodging.proto

package offeringpb

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// LodgingUnitType 住宿单元类型
type LodgingUnitType int32

const (
	// Unspecified lodging unit type
	LodgingUnitType_LODGING_UNIT_TYPE_UNSPECIFIED LodgingUnitType = 0
	// Room/kennel type
	LodgingUnitType_ROOM LodgingUnitType = 1
	// Area type
	LodgingUnitType_AREA LodgingUnitType = 2
)

// Enum value maps for LodgingUnitType.
var (
	LodgingUnitType_name = map[int32]string{
		0: "LODGING_UNIT_TYPE_UNSPECIFIED",
		1: "ROOM",
		2: "AREA",
	}
	LodgingUnitType_value = map[string]int32{
		"LODGING_UNIT_TYPE_UNSPECIFIED": 0,
		"ROOM":                          1,
		"AREA":                          2,
	}
)

func (x LodgingUnitType) Enum() *LodgingUnitType {
	p := new(LodgingUnitType)
	*p = x
	return p
}

func (x LodgingUnitType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LodgingUnitType) Descriptor() protoreflect.EnumDescriptor {
	return file_backend_proto_offering_v1_lodging_proto_enumTypes[0].Descriptor()
}

func (LodgingUnitType) Type() protoreflect.EnumType {
	return &file_backend_proto_offering_v1_lodging_proto_enumTypes[0]
}

func (x LodgingUnitType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LodgingUnitType.Descriptor instead.
func (LodgingUnitType) EnumDescriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_lodging_proto_rawDescGZIP(), []int{0}
}

// LodgingType 住宿类型信息
type LodgingType struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id of the lodging type
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// name of the lodging type
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// description of the lodging type
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// images of this lodging type
	PhotoList []string `protobuf:"bytes,4,rep,name=photo_list,json=photoList,proto3" json:"photo_list,omitempty"`
	// max pet number of this lodging type
	MaxPetNum int32 `protobuf:"varint,5,opt,name=max_pet_num,json=maxPetNum,proto3" json:"max_pet_num,omitempty"`
	// max pet total weight of this lodging type
	MaxPetTotalWeight int32 `protobuf:"varint,6,opt,name=max_pet_total_weight,json=maxPetTotalWeight,proto3" json:"max_pet_total_weight,omitempty"`
	// available for all pet size
	AllPetSizes bool `protobuf:"varint,7,opt,name=all_pet_sizes,json=allPetSizes,proto3" json:"all_pet_sizes,omitempty"`
	// available pet size (only if is_available_for_all_pet_size is false)
	// moe_pet_size.id list
	PetSizeIds []int64 `protobuf:"varint,8,rep,packed,name=pet_size_ids,json=petSizeIds,proto3" json:"pet_size_ids,omitempty"`
	// lodging unit type in this lodging type
	LodgingUnitType LodgingUnitType `protobuf:"varint,9,opt,name=lodging_unit_type,json=lodgingUnitType,proto3,enum=backend.proto.offering.v1.LodgingUnitType" json:"lodging_unit_type,omitempty"`
	// whether the lodging type is available for all pet size
	PetSizeFilter bool `protobuf:"varint,10,opt,name=pet_size_filter,json=petSizeFilter,proto3" json:"pet_size_filter,omitempty"`
	// Sort for the lodging type
	Sort int32 `protobuf:"varint,11,opt,name=sort,proto3" json:"sort,omitempty"`
	// source
	Source        OfferingSource `protobuf:"varint,12,opt,name=source,proto3,enum=backend.proto.offering.v1.OfferingSource" json:"source,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LodgingType) Reset() {
	*x = LodgingType{}
	mi := &file_backend_proto_offering_v1_lodging_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingType) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingType) ProtoMessage() {}

func (x *LodgingType) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_lodging_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingType.ProtoReflect.Descriptor instead.
func (*LodgingType) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_lodging_proto_rawDescGZIP(), []int{0}
}

func (x *LodgingType) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingType) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingType) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *LodgingType) GetPhotoList() []string {
	if x != nil {
		return x.PhotoList
	}
	return nil
}

func (x *LodgingType) GetMaxPetNum() int32 {
	if x != nil {
		return x.MaxPetNum
	}
	return 0
}

func (x *LodgingType) GetMaxPetTotalWeight() int32 {
	if x != nil {
		return x.MaxPetTotalWeight
	}
	return 0
}

func (x *LodgingType) GetAllPetSizes() bool {
	if x != nil {
		return x.AllPetSizes
	}
	return false
}

func (x *LodgingType) GetPetSizeIds() []int64 {
	if x != nil {
		return x.PetSizeIds
	}
	return nil
}

func (x *LodgingType) GetLodgingUnitType() LodgingUnitType {
	if x != nil {
		return x.LodgingUnitType
	}
	return LodgingUnitType_LODGING_UNIT_TYPE_UNSPECIFIED
}

func (x *LodgingType) GetPetSizeFilter() bool {
	if x != nil {
		return x.PetSizeFilter
	}
	return false
}

func (x *LodgingType) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

func (x *LodgingType) GetSource() OfferingSource {
	if x != nil {
		return x.Source
	}
	return OfferingSource_OFFERING_SOURCE_UNSPECIFIED
}

// LodgingTypeFilter 住宿单元过滤条件
type LodgingTypeFilter struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// lodging type id
	LodgingTypeIds []int64 `protobuf:"varint,1,rep,packed,name=lodging_type_ids,json=lodgingTypeIds,proto3" json:"lodging_type_ids,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *LodgingTypeFilter) Reset() {
	*x = LodgingTypeFilter{}
	mi := &file_backend_proto_offering_v1_lodging_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingTypeFilter) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingTypeFilter) ProtoMessage() {}

func (x *LodgingTypeFilter) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_lodging_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingTypeFilter.ProtoReflect.Descriptor instead.
func (*LodgingTypeFilter) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_lodging_proto_rawDescGZIP(), []int{1}
}

func (x *LodgingTypeFilter) GetLodgingTypeIds() []int64 {
	if x != nil {
		return x.LodgingTypeIds
	}
	return nil
}

// LodgingUnit 住宿单元信息
type LodgingUnit struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// lodging unit id
	Id int64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// lodging unit name
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// business id
	BusinessId int64 `protobuf:"varint,3,opt,name=business_id,json=businessId,proto3" json:"business_id,omitempty"`
	// lodging type id
	LodgingTypeId int64 `protobuf:"varint,4,opt,name=lodging_type_id,json=lodgingTypeId,proto3" json:"lodging_type_id,omitempty"`
	// camera id
	CameraId int64 `protobuf:"varint,5,opt,name=camera_id,json=cameraId,proto3" json:"camera_id,omitempty"`
	// sort
	Sort          int32 `protobuf:"varint,6,opt,name=sort,proto3" json:"sort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LodgingUnit) Reset() {
	*x = LodgingUnit{}
	mi := &file_backend_proto_offering_v1_lodging_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LodgingUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LodgingUnit) ProtoMessage() {}

func (x *LodgingUnit) ProtoReflect() protoreflect.Message {
	mi := &file_backend_proto_offering_v1_lodging_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LodgingUnit.ProtoReflect.Descriptor instead.
func (*LodgingUnit) Descriptor() ([]byte, []int) {
	return file_backend_proto_offering_v1_lodging_proto_rawDescGZIP(), []int{2}
}

func (x *LodgingUnit) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *LodgingUnit) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *LodgingUnit) GetBusinessId() int64 {
	if x != nil {
		return x.BusinessId
	}
	return 0
}

func (x *LodgingUnit) GetLodgingTypeId() int64 {
	if x != nil {
		return x.LodgingTypeId
	}
	return 0
}

func (x *LodgingUnit) GetCameraId() int64 {
	if x != nil {
		return x.CameraId
	}
	return 0
}

func (x *LodgingUnit) GetSort() int32 {
	if x != nil {
		return x.Sort
	}
	return 0
}

var File_backend_proto_offering_v1_lodging_proto protoreflect.FileDescriptor

const file_backend_proto_offering_v1_lodging_proto_rawDesc = "" +
	"\n" +
	"'backend/proto/offering/v1/lodging.proto\x12\x19backend.proto.offering.v1\x1a\x1fgoogle/protobuf/timestamp.proto\x1a\x1bbuf/validate/validate.proto\x1a'backend/proto/offering/v1/service.proto\x1a&backend/proto/offering/v1/common.proto\"\xe0\x03\n" +
	"\vLodgingType\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12 \n" +
	"\vdescription\x18\x03 \x01(\tR\vdescription\x12\x1d\n" +
	"\n" +
	"photo_list\x18\x04 \x03(\tR\tphotoList\x12\x1e\n" +
	"\vmax_pet_num\x18\x05 \x01(\x05R\tmaxPetNum\x12/\n" +
	"\x14max_pet_total_weight\x18\x06 \x01(\x05R\x11maxPetTotalWeight\x12\"\n" +
	"\rall_pet_sizes\x18\a \x01(\bR\vallPetSizes\x12 \n" +
	"\fpet_size_ids\x18\b \x03(\x03R\n" +
	"petSizeIds\x12V\n" +
	"\x11lodging_unit_type\x18\t \x01(\x0e2*.backend.proto.offering.v1.LodgingUnitTypeR\x0flodgingUnitType\x12&\n" +
	"\x0fpet_size_filter\x18\n" +
	" \x01(\bR\rpetSizeFilter\x12\x12\n" +
	"\x04sort\x18\v \x01(\x05R\x04sort\x12A\n" +
	"\x06source\x18\f \x01(\x0e2).backend.proto.offering.v1.OfferingSourceR\x06source\"=\n" +
	"\x11LodgingTypeFilter\x12(\n" +
	"\x10lodging_type_ids\x18\x01 \x03(\x03R\x0elodgingTypeIds\"\xab\x01\n" +
	"\vLodgingUnit\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1f\n" +
	"\vbusiness_id\x18\x03 \x01(\x03R\n" +
	"businessId\x12&\n" +
	"\x0flodging_type_id\x18\x04 \x01(\x03R\rlodgingTypeId\x12\x1b\n" +
	"\tcamera_id\x18\x05 \x01(\x03R\bcameraId\x12\x12\n" +
	"\x04sort\x18\x06 \x01(\x05R\x04sort*H\n" +
	"\x0fLodgingUnitType\x12!\n" +
	"\x1dLODGING_UNIT_TYPE_UNSPECIFIED\x10\x00\x12\b\n" +
	"\x04ROOM\x10\x01\x12\b\n" +
	"\x04AREA\x10\x02Bk\n" +
	"#com.moego.backend.proto.offering.v1P\x01ZBgithub.com/MoeGolibrary/moego/backend/proto/offering/v1;offeringpbb\x06proto3"

var (
	file_backend_proto_offering_v1_lodging_proto_rawDescOnce sync.Once
	file_backend_proto_offering_v1_lodging_proto_rawDescData []byte
)

func file_backend_proto_offering_v1_lodging_proto_rawDescGZIP() []byte {
	file_backend_proto_offering_v1_lodging_proto_rawDescOnce.Do(func() {
		file_backend_proto_offering_v1_lodging_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_lodging_proto_rawDesc), len(file_backend_proto_offering_v1_lodging_proto_rawDesc)))
	})
	return file_backend_proto_offering_v1_lodging_proto_rawDescData
}

var file_backend_proto_offering_v1_lodging_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_backend_proto_offering_v1_lodging_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_backend_proto_offering_v1_lodging_proto_goTypes = []any{
	(LodgingUnitType)(0),      // 0: backend.proto.offering.v1.LodgingUnitType
	(*LodgingType)(nil),       // 1: backend.proto.offering.v1.LodgingType
	(*LodgingTypeFilter)(nil), // 2: backend.proto.offering.v1.LodgingTypeFilter
	(*LodgingUnit)(nil),       // 3: backend.proto.offering.v1.LodgingUnit
	(OfferingSource)(0),       // 4: backend.proto.offering.v1.OfferingSource
}
var file_backend_proto_offering_v1_lodging_proto_depIdxs = []int32{
	0, // 0: backend.proto.offering.v1.LodgingType.lodging_unit_type:type_name -> backend.proto.offering.v1.LodgingUnitType
	4, // 1: backend.proto.offering.v1.LodgingType.source:type_name -> backend.proto.offering.v1.OfferingSource
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_backend_proto_offering_v1_lodging_proto_init() }
func file_backend_proto_offering_v1_lodging_proto_init() {
	if File_backend_proto_offering_v1_lodging_proto != nil {
		return
	}
	file_backend_proto_offering_v1_service_proto_init()
	file_backend_proto_offering_v1_common_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_backend_proto_offering_v1_lodging_proto_rawDesc), len(file_backend_proto_offering_v1_lodging_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_backend_proto_offering_v1_lodging_proto_goTypes,
		DependencyIndexes: file_backend_proto_offering_v1_lodging_proto_depIdxs,
		EnumInfos:         file_backend_proto_offering_v1_lodging_proto_enumTypes,
		MessageInfos:      file_backend_proto_offering_v1_lodging_proto_msgTypes,
	}.Build()
	File_backend_proto_offering_v1_lodging_proto = out.File
	file_backend_proto_offering_v1_lodging_proto_goTypes = nil
	file_backend_proto_offering_v1_lodging_proto_depIdxs = nil
}
