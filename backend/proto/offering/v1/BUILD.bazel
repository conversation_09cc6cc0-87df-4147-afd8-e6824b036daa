load("@io_bazel_rules_go//go:def.bzl", "go_library")
load("@io_bazel_rules_go//proto:def.bzl", "go_proto_library")
load("@rules_proto//proto:defs.bzl", "proto_library")

proto_library(
    name = "offeringpb_proto",
    srcs = [
        "addon.proto",
        "addon_service.proto",
        "care_type.proto",
        "care_type_service.proto",
        "common.proto",
        "lodging.proto",
        "lodging_service.proto",
        "service.proto",
        "service_category.proto",
        "service_category_service.proto",
        "service_ob_setting.proto",
        "service_service.proto",
    ],
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/organization/v1:organizationpb_proto",
        "@com_github_bufbuild_protovalidate//proto/protovalidate/buf/validate:validate_proto",
        "@com_google_protobuf//:field_mask_proto",
        "@com_google_protobuf//:struct_proto",
        "@com_google_protobuf//:timestamp_proto",
        "@googleapis//google/type:money_proto",
    ],
)

go_proto_library(
    name = "offeringpb_go_proto",
    compilers = [
        "@io_bazel_rules_go//proto:go_grpc_v2",
        "@io_bazel_rules_go//proto:go_proto",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/offering/v1",
    proto = ":offeringpb_proto",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/proto/organization/v1:organization",
        "@build_buf_gen_go_bufbuild_protovalidate_protocolbuffers_go//buf/validate:go_default_library",
        "@org_golang_google_genproto//googleapis/type/money",
    ],
)

go_library(
    name = "offering",
    embed = [":offeringpb_go_proto"],
    importpath = "github.com/MoeGolibrary/moego/backend/proto/offering/v1",
    visibility = ["//visibility:public"],
)
