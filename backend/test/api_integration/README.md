# instruction of writing and running API integration tests

## Introduction
**API integration testing** is used to observe data flow, test compatibility, and verify the interaction between 
different components of the system via APIs. These tests are essential to ensure that the APIs are working as expected 
and that the system behaves correctly under various conditions. 

We may need API integration testing when:
1. We need to verify the behavior of the API endpoints.
2. It is hard to verify using unit tests alone because the system is complex and involves multiple components.
3. Manual testing is not feasible.
4. It needs real data to test the system rather than mock data.

What we should not do in API integration testing:
1. **Do not test the internal implementation details of the system, including database.**
   Let unit tests handle the database testing. In API integration tests, we should only focus on the API response.
2. **Do not test the performance of the system.** 
   We only focus on the correctness of the API response. As for performance testing, we should use other tools like K6.

What we should do in API integration testing:
1. **Run tests in a real environment.**
   You can run tests in the test environment, staging environment, or even the production environment.
2. **Use real data.**
   We should use real data to test the system rather than mock data. Let the system behave as it would in the real world.
3. **Use assertions to validate the response.**
   We should validate the response using assertions to ensure that the API is returning as expected.
4. **Clean up after the tests.**
   We should clean up any resources created during the tests to ensure that the environment is left in a consistent state.


## Set up project
1. Install openapi-generator-cli. If you don't have JDK 11 (the minimal version supported by openapi-generator-cli) 
   or higher on your computer, you need to install one. 17 is recommended. You can install them by running the following 
   commands, or you can install them in the way you like. just make sure you have the Java binary executable available 
   on your `PATH` for this to work.
   ```shell
   ## using npm
   npm install @openapitools/openapi-generator-cli -g
   ## using yarn
   yarn global add @openapitools/openapi-generator-cli
   
   brew install openjdk@17
   ```


2. Please follow the [README.md](../../../README.md) in the root directory to install golang and bazelisk,
   to init the project.
   
   If you run `make init` successfully, you can see the generated codes in the [def](def) directory. 
   You can also generate the codes manually by running the following command.
   ```shell
   make api-test-init
   ```
   Or you can specify `CLEAN=true` to clean up the generated codes before re-generating them.
   ```shell
    make api-test-init CLEAN=true
   ```
   Also, you can specify a grey domain name to generate the codes for a specific domain.
   For example, if your branch name at server side is `feature-merge-client` or `bugfix-merge-client`,
   `GREY_NAME` should be `merge-client`. Custom grey domain name on https://grey.devops.moego.pet/ can also be specified.
   If you also change `moego-api-definitions`, you should specify `DEF_BRANCH` for it.
   ```shell
    # if you change moego-api-definitions, you should add DEF_BRANCH
    make api-test-init GREY_NAME=merge-client DEF_BRANCH=feature-merge-client
    # if you don't change moego-api-definitions, you can ignore DEF_BRANCH
    make api-test-init GREY_NAME=merge-client
   ```

3. Then you can run API integration tests simply.
   ```shell
   make api-test
   ```
   Or you can specify `GREY_NAME` to run API integration tests for a specific domain.
   ```shell
   make api-test GREY_NAME=merge-client
   ```
   For more information about `make api-test`, see [Running tests](#running-tests).

## Writing a test suite
To write a test suite, you can refer to the [hello_newbies_test.go](collection/hello/hello_newbies_test.go) file. 

Here is a brief guide:
1. Use an existing package or create a new package under [collection](collection).
2. Create a new file with the suffix `_test.go` to define your test suite.
   > Remember to use `make gazelle` command to update the BUILD.bazel file after creating or updating a test file.
3. Create a new `struct` that embeds `suite.Suite` and domain context.
   ```go
   type YourTestSuite struct {
       // you must embed suite.Suite for every test suite
       suite.Suite

       // embed domain context for every test suite
       // for example, you can use godomain.Context to manage context of `go.moego.pet` domain
       godomain.Context
       
       // Add any necessary fields here
       // ...
   }
   ```
4. Implement the `SetupSuite` and `TearDownSuite` methods to set up and clean up any resources needed for the tests.
   In most cases, you may need to log in an account and prepare some data before running all tests. You can do this in the `SetupSuite` method.
   At the end of all tests, `TearDownSuite` should clean up the data.
   ```go
   func (s *YourTestSuite) SetupSuite() {
       // You must always call `s.Context.Setup` to setup the test suite
       // You need to pass the suite (required) and the account (optional) for the test
       s.Context.Setup(&s.Suite, nil)
       
       // Then you can do your own setup stuff
       // ...
   }
   
   func (s *YourTestSuite) TearDownSuite() {
        // You must do your own teardown stuff first before calling `s.Context.Teardown`
        // ...
        
        // You must always call `s.Context.Teardown` at the end of the suite
        s.Context.Teardown()
   }
   ```
5. Write individual test methods within the suite struct. 
   Each test method should start with `Test` followed by a descriptive name for the test case. 
   And Each test should be independent and self-contained.
   ```go
   // You can define multiple test methods here
   func (s *YourTestSuite) TestYourCaseA() {
       // Write your test logic here
       // ...
   }
   
   func (s *YourTestSuite) TestYourCaseB() {
       // Write your test logic here
       // ...
   }
   ```
6. Register the test suite with the `suite.Run` function.
   ```go
   func TestYourSuite(t *testing.T) {
        suite.Run(t, new(YourTestSuite))
   }
   ```

Full example:
```go
package example

import (
   "net/http"
   "testing"
   
   "github.com/stretchr/testify/suite"
   "github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type YourTestSuite struct {
    // you must embed suite.Suite for every test suite
    suite.Suite

    // embed domain context for every test suite
    godomain.Context

    // Add any necessary fields here
    // ...
}
 
func (s *YourTestSuite) SetupSuite() {
    // You must always call `s.Context.Setup` to setup the test suite
    // You need to pass the suite (required) and the account (optional) for the test
    s.Context.Setup(&s.Suite, nil)
 
    // Then you can do your own setup stuff
    // ...
}

func (s *YourTestSuite) TearDownSuite() {
    // You must do your own teardown stuff first before calling `s.Context.Teardown`
    // ...
 
    // You must always call `s.Context.Teardown` at the end of the suite
    s.Context.Teardown()
}

func (s *YourTestSuite) TestYourCaseA() {
    // Write your test logic here
    // ...
} 

func (s *YourTestSuite) TestYourCaseB() {
    // Write your test logic here
    // ...
}

func TestYourSuite(t *testing.T) {
    suite.Run(t, new(YourTestSuite))
}
```


## Advanced usage
### Sending requests and retrieving responses
When writing a test case, you can use the `NewRequest` method provided by the domain context to make a new request.
The `NewRequest` method automatically adds the host, cookies, and other session context to the request.
Then you can use chain methods to set the method, path, payload, and result object for the request, and finally send out
the reqeuest using the `Send` method. 

The payload (request body) and result (response body) can be marshalled and unmarshalled to generated structs:
- If the API is defined in a `.proto` file in [moego-api-definitions](https://github.com/MoeGolibrary/moego-api-definitions/), 
  you can directly import them from the `out/go` directory of moego-api-definitions.
- If the API is defined in a Java HTTP server, you can use the generated structs in the [def](def) directory. 
  They are generated by [api-test-init.sh](script/api-test-init.sh).

Below is an example of sending a request and retrieving a response for a proto-defined API,  
using structs generated from [todo_api.proto](https://github.com/MoeGolibrary/moego-api-definitions/blob/production/api/moego/api/todo/v1/todo_api.proto).
Note that you should always use **`POST`** method for proto-defined APIs. 
```go
import (
    "net/http"

    todoapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/todo/v1"
	
	// ... import other necessary packages
)

func (s *HelloNewbiesSuite) TestHelloArk() {
   result := &todoapipb.HelloArkResult{}
   response, err := s.NewRequest().
       SetMethodPath(http.MethodPost, "/moego.api.todo.v1.TodoService/HelloArk").
       SetPayload(&todoapipb.HelloArkParams{
		   Message: "hello ark",
       }).
       SetResult(result).
       Send()
}
```

> Difference between response and result:
> - **Response**: The raw response object returned by the API, containing the status code, headers, response cookies and 
>                 raw response body in bytes.
> - **Result**: The unmarshalled response body in the form of a struct. This is the expected response body defined in the
>               API specification. It can help you get the data more easily.

Below is another example of sending a request and retrieving a response for a Java HTTP server-defined API,
using structs generated from [openapi-styled spring doc](https://api-docs.t2.moego.dev/?module=REST-BUSINESS).
Note that:
- APIs in Java HTTP servers need a prefix `/api` in the path.
- The request method is defined in the `@RequestMapping` annotation in the Java code.
- Some APIs may require query parameters, which can be added using the `AddQuery` method.
```go
import (
    "net/http"
    "strconv"
    
    customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
)

func (s *Context) GetCustomer(customerID int32) *customermodel.ComMoegoServerCustomerServiceDtoCustomerOverviewDto {
   result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerOverviewDto()
   response, err := s.NewRequest().
      SetMethodPath(http.MethodGet, "/api/customer/overview").
      AddQuery("customerId", strconv.FormatInt(int64(customerID), 10)).
      SetResult(result).
      Send()

   s.Require().Nil(err)
   s.Require().True(response.IsSuccess())
   
   id := result.GetData().GetCustomerDetail().GetId()
   s.Require().NotNil(id)
   
   return result.Data
}
```

### Using test accounts
When writing test cases, you may need to log in with a test account to access the APIs. You can specify a test account
when calling `s.Context.Setup` in the `SetupSuite` method.
There are a test account pool with some public shared accounts and some exclusive accounts (for specific test suites with a `owner` field).
Exclusive accounts can only be borrowed by the owner (check if `borrower` = `owner`) by specifying the account ID or email.
Public shared accounts can be borrowed by any test suite with or without specifying the account ID or email.
**Accounts that are not in the test pools cannot be borrowed.**

- randomly borrow a public shared test account
  ```go
  func (s *YourTestSuite) SetupSuite() {
      // You must always call `s.Context.Setup` to setup the test suite
      // randomly borrow an account
      s.Context.Setup(&s.Suite, &suite.BorrowAccountOptions{
          // specify the borrower name, which is used to identify the test suite. You can use the test suite name simply.
          Borrower: "YourTestSuite",
          // no need to specify the email or account ID
      })
      
      // Then you can do your own setup stuff
      // ...
  }
  ```

- borrow a test account with a specific email (If the account is owned by a specific test suite, borrower should be the owner) 
  ```go
  func (s *YourTestSuite) SetupSuite() {
      // You must always call `s.Context.Setup` to setup the test suite
      // randomly borrow an account
      s.Context.Setup(&s.Suite, &suite.BorrowAccountOptions{
          // specify the borrower name, which is used to identify the test suite. You can use the test suite name simply.
          Borrower: "YourTestSuite",
          // specify the email of the account to borrow
          Email: "<EMAIL>",
      })
      
      // Then you can do your own setup stuff
      // ...
  }
  ```

- borrow a test account with a specific account ID (If the account is owned by a specific test suite, borrower should be the owner)
  ```go
  func (s *YourTestSuite) SetupSuite() {
      // You must always call `s.Context.Setup` to setup the test suite
      // randomly borrow an account
      s.Context.Setup(&s.Suite, &suite.BorrowAccountOptions{
          // specify the borrower name, which is used to identify the test suite. You can use the test suite name simply.
          Borrower: "YourTestSuite",
          // specify the ID of the account to borrow
          ID: 123456,
      })
      
      // Then you can do your own setup stuff
      // ...
  }
  ```

**How to create a test account?**
We had already created some public shared accounts in the test pools. You can use them directly.
If public shared accounts are not enough, or you need a specific exclusive account, please contact the @Well in the
#api-integration-testing channel on Slack.


### Using API snippets
Some APIs may be frequently used in multiple test cases. For example, `CreateAppointment` is a common API that is used in
many test cases. To avoid code duplication, you can define these APIs as snippets in [utils/suite](utils/suite). Each 
domain can have its own snippet file under `utils/suite` directory. For example, `godomain` contains context and snippets 
for `go.moego.pet`, and `bookingdomain` contains context and snippets for `booking.moego.pet`. 

Here is an example of using `CreateAppointment` snippet method in `SetupSuite` method of a test suite: 
```go
type YourSuite struct {
   suite.Suite
   godomain.Context
   AppointmentID int64
}

func (s *YourSuite) SetupSuite() {
    // You must always call `s.Context.Setup` to setup the test suite
      // randomly borrow an account
      s.Context.Setup(&s.Suite, &suite.BorrowAccountOptions{
          // specify the borrower name, which is used to identify the test suite. You can use the test suite name simply.
          Borrower: "YourSuite",
          // no need to specify the email or account ID
      })
    
    // create an appointment using snippet method
    s.AppointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
        // ...
    })
}
```


### Using multi domain context

In some test cases, we may need to use multiple domain contexts simultaneously. For example, when testing online booking functionality, we need both the B-side (go.moego.pet) context to create customer data and the C-side (booking.moego.pet) context to test the booking process.

Here's an example of using multiple domain contexts:

```go
type YourTestSuite struct {
    suite.Suite
    // Define multiple domain contexts
    GoCtx      godomain.Context      // B-side context
    BookingCtx bookingdomain.Context // C-side context
    
    // Other test data fields
    CustomerID int32
    PetIDs     []int32
    setupDone  bool
}

func (s *YourTestSuite) SetupSuite() {
    // Defer a mandatory teardown to ensure cleanup even if subsequent setup fails
    defer func() {
        if !s.setupDone {
            log.WarnContext(s.GoCtx.Ctx, "SetupSuite failed, teardown suite manually")
            s.mustTearDownSuite()
        }
    }()

    // 1. Set up B-side context (login go.moego.pet as a B-user)
    s.GoCtx.Setup(&s.Suite, &godomain.BorrowAccountOptions{
        Borrower: "YourTestSuite",
        // Set other necessary attributes
        Attributes: &toolspb.Attributes{
            RegionCode: pointer.Get("AU"),
        },
    })

    // 2. Prepare test data using the first context
    // ...

    // 3. get obName of this business 
    obName := s.GoCtx.GetOBSettingInfo().GetBookOnlineInfo().GetBookOnlineName()

    // 4. Set up C-side context with B-side's obName (login booking.moego.pet as a C-user)
    phoneNumber := "**********"
    s.BookingCtx.Setup(&s.Suite, obName, phoneNumber)

    s.setupDone = true
}

func (s *YourTestSuite) TearDownSuite() {
    // Note: teardown order should be reverse of setup order
    defer s.GoCtx.Teardown()
    defer s.BookingCtx.Teardown()
    s.mustTearDownSuite()
}

func (s *YourTestSuite) mustTearDownSuite() {
    // Clean up test data
    if s.CustomerID > 0 {
        s.GoCtx.DeleteCustomer(s.CustomerID)
    }
}

```


### Tracing and logging
Once your suite embeds `godomain.Context` or contexts of other domains, and calls `s.Context.Setup`, the test suite will 
automatically be traced. Then if you log the test with `s.Ctx`, you will see the trace ID (`trace_id`) in the log. Tests 
in a suite share the same trace ID, so you can trace the whole test suite with the trace ID in Datadog APM: 
https://us5.datadoghq.com/apm/trace/{trace_id} (replace `{trace_id}` with the actual trace ID in the link).

```go
import (
    "github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

// ... other code

func (s *HelloNewbiesSuite) Print() {
    log.InfoContextf(s.Ctx, "Hello!")
}
```

Log output example:
```
=== RUN   TestHelloNewbiesTestSuite/TestHelloWell
{"level":"info","ts":"2025-04-30T08:30:55.163Z","caller":"hello/hello_newbies_test.go:51","msg":"Start to run test HelloNewbiesSuite.TestHelloWell","trace_id":4634786040697453296}
{"level":"info","ts":"2025-04-30T08:30:56.213Z","caller":"api/response.go:50","msg":"reqeust id: 5f8ba038-f885-9292-b2d6-e2f11fd79ccd, GET https://go.t2.moego.dev/api/business/debugging/well","trace_id":4634786040697453296}
{"level":"info","ts":"2025-04-30T08:30:56.216Z","caller":"hello/hello_newbies_test.go:57","msg":"Finish running test HelloNewbiesSuite.TestHelloWell","trace_id":4634786040697453296}
```


### Running tests
#### Run tests locally
You can run the tests locally using the `make api-test` command.

The parameters are explained below:

| Parameter  | Description                                                                                                                                                                                                                            | Default Value | Available Values                                                                                                                        |
|------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------|-----------------------------------------------------------------------------------------------------------------------------------------|
| ENV        | Specify the environment to run the tests in.                                                                                                                                                                                           | testing       | testing, staging, production                                                                                                            |
| GREY_NAME  | Specify the grey domain name at server side to run the tests in.                                                                                                                                                                       |               |                                                                                                                                         |
| DEF_BRANCH | Only works when `INIT` is set to true. Specify the branch name of moego-api-definitions to generate the codes.                                                                                                                         | main          |                                                                                                                                         |
| TEST       | Specify tests to run. If not set, all tests will be run                                                                                                                                                                                | ...           | test names in [BUILD.bazel](collection/BUILD.bazel), should be in format: {package_name1}:{test_name1},{package_name2}:{test_name2},... |
| TAGS       | Run tests with given tags(comma-separated). Each tag can be optionally preceded with '-' to specify excluded tags. Only those test targets will be found that contain at least one included tag and do not contain any excluded tags.  |               |                                                                                                                                         |
| LOG        | If set to true, enables logging of the test output.                                                                                                                                                                                    | true          | true, false                                                                                                                             |
| INIT       | If set to true, `make api-test-init` will be executed before running the tests.                                                                                                                                                        | false         | true, false                                                                                                                             |
| CLEAN      | Only works when `INIT` is set to true. If `CLEAN` is set to true, the generated codes will be cleaned up before re-generating them.                                                                                                    | false         | true, false                                                                                                                             |

When using TAGS, you need to add tags in BUILD.bazel file. See [BUILD.bazel](collection/hello/BUILD.bazel) as an example:
```bazel
load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "hello_test",
    srcs = ["hello_newbies_test.go"],
    tags = ["testing_only"],
    deps = [
        ...
    ],
)
```

You can specify multiple tags separated by commas. For example:
```shell
make api-test TAGS="workflow,hello"
```
This will run all tests that have "workflow" or "pet" tags.

You can also specify excluded tags by prefixing the tag with `-`. For example:
```shell
make api-test TAGS="workflow,hello,-production_only"
```
This will run all tests that have "workflow" or "pet" tags, but not those that have "production_only" tag.


There are 3 official tags:
- **testing_only**: Tests can only run in testing env.
- **staging_only**: Tests can only run in staging env.
- **production_only**: Tests can only run in production env.

If you don't specify any tags, default tags will be used:
- if ENV=testing, default tags are: -staging_only,-production_only
  
  i.e. If you run `make api-test` or `make api-test ENV=testing`, tests with `staging_only` and `production_only` tags will be excluded. 
- if ENV=staging, default tags are: staging_only

  i.e. If you run `make api-test ENV=staging`, it will only run tests with `staging_only` tag.
- if ENV=production, default tags are: production_only

  i.e. If you run `make api-test ENV=production`, it will only run tests with `production_only` tag.

#### Run tests via GitHub Actions
You can run the tests via [GitHub Actions](https://github.com/MoeGolibrary/moego/actions/workflows/api-integration-test.yml).
The workflow file is located at [api-integration-test.yml](../../../.github/workflows/api-integration-test.yml).

#### Scheduled run tests
There is a scheduled task to run all API integration tests every day. 
See the [workflow](../../../.github/workflows/api-integration-test.yml) for more details.