#!/bin/bash

# 定义 BAZEL_DIR 和 BAZEL_COMMAND
BAZEL_DIR=./bazel
BAZEL_COMMAND="bazelisk --bazelrc=${BAZEL_DIR}/.bazelrc"

# Parse environment variables or provide defaults
ENV=${ENV:-testing}
GREY_NAME=${GREY_NAME:-}
TEST=${TEST:-...}
LOG=${LOG:-true}
LOG_RESPONSE=${LOG_RESPONSE:-false}
TAGS=${TAGS:-}

checkEnv() {
  if [[ "$ENV" != "testing" && "$ENV" != "staging" && "$ENV" != "production" ]]; then
    echo "Error: ENV must be one of testing, staging, production"
    exit 1
  fi
}

checkTags() {
  for tag in $TAGS; do
    # ENV 是 testing, 则 TAGS 不能包含 staging_only 和 production_only
    if [[ "$ENV" == "testing" ]]; then
      if [[ "$tag" == "staging_only" || "$tag" == "production_only" ]]; then
        echo "Error: TAGS should not include staging_only 和 production_only"
        exit 1
      fi
    # ENV 是 staging, 则 TAGS 不能包含 testing_only 和 production_only
    elif [[ "$ENV" == "staging" ]]; then
      if [[ "$tag" == "testing_only" || "$tag" == "production_only" ]]; then
        echo "Error: TAGS should not include testing_only 和 production_only"
        exit 1
      fi
    # ENV 是 production, 则 TAGS 不能包含 testing_only 和 staging_only
    elif [[ "$ENV" == "production" ]]; then
      if [[ "$tag" == "testing_only" || "$tag" == "staging_only" ]]; then
        echo "Error: TAGS should not include testing_only 和 staging_only"
        exit 1
      fi
    fi
  done
}

# 如果 TAGS 为空, 且没有指定 TEST, 填充默认 TAGS:
# - ENV 是 testing, 则 TAGS=-staging_only,-production_only,-invoke_stripe,-single_run
# - ENV 是 staging, 则 TAGS=staging_only
# - ENV 是 production, 则 TAGS=production_only
checkEnv
if [[ -n "$TAGS" ]]; then
  checkTags
elif [[ "$TEST" == "..." ]]; then
  if [[ "$ENV" == "testing" ]]; then
    TAGS="-staging_only,-production_only,-invoke_stripe,-single_run"
  elif [[ "$ENV" == "staging" ]]; then
    TAGS="staging_only"
  elif [[ "$ENV" == "production" ]]; then
    TAGS="production_only"
  fi
fi

# echo all variables
echo "ENV=$ENV GREY_NAME=$GREY_NAME TEST=$TEST LOG=$LOG LOG_RESPONSE=$LOG_RESPONSE TAGS=$TAGS"

# Generate the test targets
BASE_PATH="//backend/test/api_integration/collection/"
if [[ "$TEST" == "..." || -z "$TEST" ]]; then
  # Run all tests
  TEST_TARGET="${BASE_PATH}..."
else
  # Run specified tests, assuming TEST is a comma-separated list of test targets
  TEST_TARGET=""
  for t in $(echo "$TEST" | tr ',' ' '); do
    TEST_TARGET="$TEST_TARGET ${BASE_PATH}$t"
  done
fi

echo "TEST_TARGET=$TEST_TARGET"


# Run Bazel test
# AWS_LAMBDA_FUNCTION_NAME 环境变量注入是为了让 datadog tracing 能够正常工作
ENV=$ENV \
GREY_NAME=$GREY_NAME \
LOG_RESPONSE=$(if [[ "$LOG_RESPONSE" == "true" ]]; then echo "true"; else echo "false"; fi) \
$BAZEL_COMMAND test $TEST_TARGET \
    --test_timeout=3600 \
    --nocache_test_results \
    --test_summary=detailed \
    --test_output=$(if [[ "$LOG" == "true" ]]; then echo "streamed"; else echo "errors"; fi) \
    $(if [[ "$LOG" == "true" ]]; then echo "--test_arg=-test.v"; fi) \
    --test_env=AWS_LAMBDA_FUNCTION_NAME="api-integration-testing" \
    --test_env=ENV \
    --test_env=GREY_NAME \
    --test_env=LOG_RESPONSE \
    --test_tag_filters=$(if [[ -n "$TAGS" ]]; then echo "$TAGS"; else echo ""; fi)