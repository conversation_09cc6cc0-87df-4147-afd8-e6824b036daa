# TODO(CRM-1785): https://api-docs.t2.moego.dev/rest/v3/customer.json -> 源码依赖
# TODO(CRM-1787 CRM-1810): 生成代码不需要 git 版本控制

# 定义 BAZEL_DIR 和 BAZEL_COMMAND
BAZEL_DIR=./bazel
BAZEL_COMMAND="bazelisk --bazelrc=${BAZEL_DIR}/.bazelrc"

GREY_NAME=${GREY_NAME:-}
DEF_BRANCH=${DEF_BRANCH:-main}
CLEAN=${CLEAN:-false}

# echo all variables
echo "BAZEL_COMMAND=$BAZEL_COMMAND"
echo "CLEAN=$CLEAN"

# change version of moego-api-definitions
echo "update moego-api-definitions, branch: ${DEF_BRANCH}"
GOPRIVATE=github.com/MoeGolibrary go get -u github.com/MoeGolibrary/moego-api-definitions@${DEF_BRANCH}
if [ $? -ne 0 ]; then
  echo "Error: Failed to execute go get command"
  exit 1
fi

OUTPUT_DIR=backend/test/api_integration/def
if [ -z "$GREY_NAME" ]; then
  DOMAIN=api-docs.t2.moego.dev
else
  DOMAIN=${GREY_NAME}-grey-api-docs.t2.moego.dev
fi

# Java HTTP Restful API
for service in customer business grooming message payment retail; do
  if [ "$CLEAN" = "true" ]; then
    [ -d "${OUTPUT_DIR}/${service}/model" ] && rm -r ${OUTPUT_DIR}/${service}/model && echo "Removed directory: ${OUTPUT_DIR}/${service}/model"
  fi

  if [ ! -d "${OUTPUT_DIR}/${service}/model" ]; then
    mkdir -p ${OUTPUT_DIR}/${service}/model
  fi

  LOG_FILE=${OUTPUT_DIR}/${service}/model/generate.log

  openapi-generator-cli generate \
  -i https://${DOMAIN}/rest/v3/${service}.json \
  -g go \
  -o ${OUTPUT_DIR}/${service}/model \
  --skip-validate-spec \
  --additional-properties structPrefix=false,withGoMod=false,generateMarshalJSON=true,generateUnmarshalJSON=true,packageName=${service}model \
  --global-property models,modelDocs=false,skipFormModel=true,supportingFiles=utils.go \
  --import-mappings types.DateTime=github.com/MoeGolibrary/moego/backend/test/api_integration/utils/types --type-mappings string+date-time=types.DateTime \
  --template-dir ${OUTPUT_DIR}/template > ${LOG_FILE}

  # 检查 openapi-generator-cli 是否发生错误
  if [ $? -ne 0 ]; then
    echo "Error occurred during generating ${service} model"
    exit 1
  fi

  # 生成或更新 BUILD.bazel 文件
  $BAZEL_COMMAND run //:gazelle -- update ${OUTPUT_DIR}/${service}/model
done

# bff 的 openapi doc 有问题，先暂时不生成
## BFF API
#if [ -z "$GREY_NAME" ]; then
#  DOMAIN=go.t2.moego.dev
#else
#  DOMAIN=${GREY_NAME}-grey-go.t2.moego.dev
#fi
#service=bff
#
#if [ "$CLEAN" = "true" ]; then
#  [ -d "${OUTPUT_DIR}/${service}/model" ] && rm -r ${OUTPUT_DIR}/${service}/model && echo "Removed directory: ${OUTPUT_DIR}/${service}/model"
#fi
#
#if [ ! -d "${OUTPUT_DIR}/${service}/model" ]; then
#  mkdir -p ${OUTPUT_DIR}/${service}/model
#fi
#
#LOG_FILE=${OUTPUT_DIR}/${service}/model/generate.log
#
#openapi-generator-cli generate \
#-i https://go.t2.moego.dev/moego.bff/openapi \
#-g go \
#-o ${OUTPUT_DIR}/${service}/model \
#--skip-validate-spec \
#--additional-properties structPrefix=false,withGoMod=false,generateMarshalJSON=true,generateUnmarshalJSON=true,packageName=${service}model \
#--global-property models,modelDocs=false,skipFormModel=true,supportingFiles=utils.go \
#--import-mappings types.DateTime=github.com/MoeGolibrary/moego/backend/test/api_integration/utils/types \
#--type-mappings string+date-time=types.DateTime \
#--type-mappings null=interface{} \
#--template-dir ${OUTPUT_DIR}/template \
#> ${LOG_FILE}
#
## 检查 openapi-generator-cli 是否发生错误
#if [ $? -ne 0 ]; then
#  echo "Error occurred during generating ${service} model"
#  exit 1
#fi
#
## 生成或更新 BUILD.bazel 文件
#$BAZEL_COMMAND run //:gazelle -- update ${OUTPUT_DIR}/${service}/model