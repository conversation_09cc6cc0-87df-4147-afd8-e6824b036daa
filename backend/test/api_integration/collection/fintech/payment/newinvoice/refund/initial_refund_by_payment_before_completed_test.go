package refund

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/shopspring/decimal"
	"github.com/stretchr/testify/suite"
	"google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/proto"

	appointmentapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	offeringapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1"
	orderapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v1"
	organizationapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/organization/v1"
	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	moneyutil "github.com/MoeGolibrary/moego/backend/common/utils/money"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	appointmentmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
	paymentmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/payment/model"
	retailjavapb "github.com/MoeGolibrary/moego/backend/test/api_integration/def/retail/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type expectedAmount struct {
	ExpectedUnits string `json:"expected_units"`
	ExpectedNanos int64  `json:"expected_nanos"`
}

type flags struct {
	CanCombineOrderPayments        bool `json:"canCombineOrderPayments"`
	IsRefundConvenienceFeeOptional bool `json:"isRefundConvenienceFeeOptional"`
}

type refundOrderPreview struct {
	RefundTotalAmount       expectedAmount `json:"refundTotalAmount"`
	RefundItemSubTotal      expectedAmount `json:"refundItemSubTotal"`
	RefundItemDiscountTotal expectedAmount `json:"refundItemDiscountTotal"`
	RefundTips              expectedAmount `json:"refundTips"`
	RefundConvenienceFee    expectedAmount `json:"refundConvenienceFee"`
	RefundTaxFee            expectedAmount `json:"refundTaxFee"`
	Product                 expectedAmount `json:"product"`
	ServiceCharge           expectedAmount `json:"service_charge"`
	Service                 expectedAmount `json:"service"`
}

type expectedValues struct {
	RefundableTips           expectedAmount     `json:"refundableTips"`
	RefundFlags              flags              `json:"refundFlags"`
	RefundableConvenienceFee expectedAmount     `json:"refundableConvenienceFee"`
	RefundOrderPreview       refundOrderPreview `json:"refundOrderPreview"`
	RefundableOrderPayments  expectedAmount     `json:"refundableOrderPayments"`
}

func getExpectedValues(
	refundableTipsUnits string, refundableTipsNanos int64,
	canCombineOrderPayments bool, isRefundConvenienceFeeOptional bool,
	refundableConvenienceFeeUnits string, refundableConvenienceFeeNanos int64,
	refundTotalAmountUnits string, refundTotalAmountNanos int64,
	refundItemSubTotalUnits string, refundItemSubTotalNanos int64,
	refundItemDiscountTotalUnits string, refundItemDiscountTotalNanos int64,
	refundTipsUnits string, refundTipsNanos int64,
	refundConvenienceFeeUnits string, refundConvenienceFeeNanos int64,
	refundTaxFeeUnits string, refundTaxFeeNanos int64,
	productUnits string, productNanos int64,
	serviceChargeUnits string, serviceChargeNanos int64,
	serviceUnits string, serviceNanos int64,
	refundableOrderPaymentsUnits string, refundableOrderPaymentsNanos int64,
) expectedValues {
	return expectedValues{
		RefundableTips: expectedAmount{
			ExpectedUnits: refundableTipsUnits,
			ExpectedNanos: refundableTipsNanos,
		},
		RefundFlags: flags{
			CanCombineOrderPayments:        canCombineOrderPayments,
			IsRefundConvenienceFeeOptional: isRefundConvenienceFeeOptional,
		},
		RefundableConvenienceFee: expectedAmount{
			ExpectedUnits: refundableConvenienceFeeUnits,
			ExpectedNanos: refundableConvenienceFeeNanos,
		},
		RefundOrderPreview: refundOrderPreview{
			RefundTotalAmount: expectedAmount{
				ExpectedUnits: refundTotalAmountUnits,
				ExpectedNanos: refundTotalAmountNanos,
			},
			RefundItemSubTotal: expectedAmount{
				ExpectedUnits: refundItemSubTotalUnits,
				ExpectedNanos: refundItemSubTotalNanos,
			},
			RefundItemDiscountTotal: expectedAmount{
				ExpectedUnits: refundItemDiscountTotalUnits,
				ExpectedNanos: refundItemDiscountTotalNanos,
			},
			RefundTips: expectedAmount{
				ExpectedUnits: refundTipsUnits,
				ExpectedNanos: refundTipsNanos,
			},
			RefundConvenienceFee: expectedAmount{
				ExpectedUnits: refundConvenienceFeeUnits,
				ExpectedNanos: refundConvenienceFeeNanos,
			},
			RefundTaxFee: expectedAmount{
				ExpectedUnits: refundTaxFeeUnits,
				ExpectedNanos: refundTaxFeeNanos,
			},
			Product: expectedAmount{
				ExpectedUnits: productUnits,
				ExpectedNanos: productNanos,
			},
			ServiceCharge: expectedAmount{
				ExpectedUnits: serviceChargeUnits,
				ExpectedNanos: serviceChargeNanos,
			},
			Service: expectedAmount{
				ExpectedUnits: serviceUnits,
				ExpectedNanos: serviceNanos,
			},
		},
		RefundableOrderPayments: expectedAmount{
			ExpectedUnits: refundableOrderPaymentsUnits,
			ExpectedNanos: refundableOrderPaymentsNanos,
		},
	}
}

type NewInvoiceBeforeRefundTestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID       int32
	PetIDs           []int32
	TagIDs           []int64
	Services         []*offeringpb.CustomizedServiceView
	Service          *offeringpb.ServiceModel
	ServicesId       []*int64
	ServiceChargeIDs []*int64
	TaxIds           []*int64
	setupDone        bool

	// Test文件维度使用的公共变量
	servicecharge1info *orderpb.ServiceCharge
	servicecharge2info *orderpb.ServiceCharge
	RetailIds          []*int64
	retail1Info        *retailjavapb.ComMoegoServerRetailServiceDtoProductInfoDto
	retail2Info        *retailjavapb.ComMoegoServerRetailServiceDtoProductInfoDto
	apptId             int64
}

func (s *NewInvoiceBeforeRefundTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "NewInvoiceNotBDTestSuite",
		Email:    "<EMAIL>", // pwd:AutoTest!123  <EMAIL>
	})

	// create a customer for testing
	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(
		&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
			PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
			PhoneNumber:         customermodel.PtrString("**********"),
			FirstName:           customermodel.PtrString("API"),
			LastName:            customermodel.PtrString("test"),
			Email:               customermodel.PtrString("<EMAIL>"),
			PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
				{
					PetName:   customermodel.PtrString("APet"),
					PetTypeId: customermodel.PtrInt32(1),
					Breed:     customermodel.PtrString("Affenpinscher"),
				},
			},
		})

	// 先充钱
	s.chargePayment(10000.00)

	s.setupDone = true
}

func (s *NewInvoiceBeforeRefundTestSuite) BeforeTest(suiteName, testName string) {
	log.InfoContextf(s.Ctx, "Start to run test %s.%s", suiteName, testName)
	// 每个 test case 执行前的初始化
	s.createApptAndEditApptForThisTesting()
}

func (s *NewInvoiceBeforeRefundTestSuite) AfterTest(suiteName, testName string) {
	log.InfoContextf(s.Ctx, "Finish running test %s.%s", suiteName, testName)
	// 再删除 serivce 、 service charges、 tax 、retails
	s.deleteApptAndEditApptForThisTesting()
}

func (s *NewInvoiceBeforeRefundTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *NewInvoiceBeforeRefundTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	// 先删除了 customer
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

/*
@desc: 清理操作
*/
func (s *NewInvoiceBeforeRefundTestSuite) deleteApptAndEditApptForThisTesting() {

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(s.apptId)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})

	// 删除 service , 需要传入的参数是 service id ，需要在 stuct 里面定义, 调用的时候直接取值就可以了，全局唯一
	if idPtr := pointer.Get(s.ServicesId[0]); idPtr != nil && *idPtr != nil {
		s.DeleteService(**idPtr) // 解引用两次，获取 int64 类型值
	}

	// 删除 service charges
	for _, idPtr := range s.ServiceChargeIDs {
		if idPtr != nil {
			s.DeleteServiceCharge(&orderapipb.DeleteServiceChargeRequest{
				Id:                *idPtr,
				ApplyUpcomingAppt: pointer.Get(false),
			})
		}
	}

	// 删除 tax
	for _, idPtr := range s.TaxIds {
		if idPtr != nil {
			// 调用 DeleteTaxRule 删除每个 tax ID
			s.DeleteTaxRule(&organizationapipb.DeleteTaxRuleParams{
				Id: *idPtr, // 解引用 idPtr 获取税务 ID
			})
		}
	}

	// 删除 retails
	for _, retailIdPtr := range s.RetailIds {
		if retailIdPtr != nil {
			s.DeleteRetailProduct(&retailjavapb.ComMoegoServerRetailServiceParamsProductIdParams{
				ProductId: int32(*retailIdPtr),
			})
		}
	}
}

/*
@Desc:为了防止退款的时候没有钱, 这里是为了先给当前账号充钱
*/
func (s *NewInvoiceBeforeRefundTestSuite) chargePayment(amount float32) {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	// 先给 MGP 账号充钱
	s.StripeTakePayment(&paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:      amount,
		TipsAmount:  proto.Float32(0),
		CardNumber:  proto.String("1111"),
		CardType:    proto.String("Visa"),
		CustomerId:  proto.Int32(int32(s.CustomerID)),
		Description: proto.String(""),
		ExpMonth:    proto.String("11"),
		ExpYear:     proto.String("2052"),
		InvoiceId: int32(s.GetAppointment(&appointmentapipb.GetAppointmentParams{
			AppointmentId: appointmentID,
		}).GetInvoice().InvoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_visa"),
		AddProcessingFee:      proto.Bool(true),
	}, false)
}

/*
@desc: 本次测试的时候的公共方法 创建 appt
*/
func (s *NewInvoiceBeforeRefundTestSuite) createDefaultService() *offeringpb.ServiceModel {
	serviceName := "diy test grooming service"
	servicePrice := 10.0
	serviceDuration := 10

	taxes := s.ListTaxes()
	if len(taxes) == 0 {
		s.T().Fatalf("before creating service, please create a tax")
	}

	result := &offeringapipb.CreateServiceResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/CreateService").
		SetPayload(offeringapipb.CreateServiceParams{
			Service: &offeringpb.CreateServiceDef{
				Name:                    serviceName,
				Inactive:                false,
				ServiceItemType:         offeringpb.ServiceItemType_GROOMING.Enum(),
				Price:                   servicePrice,
				PriceUnit:               offeringpb.ServicePriceUnit_PER_SESSION,
				TaxId:                   taxes[0].GetId(),
				Duration:                int32(serviceDuration),
				AddToCommissionBase:     true,
				CanTip:                  true,
				IsAllLocation:           true,
				BreedFilter:             false,
				PetSizeFilter:           false,
				WeightFilter:            false,
				CoatFilter:              false,
				RequireDedicatedLodging: false,
				LodgingFilter:           false,
				Type:                    offeringpb.ServiceType_SERVICE.Enum(),
				RequireDedicatedStaff:   false,
				AvailableForAllStaff:    false,
				Source:                  offeringpb.ServiceModel_MOEGO_PLATFORM.Enum(),
			},
		}).SetResult(result).Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result.GetService()
}

/*
@desc:因为 refund 的测试基本都是一样的，我们需要做大量的准备工作嘻嘻
*/
func (s *NewInvoiceBeforeRefundTestSuite) createApptAndEditApptForThisTesting() {

	// 需要先清理
	s.retail1Info = nil
	s.retail2Info = nil
	s.TaxIds = []*int64{}
	s.servicecharge1info = nil
	s.servicecharge2info = nil
	s.ServiceChargeIDs = []*int64{}
	s.ServicesId = []*int64{}
	s.Service = nil
	s.RetailIds = []*int64{}

	// 创建本次测试专用的service 然后返回 serivce id 以方便后续使用
	service := s.createDefaultService()
	serviceID := service.GetServiceId()

	//创建tax 测试完成之后删除
	AddTaxRuleResult := s.AddTaxRule(&organizationapipb.AddTaxRuleParams{
		TaxRule: &organizationpb.TaxRuleDef{
			Name: "test tax",
			Rate: *proto.Float64(1.0),
		},
	})

	// 创建service charges 名字叫做 servicecharge1
	servicecharge1info := s.AddCompanyServiceCharge(&orderapipb.AddCompanyServiceChargeParams{
		Name:              "servicecharge1",
		Price:             10,
		TaxId:             int32(AddTaxRuleResult.Id),
		IsActive:          proto.Bool(true),
		IsAllLocation:     proto.Bool(true),
		AutoApplyStatus:   orderpb.ServiceCharge_AUTO_APPLY_DISABLED.Enum(),
		AutoApplyTime:     proto.Int32(0),
		ApplyType:         orderpb.ServiceCharge_PER_APPOINTMENT.Enum(),
		Description:       proto.String("test service charge1"),
		ApplyUpcomingAppt: proto.Bool(false),
		SurchargeType:     orderpb.SurchargeType_CUSTOM_FEE.Enum(),
	})

	// 创建service charges 名字叫做 servicecharge1
	servicecharge2info := s.AddCompanyServiceCharge(&orderapipb.AddCompanyServiceChargeParams{
		Name:              "servicecharge2",
		Price:             20,
		TaxId:             int32(AddTaxRuleResult.Id),
		IsActive:          proto.Bool(true),
		IsAllLocation:     proto.Bool(true),
		AutoApplyStatus:   orderpb.ServiceCharge_AUTO_APPLY_DISABLED.Enum(),
		AutoApplyTime:     proto.Int32(0),
		ApplyType:         orderpb.ServiceCharge_PER_APPOINTMENT.Enum(),
		Description:       proto.String("test service charge2"),
		ApplyUpcomingAppt: proto.Bool(false),
		SurchargeType:     orderpb.SurchargeType_CUSTOM_FEE.Enum(),
	})

	// 新增两个retails ，一个 10USD单价，另一个20USD单价，并分别将其添加到订单中
	retail1Info := s.AddRetailProduct(&retailjavapb.ComMoegoServerRetailServiceParamsProductParams{
		Name:        proto.String("retail1"),
		RetailPrice: proto.Float32(10),
		Stock:       proto.Int32(10),
	})

	retail2Info := s.AddRetailProduct(&retailjavapb.ComMoegoServerRetailServiceParamsProductParams{
		Name:        proto.String("retail2"),
		RetailPrice: proto.Float32(20),
		Stock:       proto.Int32(10),
	})

	// 确定当前支付 一定要包含 fee
	params := &paymentmodel.ComMoegoServerPaymentDtoPaymentSettingDTO{
		BusinessId:             proto.Int32(int32(s.GetAuthInfo().BusinessID)),
		ProcessingFeePayBy:     proto.Int32(1),
		CustomizedFeeName:      proto.String("Fees"),
		OnlineFeeRate:          proto.Float32(3.4),
		OnlineFeeCents:         proto.Int32(30),
		ReaderFeeRate:          proto.Float32(2.9),
		ReaderFeeCents:         proto.Int32(50),
		ProcessingFeeCalMethod: proto.Int32(0),
		AutoCancelFeeByClient:  proto.Int32(0),
	}
	// 调用一次就好咯
	s.SetPaymentSettingInfo(params)

	// 设置 test 级别的临时变量，每一个 测试线程 之间相互独立
	s.RetailIds = append(s.RetailIds,
		new(int64), // 创建一个新的 *int64 变量
		new(int64)) // 创建一个新的 *int64 变量
	*s.RetailIds[0] = int64(*retail1Info.GetId()) // 解引用并将值赋给 *int64
	*s.RetailIds[1] = int64(*retail2Info.GetId()) // 解引用并将值赋给 *int64

	s.retail1Info = retail1Info
	s.retail2Info = retail2Info
	s.TaxIds = append(s.TaxIds, &AddTaxRuleResult.Id)
	s.servicecharge1info = servicecharge1info
	s.servicecharge2info = servicecharge2info
	id1 := s.servicecharge1info.GetId()
	id2 := s.servicecharge2info.GetId()
	s.ServiceChargeIDs = append(s.ServiceChargeIDs, &id1, &id2)
	s.ServicesId = append(s.ServicesId, &serviceID)

	s.Service = service

}

/*
desc：创建 appt ，appt 是每个case 独立的并不是共享变量
*/
func (s *NewInvoiceBeforeRefundTestSuite) createApptAndOrderWithOnceTest(service *offeringpb.ServiceModel) int64 {
	// 创建 appt
	today := time.Now().Format("2006-01-02")
	appointmentID := s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    service.GetServiceId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600), // 无关紧要的值，写死处理
						EndTime:      proto.Int32(610), // 无关紧要的值，写死处理
						ServiceTime:  proto.Int32(service.GetDuration()),
						ServicePrice: proto.Float64(service.GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	appintmentInfo := s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId := appintmentInfo.GetInvoice().InvoiceId

	// 将 service charges 应用到 appt 中
	s.AddServiceChargeToOrder(&orderapipb.AddOrRemoveServiceChargeRequest{
		OrderId:         invoiceId,
		ServiceChargeId: []int64{s.servicecharge1info.GetId(), s.servicecharge2info.GetId()},
	})

	// 添加 tips 10USD
	s.SetTips(&orderapipb.SetTipsRequest{
		CheckRefund: pointer.Get(true),
		InvoiceId:   int64(invoiceId),
		OmitResult:  *pointer.Get(false),
		Value:       *proto.Float64(10),
		ValueType:   "amount",
	})

	// 增加1% 和 2%的折扣
	s.SetDiscount(&retailjavapb.ComMoegoServerRetailServiceParamsSetDiscountParams{
		CheckRefund: pointer.Get(true),
		OrderId:     invoiceId,
		LineDiscounts: []retailjavapb.ComMoegoServerRetailServiceParamsOrderLineDiscountParams{
			{
				ApplyType:      pointer.Get("all"),
				DiscountType:   pointer.Get("percentage"),
				DiscountAmount: pointer.Get(*proto.Float32(0)),
				DiscountRate:   pointer.Get(float32(1)),
			},
		},
	})

	s.SetDiscount(&retailjavapb.ComMoegoServerRetailServiceParamsSetDiscountParams{
		CheckRefund: pointer.Get(true),
		OrderId:     invoiceId,
		LineDiscounts: []retailjavapb.ComMoegoServerRetailServiceParamsOrderLineDiscountParams{
			{
				ApplyType:      pointer.Get("all"),
				DiscountType:   pointer.Get("percentage"),
				DiscountAmount: pointer.Get(*proto.Float32(0)),
				DiscountRate:   pointer.Get(float32(2)),
			},
		},
	})

	// 设置 retails 到 order 里面去
	s.PutRetailsItemsOnOrder(&retailjavapb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
		OrderId: invoiceId,
		RetailItems: []retailjavapb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
			{
				Name:      s.retail1Info.Name,
				Quantity:  pointer.Get(int32(2)),
				StaffId:   proto.Int64(s.GetAuthInfo().StaffID),
				ObjectId:  pointer.Get(int64(*s.retail1Info.Id)),
				Type:      pointer.Get("product"),
				UnitPrice: pointer.Get(*s.retail1Info.RetailPrice),
				LineTaxes: []retailjavapb.ComMoegoServerRetailServiceParamsOrderLineTaxParams{
					{
						TaxId:   pointer.Get(int64(*s.retail1Info.TaxId)),
						TaxRate: pointer.Get(*s.retail1Info.TaxRate),
					},
				},
			},
		},
		CheckRefund: pointer.Get(true),
	})

	// 设置 retails 到 order 里面去
	s.PutRetailsItemsOnOrder(&retailjavapb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
		OrderId: invoiceId,
		RetailItems: []retailjavapb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
			{
				Name:      s.retail2Info.Name,
				Quantity:  pointer.Get(int32(2)),
				StaffId:   proto.Int64(s.GetAuthInfo().StaffID),
				ObjectId:  pointer.Get(int64(*s.retail2Info.Id)),
				Type:      pointer.Get("product"),
				UnitPrice: pointer.Get(*s.retail2Info.RetailPrice),
				LineTaxes: []retailjavapb.ComMoegoServerRetailServiceParamsOrderLineTaxParams{
					{
						TaxId:   pointer.Get(int64(*s.retail2Info.TaxId)),
						TaxRate: pointer.Get(*s.retail2Info.TaxRate),
					},
				},
			},
		},
		CheckRefund: pointer.Get(true),
	})

	return appointmentID
}

/*
desc：构建 通用的支付函数 参数
*/
func (s *NewInvoiceBeforeRefundTestSuite) commonStripeTakePaymentForTest(amount float32, invoiceId int32, tipsAmount float32) *paymentmodel.ComMoegoCommonDtoPaymentSummaryPaymentDto {

	// 定义局部变量（不存储到测试套件字段中）
	var paymentResult *paymentmodel.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// 使用 defer 在方法返回前清理
	defer func() {
		// 清理逻辑
		paymentResult = nil
		// 其他需要清理的变量...
	}()

	paymentResult = s.StripeTakePayment(&paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                amount,
		TipsAmount:            proto.Float32(tipsAmount),
		CardNumber:            proto.String("1111"),
		CardType:              proto.String("Visa"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             invoiceId,
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_visa"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	return paymentResult
}

func TestNewInvoiceRefund(t *testing.T) {
	suite.Run(t, new(NewInvoiceBeforeRefundTestSuite))
}

/*
desc：构建 PreviewRefundOrderPayments 参数
*/
func buildPreviewRefundOrderPaymentsParams(
	refundAmount *money.Money,
	isConvenienceFeeIncluded bool,
	orderPaymentId, invoiceId int64,
	companyId, businessId, customerId, staffId int64,
	paymentMethodId int64,
	paymentMethod, paymentMethodDisplayName, paymentMethodVendor, paidBy string,
	isOnline, isDeposit bool,
	totalAmount *money.Money,
	amount *money.Money,
	refundedAmount *money.Money,
	processingFee *money.Money,
	paymentTips *money.Money,
	paymentTipsBeforeCreate *money.Money,
	paymentTipsAfterCreate *money.Money,
	paymentConvenienceFee *money.Money,
	refundedPaymentConvenienceFee *money.Money,
	paymentStatus orderpb.OrderPaymentStatus,
) *orderapipb.PreviewRefundOrderPaymentsParams {
	return &orderapipb.PreviewRefundOrderPaymentsParams{
		RefundAmount: refundAmount,
		RefundAmountFlag: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
			IsConvenienceFeeIncluded: isConvenienceFeeIncluded,
		},
		SourceOrderPayments: []*orderpb.OrderPaymentModel{
			{
				Id:                            orderPaymentId,
				OrderId:                       invoiceId,
				CompanyId:                     companyId,
				BusinessId:                    businessId,
				CustomerId:                    customerId,
				StaffId:                       staffId,
				PaymentMethodId:               paymentMethodId,
				PaymentMethod:                 paymentMethod,
				PaymentMethodDisplayName:      paymentMethodDisplayName,
				PaymentMethodVendor:           paymentMethodVendor,
				IsOnline:                      isOnline,
				IsDeposit:                     isDeposit,
				PaidBy:                        paidBy,
				TotalAmount:                   totalAmount,
				Amount:                        amount,
				RefundedAmount:                refundedAmount,
				ProcessingFee:                 processingFee,
				PaymentTips:                   paymentTips,
				PaymentTipsBeforeCreate:       paymentTipsBeforeCreate,
				PaymentTipsAfterCreate:        paymentTipsAfterCreate,
				PaymentConvenienceFee:         paymentConvenienceFee,
				RefundedPaymentConvenienceFee: refundedPaymentConvenienceFee,
				PaymentStatus:                 paymentStatus,
			},
		},
	}
}

/*
desc：refund items 校验
*/
func (s *NewInvoiceBeforeRefundTestSuite) checkPreviewRefundOrderData(result *orderapipb.PreviewRefundOrderResult, expectedValues *expectedValues) {
	// check refundable tips
	s.Require().EqualValues(expectedValues.RefundableTips.ExpectedNanos, result.RefundableTips.Nanos)
	s.Require().EqualValues(expectedValues.RefundableTips.ExpectedUnits, strconv.FormatInt(result.RefundableTips.Units, 10))

	// check refund flags
	s.Require().EqualValues(expectedValues.RefundFlags.CanCombineOrderPayments, result.RefundFlags.CanCombineOrderPayments)

	// check refund order preview
	// RefundItemSubTotal
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundItemSubTotal.ExpectedNanos, result.RefundOrderPreview.RefundOrderDetail.RefundOrder.RefundItemSubTotal.Nanos)
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundItemSubTotal.ExpectedUnits, strconv.FormatInt(result.RefundOrderPreview.RefundOrderDetail.RefundOrder.RefundItemSubTotal.Units, 10))
	// RefundItemDiscountTotal
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundItemDiscountTotal.ExpectedNanos, result.RefundOrderPreview.RefundOrderDetail.RefundOrder.RefundItemDiscountTotal.Nanos)
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundItemDiscountTotal.ExpectedUnits, strconv.FormatInt(result.RefundOrderPreview.RefundOrderDetail.RefundOrder.RefundItemDiscountTotal.Units, 10))
	// refund tips
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundTips.ExpectedNanos, result.RefundOrderPreview.RefundOrderDetail.RefundOrder.RefundTips.Nanos)
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundTips.ExpectedUnits, strconv.FormatInt(result.RefundOrderPreview.RefundOrderDetail.RefundOrder.RefundTips.Units, 10))
	// refund tax
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundTaxFee.ExpectedNanos, result.RefundOrderPreview.RefundOrderDetail.RefundOrder.RefundTaxFee.Nanos)
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundTaxFee.ExpectedUnits, strconv.FormatInt(result.RefundOrderPreview.RefundOrderDetail.RefundOrder.RefundTaxFee.Units, 10))

	if expectedValues.RefundOrderPreview.Product.ExpectedUnits != "nullstring" {
		s.Require().EqualValues(expectedValues.RefundOrderPreview.Product.ExpectedNanos, result.OrderDetail.SubtotalByItemType["product"].Nanos)
		s.Require().EqualValues(expectedValues.RefundOrderPreview.Product.ExpectedUnits, strconv.FormatInt(result.OrderDetail.SubtotalByItemType["product"].Units, 10))
	}

	if expectedValues.RefundOrderPreview.ServiceCharge.ExpectedUnits != "nullstring" {
		s.Require().EqualValues(expectedValues.RefundOrderPreview.ServiceCharge.ExpectedNanos, result.OrderDetail.SubtotalByItemType["service_charge"].Nanos)
		s.Require().EqualValues(expectedValues.RefundOrderPreview.ServiceCharge.ExpectedUnits, strconv.FormatInt(result.OrderDetail.SubtotalByItemType["service_charge"].Units, 10))
	}

	if expectedValues.RefundOrderPreview.Service.ExpectedUnits != "nullstring" {
		s.Require().EqualValues(expectedValues.RefundOrderPreview.Service.ExpectedNanos, result.OrderDetail.SubtotalByItemType["service"].Nanos)
		s.Require().EqualValues(expectedValues.RefundOrderPreview.Service.ExpectedUnits, strconv.FormatInt(result.OrderDetail.SubtotalByItemType["service"].Units, 10))
	}

	// check refundable order payments
	s.Require().EqualValues(expectedValues.RefundableOrderPayments.ExpectedNanos, result.RefundableOrderPayments[0].RefundableAmount.Nanos)
	s.Require().EqualValues(expectedValues.RefundableOrderPayments.ExpectedUnits, strconv.FormatInt(result.RefundableOrderPayments[0].RefundableAmount.Units, 10))

}

/*
desc：fee and total refund amount 校验
*/
func (s *NewInvoiceBeforeRefundTestSuite) checkPreviewRefundOrderPaymentsData(result *orderapipb.PreviewRefundOrderPaymentsResult, expectedValues *expectedValues) {
	// refundableConvenienceFee
	s.Require().EqualValues(expectedValues.RefundableConvenienceFee.ExpectedNanos, result.RefundConvenienceFee.Nanos)
	s.Require().EqualValues(expectedValues.RefundableConvenienceFee.ExpectedUnits, strconv.FormatInt(result.RefundConvenienceFee.Units, 10))

	// refundTotalAmount
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundTotalAmount.ExpectedNanos, result.RefundOrderPayments[0].RefundTotalAmount.Nanos)
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundTotalAmount.ExpectedUnits, strconv.FormatInt(result.RefundOrderPayments[0].RefundTotalAmount.Units, 10))

}

/*
desc：fee and total refund amount 校验
*/
func (s *NewInvoiceBeforeRefundTestSuite) checkPreviewRefundOrderFeeData(result *orderapipb.PreviewRefundOrderResult, expectedValues *expectedValues) {
	// refundableConvenienceFee
	s.Require().EqualValues(expectedValues.RefundableConvenienceFee.ExpectedNanos, result.RefundableConvenienceFee.Nanos)
	s.Require().EqualValues(expectedValues.RefundableConvenienceFee.ExpectedUnits, strconv.FormatInt(result.RefundableConvenienceFee.Units, 10))

	// refundTotalAmount
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundTotalAmount.ExpectedNanos, result.RefundOrderPreview.RefundOrderDetail.RefundOrder.RefundTotalAmount.Nanos)
	s.Require().EqualValues(expectedValues.RefundOrderPreview.RefundTotalAmount.ExpectedUnits, strconv.FormatInt(int64(result.RefundOrderPreview.RefundOrderDetail.RefundOrder.RefundTotalAmount.Units), 10))

}

/*
author:<EMAIL>
desc：stripe 超额支付10USD 后 关单前  payment only 退款，

	模拟从order detail page里面退款
*/
func (s *NewInvoiceBeforeRefundTestSuite) TestNewInvoiceOverPaidBeforeCompleteRefund() {
	// 前置设置 创建 appt 然后将 中间变量 set 到 appt 里面
	appointmentId := s.createApptAndOrderWithOnceTest(s.Service)
	s.apptId = appointmentId

	// 根据 appt id 获取 appt 信息 和 invoice id
	appintmentInfo := s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentId,
	})
	invoiceId := appintmentInfo.GetInvoice().InvoiceId

	// 支付
	s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance+10.0), int32(invoiceId), float32(0.0))
	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。

	// 关单前退款
	paymentListResult := s.GetPaymentListByCustomerId(int32(s.CustomerID), 1, 100)
	var orderPaymentId int64
	var currencyRefundAmount float32 = 10.0
	var totalAmountValue float32
	for _, p := range paymentListResult.PaymentList {
		if p.InvoiceId != nil && *p.InvoiceId == int32(invoiceId) {
			orderPaymentId = *p.OrderPaymentId
			totalAmountValue = *p.Amount
		}
	}

	refundMoney := moneyutil.FromFloat(float64(currencyRefundAmount), "USD")

	//PreviewRefundOrder API 调用
	params := &orderapipb.PreviewRefundOrderParams{
		OrderId:    invoiceId,
		RefundMode: orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.PreviewRefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				RefundAmount: refundMoney,
			},
		},
	}
	previewRefundOrderResult := s.PreviewRefundOrder(params)

	// PreviewRefundOrderPayments API 调用
	params1 := buildPreviewRefundOrderPaymentsParams(
		moneyutil.FromFloat(10.0, "USD"), // RefundAmount
		false,                            // IsConvenienceFeeIncluded
		orderPaymentId, invoiceId,        // orderPaymentId, invoiceId
		s.GetAuthInfo().CompanyID, s.GetAuthInfo().BusinessID, int64(s.CustomerID), s.GetAuthInfo().StaffID, // companyId, businessId, customerId, staffId
		1,                              // paymentMethodId ；case 过程参数 ，允许写死
		"Credit card",                  // paymentMethod ；case 过程参数 ，允许写死
		"Credit card - card4242(visa)", // paymentMethodDisplayName ；case 过程参数 ，允许写死
		"Stripe",                       // paymentMethodVendor ；case 过程参数 ，允许写死
		"API test",                     // paidBy ；case 过程参数 ，允许写死
		false, false,                   // isOnline, isDeposit ；case 过程参数 ，允许写死
		moneyutil.FromFloat(float64(totalAmountValue), "USD"),                           // totalAmount
		moneyutil.FromFloat(appintmentInfo.GetInvoice().OutstandingBalance+10.0, "USD"), // amount
		moneyutil.FromFloat(0.0, "USD"),                                                 // refundedAmount ；case 特殊参数 ，允许写死
		moneyutil.FromFloat(4.44, "USD"),                                                // processingFee ；case 特殊参数 ，允许写死
		moneyutil.FromFloat(0.0, "USD"),                                                 // paymentTips ；case 特殊参数 ，允许写死
		moneyutil.FromFloat(0.0, "USD"),                                                 // paymentTipsBeforeCreate ；case 特殊参数 ，允许写死
		moneyutil.FromFloat(-1.0, "USD"),                                                // paymentTipsAfterCreate ；case 特殊参数 ，允许写死
		moneyutil.FromFloat(4.44, "USD"),                                                // paymentConvenienceFee ；case 特殊参数 ，允许写死
		moneyutil.FromFloat(0.0, "USD"),                                                 // refundedPaymentConvenienceFee ；case 特殊参数 ，允许写死
		300,                                                                             // paymentStatus
	)

	previewRefundOrderPaymentsResult := s.PreviewRefundOrderPayments(params1)

	// 声明期望结果
	expectedValues := getExpectedValues(
		"0", 0, // refundableTips
		false, false, // refundFlags
		"0", *********, // refundableConvenienceFee
		"10", *********, // refundTotalAmount
		"0", 0, // refundItemSubTotal
		"0", 0, // refundItemDiscountTotal
		"0", 0, // refundTips
		"0", *********, // refundConvenienceFee
		"0", 0, // refundTaxFee
		"58", *********, // product
		"29", *********, // serviceCharge
		"9", *********, // service
		"121", *********, // refundableOrderPayments
	)

	// 期望结果与preview 接口核对
	s.checkPreviewRefundOrderData(previewRefundOrderResult, &expectedValues)
	s.checkPreviewRefundOrderPaymentsData(previewRefundOrderPaymentsResult, &expectedValues)

	// 发起最终退款
	paramsRealRefund := &orderapipb.RefundOrderParams{
		OrderId:      invoiceId,
		RefundMode:   orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		RefundReason: "",
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.RefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmount:    refundMoney,
			},
		},
	}

	s.RefundOrder(paramsRealRefund)
}

/*
author:<EMAIL>
desc：stripe 全额支付 后 关单前  payment only 退款

	关单前 refund by payment && fully paid，单纯退款10USD, 10USD  不包含 CV FEE, CV FEE 需要额外计算"
	is_convenience_fee_included 为 false
	这种场景实际上不存在，只需要计算这个钱算的是对就可以了
*/
func (s *NewInvoiceBeforeRefundTestSuite) TestNewInvoiceFullyPaidBeforeCompleteRefundWithNotIncludedFee() {
	// 前置设置 创建 appt 然后将 中间变量 set 到 appt 里面
	appointmentId := s.createApptAndOrderWithOnceTest(s.Service)

	// 根据 appt id 获取 appt 信息 和 invoice id
	appintmentInfo := s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentId,
	})
	s.apptId = appointmentId
	invoiceId := appintmentInfo.GetInvoice().InvoiceId

	// 支付
	s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance), int32(invoiceId), float32(0.0))
	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。

	// 关单前退款
	paymentListResult := s.GetPaymentListByCustomerId(int32(s.CustomerID), 1, 100)
	var orderPaymentId int64
	var currencyRefundAmount float32 = 10.0
	// var totalAmountValue float32
	for _, p := range paymentListResult.PaymentList {
		if p.InvoiceId != nil && *p.InvoiceId == int32(invoiceId) {
			orderPaymentId = *p.OrderPaymentId
			// totalAmountValue = *p.Amount
		}
	}

	refundMoney := moneyutil.FromFloat(float64(currencyRefundAmount), "USD")

	// PreviewRefundOrder API 调用
	params := &orderapipb.PreviewRefundOrderParams{
		OrderId:    invoiceId,
		RefundMode: orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.PreviewRefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				RefundAmount: refundMoney,
			},
		},
	}
	previewRefundOrderResult := s.PreviewRefundOrder(params)

	// 声明期望结果
	expectedValues := getExpectedValues(
		"0", 0, // refundableTips ✅
		false, false, // refundFlags ✅
		"0", *********, // refundableConvenienceFee 【PreviewRefundOrderPayments】
		"10", *********, // refundTotalAmount  【PreviewRefundOrderPayments】
		"0", 0, // refundItemSubTotal ✅
		"0", 0, // refundItemDiscountTotal ✅
		"0", 0, // refundTips ✅
		"0", *********, // refundConvenienceFee 【PreviewRefundOrderPayments】
		"0", 0, // refundTaxFee ✅
		"58", *********, // product ✅
		"29", *********, // serviceCharge ✅
		"9", *********, // service ✅
		"111", *********, // refundableOrderPayments ✅
	)

	// 期望结果与preview 接口核对
	s.checkPreviewRefundOrderData(previewRefundOrderResult, &expectedValues)
	s.checkPreviewRefundOrderFeeData(previewRefundOrderResult, &expectedValues)

	// 发起最终退款
	paramsRealRefund := &orderapipb.RefundOrderParams{
		OrderId:      invoiceId,
		RefundMode:   orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		RefundReason: "",
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.RefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmount:    refundMoney,
			},
		},
	}

	s.RefundOrder(paramsRealRefund)
}

/*
author:<EMAIL>
desc：stripe 全额支付 后 关单前  payment only 退款

	关单前 refund by payment && fully paid
	单纯退款10USD, 10USD 包含 CV FEE，is_convenience_fee_included 为 true
	这种场景一定会存在，不管是要计算钱，还需要关注关单前后的状态是否正确
*/
func (s *NewInvoiceBeforeRefundTestSuite) TestNewInvoiceFullyPaidBeforeCompleteRefundWithIncludedFee() {
	// 前置设置 创建 appt 然后将 中间变量 set 到 appt 里面
	appointmentId := s.createApptAndOrderWithOnceTest(s.Service)
	s.apptId = appointmentId

	// 根据 appt id 获取 appt 信息 和 invoice id
	appintmentInfo := s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentId,
	})
	invoiceId := appintmentInfo.GetInvoice().InvoiceId
	// 支付
	s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance), int32(invoiceId), float32(0.0))
	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。

	// 关单前退款
	paymentListResult := s.GetPaymentListByCustomerId(int32(s.CustomerID), 1, 100)
	var orderPaymentId int64
	var currencyRefundAmount float32 = 10.0
	// var totalAmountValue float32
	for _, p := range paymentListResult.PaymentList {
		if p.InvoiceId != nil && *p.InvoiceId == int32(invoiceId) {
			orderPaymentId = *p.OrderPaymentId
			// totalAmountValue = *p.Amount
		}
	}

	refundMoney := moneyutil.FromFloat(float64(currencyRefundAmount), "USD")

	// previewRefundOrder API 调用
	params := &orderapipb.PreviewRefundOrderParams{
		OrderId:    invoiceId,
		RefundMode: orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.PreviewRefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: true,
				},
				RefundAmount: refundMoney,
			},
		},
	}
	previewRefundOrderResult := s.PreviewRefundOrder(params)

	// 声明期望结果
	expectedValues := getExpectedValues(
		"0", 0, // refundableTips ✅
		false, false, // refundFlags ✅
		"0", *********, // refundableConvenienceFee
		"10", 0, // refundTotalAmount
		"0", 0, // refundItemSubTotal ✅
		"0", 0, // refundItemDiscountTotal ✅
		"0", 0, // refundTips ✅
		"0", *********, // refundConvenienceFee
		"0", 0, // refundTaxFee ✅
		"58", *********, // product ✅
		"29", *********, // serviceCharge ✅
		"9", *********, // service ✅
		"111", *********, // refundableOrderPayments ✅
	)
	// 期望结果与preview 接口核对
	s.checkPreviewRefundOrderData(previewRefundOrderResult, &expectedValues)
	s.checkPreviewRefundOrderFeeData(previewRefundOrderResult, &expectedValues)

	// 发起最终退款
	paramsRealRefund := &orderapipb.RefundOrderParams{
		OrderId:      invoiceId,
		RefundMode:   orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		RefundReason: "",
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.RefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmount:    refundMoney,
			},
		},
	}

	s.RefundOrder(paramsRealRefund)
}

/*
author:<EMAIL>
desc：stripe 部分支付 后 关单前  payment only 退款

	关单前 refund by payment && fully paid，编辑items里面退款"
	"减少tips，is_convenience_fee_included 为 false
*/
func (s *NewInvoiceBeforeRefundTestSuite) TestNewInvoiceFullyPaidEditTipsBeforeCompleteRefund() {
	// 前置设置 创建 appt 然后将 中间变量 set 到 appt 里面
	appointmentId := s.createApptAndOrderWithOnceTest(s.Service)
	s.apptId = appointmentId

	// 根据 appt id 获取 appt 信息 和 invoice id
	appintmentInfo := s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentId,
	})
	invoiceId := appintmentInfo.GetInvoice().InvoiceId
	// 支付
	s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance-0.01), int32(invoiceId), float32(0.0))
	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。
	// 关单前退款

	// 设置当前的 tips 减半
	var targetRefundAmount float32 = 5.0
	s.SetTips(&orderapipb.SetTipsRequest{
		CheckRefund: pointer.Get(false),
		InvoiceId:   int64(invoiceId),
		OmitResult:  *pointer.Get(false),
		Value:       float64(targetRefundAmount),
		ValueType:   "amount",
	})

	// 获取当前appt 被编辑后的现状
	AfterlistOrderDetailsRes := s.ListOrderDetailsV2(int32(invoiceId))

	// 获取order payment id
	paymentListResult := s.GetPaymentListByCustomerId(int32(s.CustomerID), 1, 100)
	var orderPaymentId int64

	for _, p := range paymentListResult.PaymentList {
		if p.InvoiceId != nil && *p.InvoiceId == int32(invoiceId) {
			orderPaymentId = *p.OrderPaymentId
		}
	}

	// 设置当前需要退款的金额
	rawStr := fmt.Sprintf("%.2f", *AfterlistOrderDetailsRes.RemainAmountV2) // 你只关心 2 位小数
	amount, err := decimal.NewFromString(rawStr)
	s.Require().Nil(err)

	targetRefundMoney := moneyutil.FromDecimal(amount.Neg(), "USD")

	//previewRefundOrder 接口调用
	previewRefundOrderParams := &orderapipb.PreviewRefundOrderParams{
		OrderId:    invoiceId,
		RefundMode: orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.PreviewRefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				RefundAmount: targetRefundMoney,
			},
		},
	}
	previewRefundOrderResult := s.PreviewRefundOrder(previewRefundOrderParams)

	//previewRefundOrderPayments 接口调用
	params1 := buildPreviewRefundOrderPaymentsParams(
		targetRefundMoney,              // RefundAmount
		false,                          // IsConvenienceFeeIncluded
		orderPaymentId,                 // orderPaymentId
		invoiceId,                      // invoiceId
		s.GetAuthInfo().CompanyID,      // companyId
		s.GetAuthInfo().BusinessID,     // businessId
		int64(s.CustomerID),            // customerId
		s.GetAuthInfo().StaffID,        // staffId
		1,                              // paymentMethodId ；case 过程参数 ，允许写死
		"Credit card",                  // paymentMethod ；case 过程参数 ，允许写死
		"Credit card - card4242(visa)", // paymentMethodDisplayName ；case 过程参数 ，允许写死
		"Stripe",                       // paymentMethodVendor ；case 过程参数 ，允许写死
		"API test",                     // paidBy ；case 过程参数 ，允许写死
		false,                          // isOnline ；case 过程参数 ，允许写死
		false,                          // isDeposit  ；case 过程参数 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.TotalCollected), "USD"),                                          // totalAmount
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.TotalCollected-*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // amount
		moneyutil.FromFloat(0.0, "USD"),                                               // refundedAmount  case 固定为0 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // processingFee
		moneyutil.FromFloat(0.0, "USD"),                                               // paymentTips  case 固定为0 ，允许写死
		moneyutil.FromFloat(0.0, "USD"),                                               // paymentTipsBeforeCreate  case 固定为0 ，允许写死
		moneyutil.FromFloat(-1.0, "USD"),                                              // paymentTipsAfterCreate  case 固定为-1 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // paymentConvenienceFee
		moneyutil.FromFloat(0.0, "USD"),                                               // refundedPaymentConvenienceFee  case 固定为0 ，允许写死
		orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,                          // paymentStatus = 300
	)

	previewRefundOrderPaymentsResult := s.PreviewRefundOrderPayments(params1)

	// 声明期望结果
	expectedValues := getExpectedValues(
		"0", 0, // refundableTips
		false, false, // refundFlags
		"0", *********, // refundableConvenienceFee
		"5", *********, // refundTotalAmount
		"0", 0, // refundItemSubTotal
		"0", 0, // refundItemDiscountTotal
		"0", 0, // refundTips
		"0", *********, // refundConvenienceFee
		"0", 0, // refundTaxFee
		"58", *********, // product
		"29", *********, // serviceCharge
		"9", *********, // service
		"111", *********, // refundableOrderPayments
	)

	// 期望结果与preview 接口核对
	s.checkPreviewRefundOrderData(previewRefundOrderResult, &expectedValues)
	s.checkPreviewRefundOrderPaymentsData(previewRefundOrderPaymentsResult, &expectedValues)

	// 发起最终退款
	paramsRealRefund := &orderapipb.RefundOrderParams{
		OrderId:      invoiceId,
		RefundMode:   orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		RefundReason: "",
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.RefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmount:    targetRefundMoney,
			},
		},
	}

	s.RefundOrder(paramsRealRefund)
}

/*
author:<EMAIL>
desc：stripe 部分支付 后 关单前  payment only 退款

	关单前 refund by payment && fully paid，编辑items里面退款"
	"减少tips，is_convenience_fee_included 为 false
*/
func (s *NewInvoiceBeforeRefundTestSuite) TestNewInvoiceFullyPaidEditRetailsPriceBeforeCompleteRefund() {
	// 前置设置 创建 appt 然后将 中间变量 set 到 appt 里面
	appointmentId := s.createApptAndOrderWithOnceTest(s.Service)
	s.apptId = appointmentId

	// 根据 appt id 获取 appt 信息 和 invoice id
	appintmentInfo := s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentId,
	})
	invoiceId := appintmentInfo.GetInvoice().InvoiceId
	// 支付
	s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance-0.01), int32(invoiceId), float32(0.0))
	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。

	// 关单前退款 主流程
	// 设置当前的 retails price 减半
	beforeListOrderDetailsRes := s.ListOrderDetailsV2(int32(invoiceId))

	for _, item := range beforeListOrderDetailsRes.Items {
		updateRetail := &retailjavapb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
			OrderId: invoiceId,
			RetailItems: []retailjavapb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
				{
					ObjectId:    pointer.Get(int64(*item.ObjectId)),
					Type:        item.Type,
					Name:        item.Name,
					StaffId:     &s.GetAuthInfo().StaffID,
					UnitPrice:   pointer.Get(*item.UnitPrice / float32(2)),
					Quantity:    item.Quantity,                                                        // 固定数量
					LineTaxes:   []retailjavapb.ComMoegoServerRetailServiceParamsOrderLineTaxParams{}, // 空税
					OrderItemId: pointer.Get(int64(*item.Id)),                                         // 每项自己的 orderItemId
				},
			},
			CheckRefund: pointer.Get(false),
		}

		s.PutRetailsItemsOnOrder(updateRetail)
	}
	// 获取当前appt 被编辑后的现状
	AfterlistOrderDetailsRes := s.ListOrderDetailsV2(int32(invoiceId))

	// 获取order payment id
	paymentListResult := s.GetPaymentListByCustomerId(int32(s.CustomerID), 1, 100)
	var orderPaymentId int64

	for _, p := range paymentListResult.PaymentList {
		if p.InvoiceId != nil && *p.InvoiceId == int32(invoiceId) {
			orderPaymentId = *p.OrderPaymentId
		}
	}
	// 设置当前需要退款的金额
	rawStr := fmt.Sprintf("%.2f", *AfterlistOrderDetailsRes.RemainAmountV2) // 你只关心 2 位小数
	amount, err := decimal.NewFromString(rawStr)
	s.Require().Nil(err)

	targetRefundMoney := moneyutil.FromDecimal(amount.Neg(), "USD")

	//previewRefundOrder 接口调用
	previewRefundOrderParams := &orderapipb.PreviewRefundOrderParams{
		OrderId:    invoiceId,
		RefundMode: orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.PreviewRefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				RefundAmount: targetRefundMoney,
			},
		},
	}

	previewRefundOrderResult := s.PreviewRefundOrder(previewRefundOrderParams)

	//previewRefundOrderPayments 接口调用
	previewRefundOrderPaymentsParams := buildPreviewRefundOrderPaymentsParams(
		targetRefundMoney,              // RefundAmount
		false,                          // IsConvenienceFeeIncluded
		orderPaymentId,                 // orderPaymentId
		invoiceId,                      // invoiceId
		s.GetAuthInfo().CompanyID,      // companyId
		s.GetAuthInfo().BusinessID,     // businessId
		int64(s.CustomerID),            // customerId
		s.GetAuthInfo().StaffID,        // staffId
		1,                              // paymentMethodId ；case 过程参数 ，允许写死
		"Credit card",                  // paymentMethod ；case 过程参数 ，允许写死
		"Credit card - card4242(visa)", // paymentMethodDisplayName ；case 过程参数 ，允许写死
		"Stripe",                       // paymentMethodVendor ；case 过程参数 ，允许写死
		"API test",                     // paidBy ；case 过程参数 ，允许写死
		false,                          // isOnline ；case 过程参数 ，允许写死
		false,                          // isDeposit  ；case 过程参数 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.TotalCollected), "USD"),                                          // totalAmount
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.TotalCollected-*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // amount
		moneyutil.FromFloat(0.0, "USD"),                                               // refundedAmount  case 固定为0 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // processingFee
		moneyutil.FromFloat(0.0, "USD"),                                               // paymentTips  case 固定为0 ，允许写死
		moneyutil.FromFloat(0.0, "USD"),                                               // paymentTipsBeforeCreate  case 固定为0 ，允许写死
		moneyutil.FromFloat(-1.0, "USD"),                                              // paymentTipsAfterCreate  case 固定为-1 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // paymentConvenienceFee
		moneyutil.FromFloat(0.0, "USD"),                                               // refundedPaymentConvenienceFee  case 固定为0 ，允许写死
		orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,                          // paymentStatus = 300
	)

	previewRefundOrderPaymentsResult := s.PreviewRefundOrderPayments(previewRefundOrderPaymentsParams)

	// 声明期望结果
	expectedValues := getExpectedValues(
		"0", 0, // refundableTips
		false, false, // refundFlags
		"1", *********, // refundableConvenienceFee
		"30", *********, // refundTotalAmount
		"0", 0, // refundItemSubTotal
		"0", 0, // refundItemDiscountTotal
		"0", 0, // refundTips
		"1", *********, // refundConvenienceFee
		"0", 0, // refundTaxFee
		"29", *********, // product
		"29", *********, // serviceCharge
		"9", *********, // service
		"111", *********, // refundableOrderPayments
	)

	// 期望结果与preview 接口核对
	s.checkPreviewRefundOrderData(previewRefundOrderResult, &expectedValues)
	s.checkPreviewRefundOrderPaymentsData(previewRefundOrderPaymentsResult, &expectedValues)

	// 发起最终退款
	paramsRealRefund := &orderapipb.RefundOrderParams{
		OrderId:      invoiceId,
		RefundMode:   orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		RefundReason: "",
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.RefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmount:    targetRefundMoney,
			},
		},
	}

	s.RefundOrder(paramsRealRefund)
}

/*
author:<EMAIL>
desc：stripe 部分支付 后 关单前  payment only 退款

	删除所有的service charges
	关单前 refund by payment && fully paid，编辑items里面退款
	is_convenience_fee_included 为 false
*/
func (s *NewInvoiceBeforeRefundTestSuite) TestNewInvoiceFullyPaidDeleteServiceChargesBeforeCompleteRefund() {
	// 前置设置 创建 appt 然后将 中间变量 set 到 appt 里面
	appointmentId := s.createApptAndOrderWithOnceTest(s.Service)
	s.apptId = appointmentId

	// 根据 appt id 获取 appt 信息 和 invoice id
	appintmentInfo := s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentId,
	})
	invoiceId := appintmentInfo.GetInvoice().InvoiceId
	// 支付
	s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance-0.01), int32(invoiceId), float32(0.0))
	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。

	// 关单前退款 主流程
	// 删除所有的service charges
	for _, id := range s.ServiceChargeIDs {
		params := &orderapipb.AddOrRemoveServiceChargeRequest{
			OrderId:         invoiceId,
			ServiceChargeId: []int64{*id}, // 解引用
		}
		s.RemoveServiceChargeFromOrder(params)
	}

	// 获取当前appt 被编辑后的现状
	AfterlistOrderDetailsRes := s.ListOrderDetailsV2(int32(invoiceId))

	// 获取order payment id
	paymentListResult := s.GetPaymentListByCustomerId(int32(s.CustomerID), 1, 100)
	var orderPaymentId int64

	for _, p := range paymentListResult.PaymentList {
		if p.InvoiceId != nil && *p.InvoiceId == int32(invoiceId) {
			orderPaymentId = *p.OrderPaymentId
		}
	}
	// 设置当前需要退款的金额
	rawStr := fmt.Sprintf("%.2f", *AfterlistOrderDetailsRes.RemainAmountV2) // 你只关心 2 位小数
	amount, err := decimal.NewFromString(rawStr)
	s.Require().Nil(err)

	targetRefundMoney := moneyutil.FromDecimal(amount.Neg(), "USD")

	//previewRefundOrder 接口调用
	previewRefundOrderParams := &orderapipb.PreviewRefundOrderParams{
		OrderId:    invoiceId,
		RefundMode: orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.PreviewRefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				RefundAmount: targetRefundMoney,
			},
		},
	}

	previewRefundOrderResult := s.PreviewRefundOrder(previewRefundOrderParams)

	//previewRefundOrderPayments 接口调用
	previewRefundOrderPaymentsParams := buildPreviewRefundOrderPaymentsParams(
		targetRefundMoney,              // RefundAmount
		false,                          // IsConvenienceFeeIncluded
		orderPaymentId,                 // orderPaymentId
		invoiceId,                      // invoiceId
		s.GetAuthInfo().CompanyID,      // companyId
		s.GetAuthInfo().BusinessID,     // businessId
		int64(s.CustomerID),            // customerId
		s.GetAuthInfo().StaffID,        // staffId
		1,                              // paymentMethodId ；case 过程参数 ，允许写死
		"Credit card",                  // paymentMethod ；case 过程参数 ，允许写死
		"Credit card - card4242(visa)", // paymentMethodDisplayName ；case 过程参数 ，允许写死
		"Stripe",                       // paymentMethodVendor ；case 过程参数 ，允许写死
		"API test",                     // paidBy ；case 过程参数 ，允许写死
		false,                          // isOnline ；case 过程参数 ，允许写死
		false,                          // isDeposit  ；case 过程参数 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.TotalCollected), "USD"),                                          // totalAmount
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.TotalCollected-*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // amount
		moneyutil.FromFloat(0.0, "USD"),                                               // refundedAmount  case 固定为0 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // processingFee
		moneyutil.FromFloat(0.0, "USD"),                                               // paymentTips  case 固定为0 ，允许写死
		moneyutil.FromFloat(0.0, "USD"),                                               // paymentTipsBeforeCreate  case 固定为0 ，允许写死
		moneyutil.FromFloat(-1.0, "USD"),                                              // paymentTipsAfterCreate  case 固定为-1 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // paymentConvenienceFee
		moneyutil.FromFloat(0.0, "USD"),                                               // refundedPaymentConvenienceFee  case 固定为0 ，允许写死
		orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,                          // paymentStatus = 300
	)

	previewRefundOrderPaymentsResult := s.PreviewRefundOrderPayments(previewRefundOrderPaymentsParams)

	// 声明期望结果
	expectedValues := getExpectedValues(
		"0", 0, // refundableTips
		false, false, // refundFlags
		"1", *********, // refundableConvenienceFee
		"30", *********, // refundTotalAmount
		"0", 0, // refundItemSubTotal
		"0", 0, // refundItemDiscountTotal
		"0", 0, // refundTips
		"1", *********, // refundConvenienceFee
		"0", 0, // refundTaxFee
		"58", *********, // product
		"nullstring", *********, // serviceCharge 不参与校验
		"9", *********, // service
		"111", *********, // refundableOrderPayments
	)

	// 期望结果与preview 接口核对
	s.checkPreviewRefundOrderData(previewRefundOrderResult, &expectedValues)
	s.checkPreviewRefundOrderPaymentsData(previewRefundOrderPaymentsResult, &expectedValues)

	// 发起最终退款
	paramsRealRefund := &orderapipb.RefundOrderParams{
		OrderId:      invoiceId,
		RefundMode:   orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		RefundReason: "",
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.RefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmount:    targetRefundMoney,
			},
		},
	}

	s.RefundOrder(paramsRealRefund)
}

/*
author:<EMAIL>
desc：stripe 部分支付 后 关单前  payment only 退款

	增加 11.11 的discount code
	关单前 refund by payment && fully paid，编辑items里面退款
	is_convenience_fee_included 为 false
*/
func (s *NewInvoiceBeforeRefundTestSuite) TestNewInvoiceFullyPaidAddDiscountCodeBeforeCompleteRefund() {
	// 前置设置 创建 appt 然后将 中间变量 set 到 appt 里面
	appointmentId := s.createApptAndOrderWithOnceTest(s.Service)
	s.apptId = appointmentId

	// 根据 appt id 获取 appt 信息 和 invoice id
	appintmentInfo := s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentId,
	})
	invoiceId := appintmentInfo.GetInvoice().InvoiceId
	// 支付
	s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance-0.01), int32(invoiceId), float32(0.0))
	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。

	// 关单前退款 主流程
	// 添加 11.11 USD 的 discount code
	s.SetDiscount(&retailjavapb.ComMoegoServerRetailServiceParamsSetDiscountParams{
		CheckRefund: pointer.Get(false),
		OrderId:     invoiceId,
		LineDiscounts: []retailjavapb.ComMoegoServerRetailServiceParamsOrderLineDiscountParams{
			{
				ApplyType:      pointer.Get("all"),
				DiscountType:   pointer.Get("amount"),
				DiscountAmount: pointer.Get(*proto.Float32(11.11)),
				DiscountRate:   pointer.Get(float32(0)),
			},
		},
	})

	// 获取当前appt 被编辑后的现状
	AfterlistOrderDetailsRes := s.ListOrderDetailsV2(int32(invoiceId))

	// 获取order payment id
	paymentListResult := s.GetPaymentListByCustomerId(int32(s.CustomerID), 1, 100)
	var orderPaymentId int64

	for _, p := range paymentListResult.PaymentList {
		if p.InvoiceId != nil && *p.InvoiceId == int32(invoiceId) {
			orderPaymentId = *p.OrderPaymentId
		}
	}
	// 设置当前需要退款的金额
	rawStr := fmt.Sprintf("%.2f", *AfterlistOrderDetailsRes.RemainAmountV2) // 你只关心 2 位小数
	amount, err := decimal.NewFromString(rawStr)
	s.Require().Nil(err)

	targetRefundMoney := moneyutil.FromDecimal(amount.Neg(), "USD")

	//previewRefundOrder 接口调用
	previewRefundOrderParams := &orderapipb.PreviewRefundOrderParams{
		OrderId:    invoiceId,
		RefundMode: orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.PreviewRefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				RefundAmount: targetRefundMoney,
			},
		},
	}

	previewRefundOrderResult := s.PreviewRefundOrder(previewRefundOrderParams)

	//previewRefundOrderPayments 接口调用
	previewRefundOrderPaymentsParams := buildPreviewRefundOrderPaymentsParams(
		targetRefundMoney,              // RefundAmount
		false,                          // IsConvenienceFeeIncluded
		orderPaymentId,                 // orderPaymentId
		invoiceId,                      // invoiceId
		s.GetAuthInfo().CompanyID,      // companyId
		s.GetAuthInfo().BusinessID,     // businessId
		int64(s.CustomerID),            // customerId
		s.GetAuthInfo().StaffID,        // staffId
		1,                              // paymentMethodId ；case 过程参数 ，允许写死
		"Credit card",                  // paymentMethod ；case 过程参数 ，允许写死
		"Credit card - card4242(visa)", // paymentMethodDisplayName ；case 过程参数 ，允许写死
		"Stripe",                       // paymentMethodVendor ；case 过程参数 ，允许写死
		"API test",                     // paidBy ；case 过程参数 ，允许写死
		false,                          // isOnline ；case 过程参数 ，允许写死
		false,                          // isDeposit  ；case 过程参数 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.TotalCollected), "USD"),                                          // totalAmount
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.TotalCollected-*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // amount
		moneyutil.FromFloat(0.0, "USD"),                                               // refundedAmount  case 固定为0 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // processingFee
		moneyutil.FromFloat(0.0, "USD"),                                               // paymentTips  case 固定为0 ，允许写死
		moneyutil.FromFloat(0.0, "USD"),                                               // paymentTipsBeforeCreate  case 固定为0 ，允许写死
		moneyutil.FromFloat(-1.0, "USD"),                                              // paymentTipsAfterCreate  case 固定为-1 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // paymentConvenienceFee
		moneyutil.FromFloat(0.0, "USD"),                                               // refundedPaymentConvenienceFee  case 固定为0 ，允许写死
		orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,                          // paymentStatus = 300
	)

	previewRefundOrderPaymentsResult := s.PreviewRefundOrderPayments(previewRefundOrderPaymentsParams)

	// 声明期望结果
	expectedValues := getExpectedValues(
		"0", 0, // refundableTips
		false, false, // refundFlags
		"0", *********, // refundableConvenienceFee
		"11", *********, // refundTotalAmount
		"0", 0, // refundItemSubTotal
		"0", 0, // refundItemDiscountTotal
		"0", 0, // refundTips
		"0", *********, // refundConvenienceFee
		"0", 0, // refundTaxFee
		"51", *********, // product  参与分摊
		"25", *********, // serviceCharge 参与分摊
		"8", *********, // service  参与分摊
		"111", *********, // refundableOrderPayments
	)

	// 期望结果与preview 接口核对
	s.checkPreviewRefundOrderData(previewRefundOrderResult, &expectedValues)
	s.checkPreviewRefundOrderPaymentsData(previewRefundOrderPaymentsResult, &expectedValues)

	// 发起最终退款
	paramsRealRefund := &orderapipb.RefundOrderParams{
		OrderId:      invoiceId,
		RefundMode:   orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		RefundReason: "",
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.RefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmount:    targetRefundMoney,
			},
		},
	}

	s.RefundOrder(paramsRealRefund)
}

/*
author:<EMAIL>
desc：stripe 部分支付 后 关单前  payment only 退款

	减少service 金额
	关单前 refund by payment && fully paid，编辑items里面退款
	is_convenience_fee_included 为 false
*/
func (s *NewInvoiceBeforeRefundTestSuite) TestNewInvoiceFullyPaidReduceServicePriceBeforeCompleteRefund() {
	// 前置设置 创建 appt 然后将 中间变量 set 到 appt 里面
	appointmentId := s.createApptAndOrderWithOnceTest(s.Service)
	s.apptId = appointmentId

	// 根据 appt id 获取 appt 信息 和 invoice id
	appintmentInfo := s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentId,
	})
	invoiceId := appintmentInfo.GetInvoice().InvoiceId
	// 支付
	s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance-0.01), int32(invoiceId), float32(0.0))
	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。

	// 关单前退款 主流程
	// 修改service price 为 当前值的 1/2
	today := time.Now().Format("2006-01-02")
	params := &appointmentapipb.SaveOrUpdatePetDetailsParams{
		AppointmentId: appointmentId,
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:            appintmentInfo.ServiceDetail[0].Services[0].ServiceDetail.ServiceId,
						ServicePrice:         proto.Float64(appintmentInfo.ServiceDetail[0].Services[0].ServiceDetail.ServicePrice / 2),
						ServiceTime:          &appintmentInfo.ServiceDetail[0].Services[0].ServiceDetail.ServiceTime,
						StartDate:            today,
						EndDate:              &today,
						StartTime:            &appintmentInfo.ServiceDetail[0].Services[0].ServiceDetail.StartTime,
						EndTime:              &appintmentInfo.ServiceDetail[0].Services[0].ServiceDetail.EndTime,
						ScopeTypePrice:       offeringpb.ServiceScopeType_DO_NOT_SAVE.Enum(),
						ScopeTypeTime:        offeringpb.ServiceScopeType_DO_NOT_SAVE.Enum(),
						PriceOverrideType:    offeringpb.ServiceOverrideType_SERVICE_OVERRIDE_TYPE_UNSPECIFIED.Enum(),
						DurationOverrideType: offeringpb.ServiceOverrideType(0).Enum(),
						WorkMode:             appointmentpb.WorkMode_PARALLEL.Enum(),
						EnableOperation:      proto.Bool(false),
						StaffId:              proto.Int64(s.GetAuthInfo().StaffID),
						SpecificDates:        []string{},
						Operations:           []*appointmentpb.ServiceOperationDef{},
						Feedings:             []*appointmentpb.AppointmentPetFeedingScheduleDef{},
						Medications:          []*appointmentpb.AppointmentPetMedicationScheduleDef{},
					},
				},
				AddOns:      []*appointmentpb.SelectedAddOnDef{},
				Evaluations: []*appointmentpb.SelectedEvaluationDef{},
			},
		},
		RepeatAppointmentModifyScope: appointmentpb.RepeatAppointmentModifyScope_ONLY_THIS.Enum(),
	}

	s.SaveOrUpdatePetDetails(params)

	// 获取当前appt 被编辑后的现状
	AfterlistOrderDetailsRes := s.ListOrderDetailsV2(int32(invoiceId))

	// 获取order payment id
	paymentListResult := s.GetPaymentListByCustomerId(int32(s.CustomerID), 1, 100)
	var orderPaymentId int64

	for _, p := range paymentListResult.PaymentList {
		if p.InvoiceId != nil && *p.InvoiceId == int32(invoiceId) {
			orderPaymentId = *p.OrderPaymentId
		}
	}
	// 设置当前需要退款的金额
	rawStr := fmt.Sprintf("%.2f", *AfterlistOrderDetailsRes.RemainAmountV2) // 你只关心 2 位小数
	amount, err := decimal.NewFromString(rawStr)
	s.Require().Nil(err)

	targetRefundMoney := moneyutil.FromDecimal(amount.Neg(), "USD")

	//previewRefundOrder 接口调用
	previewRefundOrderParams := &orderapipb.PreviewRefundOrderParams{
		OrderId:    invoiceId,
		RefundMode: orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.PreviewRefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				RefundAmount: targetRefundMoney,
			},
		},
	}

	previewRefundOrderResult := s.PreviewRefundOrder(previewRefundOrderParams)

	//previewRefundOrderPayments 接口调用
	previewRefundOrderPaymentsParams := buildPreviewRefundOrderPaymentsParams(
		targetRefundMoney,              // RefundAmount
		false,                          // IsConvenienceFeeIncluded
		orderPaymentId,                 // orderPaymentId
		invoiceId,                      // invoiceId
		s.GetAuthInfo().CompanyID,      // companyId
		s.GetAuthInfo().BusinessID,     // businessId
		int64(s.CustomerID),            // customerId
		s.GetAuthInfo().StaffID,        // staffId
		1,                              // paymentMethodId ；case 过程参数 ，允许写死
		"Credit card",                  // paymentMethod ；case 过程参数 ，允许写死
		"Credit card - card4242(visa)", // paymentMethodDisplayName ；case 过程参数 ，允许写死
		"Stripe",                       // paymentMethodVendor ；case 过程参数 ，允许写死
		"API test",                     // paidBy ；case 过程参数 ，允许写死
		false,                          // isOnline ；case 过程参数 ，允许写死
		false,                          // isDeposit  ；case 过程参数 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.TotalCollected), "USD"),                                          // totalAmount
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.TotalCollected-*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // amount
		moneyutil.FromFloat(0.0, "USD"),                                               // refundedAmount  case 固定为0 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // processingFee
		moneyutil.FromFloat(0.0, "USD"),                                               // paymentTips  case 固定为0 ，允许写死
		moneyutil.FromFloat(0.0, "USD"),                                               // paymentTipsBeforeCreate  case 固定为0 ，允许写死
		moneyutil.FromFloat(-1.0, "USD"),                                              // paymentTipsAfterCreate  case 固定为-1 ，允许写死
		moneyutil.FromFloat(float64(*AfterlistOrderDetailsRes.ConvenienceFee), "USD"), // paymentConvenienceFee
		moneyutil.FromFloat(0.0, "USD"),                                               // refundedPaymentConvenienceFee  case 固定为0 ，允许写死
		orderpb.OrderPaymentStatus_ORDER_PAYMENT_STATUS_PAID,                          // paymentStatus = 300
	)

	previewRefundOrderPaymentsResult := s.PreviewRefundOrderPayments(previewRefundOrderPaymentsParams)

	// 声明期望结果
	expectedValues := getExpectedValues(
		"0", 0, // refundableTips
		false, false, // refundFlags
		"0", *********, // refundableConvenienceFee
		"5", 20000000, // refundTotalAmount
		"0", 0, // refundItemSubTotal
		"0", 0, // refundItemDiscountTotal
		"0", 0, // refundTips
		"0", *********, // refundConvenienceFee
		"0", 0, // refundTaxFee
		"58", *********, // product  参与分摊
		"29", *********, // serviceCharge 参与分摊
		"4", *********, // service  参与分摊
		"111", *********, // refundableOrderPayments
	)

	// 期望结果与preview 接口核对
	s.checkPreviewRefundOrderData(previewRefundOrderResult, &expectedValues)
	s.checkPreviewRefundOrderPaymentsData(previewRefundOrderPaymentsResult, &expectedValues)

	// 发起最终退款
	paramsRealRefund := &orderapipb.RefundOrderParams{
		OrderId:      invoiceId,
		RefundMode:   orderpb.RefundMode_REFUND_MODE_BY_PAYMENT,
		RefundReason: "",
		SourceOrderPayments: []*ordersvcpb.RefundOrderRequest_OrderPayment{
			{Id: orderPaymentId},
		},
		RefundBy: &orderapipb.RefundOrderParams_RefundByPayment{
			RefundByPayment: &ordersvcpb.RefundOrderRequest_RefundByPayment{
				RefundAmountFlags: &ordersvcpb.RefundOrderRequest_RefundByPayment_RefundAmountFlags{
					IsConvenienceFeeIncluded: false,
				},
				OrderPaymentIds: []int64{orderPaymentId},
				RefundAmount:    targetRefundMoney,
			},
		},
	}

	s.RefundOrder(paramsRealRefund)
}
