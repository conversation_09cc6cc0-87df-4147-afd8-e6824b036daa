package bulkpayment

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	appointmentapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	appointmentmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type BulkPaymentTestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID     int32
	PetIDs         []int32
	TagIDs         []int64
	Services       []*offeringpb.CustomizedServiceView
	AppointmentID  int64
	setupDone      bool
	GroomingApptId int64
	BoradingApptId int64
	DaycareApptId  int64
}

func (s *BulkPaymentTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "BulkPaymentTestSuite",
		Email:    "<EMAIL>", // pwd:AutoTest!123  <EMAIL>
	})

	// create a customer for testing
	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(
		&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
			PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
			PhoneNumber:         customermodel.PtrString("**********"),
			FirstName:           customermodel.PtrString("API"),
			LastName:            customermodel.PtrString("test"),
			Email:               customermodel.PtrString("<EMAIL>"),
			PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
				{
					PetName:   customermodel.PtrString("APet"),
					PetTypeId: customermodel.PtrInt32(1),
					Breed:     customermodel.PtrString("Affenpinscher"),
				},
			},
		})

	s.setupDone = true
}

func (s *BulkPaymentTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *BulkPaymentTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

/*
author:<EMAIL>
desc:client 名下无未付款订单，验证list order 接口逻辑
*/
func (s *BulkPaymentTestSuite) TestListNotUnpaidOrder() {
	// start your test

	// Assert your test

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(s.AppointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc:创建单个grooming未付款订单，验证list合单支付逻辑
*/
func (s *BulkPaymentTestSuite) TestListGroomingSingleUnpaidOrder() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	s.AppointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", s.AppointmentID)

	// start your test

	// Assert your test

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(s.AppointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author: <EMAIL>
desc: 创建多个 grooming 未付款订单，验证 list 合单支付逻辑
*/
func (s *BulkPaymentTestSuite) TestListGroomingMultiUnpaidOrder() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")

	var apptIDs []int64

	// 创建两个 grooming appointment
	for i := 0; i < 2; i++ {
		apptID := s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
			BusinessId: s.GetAuthInfo().BusinessID,
			Appointment: &appointmentpb.AppointmentCreateDef{
				CustomerId: int64(s.CustomerID),
				Source:     appointmentpb.AppointmentSource_WEB,
			},
			PetDetails: []*appointmentpb.PetDetailDef{
				{
					PetId: int64(s.PetIDs[0]),
					Services: []*appointmentpb.SelectedServiceDef{
						{
							ServiceId:    s.Services[0].GetId(),
							StartDate:    today,
							EndDate:      &today,
							StartTime:    proto.Int32(600 + int32(i*100)), // 避免重复时间冲突
							EndTime:      proto.Int32(660 + int32(i*100)),
							ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
							ServicePrice: proto.Float64(s.Services[0].GetPrice()),
							StaffId:      &s.GetAuthInfo().StaffID,
						},
					},
				},
			},
		})
		log.InfoContextf(s.Ctx, "-------- index: %d, appt id: %d", i, apptID)
		apptIDs = append(apptIDs, apptID)
	}

	// TODO: Add your test logic/assertions here

	// Cancel each appointment
	for _, id := range apptIDs {
		s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
			Id:             appointmentmodel.PtrInt32(int32(id)),
			NoShow:         appointmentmodel.PtrInt32(2),
			CancelByType:   appointmentmodel.PtrInt32(0),
			CancelReason:   appointmentmodel.PtrString(""),
			ReleasePreAuth: appointmentmodel.PtrBool(true),
		})
	}
}

/*
author:<EMAIL>
desc:创建单个daycare未付款订单，验证list合单支付逻辑
*/
func (s *BulkPaymentTestSuite) TestListDaycareSingleUnpaidOrder() {
	// list applicable grooming services
	s.Services = s.ListApplicableDaycareServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	s.AppointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", s.AppointmentID)

	// start your test

	// Assert your test

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(s.AppointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc:创建多个daycare未付款订单，验证list合单支付逻辑
*/
func (s *BulkPaymentTestSuite) TestListDaycareMultiUnpaidOrder() {
	// list applicable grooming services
	s.Services = s.ListApplicableDaycareServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")

	var apptIDs []int64

	for range 2 {
		s.AppointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
			BusinessId: s.GetAuthInfo().BusinessID,
			Appointment: &appointmentpb.AppointmentCreateDef{
				CustomerId: int64(s.CustomerID),
				Source:     appointmentpb.AppointmentSource_WEB,
			},
			PetDetails: []*appointmentpb.PetDetailDef{
				{
					PetId: int64(s.PetIDs[0]),
					Services: []*appointmentpb.SelectedServiceDef{
						{
							ServiceId:    s.Services[0].GetId(),
							StartDate:    today,
							EndDate:      &today,
							StartTime:    proto.Int32(600),
							EndTime:      proto.Int32(660),
							ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
							ServicePrice: proto.Float64(s.Services[0].GetPrice()),
							StaffId:      &s.GetAuthInfo().StaffID,
						},
					},
				},
			},
		})
		log.InfoContextf(s.Ctx, "-------- appt id: %d", s.AppointmentID)
		apptIDs = append(apptIDs, s.AppointmentID)
	}

	// start your test

	// Assert your test

	// clear your apptointment
	for _, id := range apptIDs {
		s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
			Id:             appointmentmodel.PtrInt32(int32(id)),
			NoShow:         appointmentmodel.PtrInt32(2),
			CancelByType:   appointmentmodel.PtrInt32(0),
			CancelReason:   appointmentmodel.PtrString(""),
			ReleasePreAuth: appointmentmodel.PtrBool(true),
		})
	}

}

/*
author:<EMAIL>
desc:创建单个boarding未付款订单，验证list合单支付逻辑
*/
func (s *BulkPaymentTestSuite) TestListBoradingSingleUnpaidOrder() {
	// list applicable grooming services
	s.Services = s.ListApplicableBoardingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	s.AppointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", s.AppointmentID)

	// start your test

	// Assert your test

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(s.AppointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc:创建多个boarding未付款订单，验证list合单支付逻辑
*/
func (s *BulkPaymentTestSuite) TestListBoradingMultiUnpaidOrder() {
	// list applicable grooming services
	s.Services = s.ListApplicableBoardingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")

	var apptIDs []int

	for range 2 {
		s.AppointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
			BusinessId: s.GetAuthInfo().BusinessID,
			Appointment: &appointmentpb.AppointmentCreateDef{
				CustomerId: int64(s.CustomerID),
				Source:     appointmentpb.AppointmentSource_WEB,
			},
			PetDetails: []*appointmentpb.PetDetailDef{
				{
					PetId: int64(s.PetIDs[0]),
					Services: []*appointmentpb.SelectedServiceDef{
						{
							ServiceId:    s.Services[0].GetId(),
							StartDate:    today,
							EndDate:      &today,
							StartTime:    proto.Int32(600),
							EndTime:      proto.Int32(660),
							ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
							ServicePrice: proto.Float64(s.Services[0].GetPrice()),
							StaffId:      &s.GetAuthInfo().StaffID,
						},
					},
				},
			},
		})
		log.InfoContextf(s.Ctx, "-------- appt id: %d", s.AppointmentID)
		apptIDs = append(apptIDs, int(s.AppointmentID))
	}

	// start your test

	// Assert your test

	// clear your apptointment
	for _, id := range apptIDs {
		s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
			Id:             appointmentmodel.PtrInt32(int32(id)),
			NoShow:         appointmentmodel.PtrInt32(2),
			CancelByType:   appointmentmodel.PtrInt32(0),
			CancelReason:   appointmentmodel.PtrString(""),
			ReleasePreAuth: appointmentmodel.PtrBool(true),
		})
	}
}

/*
author:<EMAIL>
desc:创建单个evaluation未付款订单，验证list合单支付逻辑
*/
func (s *BulkPaymentTestSuite) TestListEvaluationSingleUnpaidOrder() {

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	s.AppointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Evaluations: []*appointmentpb.SelectedEvaluationDef{
					{
						ServiceId:    887, // evluation 的service id 可以固定下来，一个账号只有一个
						StartDate:    today,
						StartTime:    proto.Int32(1384),
						ServiceTime:  proto.Int32(123),
						ServicePrice: proto.Float64(77.77),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", s.AppointmentID)

	// start your test

	// Assert your test

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(s.AppointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc:创建多个evaluation未付款订单，验证list合单支付逻辑
*/
func (s *BulkPaymentTestSuite) TestListEvaluationMultiUnpaidOrder() {

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")

	var apptIDs []int

	for range 2 {
		s.AppointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
			BusinessId: s.GetAuthInfo().BusinessID,
			Appointment: &appointmentpb.AppointmentCreateDef{
				CustomerId: int64(s.CustomerID),
				Source:     appointmentpb.AppointmentSource_WEB,
			},
			PetDetails: []*appointmentpb.PetDetailDef{
				{
					PetId: int64(s.PetIDs[0]),
					Evaluations: []*appointmentpb.SelectedEvaluationDef{
						{
							ServiceId:    887, // evluation 的service id 可以固定下来，一个账号只有一个
							StartDate:    today,
							StartTime:    proto.Int32(1384),
							ServiceTime:  proto.Int32(123),
							ServicePrice: proto.Float64(77.77),
							StaffId:      &s.GetAuthInfo().StaffID,
						},
					},
				},
			},
		})
		log.InfoContextf(s.Ctx, "-------- appt id: %d", s.AppointmentID)
		apptIDs = append(apptIDs, int(s.AppointmentID))
	}

	// start your test

	// Assert your test

	// clear your apptointment
	for _, id := range apptIDs {
		s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
			Id:             appointmentmodel.PtrInt32(int32(id)),
			NoShow:         appointmentmodel.PtrInt32(2),
			CancelByType:   appointmentmodel.PtrInt32(0),
			CancelReason:   appointmentmodel.PtrString(""),
			ReleasePreAuth: appointmentmodel.PtrBool(true),
		})
	}
}

func TestBulkPaymentSuite(t *testing.T) {
	suite.Run(t, new(BulkPaymentTestSuite))
}
