load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "tips_test",
    srcs = ["tips_test.go"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/def/customer/model",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//proto",
    ],
)
