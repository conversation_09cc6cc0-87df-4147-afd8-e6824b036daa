package tips

import (
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	appointmentapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type OrderV1TestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID    int32
	PetIDs        []int32
	Services      []*offeringpb.CustomizedServiceView
	AppointmentID int64
	OrderID       int64
	setupDone     bool
}

func (s *OrderV1TestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	// 先借用一个 B 端测试账号
	s.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "OrderV1TestSuite",
	})

	// create a customer for testing
	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(
		&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
			PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
			PhoneNumber:         customermodel.PtrString("***********"),
			FirstName:           customermodel.PtrString("Tips"),
			LastName:            customermodel.PtrString("Test"),
			Email:               customermodel.PtrString("<EMAIL>"),
			PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
				{
					PetName:   customermodel.PtrString("TipsTestDog"),
					PetTypeId: customermodel.PtrInt32(1),
					Breed:     customermodel.PtrString("Affenpinscher"),
				},
			},
		})
	s.Require().Greater(s.CustomerID, int32(0))

	// create an appointment
	today := time.Now().Format("2006-01-02")
	customerDto := s.MustGetCustomer(s.CustomerID)
	petId := *customerDto.GetPetList()[0].PetDetail.GetPetId()
	// get services
	s.Services = s.ListApplicableGroomingServices(petId)
	s.Require().GreaterOrEqual(len(s.Services), 1)
	// create appointments
	s.AppointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_GOOGLE_CALENDAR,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(*customerDto.GetPetList()[0].PetDetail.GetPetId()),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	apptPupup := s.GetAppointmentDetail(s.AppointmentID)

	s.OrderID = int64(*apptPupup.InvoiceId)
	s.setupDone = true
}

func (s *OrderV1TestSuite) TearDownSuite() {
	defer s.Teardown()

	s.mustTearDownSuite()
}

func (s *OrderV1TestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

// func (s *OrderV1TestSuite) TestSaveTipsByApiV3() {
// 	// fully pay a order
// 	orderResult := s.ListOrders(s.GetAuthInfo().BusinessID, s.OrderID)
// 	originOrder := orderResult.Orders[0]
// 	s.CashTakePayment(int32(originOrder.Id), float32(money.ToFloat(originOrder.RemainAmount)))
// 	// continue check if order is paid successfully
// 	apptPupup := s.GetAppointmentDetail(s.AppointmentID)
// 	count := 0
// 	for count < 5 && *apptPupup.IsPaid != 1 {
// 		time.Sleep(1 * time.Second)
// 		apptPupup = s.GetAppointmentDetail(s.AppointmentID)
// 		count++
// 	}
// 	// 检查最后支付结果，如果还是没有 fully paid，就跳过该用例
// 	if *apptPupup.IsPaid != 1 {
// 		log.InfoContext(s.Ctx, "order is not paid successfully")
// 		return
// 	}

// 	s.Require().Equal(*apptPupup.IsPaid, int32(1))
// 	// get tips split model
// 	result := &orderapipb.GetTipsSplitResult{}
// 	response, err := s.NewRequest().
// 		SetMethodPath(http.MethodPost, "/moego.api.order.v1.SplitTipsService/GetTipsSplitDetails").
// 		SetPayload(&orderapipb.GetTipsSplitParams{
// 			BusinessId: s.GetAuthInfo().BusinessID,
// 			SourceId:   s.AppointmentID,
// 			SourceType: orderv1mod.OrderSourceType_APPOINTMENT,
// 		}).
// 		SetResult(result).
// 		Send()
// 	s.Require().Nil(err)
// 	s.Require().True(response.IsSuccess())
// 	s.Require().NotNil(result)
// 	// check tips split model
// 	originOrderId := result.Orders[0].Id
// 	saveResult := &orderapipb.SaveSplitTipsResponse{}
// 	response, err = s.NewRequest().
// 		SetMethodPath(http.MethodPost, "/moego.api.order.v1.SplitTipsService/SaveTipSplit").
// 		SetPayload(&orderapipb.SaveSplitTipsRequest{
// 			OrderId:     originOrderId,
// 			SplitMethod: orderv1mod.SplitTipsMethod_SPLIT_TIPS_METHOD_BY_EQUALLY,
// 		}).
// 		SetResult(saveResult).
// 		Send()
// 	s.Require().Nil(err)
// 	s.Require().True(response.IsSuccess())
// 	s.Require().NotNil(result)
// }

func TestTipsTestSuite(t *testing.T) {
	suite.Run(t, new(OrderV1TestSuite))
}
