package stripepaytest

import (
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	appointmentapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	appointmentmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
	paymentapipb "github.com/MoeGolibrary/moego/backend/test/api_integration/def/payment/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type StripePayTestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID int32
	PetIDs     []int32
	TagIDs     []int64
	Services   []*offeringpb.CustomizedServiceView
	setupDone  bool
}

func (s *StripePayTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "StripePayTestSuite",
		Email:    "<EMAIL>", // pwd:AutoTest!123  <EMAIL>
	})

	// create a customer for testing
	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(
		&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
			PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
			PhoneNumber:         customermodel.PtrString("**********"),
			FirstName:           customermodel.PtrString("API"),
			LastName:            customermodel.PtrString("test"),
			Email:               customermodel.PtrString("<EMAIL>"),
			PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
				{
					PetName:   customermodel.PtrString("APet"),
					PetTypeId: customermodel.PtrInt32(1),
					Breed:     customermodel.PtrString("Affenpinscher"),
				},
			},
		})

	s.setupDone = true
}

func (s *StripePayTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *StripePayTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

// 验证 order 状态以及支付渠道
func (s *StripePayTestSuite) ValidateOrderPaymentStatus(invoiceId int32, cardType, cardFunding string) {
	maxRetries := 5                  // 设置最大重试次数
	retryInterval := time.Second * 1 // 设置每次轮询的间隔时间

	var orderDetailsInfo *appointmentmodel.ComMoegoServerGroomingDtoOrderInvoiceSummaryDTO

	// 轮询验证支付状态
	for i := 0; i < maxRetries; i++ {
		// 获取最新的订单详情
		orderDetailsInfo = s.ListOrderDetailsV2(invoiceId)

		// 获取支付状态并判断是否为 "paid"
		if strings.ToLower(*orderDetailsInfo.GetPaymentStatus()) == "paid" {
			s.Require().Equal("paid", strings.ToLower(*orderDetailsInfo.GetPaymentStatus()))
			for _, payment := range orderDetailsInfo.GetPaymentSummary().GetPayments() {
				s.Require().Equal(cardType, strings.ToLower(*payment.GetCardType()))
				s.Require().Equal(cardFunding, strings.ToLower(*payment.GetCardFunding()))
			}
			return
		}

		// 如果未支付，等待一段时间再重试
		time.Sleep(retryInterval)
	}

	// 如果达到最大重试次数仍未支付，可以进行失败处理
	s.Require().Equal("paid", strings.ToLower(*orderDetailsInfo.GetPaymentStatus()), "支付状态超时或未支付")
}

func (s *StripePayTestSuite) ValidateOrderPaymentAmount(invoiceId int32, payAmount float32) {
	maxRetries := 5
	retryInterval := time.Second * 1

	var OrderDetailsInfo *appointmentmodel.ComMoegoServerGroomingDtoOrderInvoiceSummaryDTO

	for i := 0; i < maxRetries; i++ {
		OrderDetailsInfo = s.ListOrderDetailsV2(invoiceId)

		if strings.ToLower(*OrderDetailsInfo.GetPaymentStatus()) == "partial_paid" {
			s.Require().Equal("partial_paid", strings.ToLower(*OrderDetailsInfo.GetPaymentStatus()))
			var paymentAmount = *OrderDetailsInfo.GetPaidAmount()
			s.Require().Equal(payAmount, paymentAmount)
			return
		}

		if i < maxRetries-1 {
			time.Sleep(retryInterval)
		}
	}

	s.Require().Equal("partial_paid", strings.ToLower(*OrderDetailsInfo.GetPaymentStatus()), "支付状态未在预期时间内变为 partial_paid")
}

/*
author:<EMAIL>
desc：stripe 支付，4111的卡支付成功
*/
func (s *StripePayTestSuite) TestStripeCreditCardPaySucc() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("1111"),
		CardType:              proto.String("Visa"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_visa"),
		AddProcessingFee:      proto.Bool(true),
	}, false)
	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentStatus(int32(invoiceId), "visa", "credit")

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 支付，4000 0002 的卡支付 declined
*/
func (s *StripePayTestSuite) TestStripeCreditCardPayDecline() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("0002"),
		CardType:              proto.String("Visa"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_visa_chargeDeclined"),
		AddProcessingFee:      proto.Bool(true),
	}, true)
	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 支付，4000 0259 的卡支付 despite
*/
func (s *StripePayTestSuite) TestStripeCreditCardPayDespite() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("0259"),
		CardType:              proto.String("Visa"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_createDispute"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe Mastercard **************** card 支付
*/
func (s *StripePayTestSuite) TestStripeMastercardDebitCardPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("8210"),
		CardType:              proto.String("MasterCard"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_mastercard_debit"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentStatus(int32(invoiceId), "mastercard", "debit")

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe Mastercard **************** card 支付
*/
func (s *StripePayTestSuite) TestStripeMastercardCreditCardPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("4444"),
		CardType:              proto.String("MasterCard"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_mastercard"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentStatus(int32(invoiceId), "mastercard", "credit")

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe Visa 卡****************支付
*/
func (s *StripePayTestSuite) TestStripeVisaDebitCardPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("5556"),
		CardType:              proto.String("Visa"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_visa_debit"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentStatus(int32(invoiceId), "visa", "debit")

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 运通 卡***************支付
*/
func (s *StripePayTestSuite) TestStripeAmericanExpressCreditCardPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("8431"),
		CardType:              proto.String("American Express"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_amex"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentStatus(int32(invoiceId), "amex", "credit")

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe Discover **************** 支付
*/
func (s *StripePayTestSuite) TestStripeDiscoverCreditCardPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("9424"),
		CardType:              proto.String("Discover"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_discover"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentStatus(int32(invoiceId), "discover", "credit")

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe Discover **************** 支付
*/
func (s *StripePayTestSuite) TestStripeDiscoverDebitCardPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("1113"),
		CardType:              proto.String("Discover"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_discover_debit"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	// s.ValidateOrderPaymentStatus("visa","debit")

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe Diners Club 3056930009020004 支付
*/
func (s *StripePayTestSuite) TestStripeDinersClubCardPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("0004"),
		CardType:              proto.String("Diners Club"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_diners"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentStatus(int32(invoiceId), "diners", "credit")

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 银联支付 6200000000000005 支付
*/
func (s *StripePayTestSuite) TestStripeUnionPayCardPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("0005"),
		CardType:              proto.String("UnionPay"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_unionpay"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentStatus(int32(invoiceId), "unionpay", "credit")

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe Mastercard **************** 支付
*/
func (s *StripePayTestSuite) TestStripePrepaidCardPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                90,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("5100"),
		CardType:              proto.String("MasterCard"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_mastercard_prepaid"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentStatus(int32(invoiceId), "mastercard", "prepaid")

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 小数支付成功
*/
func (s *StripePayTestSuite) TestStripeDecimalAmountPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                1.11,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("1111"),
		CardType:              proto.String("Visa"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_visa"),
		AddProcessingFee:      proto.Bool(true),
	}, false)
	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentAmount(int32(invoiceId), 1.46)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 最小值支付成功
*/
func (s *StripePayTestSuite) TestStripeMinimumAmountPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                1,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("1111"),
		CardType:              proto.String("Visa"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_visa"),
		AddProcessingFee:      proto.Bool(true),
	}, false)
	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// 验证 order 状态 以及 支付渠道
	s.ValidateOrderPaymentAmount(int32(invoiceId), 1.35)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 最大值支付成功
*/
func (s *StripePayTestSuite) TestStripeMaxAmountPay() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// Set the date to four days before the current time.
	today := time.Now().AddDate(0, 0, -4).Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult = s.StripeTakePayment(&paymentapipb.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                999800,
		TipsAmount:            proto.Float32(0),
		CardNumber:            proto.String("1111"),
		CardType:              proto.String("Visa"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             int32(invoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_visa"),
		AddProcessingFee:      proto.Bool(false),
	}, false)
	log.InfoContextf(s.Ctx, "paymentResult res is : %d", paymentResult)
	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

func TestStripePaySuite(t *testing.T) {
	suite.Run(t, new(StripePayTestSuite))
}
