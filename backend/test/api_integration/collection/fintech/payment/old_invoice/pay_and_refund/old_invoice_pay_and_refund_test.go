package oldinvoicepayandrefundtest

import (
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	appointmentapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	orderapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v1"
	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	appointmentmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
	paymentmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/payment/model"
	retailmodelpb "github.com/MoeGolibrary/moego/backend/test/api_integration/def/retail/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type OldInvoicePayAndRefundTestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID int32
	PetIDs     []int32
	TagIDs     []int64
	Services   []*offeringpb.CustomizedServiceView
	setupDone  bool
}

func (s *OldInvoicePayAndRefundTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "StripePayTestSuite",
		Email:    "<EMAIL>", // pwd:AutoTest!123  <EMAIL>
	})

	// create a customer for testing
	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(
		&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
			PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
			PhoneNumber:         customermodel.PtrString("**********"),
			FirstName:           customermodel.PtrString("API"),
			LastName:            customermodel.PtrString("test"),
			Email:               customermodel.PtrString("<EMAIL>"),
			PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
				{
					PetName:   customermodel.PtrString("APet"),
					PetTypeId: customermodel.PtrInt32(1),
					Breed:     customermodel.PtrString("Affenpinscher"),
				},
			},
		})

	s.setupDone = true
}

func (s *OldInvoicePayAndRefundTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *OldInvoicePayAndRefundTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *OldInvoicePayAndRefundTestSuite) waitForPaymentInfoQuery(invoiceId int32) (int32, float32) {
	maxRetries := 5
	retryInterval := time.Second * 1
	var paymentListResult *paymentmodel.ComMoegoServerPaymentDtoPaymentListDto

	for i := 0; i < maxRetries; i++ {
		paymentListResult = s.GetPaymentListByCustomerId(int32(s.CustomerID), 1, 100)

		for _, paymentList := range paymentListResult.GetPaymentList() {
			if invoiceId == *paymentList.GetInvoiceId() {
				paymentId := *paymentList.GetId()
				refundAmount := *paymentList.GetAmount()
				return paymentId, refundAmount
			}
		}

		time.Sleep(retryInterval)
	}

	// 没找到就返回默认值（0, 0.0）
	return 0, 0
}

/*
@Desc:为了防止退款的时候没有钱, 这里是为了先给当前账号充钱
*/
func (s *OldInvoicePayAndRefundTestSuite) ChargePayment() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	// 先给 MGP 账号充钱
	s.StripeTakePayment(&paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:      500,
		TipsAmount:  proto.Float32(0),
		CardNumber:  proto.String("1111"),
		CardType:    proto.String("Visa"),
		CustomerId:  proto.Int32(int32(s.CustomerID)),
		Description: proto.String(""),
		ExpMonth:    proto.String("11"),
		ExpYear:     proto.String("2052"),
		InvoiceId: int32(s.GetAppointment(&appointmentapipb.GetAppointmentParams{
			AppointmentId: appointmentID,
		}).GetInvoice().InvoiceId),
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_visa"),
		AddProcessingFee:      proto.Bool(true),
	}, false)
}

/*
author:<EMAIL>
desc：stripe 全额支付 后 payment only 退全款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceStripePayAndRefundOnly() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var refundResult *paymentmodel.ComMoegoServerPaymentDtoPaymentDTO

	// 先充钱
	s.ChargePayment()

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance), int32(invoiceId), float32(0.0))

	// 查找 invoice 对应的 payment id
	paymentId, refundAmount := s.waitForPaymentInfoQuery(int32(invoiceId))

	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。
	// 进行全额退款
	refundResult = s.CreateRefund(&paymentmodel.ComMoegoServerPaymentParamsCreateRefundParams{
		PaymentId: paymentId,
		Amount:    refundAmount,
		Reason:    nil,
	})

	// 校验退款结果
	OrderDetailsInfo := s.ListOrderDetailsV2(int32(invoiceId))
	s.Require().Equal(*refundResult.RefundedAmount, refundAmount)
	s.Require().Equal(*OrderDetailsInfo.RefundedAmount, refundAmount)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：cash 全额支付 后 payment only 退全款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceCashPayAndRefundOnly() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentmodel.ComMoegoCommonDtoPaymentSummaryPaymentDto

	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment by cash
	paymentResult = s.BookkeepingTakePayment(&paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:    float32(appintmentInfo.GetInvoice().OutstandingBalance),
		InvoiceId: int32(invoiceId),
		Method:    pointer.Get("Cash"),
		MethodId:  pointer.Get(int32(2)),
		Module:    pointer.Get("grooming"),
		PaidBy:    pointer.Get("api-integration-payment-in-godomian"),
		IsOnline:  pointer.Get(false),
	})

	s.Require().Equal("paid", strings.ToLower(*paymentResult.GetStatus()))
	paymentId, refundAmount := paymentResult.GetId(), paymentResult.GetAmount()

	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。
	// 进行全额退款
	refundResult := s.CreateRefund(&paymentmodel.ComMoegoServerPaymentParamsCreateRefundParams{
		PaymentId: *paymentId,
		Amount:    *refundAmount,
		Reason:    nil,
	})

	// 校验退款结果
	OrderDetailsInfo := s.ListOrderDetailsV2(int32(invoiceId))
	s.Require().Equal(*refundResult.RefundedAmount, *refundAmount)
	s.Require().Equal(*OrderDetailsInfo.RefundedAmount, *refundAmount)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 全额支付 后 编辑invoice 删除 tips 触发退款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceStripePayAndEditRefund() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult

	// 先充钱
	s.ChargePayment()

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	var tipsAmount float64 = 20
	var resetTipsAmount float64 = 0
	// 根据 appt id 获取 invoice id
	s.SetTips(&orderapipb.SetTipsRequest{
		CheckRefund: pointer.Get(true),
		InvoiceId: int64(s.GetAppointment(&appointmentapipb.GetAppointmentParams{
			AppointmentId: appointmentID,
		}).GetInvoice().InvoiceId),
		OmitResult: *pointer.Get(false),
		Value:      tipsAmount,
		ValueType:  "amount",
	})

	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance), int32(invoiceId), float32(0.0))

	// 查找 invoice 对应的 payment id
	paymentId, refundAmount := s.waitForPaymentInfoQuery(int32(invoiceId))

	// 先进行revert
	s.Revert(&appointmentmodel.ComMoegoServerGroomingParamsApptReopenParams{
		GroomingId: int32(appointmentID),
	})
	s.SetTips(&orderapipb.SetTipsRequest{
		CheckRefund: pointer.Get(true),
		InvoiceId:   int64(invoiceId),
		OmitResult:  *pointer.Get(false),
		Value:       resetTipsAmount,
		ValueType:   "amount",
	})

	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。
	refundReason := "API test"
	paymentMethod := "Credit card - card4242(visa)"

	s.SubmitRefund(&paymentmodel.ComMoegoServerPaymentWebVoSubmitInvoiceRefundVo{
		InvoiceId:    int32(invoiceId),
		RefundAmount: float32(tipsAmount),
		RefundReason: &refundReason,
		Refunds: []paymentmodel.ComMoegoServerPaymentDtoCanRefundChannel{
			{
				PaymentMethod:   &paymentMethod,
				PaymentId:       paymentId,
				CanRefundAmount: refundAmount,
			},
		},
	})

	paymentId, refundAmount = 0, 0.0

	s.SetTips(&orderapipb.SetTipsRequest{
		CheckRefund: pointer.Get(true),
		InvoiceId:   int64(invoiceId),
		OmitResult:  *pointer.Get(false),
		Value:       resetTipsAmount,
		ValueType:   "amount",
	})

	// 校验退款结果
	OrderDetailsInfo := s.ListOrderDetailsV2(int32(invoiceId))
	s.Require().Equal(*OrderDetailsInfo.RefundedAmount, float32(tipsAmount))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：Cash 全额支付 后 编辑invoice 删除 tips 触发退款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceCashPayAndEditRefund() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult

	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	var tipsAmount float64 = 20
	var resetTipsAmount float64 = 0
	// 根据 appt id 获取 invoice id
	s.SetTips(&orderapipb.SetTipsRequest{
		CheckRefund: pointer.Get(true),
		InvoiceId: int64(s.GetAppointment(&appointmentapipb.GetAppointmentParams{
			AppointmentId: appointmentID,
		}).GetInvoice().InvoiceId),
		OmitResult: *pointer.Get(false),
		Value:      tipsAmount,
		ValueType:  "amount",
	})

	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment by cash
	paymentResult := s.BookkeepingTakePayment(&paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:    float32(appintmentInfo.GetInvoice().OutstandingBalance),
		InvoiceId: int32(invoiceId),
		Method:    pointer.Get("Cash"),
		MethodId:  pointer.Get(int32(2)),
		Module:    pointer.Get("grooming"),
		PaidBy:    pointer.Get("api-integration-payment-in-godomian"),
		IsOnline:  pointer.Get(false),
	})
	s.Require().Equal("paid", strings.ToLower(*paymentResult.GetStatus()))
	paymentId, refundAmount := paymentResult.GetId(), paymentResult.GetAmount()

	// 先进行revert
	s.Revert(&appointmentmodel.ComMoegoServerGroomingParamsApptReopenParams{
		GroomingId: int32(appointmentID),
	})
	s.SetTips(&orderapipb.SetTipsRequest{
		CheckRefund: pointer.Get(true),
		InvoiceId:   int64(invoiceId),
		OmitResult:  *pointer.Get(false),
		Value:       resetTipsAmount,
		ValueType:   "amount",
	})

	refundReason := "API test"
	paymentMethod := "Cash"

	s.SubmitRefund(&paymentmodel.ComMoegoServerPaymentWebVoSubmitInvoiceRefundVo{
		InvoiceId:    int32(invoiceId),
		RefundAmount: 20,
		RefundReason: &refundReason,
		Refunds: []paymentmodel.ComMoegoServerPaymentDtoCanRefundChannel{
			{
				PaymentMethod:   &paymentMethod,
				PaymentId:       *paymentId,
				CanRefundAmount: *refundAmount,
			},
		},
	})

	s.SetTips(&orderapipb.SetTipsRequest{
		CheckRefund: pointer.Get(true),
		InvoiceId:   int64(invoiceId),
		OmitResult:  *pointer.Get(false),
		Value:       resetTipsAmount,
		ValueType:   "amount",
	})

	// 校验退款结果
	OrderDetailsInfo := s.ListOrderDetailsV2(int32(invoiceId))
	s.Require().Equal(*OrderDetailsInfo.RefundedAmount, float32(tipsAmount))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：Cash 全额支付 后 add discount 触发退款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceAddDiscountRefund() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult

	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment by cash
	paymentResult := s.BookkeepingTakePayment(&paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:    float32(appintmentInfo.GetInvoice().OutstandingBalance),
		InvoiceId: int32(invoiceId),
		Method:    pointer.Get("Cash"),
		MethodId:  pointer.Get(int32(2)),
		Module:    pointer.Get("grooming"),
		PaidBy:    pointer.Get("api-integration-payment-in-godomian"),
		IsOnline:  pointer.Get(false),
	})

	s.Require().Equal("paid", strings.ToLower(*paymentResult.GetStatus()))
	paymentId, refundAmount := paymentResult.GetId(), paymentResult.GetAmount()

	// 先进行revert
	s.Revert(&appointmentmodel.ComMoegoServerGroomingParamsApptReopenParams{
		GroomingId: int32(appointmentID),
	})

	// add discount code
	var discountAmount float32 = 10
	s.SetDiscount(&retailmodelpb.ComMoegoServerRetailServiceParamsSetDiscountParams{
		CheckRefund: pointer.Get(true),
		OrderId:     invoiceId,
		LineDiscounts: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderLineDiscountParams{
			{
				ApplyType:      pointer.Get("all"),
				DiscountType:   pointer.Get("amount"),
				DiscountAmount: pointer.Get(discountAmount),
				DiscountRate:   pointer.Get(float32(0)),
			},
		},
	})

	refundReason := "API test"
	paymentMethod := "Cash"

	s.SubmitRefund(&paymentmodel.ComMoegoServerPaymentWebVoSubmitInvoiceRefundVo{
		InvoiceId:    int32(invoiceId),
		RefundAmount: discountAmount,
		RefundReason: &refundReason,
		Refunds: []paymentmodel.ComMoegoServerPaymentDtoCanRefundChannel{
			{
				PaymentMethod:   &paymentMethod,
				PaymentId:       *paymentId,
				CanRefundAmount: *refundAmount,
			},
		},
	})

	s.SetDiscount(&retailmodelpb.ComMoegoServerRetailServiceParamsSetDiscountParams{
		CheckRefund: pointer.Get(true),
		OrderId:     invoiceId,
		LineDiscounts: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderLineDiscountParams{
			{
				ApplyType:      pointer.Get("all"),
				DiscountType:   pointer.Get("amount"),
				DiscountAmount: pointer.Get(discountAmount),
				DiscountRate:   pointer.Get(float32(0)),
			},
		},
	})

	// 校验退款结果
	OrderDetailsInfo := s.ListOrderDetailsV2(int32(invoiceId))
	s.Require().Equal(*OrderDetailsInfo.RefundedAmount, discountAmount)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：Cash 全额支付 后 删除 retails 触发退款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceDeleteRetailRefund() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult

	// 获取账号默认的 retails 列表 add 本次测试的数量 + 2 为下面的测试做准备
	retailList := s.SearchRetailProducts()
	retailInfo := retailList[0]
	s.UpdateRetailProduct(&retailmodelpb.ComMoegoServerRetailServiceParamsUpdateProductParams{
		ProductId:    *proto.Int32(*retailInfo.Id),
		Stock:        proto.Int32(*retailInfo.Stock + 1), // 当前的 stock 加 + 1
		SpecialPrice: proto.Float32(*retailInfo.SpecialPrice),
	})

	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	// 根据 appt id 获取 invoice id
	invoiceId = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	}).GetInvoice().InvoiceId

	// 编辑 invoice, 增加 retails
	s.PutRetailsItemsOnOrder(&retailmodelpb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
		OrderId: invoiceId,
		RetailItems: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
			{
				Name:      retailInfo.Name,
				Quantity:  pointer.Get(int32(1)),
				StaffId:   proto.Int64(s.GetAuthInfo().StaffID),
				ObjectId:  pointer.Get(int64(*retailInfo.Id)),
				Type:      pointer.Get("product"),
				UnitPrice: pointer.Get(*retailInfo.SpecialPrice),
				LineTaxes: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderLineTaxParams{
					{
						TaxId:   pointer.Get(int64(*retailInfo.TaxId)),
						TaxRate: pointer.Get(*retailInfo.TaxRate),
					},
				},
			},
		},
		CheckRefund: pointer.Get(true),
	})

	// 根据 appt id 获取 最新的 appt info ，因为此时已经加入了 retails
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	// take payment by cash
	paymentResult := s.BookkeepingTakePayment(&paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:    float32(appintmentInfo.GetInvoice().OutstandingBalance) + *pointer.Get(*retailInfo.SpecialPrice),
		InvoiceId: int32(invoiceId),
		Method:    pointer.Get("Cash"),
		MethodId:  pointer.Get(int32(2)),
		Module:    pointer.Get("grooming"),
		PaidBy:    pointer.Get("api-integration-payment-in-godomian"),
		IsOnline:  pointer.Get(false),
	})

	s.Require().Equal("paid", strings.ToLower(*paymentResult.GetStatus()))
	paymentId, refundAmount := paymentResult.GetId(), paymentResult.GetAmount()

	// 先进行revert
	s.Revert(&appointmentmodel.ComMoegoServerGroomingParamsApptReopenParams{
		GroomingId: int32(appointmentID),
	})

	// delete retails
	OrderDetailsInfo := s.ListOrderDetailsV2(int32(invoiceId))
	var itemId int64
	for _, item := range OrderDetailsInfo.GetItems() {
		if *item.Name == *retailInfo.Name {
			itemId = int64(*item.Id)
		}
	}

	s.PutRetailsItemsOnOrder(&retailmodelpb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
		OrderId: invoiceId,
		RetailItems: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
			{
				Type:        pointer.Get("product"),
				OrderItemId: pointer.Get(itemId),
				IsDeleted:   pointer.Get(true),
			},
		},
		CheckRefund: pointer.Get(true),
	})

	refundReason := "API test"
	paymentMethod := "Cash"

	s.SubmitRefund(&paymentmodel.ComMoegoServerPaymentWebVoSubmitInvoiceRefundVo{
		InvoiceId:    int32(invoiceId),
		RefundAmount: *retailInfo.SpecialPrice,
		RefundReason: &refundReason,
		Refunds: []paymentmodel.ComMoegoServerPaymentDtoCanRefundChannel{
			{
				PaymentMethod:   &paymentMethod,
				PaymentId:       *paymentId,
				CanRefundAmount: *refundAmount,
			},
		},
	})

	s.PutRetailsItemsOnOrder(&retailmodelpb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
		OrderId: invoiceId,
		RetailItems: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
			{
				Type:        pointer.Get("product"),
				OrderItemId: pointer.Get(itemId),
				IsDeleted:   pointer.Get(true),
			},
		},
		CheckRefund: pointer.Get(true),
	})

	// 校验退款结果
	s.Require().Equal(*s.ListOrderDetailsV2(int32(invoiceId)).RefundedAmount, float32(*pointer.Get(*retailInfo.SpecialPrice)))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：Cash 全额支付 后 ，减少 retail 数量 触发退款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceReduceRetailQuantityRefund() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult

	// 获取账号默认的 retails 列表 add 本次测试的数量 + 2 为下面的测试做准备
	retailList := s.SearchRetailProducts()
	retailInfo := retailList[0]
	s.UpdateRetailProduct(&retailmodelpb.ComMoegoServerRetailServiceParamsUpdateProductParams{
		ProductId:    *proto.Int32(*retailInfo.Id),
		Stock:        proto.Int32(*retailInfo.Stock + 1), // 当前的 stock 加 + 1
		SpecialPrice: proto.Float32(*retailInfo.SpecialPrice),
	})

	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	// 根据 appt id 获取 invoice id
	invoiceId = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	}).GetInvoice().InvoiceId

	// 编辑 invoice, 增加 retails，与上面不同的是，增加数量为2
	s.PutRetailsItemsOnOrder(&retailmodelpb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
		OrderId: invoiceId,
		RetailItems: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
			{
				Name:      retailInfo.Name,
				Quantity:  pointer.Get(int32(2)),
				StaffId:   proto.Int64(s.GetAuthInfo().StaffID),
				ObjectId:  pointer.Get(int64(*retailInfo.Id)),
				Type:      pointer.Get("product"),
				UnitPrice: pointer.Get(*retailInfo.SpecialPrice),
				LineTaxes: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderLineTaxParams{
					{
						TaxId:   pointer.Get(int64(*retailInfo.TaxId)),
						TaxRate: pointer.Get(*retailInfo.TaxRate),
					},
				},
			},
		},
		CheckRefund: pointer.Get(true),
	})

	// 根据 appt id 获取 最新的 appt info ，因为此时已经加入了 retails
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	// take payment by cash
	paymentResult := s.BookkeepingTakePayment(&paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:    float32(appintmentInfo.GetInvoice().OutstandingBalance),
		InvoiceId: int32(invoiceId),
		Method:    pointer.Get("Cash"),
		MethodId:  pointer.Get(int32(2)),
		Module:    pointer.Get("grooming"),
		PaidBy:    pointer.Get("api-integration-payment-in-godomian"),
		IsOnline:  pointer.Get(false),
	})

	s.Require().Equal("paid", strings.ToLower(*paymentResult.GetStatus()))
	paymentId, refundAmount := paymentResult.GetId(), paymentResult.GetAmount()

	// 先进行revert
	s.Revert(&appointmentmodel.ComMoegoServerGroomingParamsApptReopenParams{
		GroomingId: int32(appointmentID),
	})

	// delete retails
	OrderDetailsInfo := s.ListOrderDetailsV2(int32(invoiceId))
	var itemId int64
	for _, item := range OrderDetailsInfo.GetItems() {
		if *item.Name == *retailInfo.Name {
			itemId = int64(*item.Id)
		}
	}

	s.PutRetailsItemsOnOrder(s.setUpdateRetailItemParams(invoiceId, itemId, *retailInfo.Name,
		*retailInfo.SpecialPrice, 1, 0,
		int64(*retailInfo.TaxId), *retailInfo.TaxRate))

	refundReason := "API test"
	paymentMethod := "Cash"

	s.SubmitRefund(&paymentmodel.ComMoegoServerPaymentWebVoSubmitInvoiceRefundVo{
		InvoiceId:    int32(invoiceId),
		RefundAmount: *retailInfo.SpecialPrice,
		RefundReason: &refundReason,
		Refunds: []paymentmodel.ComMoegoServerPaymentDtoCanRefundChannel{
			{
				PaymentMethod:   &paymentMethod,
				PaymentId:       *paymentId,
				CanRefundAmount: *refundAmount,
			},
		},
	})

	s.PutRetailsItemsOnOrder(s.setUpdateRetailItemParams(invoiceId, itemId, *retailInfo.Name,
		*retailInfo.SpecialPrice, 1, 0,
		int64(*retailInfo.TaxId), *retailInfo.TaxRate))

	// 校验退款结果
	s.Require().Equal(*s.ListOrderDetailsV2(int32(invoiceId)).RefundedAmount, float32(*pointer.Get(*retailInfo.SpecialPrice)))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：Cash 全额支付 后 ，减少 retail 金额 触发退款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceReduceRetailPriceRefund() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult

	// 获取账号默认的 retails 列表 add 本次测试的数量 + 2 为下面的测试做准备
	retailList := s.SearchRetailProducts()
	retailInfo := retailList[0]
	s.UpdateRetailProduct(&retailmodelpb.ComMoegoServerRetailServiceParamsUpdateProductParams{
		ProductId:    *proto.Int32(*retailInfo.Id),
		Stock:        proto.Int32(*retailInfo.Stock + 1), // 当前的 stock 加 + 1
		SpecialPrice: proto.Float32(*retailInfo.SpecialPrice),
	})

	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	// 根据 appt id 获取 invoice id
	invoiceId = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	}).GetInvoice().InvoiceId

	// 编辑 invoice, 增加 retails，与上面不同的是，增加数量为2
	s.PutRetailsItemsOnOrder(&retailmodelpb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
		OrderId: invoiceId,
		RetailItems: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
			{
				Name:      retailInfo.Name,
				Quantity:  pointer.Get(int32(2)),
				StaffId:   proto.Int64(s.GetAuthInfo().StaffID),
				ObjectId:  pointer.Get(int64(*retailInfo.Id)),
				Type:      pointer.Get("product"),
				UnitPrice: pointer.Get(*retailInfo.SpecialPrice),
				LineTaxes: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderLineTaxParams{
					{
						TaxId:   pointer.Get(int64(*retailInfo.TaxId)),
						TaxRate: pointer.Get(*retailInfo.TaxRate),
					},
				},
			},
		},
		CheckRefund: pointer.Get(true),
	})

	// 根据 appt id 获取 最新的 appt info ，因为此时已经加入了 retails
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	// take payment by cash
	paymentResult := s.BookkeepingTakePayment(&paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:    float32(appintmentInfo.GetInvoice().OutstandingBalance),
		InvoiceId: int32(invoiceId),
		Method:    pointer.Get("Cash"),
		MethodId:  pointer.Get(int32(2)),
		Module:    pointer.Get("grooming"),
		PaidBy:    pointer.Get("api-integration-payment-in-godomian"),
		IsOnline:  pointer.Get(false),
	})

	s.Require().Equal("paid", strings.ToLower(*paymentResult.GetStatus()))
	paymentId, refundAmount := paymentResult.GetId(), paymentResult.GetAmount()

	// 先进行revert
	s.Revert(&appointmentmodel.ComMoegoServerGroomingParamsApptReopenParams{
		GroomingId: int32(appointmentID),
	})

	// delete retails
	OrderDetailsInfo := s.ListOrderDetailsV2(int32(invoiceId))
	var itemId int64
	for _, item := range OrderDetailsInfo.GetItems() {
		if *item.Name == *retailInfo.Name {
			itemId = int64(*item.Id)
		}
	}

	s.PutRetailsItemsOnOrder(s.setUpdateRetailItemParams(invoiceId, itemId, *retailInfo.Name,
		*retailInfo.SpecialPrice-1, 2, 0,
		int64(*retailInfo.TaxId), *retailInfo.TaxRate))

	refundReason := "API test"
	paymentMethod := "Cash"

	s.SubmitRefund(&paymentmodel.ComMoegoServerPaymentWebVoSubmitInvoiceRefundVo{
		InvoiceId:    int32(invoiceId),
		RefundAmount: 2,
		RefundReason: &refundReason,
		Refunds: []paymentmodel.ComMoegoServerPaymentDtoCanRefundChannel{
			{
				PaymentMethod:   &paymentMethod,
				PaymentId:       *paymentId,
				CanRefundAmount: *refundAmount,
			},
		},
	})

	s.PutRetailsItemsOnOrder(s.setUpdateRetailItemParams(invoiceId, itemId, *retailInfo.Name,
		*retailInfo.SpecialPrice-1, 2, 0,
		int64(*retailInfo.TaxId), *retailInfo.TaxRate))

	// 校验退款结果
	s.Require().Equal(*s.ListOrderDetailsV2(int32(invoiceId)).RefundedAmount, float32(2.0))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 全额支付 后 ，减少 retail 金额 触发退款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceReduceRetailPriceRefundByStripe() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult

	// 先充钱
	s.ChargePayment()

	// 获取账号默认的 retails 列表 add 本次测试的数量 + 2 为下面的测试做准备
	retailList := s.SearchRetailProducts()
	retailInfo := retailList[0]
	s.UpdateRetailProduct(&retailmodelpb.ComMoegoServerRetailServiceParamsUpdateProductParams{
		ProductId:    *proto.Int32(*retailInfo.Id),
		Stock:        proto.Int32(*retailInfo.Stock + 1), // 当前的 stock 加 + 1
		SpecialPrice: proto.Float32(*retailInfo.SpecialPrice),
	})

	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	// 根据 appt id 获取 invoice id
	invoiceId = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	}).GetInvoice().InvoiceId

	// 编辑 invoice, 增加 retails，与上面不同的是，增加数量为2
	s.PutRetailsItemsOnOrder(&retailmodelpb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
		OrderId: invoiceId,
		RetailItems: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
			{
				Name:      retailInfo.Name,
				Quantity:  pointer.Get(int32(2)),
				StaffId:   proto.Int64(s.GetAuthInfo().StaffID),
				ObjectId:  pointer.Get(int64(*retailInfo.Id)),
				Type:      pointer.Get("product"),
				UnitPrice: pointer.Get(*retailInfo.SpecialPrice),
				LineTaxes: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderLineTaxParams{
					{
						TaxId:   pointer.Get(int64(*retailInfo.TaxId)),
						TaxRate: pointer.Get(*retailInfo.TaxRate),
					},
				},
			},
		},
		CheckRefund: pointer.Get(true),
	})

	// 根据 appt id 获取 最新的 appt info ，因为此时已经加入了 retails
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	// take payment
	paymentResult := s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance), int32(invoiceId), float32(0.0))
	paymentId, refundAmount := paymentResult.GetId(), paymentResult.GetAmount()

	// 先进行revert
	s.Revert(&appointmentmodel.ComMoegoServerGroomingParamsApptReopenParams{
		GroomingId: int32(appointmentID),
	})

	// delete retails
	OrderDetailsInfo := s.ListOrderDetailsV2(int32(invoiceId))
	var itemId int64
	for _, item := range OrderDetailsInfo.GetItems() {
		if *item.Name == *retailInfo.Name {
			itemId = int64(*item.Id)
		}
	}

	s.PutRetailsItemsOnOrder(s.setUpdateRetailItemParams(invoiceId, itemId, *retailInfo.Name,
		*retailInfo.SpecialPrice-1, 2, 0,
		int64(*retailInfo.TaxId), *retailInfo.TaxRate))

	refundReason := "API test"
	paymentMethod := "Credit card - card4242(visa)"

	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。
	s.SubmitRefund(&paymentmodel.ComMoegoServerPaymentWebVoSubmitInvoiceRefundVo{
		InvoiceId:    int32(invoiceId),
		RefundAmount: *proto.Float32(2.0),
		RefundReason: &refundReason,
		Refunds: []paymentmodel.ComMoegoServerPaymentDtoCanRefundChannel{
			{
				PaymentMethod:   &paymentMethod,
				PaymentId:       *paymentId,
				CanRefundAmount: *refundAmount,
			},
		},
	})

	s.PutRetailsItemsOnOrder(s.setUpdateRetailItemParams(invoiceId, itemId, *retailInfo.Name,
		*retailInfo.SpecialPrice-1, 2, 0,
		int64(*retailInfo.TaxId), *retailInfo.TaxRate))

	// 校验退款结果
	s.Require().Equal(*s.ListOrderDetailsV2(int32(invoiceId)).RefundedAmount, float32(2.0))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 全额支付 后 ，减少 retail 数量 触发退款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceReduceRetailQuantityRefundByStripe() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult

	// 先充钱
	s.ChargePayment()

	// 获取账号默认的 retails 列表 add 本次测试的数量 + 2 为下面的测试做准备
	retailList := s.SearchRetailProducts()
	retailInfo := retailList[0]
	s.UpdateRetailProduct(&retailmodelpb.ComMoegoServerRetailServiceParamsUpdateProductParams{
		ProductId:    *proto.Int32(*retailInfo.Id),
		Stock:        proto.Int32(*retailInfo.Stock + 1), // 当前的 stock 加 + 1
		SpecialPrice: proto.Float32(*retailInfo.SpecialPrice),
	})

	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	// 根据 appt id 获取 invoice id
	invoiceId = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	}).GetInvoice().InvoiceId

	// 编辑 invoice, 增加 retails，与上面不同的是，增加数量为2
	s.PutRetailsItemsOnOrder(&retailmodelpb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
		OrderId: invoiceId,
		RetailItems: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
			{
				Name:      retailInfo.Name,
				Quantity:  pointer.Get(int32(2)),
				StaffId:   proto.Int64(s.GetAuthInfo().StaffID),
				ObjectId:  pointer.Get(int64(*retailInfo.Id)),
				Type:      pointer.Get("product"),
				UnitPrice: pointer.Get(*retailInfo.SpecialPrice),
				LineTaxes: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderLineTaxParams{
					{
						TaxId:   pointer.Get(int64(*retailInfo.TaxId)),
						TaxRate: pointer.Get(*retailInfo.TaxRate),
					},
				},
			},
		},
		CheckRefund: pointer.Get(true),
	})

	// 根据 appt id 获取 最新的 appt info ，因为此时已经加入了 retails
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	// take payment
	paymentResult := s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance), int32(invoiceId), float32(0.0))
	paymentId, refundAmount := paymentResult.GetId(), paymentResult.GetAmount()

	// 先进行revert 调用
	s.Revert(&appointmentmodel.ComMoegoServerGroomingParamsApptReopenParams{
		GroomingId: int32(appointmentID),
	})

	// delete retails
	OrderDetailsInfo := s.ListOrderDetailsV2(int32(invoiceId))
	var itemId int64
	for _, item := range OrderDetailsInfo.GetItems() {
		if *item.Name == *retailInfo.Name {
			itemId = int64(*item.Id)
		}
	}

	s.PutRetailsItemsOnOrder(s.setUpdateRetailItemParams(invoiceId, itemId, *retailInfo.Name,
		*retailInfo.SpecialPrice, 1, 0,
		int64(*retailInfo.TaxId), *retailInfo.TaxRate))

	refundReason := "API test"
	paymentMethod := "Credit card - card4242(visa)"

	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。
	s.SubmitRefund(&paymentmodel.ComMoegoServerPaymentWebVoSubmitInvoiceRefundVo{
		InvoiceId:    int32(invoiceId),
		RefundAmount: *proto.Float32(*retailInfo.SpecialPrice),
		RefundReason: &refundReason,
		Refunds: []paymentmodel.ComMoegoServerPaymentDtoCanRefundChannel{
			{
				PaymentMethod:   &paymentMethod,
				PaymentId:       *paymentId,
				CanRefundAmount: *refundAmount,
			},
		},
	})

	s.PutRetailsItemsOnOrder(s.setUpdateRetailItemParams(invoiceId, itemId, *retailInfo.Name,
		*retailInfo.SpecialPrice, 1, 0,
		int64(*retailInfo.TaxId), *retailInfo.TaxRate))

	// 校验退款结果
	s.Require().Equal(*s.ListOrderDetailsV2(int32(invoiceId)).RefundedAmount, float32(*retailInfo.SpecialPrice))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：stripe 全额支付 后 ，增加 discount code 进行退款
*/
func (s *OldInvoicePayAndRefundTestSuite) TestOldInvoiceAddDiscountRefundByStripe() {
	var appointmentID int64
	var invoiceId int64
	var appintmentInfo *appointmentapipb.GetAppointmentResult

	// 先充钱
	s.ChargePayment()

	// 获取账号默认的 retails 列表 add 本次测试的数量 + 2 为下面的测试做准备
	retailList := s.SearchRetailProducts()
	retailInfo := retailList[0]
	s.UpdateRetailProduct(&retailmodelpb.ComMoegoServerRetailServiceParamsUpdateProductParams{
		ProductId:    *proto.Int32(*retailInfo.Id),
		Stock:        proto.Int32(*retailInfo.Stock + 1), // 当前的 stock 加 + 1
		SpecialPrice: proto.Float32(*retailInfo.SpecialPrice),
	})

	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	// 根据 appt id 获取 最新的 appt info ，因为此时已经加入了 retails
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = appintmentInfo.GetInvoice().InvoiceId

	// take payment
	paymentResult := s.commonStripeTakePaymentForTest(float32(appintmentInfo.GetInvoice().OutstandingBalance), int32(invoiceId), float32(0.0))
	paymentId, refundAmount := paymentResult.GetId(), paymentResult.GetAmount()

	// 先进行revert 调用
	s.Revert(&appointmentmodel.ComMoegoServerGroomingParamsApptReopenParams{
		GroomingId: int32(appointmentID),
	})

	// add discount code
	var discountAmount float32 = 10
	s.SetDiscount(&retailmodelpb.ComMoegoServerRetailServiceParamsSetDiscountParams{
		CheckRefund: pointer.Get(true),
		OrderId:     invoiceId,
		LineDiscounts: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderLineDiscountParams{
			{
				ApplyType:      pointer.Get("all"),
				DiscountType:   pointer.Get("amount"),
				DiscountAmount: pointer.Get(discountAmount),
				DiscountRate:   pointer.Get(float32(0)),
			},
		},
	})

	refundReason := "API test"
	paymentMethod := "Credit card - card4242(visa)"
	time.Sleep(2 * time.Second) // 支付完成之后要等回调，如果不等待直接退款会失败。
	s.SubmitRefund(&paymentmodel.ComMoegoServerPaymentWebVoSubmitInvoiceRefundVo{
		InvoiceId:    int32(invoiceId),
		RefundAmount: *proto.Float32(discountAmount),
		RefundReason: &refundReason,
		Refunds: []paymentmodel.ComMoegoServerPaymentDtoCanRefundChannel{
			{
				PaymentMethod:   &paymentMethod,
				PaymentId:       *paymentId,
				CanRefundAmount: *refundAmount,
			},
		},
	})

	s.SetDiscount(&retailmodelpb.ComMoegoServerRetailServiceParamsSetDiscountParams{
		CheckRefund: pointer.Get(true),
		OrderId:     invoiceId,
		LineDiscounts: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderLineDiscountParams{
			{
				ApplyType:      pointer.Get("all"),
				DiscountType:   pointer.Get("amount"),
				DiscountAmount: pointer.Get(discountAmount),
				DiscountRate:   pointer.Get(float32(0)),
			},
		},
	})

	// 校验退款结果
	s.Require().Equal(*s.ListOrderDetailsV2(int32(invoiceId)).RefundedAmount, float32(discountAmount))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

func (s *OldInvoicePayAndRefundTestSuite) commonStripeTakePaymentForTest(amount float32, invoiceId int32, tipsAmount float32) *paymentmodel.ComMoegoCommonDtoPaymentSummaryPaymentDto {

	// 定义局部变量（不存储到测试套件字段中）
	var paymentResult *paymentmodel.ComMoegoCommonDtoPaymentSummaryPaymentDto

	// 使用 defer 在方法返回前清理
	defer func() {
		// 清理逻辑
		paymentResult = nil
		// 其他需要清理的变量...
	}()

	paymentResult = s.StripeTakePayment(&paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		Amount:                amount,
		TipsAmount:            proto.Float32(tipsAmount),
		CardNumber:            proto.String("1111"),
		CardType:              proto.String("Visa"),
		CustomerId:            proto.Int32(int32(s.CustomerID)),
		Description:           proto.String(""),
		ExpMonth:              proto.String("11"),
		ExpYear:               proto.String("2052"),
		InvoiceId:             invoiceId,
		MethodId:              proto.Int32(1),
		Module:                proto.String("grooming"),
		PaidBy:                proto.String("API test"),
		Signature:             proto.String(""),
		StaffId:               proto.Int32(int32(s.GetAuthInfo().StaffID)),
		StripePaymentMethodId: proto.String(""),
		StripePaymentMethod:   proto.Int32(1),
		IsOnline:              proto.Bool(false),
		SaveCard:              proto.Bool(false),
		IsDeposit:             proto.Int32(0),
		ChargeToken:           proto.String("tok_visa"),
		AddProcessingFee:      proto.Bool(true),
	}, false)

	s.Require().NotNil(paymentResult)
	s.Require().Equal("succeeded", strings.ToLower(*paymentResult.GetStripeStatus()))

	return paymentResult
}

/*
设置减小retails数量、价格退款参数
*/
func (s *OldInvoicePayAndRefundTestSuite) setUpdateRetailItemParams(
	orderId int64,
	orderItemId int64,
	name string,
	unitPrice float32,
	quantity int32,
	purchasedQuantity int32,
	lineTaxId int64,
	lineTaxRate float32,
) *retailmodelpb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams {
	return &retailmodelpb.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams{
		OrderId: orderId,
		RetailItems: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderRetailItemParams{
			{
				Type:              pointer.Get("product"),
				OrderItemId:       pointer.Get(orderItemId),
				IsDeleted:         pointer.Get(false),
				Name:              pointer.Get(name),
				Description:       pointer.Get(""),
				UnitPrice:         pointer.Get(unitPrice),
				Quantity:          pointer.Get(quantity),
				PurchasedQuantity: pointer.Get(purchasedQuantity),
				LineTaxes: []retailmodelpb.ComMoegoServerRetailServiceParamsOrderLineTaxParams{
					{
						ApplyType:     pointer.Get("item"),
						IsDeleted:     pointer.Get(false),
						TaxId:         pointer.Get(lineTaxId),
						TaxRate:       pointer.Get(lineTaxRate),
						ApplySequence: pointer.Get(int32(0)),
					},
				},
			},
		},
		CheckRefund: pointer.Get(true),
	}
}

func TestOldInvoicePayAndRefund(t *testing.T) {
	suite.Run(t, new(OldInvoicePayAndRefundTestSuite))
}
