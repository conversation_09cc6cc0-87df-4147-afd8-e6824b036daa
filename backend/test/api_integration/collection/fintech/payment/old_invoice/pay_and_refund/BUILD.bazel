load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "old_invoice_pay_and_refund_test",
    srcs = ["old_invoice_pay_and_refund_test.go"],
    tags = [
        "invoke_stripe",
        "single_run",
        "testing_only",
    ],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "//backend/test/api_integration/def/customer/model",
        "//backend/test/api_integration/def/grooming/model",
        "//backend/test/api_integration/def/payment/model",
        "//backend/test/api_integration/def/retail/model",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/order/v1:order",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/appointment/v1:appointment",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/offering/v1:offering",
        "@com_github_stretchr_testify//suite",
        "@org_golang_google_protobuf//proto",
    ],
)
