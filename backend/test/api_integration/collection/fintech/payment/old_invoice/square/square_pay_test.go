package stripepaytest

import (
	"strconv"
	"strings"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	appointmentapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	appointmentmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
	paymentapipb "github.com/MoeGolibrary/moego/backend/test/api_integration/def/payment/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type SquarePayTestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID int32
	PetIDs     []int32
	TagIDs     []int64
	Services   []*offeringpb.CustomizedServiceView
	setupDone  bool
}

func (s *SquarePayTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "SquarePayTestSuite",
		Email:    "<EMAIL>", // pwd:AutoTest!123  <EMAIL>
	})

	// create a customer for testing
	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(
		&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
			PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
			PhoneNumber:         customermodel.PtrString("**********"),
			FirstName:           customermodel.PtrString("API"),
			LastName:            customermodel.PtrString("test"),
			Email:               customermodel.PtrString("<EMAIL>"),
			PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
				{
					PetName:   customermodel.PtrString("APet"),
					PetTypeId: customermodel.PtrInt32(1),
					Breed:     customermodel.PtrString("Affenpinscher"),
				},
			},
		})

	s.setupDone = true
}

func (s *SquarePayTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *SquarePayTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *SquarePayTestSuite) ValidateOrderPaymentAmount(invoiceId int32, payAmount float32) {
	maxRetries := 5
	retryInterval := time.Second * 1

	var OrderDetailsInfo *appointmentmodel.ComMoegoServerGroomingDtoOrderInvoiceSummaryDTO

	for i := 0; i < maxRetries; i++ {
		OrderDetailsInfo = s.ListOrderDetailsV2(invoiceId)

		if strings.ToLower(*OrderDetailsInfo.GetPaymentStatus()) == "paid" {
			s.Require().Equal("paid", strings.ToLower(*OrderDetailsInfo.GetPaymentStatus()))
			var paymentAmount = *OrderDetailsInfo.GetPaidAmount()
			s.Require().Equal(payAmount, paymentAmount)
			return
		}

		if i < maxRetries-1 {
			time.Sleep(retryInterval)
		}
	}

	s.Require().Equal("paid", strings.ToLower(*OrderDetailsInfo.GetPaymentStatus()), "支付状态未在预期时间内变为 paid")
}

/*
author:<EMAIL>
desc：square 支付 card nonce ok
*/
func (s *SquarePayTestSuite) TestSquareCardNonceOk() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int32
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoServerPaymentDtoSquareSquareTakePaymentResponse

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = int32(appintmentInfo.GetInvoice().InvoiceId)

	// take payment
	paymentResult = s.SquareTakePayment(&paymentapipb.ComMoegoServerPaymentDtoSquareSquarePaymentRequest{
		Amount:      90,
		TipsAmount:  proto.Float32(0),
		CustomerId:  proto.Int32(15140343),
		Description: proto.String(""),
		InvoiceId:   invoiceId,
		Module:      proto.String("grooming"),
		PaidBy:      proto.String("Demo Profile"),
		Signature:   proto.String(""),
		StaffId:     proto.Int32(180299),
		CardNonce:   "cnon:card-nonce-ok",
		IsOnline:    proto.Bool(false),
		IsDeposit:   proto.Int32(0),
		GroomingId:  proto.Int32(17777884),
	}, false)

	s.Require().NotNil(paymentResult)
	s.Require().Equal("completed", strings.ToLower(*paymentResult.GetStatus()))
	s.Require().Equal(float32(90), *paymentResult.GetAmount())

	s.ValidateOrderPaymentAmount(invoiceId, *proto.Float32(90))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：square 支付 gift-card-nonce-ok
*/
func (s *SquarePayTestSuite) TestSquareGiftCardNonceOk() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int32
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoServerPaymentDtoSquareSquareTakePaymentResponse

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = int32(appintmentInfo.GetInvoice().InvoiceId)

	// take payment
	paymentResult = s.SquareTakePayment(&paymentapipb.ComMoegoServerPaymentDtoSquareSquarePaymentRequest{
		Amount:      90,
		TipsAmount:  proto.Float32(0),
		CustomerId:  proto.Int32(15140343),
		Description: proto.String(""),
		InvoiceId:   invoiceId,
		Module:      proto.String("grooming"),
		PaidBy:      proto.String("Demo Profile"),
		Signature:   proto.String(""),
		StaffId:     proto.Int32(180299),
		CardNonce:   "cnon:gift-card-nonce-ok",
		IsOnline:    proto.Bool(false),
		IsDeposit:   proto.Int32(0),
		GroomingId:  proto.Int32(17777884),
	}, false)

	s.Require().NotNil(paymentResult)
	s.Require().Equal("completed", strings.ToLower(*paymentResult.GetStatus()))
	s.Require().Equal(float32(90), *paymentResult.GetAmount())

	s.ValidateOrderPaymentAmount(invoiceId, *proto.Float32(90))

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：square 支付 card-nonce-rejected-cvv
*/
func (s *SquarePayTestSuite) TestSquareCardRejectedCvv() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int32
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoServerPaymentDtoSquareSquareTakePaymentResponse

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = int32(appintmentInfo.GetInvoice().InvoiceId)

	// take payment
	paymentResult = s.SquareTakePayment(&paymentapipb.ComMoegoServerPaymentDtoSquareSquarePaymentRequest{
		Amount:      90,
		TipsAmount:  proto.Float32(0),
		CustomerId:  proto.Int32(15140343),
		Description: proto.String(""),
		InvoiceId:   invoiceId,
		Module:      proto.String("grooming"),
		PaidBy:      proto.String("Demo Profile"),
		Signature:   proto.String(""),
		StaffId:     proto.Int32(180299),
		CardNonce:   "cnon:card-nonce-rejected-cvv",
		IsOnline:    proto.Bool(false),
		IsDeposit:   proto.Int32(0),
		GroomingId:  proto.Int32(17777884),
	}, true)

	s.Require().NotNil(paymentResult)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：square 支付 card-nonce-rejected-postalcode
*/
func (s *SquarePayTestSuite) TestSquareCardRejectedPostalcode() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int32
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoServerPaymentDtoSquareSquareTakePaymentResponse

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = int32(appintmentInfo.GetInvoice().InvoiceId)

	// take payment
	paymentResult = s.SquareTakePayment(&paymentapipb.ComMoegoServerPaymentDtoSquareSquarePaymentRequest{
		Amount:      90,
		TipsAmount:  proto.Float32(0),
		CustomerId:  proto.Int32(15140343),
		Description: proto.String(""),
		InvoiceId:   invoiceId,
		Module:      proto.String("grooming"),
		PaidBy:      proto.String("Demo Profile"),
		Signature:   proto.String(""),
		StaffId:     proto.Int32(180299),
		CardNonce:   "cnon:card-nonce-rejected-postalcode",
		IsOnline:    proto.Bool(false),
		IsDeposit:   proto.Int32(0),
		GroomingId:  proto.Int32(17777884),
	}, true)

	s.Require().NotNil(paymentResult)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：square 支付 card-nonce-rejected-expiration
*/
func (s *SquarePayTestSuite) TestSquareCardRejectedExpiration() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int32
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoServerPaymentDtoSquareSquareTakePaymentResponse

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = int32(appintmentInfo.GetInvoice().InvoiceId)

	// take payment
	paymentResult = s.SquareTakePayment(&paymentapipb.ComMoegoServerPaymentDtoSquareSquarePaymentRequest{
		Amount:      90,
		TipsAmount:  proto.Float32(0),
		CustomerId:  proto.Int32(15140343),
		Description: proto.String(""),
		InvoiceId:   invoiceId,
		Module:      proto.String("grooming"),
		PaidBy:      proto.String("Demo Profile"),
		Signature:   proto.String(""),
		StaffId:     proto.Int32(180299),
		CardNonce:   "cnon:card-nonce-rejected-expiration",
		IsOnline:    proto.Bool(false),
		IsDeposit:   proto.Int32(0),
		GroomingId:  proto.Int32(17777884),
	}, true)

	s.Require().NotNil(paymentResult)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：square 支付 card-nonce-already-used
*/
func (s *SquarePayTestSuite) TestSquareCardNonceAlreadyUsed() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int32
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoServerPaymentDtoSquareSquareTakePaymentResponse

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = int32(appintmentInfo.GetInvoice().InvoiceId)

	// take payment
	paymentResult = s.SquareTakePayment(&paymentapipb.ComMoegoServerPaymentDtoSquareSquarePaymentRequest{
		Amount:      90,
		TipsAmount:  proto.Float32(0),
		CustomerId:  proto.Int32(15140343),
		Description: proto.String(""),
		InvoiceId:   invoiceId,
		Module:      proto.String("grooming"),
		PaidBy:      proto.String("Demo Profile"),
		Signature:   proto.String(""),
		StaffId:     proto.Int32(180299),
		CardNonce:   "cnon:card-nonce-already-used",
		IsOnline:    proto.Bool(false),
		IsDeposit:   proto.Int32(0),
		GroomingId:  proto.Int32(17777884),
	}, true)

	s.Require().NotNil(paymentResult)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：square 支付 gift-card-nonce-insufficient-funds
*/
func (s *SquarePayTestSuite) TestSquareCardNonceInsufficientFunds() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int32
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoServerPaymentDtoSquareSquareTakePaymentResponse

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = int32(appintmentInfo.GetInvoice().InvoiceId)

	// take payment
	paymentResult = s.SquareTakePayment(&paymentapipb.ComMoegoServerPaymentDtoSquareSquarePaymentRequest{
		Amount:      90,
		TipsAmount:  proto.Float32(0),
		CustomerId:  proto.Int32(15140343),
		Description: proto.String(""),
		InvoiceId:   invoiceId,
		Module:      proto.String("grooming"),
		PaidBy:      proto.String("Demo Profile"),
		Signature:   proto.String(""),
		StaffId:     proto.Int32(180299),
		CardNonce:   "cnon:gift-card-nonce-insufficient-funds",
		IsOnline:    proto.Bool(false),
		IsDeposit:   proto.Int32(0),
		GroomingId:  proto.Int32(17777884),
	}, true)

	s.Require().NotNil(paymentResult)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

/*
author:<EMAIL>
desc：square 支付 gift-card-nonce-insufficient-permission
*/
func (s *SquarePayTestSuite) TestSquareCardNonceInsufficientPermission() {
	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	var appointmentID int64
	var invoiceId int32
	var appintmentInfo *appointmentapipb.GetAppointmentResult
	var paymentResult *paymentapipb.ComMoegoServerPaymentDtoSquareSquareTakePaymentResponse

	// Set the date to four days before the current time.
	today := time.Now().Format("2006-01-02")
	appointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})
	log.InfoContextf(s.Ctx, "-------- appt id: %d", appointmentID)

	// 根据 appt id 获取 invoice id
	appintmentInfo = s.GetAppointment(&appointmentapipb.GetAppointmentParams{
		AppointmentId: appointmentID,
	})

	invoiceId = int32(appintmentInfo.GetInvoice().InvoiceId)

	// take payment
	paymentResult = s.SquareTakePayment(&paymentapipb.ComMoegoServerPaymentDtoSquareSquarePaymentRequest{
		Amount:      90,
		TipsAmount:  proto.Float32(0),
		CustomerId:  proto.Int32(15140343),
		Description: proto.String(""),
		InvoiceId:   invoiceId,
		Module:      proto.String("grooming"),
		PaidBy:      proto.String("Demo Profile"),
		Signature:   proto.String(""),
		StaffId:     proto.Int32(180299),
		CardNonce:   "cnon:gift-card-nonce-insufficient-permission",
		IsOnline:    proto.Bool(false),
		IsDeposit:   proto.Int32(0),
		GroomingId:  proto.Int32(17777884),
	}, true)

	s.Require().NotNil(paymentResult)

	// clear your apptointment
	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})
}

func TestSquarePaySuite(t *testing.T) {
	suite.Run(t, new(SquarePayTestSuite))
}
