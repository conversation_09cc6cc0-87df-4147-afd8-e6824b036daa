load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "old_invoice_permission_test",
    srcs = ["old_invoice_permission_test.go"],
    tags = ["single_run"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/permission/v1:permission",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/permission/v1:permission",
        "@com_github_stretchr_testify//suite",
    ],
)
