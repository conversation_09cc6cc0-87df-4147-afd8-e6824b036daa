package oldinvoicepermissiontest

import (
	"context"
	"testing"

	"github.com/stretchr/testify/suite"

	permissionapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/permission/v1"
	permissionpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/permission/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type OldInvoicePermissionTestSuite struct {
	suite.Suite
	godomain.Context
	setupDone bool
}

func (s *OldInvoicePermissionTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "StripePayTestSuite",
		Email:    "<EMAIL>", // pwd:AutoTest!123  <EMAIL>
	})
	s.setupDone = true
}

func (s *OldInvoicePermissionTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *OldInvoicePermissionTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
}

// Create a helper function to find permission by DisplayName
func findPermissionByDisplayName(permissions []*permissionpb.PermissionModel, displayName string) *permissionpb.PermissionModel {
	for _, permission := range permissions {
		if permission.DisplayName == displayName {
			return permission
		}
	}
	return nil
}

// Compare IsSelected field for RoleDetailPermissionList and ResultPermissionList
func comparePermissions(ctx context.Context, RoleDetailPermissionList, ResultPermissionList []*permissionpb.PermissionModel) {
	for _, rolePermission := range RoleDetailPermissionList {
		resultPermission := findPermissionByDisplayName(ResultPermissionList, rolePermission.DisplayName)
		if resultPermission != nil {
			if rolePermission.IsSelected != resultPermission.IsSelected {
				log.InfoContextf(ctx, "Mismatch found for permission: %s, RoleDetail IsSelected: %v, Result IsSelected: %v",
					rolePermission.DisplayName, rolePermission.IsSelected, resultPermission.IsSelected)
			}
		} else {
			log.InfoContextf(ctx, "Permission not found in ResultPermissionList: %s", rolePermission.DisplayName)
		}
	}
}

/*
author:<EMAIL>
desc：给第一个 staff 设置 payment 权限均为 false，验证权限下发
*/
func (s *OldInvoicePermissionTestSuite) TestAllPaymentPermissionFalseDispatch() {
	// 获取当前账号的 role list
	roleList := s.GetRoleList(&permissionapipb.GetRoleListParams{})
	var roleInfos []map[string]interface{}
	for _, role := range roleList.GetRoleList() {
		roleInfos = append(roleInfos, map[string]interface{}{
			"id":   role.Id,
			"name": role.Name,
		})
	}

	// 构造 SubPermissions
	subPermissions := []*permissionpb.EditPermissionDef{
		{PermissionId: 301, IsActive: true},
		{PermissionId: 302, IsActive: true},
	}

	// 构造完整的权限结构
	params := &permissionapipb.EditPermissionsParams{
		RoleId: roleInfos[0]["id"].(int64),
		PermissionCategoryList: []*permissionpb.EditCategoryPermissionDef{
			{
				CategoryId: 1, // 权限分类 ID
				Permissions: []*permissionpb.EditPermissionDef{
					{PermissionId: 2, IsActive: false},
					{PermissionId: 3, IsActive: false},
					{PermissionId: 4, IsActive: false},
					{PermissionId: 88, IsActive: false},
					{PermissionId: 101, IsActive: true},
					{
						PermissionId:   102,
						IsActive:       true,
						SubPermissions: subPermissions,
					},
					{PermissionId: 103, IsActive: false},
					{PermissionId: 104, IsActive: false},
					{PermissionId: 106, IsActive: true},
					{PermissionId: 303, IsActive: false},
				},
			},
		},
	}

	// 发起权限下发请求
	result := s.EditPermissions(params)

	// 通过 GetRoleDetail 获取最新的角色权限详情
	detail := s.GetRoleDetail(&permissionapipb.GetRoleDetailParams{
		RoleId: params.RoleId,
	})

	var RoleDetailPermissionList []*permissionpb.PermissionModel
	for _, category := range detail.RoleDetail.PermissionCategoryList {
		if category.DisplayName == "Payment" {
			RoleDetailPermissionList = category.GetPermissionList() // 调用方法
			break                                                   // 如果找到了 "Payment" 类别，跳出循环
		}
	}

	var ResultPermissionList []*permissionpb.PermissionModel
	for _, category := range result.PermissionCategoryList {
		if category.DisplayName == "Payment" {
			ResultPermissionList = category.GetPermissionList() // 调用方法
			break                                               // 如果找到了 "Payment" 类别，跳出循环
		}
	}

	// 比对下发结果
	comparePermissions(s.Ctx, RoleDetailPermissionList, ResultPermissionList)
}

/*
author:<EMAIL>
desc：给第一个 staff 设置 payment 权限 全都开启，验证 去权限下发
*/
func (s *OldInvoicePermissionTestSuite) TestAllPaymentPermissionTrueDispatch() {
	// 获取当前账号的 role list
	roleList := s.GetRoleList(&permissionapipb.GetRoleListParams{})
	var roleInfos []map[string]interface{}
	for _, role := range roleList.GetRoleList() {
		roleInfos = append(roleInfos, map[string]interface{}{
			"id":   role.Id,
			"name": role.Name,
		})
	}

	// 构造 SubPermissions
	subPermissions := []*permissionpb.EditPermissionDef{
		{PermissionId: 301, IsActive: true},
		{PermissionId: 302, IsActive: true},
	}

	// 构造完整的权限结构
	params := &permissionapipb.EditPermissionsParams{
		RoleId: roleInfos[0]["id"].(int64),
		PermissionCategoryList: []*permissionpb.EditCategoryPermissionDef{
			{
				CategoryId: 1, // 权限分类 ID
				Permissions: []*permissionpb.EditPermissionDef{
					{PermissionId: 2, IsActive: true},
					{PermissionId: 3, IsActive: true},
					{PermissionId: 4, IsActive: true},
					{PermissionId: 88, IsActive: true},
					{PermissionId: 101, IsActive: true},
					{
						PermissionId:   102,
						IsActive:       true,
						SubPermissions: subPermissions,
					},
					{PermissionId: 103, IsActive: false},
					{PermissionId: 104, IsActive: false},
					{PermissionId: 106, IsActive: true},
					{PermissionId: 303, IsActive: true},
				},
			},
		},
	}

	// 发起权限下发请求
	result := s.EditPermissions(params)

	// 通过 GetRoleDetail 获取最新的角色权限详情
	detail := s.GetRoleDetail(&permissionapipb.GetRoleDetailParams{
		RoleId: params.RoleId,
	})

	var RoleDetailPermissionList []*permissionpb.PermissionModel
	for _, category := range detail.RoleDetail.PermissionCategoryList {
		if category.DisplayName == "Payment" {
			RoleDetailPermissionList = category.GetPermissionList() // 调用方法
			break                                                   // 如果找到了 "Payment" 类别，跳出循环
		}
	}

	var ResultPermissionList []*permissionpb.PermissionModel
	for _, category := range result.PermissionCategoryList {
		if category.DisplayName == "Payment" {
			ResultPermissionList = category.GetPermissionList() // 调用方法
			break                                               // 如果找到了 "Payment" 类别，跳出循环
		}
	}

	// 比对下发结果
	comparePermissions(s.Ctx, RoleDetailPermissionList, ResultPermissionList)
}

func TestOldInvoicePermissionSuite(t *testing.T) {
	suite.Run(t, new(OldInvoicePermissionTestSuite))
}
