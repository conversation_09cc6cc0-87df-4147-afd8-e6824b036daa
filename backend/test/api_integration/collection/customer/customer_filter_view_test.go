package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerFilterViewTestSuite struct {
	suite.Suite
	godomain.Context
	viewID int64
}

func (s *CustomerFilterViewTestSuite) createCustomerView(
	params *customermodel.ComMoegoServerCustomerWebParamsClientsClientFilterParams) int64 {
	result := customermodel.NewComMoegoServerCustomerWebVoClientClientFilterVO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/filter/view/add").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	viewID := result.GetId()
	s.Require().NotNil(viewID)

	return *viewID
}

func (s *CustomerFilterViewTestSuite) deleteCustomerView(viewID int64) {
	response, err := s.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/filter/view/delete").
		AddQuery("id", strconv.FormatInt(viewID, 10)).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
}

func (s *CustomerFilterViewTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerFilterViewListTestSuite",
	})

	s.viewID = s.createCustomerView(&customermodel.ComMoegoServerCustomerWebParamsClientsClientFilterParams{
		Title: customermodel.PtrString("Test view"),
		Filter: &customermodel.ComMoegoCommonParamsFilterParams{
			Type: customermodel.PtrString("TYPE_AND"),
			Filters: []customermodel.ComMoegoCommonParamsFilterParams{
				{
					Operator: customermodel.PtrString("OPERATOR_EQUAL"),
					Property: customermodel.PtrString("client_status"),
					Value:    customermodel.PtrString("active"),
				},
				{
					Operator: customermodel.PtrString("OPERATOR_IN"),
					Property: customermodel.PtrString("client_type"),
					Values:   []string{"new"},
				},
			},
		},
	})
}

func (s *CustomerFilterViewTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *CustomerFilterViewTestSuite) mustTearDownSuite() {
	if s.viewID != 0 {
		s.deleteCustomerView(s.viewID)
	}
}

func (s *CustomerFilterViewTestSuite) TestCustomerFilterView() {
	result := customermodel.NewComMoegoServerCustomerWebVoClientClientFilterVO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/filter/view/detail").
		AddQuery("id", strconv.FormatInt(s.viewID, 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	ID := *result.GetId()
	s.Require().Equal(s.viewID, ID)
	title := *result.GetTitle()
	s.Require().Equal("Test view", title)
	filter := *result.GetFilter()
	s.Require().NotNil(filter)
}

func (s *CustomerFilterViewTestSuite) TestViewListContainsNewView() {
	result := &[]customermodel.ComMoegoServerCustomerWebVoClientClientFilterVO{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/filter/view/list").
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	found := false
	for _, view := range *result {
		if view.Id != nil && *view.Id == s.viewID {
			found = true
			break
		}
	}
	s.Require().True(found)
}

func (s *CustomerFilterViewTestSuite) TestUpdateCustomerViewWithTitle() {
	payload := &customermodel.ComMoegoServerCustomerWebParamsClientsClientFilterParams{
		Id:    customermodel.PtrInt64(s.viewID),
		Title: customermodel.PtrString("Update view"),
	}
	result := customermodel.NewComMoegoServerCustomerWebVoClientClientFilterVO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/filter/view/update").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	title := *result.GetTitle()
	s.Require().Equal("Update view", title)
}

func (s *CustomerFilterViewTestSuite) TestUpdateCustomerViewWithFilter() {
	payload := &customermodel.ComMoegoServerCustomerWebParamsClientsClientFilterParams{
		Id: customermodel.PtrInt64(s.viewID),
		Filter: &customermodel.ComMoegoCommonParamsFilterParams{
			Type: customermodel.PtrString("TYPE_AND"),
			Filters: []customermodel.ComMoegoCommonParamsFilterParams{
				{
					Operator: customermodel.PtrString("OPERATOR_EQUAL"),
					Property: customermodel.PtrString("client_status"),
					Value:    customermodel.PtrString("active"),
				},
			},
		},
	}
	result := customermodel.NewComMoegoServerCustomerWebVoClientClientFilterVO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/filter/view/update").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	filter := *result.GetFilter()
	firstFilter := filter.GetFilters()[0]
	s.Require().Len(filter.Filters, 1)
	s.Require().Equal("TYPE_AND", *filter.GetType())
	s.Require().Equal("OPERATOR_EQUAL", *firstFilter.GetOperator())
	s.Require().Equal("client_status", *firstFilter.GetProperty())
	s.Require().Equal("active", *firstFilter.GetValue())
}

func TestCustomerFilterViewTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerFilterViewTestSuite))
}
