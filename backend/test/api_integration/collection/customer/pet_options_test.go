package customer

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"

	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type PetOptionsTestSuite struct {
	suite.Suite
	godomain.Context
}

func (s *PetOptionsTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "PetOptionsTestSuite",
	})
}

func (s *PetOptionsTestSuite) TestDefaultPetOptions() {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoCustomerPetOptionDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/pet/options").
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	hairLengthList := result.GetData().GetHairLengthList()
	s.Require().Len(hairLengthList, 9)

	petBehaviorList := result.GetData().GetPetBehaviorList()
	s.Require().Len(petBehaviorList, 6)

	petBreedList := result.GetData().GetPetBreedList()
	s.Require().Len(petBreedList, 432)

	petCodeList := result.GetData().GetPetCodeList()
	s.Require().Len(petCodeList, 22)

	petColorList := result.GetData().GetPetColorList()
	s.Require().Len(petColorList, 6)

	petDeactivateReasonList := result.GetData().GetPetDeactivateReasonList()
	s.Require().Len(petDeactivateReasonList, 3)

	petFixedList := result.GetData().GetPetFixedList()
	s.Require().Len(petFixedList, 2)

	petTypeList := result.GetData().GetPetTypeList()
	s.Require().Len(petTypeList, 3)
}

func TestPetOptionsTestSuite(t *testing.T) {
	suite.Run(t, new(PetOptionsTestSuite))
}
