package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerAddressTestSuite struct {
	suite.Suite
	godomain.Context
	setupDone  bool
	CustomerID int32
}

func (s *CustomerAddressTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerOverviewTestSuite",
	})

	s.CustomerID, _ = s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
	})

	s.setupDone = true
}

func (s *CustomerAddressTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *CustomerAddressTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *CustomerAddressTestSuite) createAddress(payload *customermodel.ComMoegoServerCustomerDtoCustomerAddressDto) *customermodel.ComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoAddResultDTO {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoAddResultDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/address").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result
}

func (s *CustomerAddressTestSuite) updateAddress(payload *customermodel.ComMoegoServerCustomerWebVoCustomerAddressUpdateVo) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/address").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().True(*result.GetSuccess())
}

func (s *CustomerAddressTestSuite) deleteAddress(addressID int32) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/address").
		SetPayload(&customermodel.ComMoegoServerCustomerServiceParamsCommonIdParams{
			Id: addressID,
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().True(*result.GetData())
}

func (s *CustomerAddressTestSuite) TestAddAddressSuccess() {
	// create address
	payload := &customermodel.ComMoegoServerCustomerDtoCustomerAddressDto{
		Address1:   customermodel.PtrString("2434 South Harwood Street"),
		City:       customermodel.PtrString("Dallas"),
		Country:    customermodel.PtrString("US"),
		State:      customermodel.PtrString("TX"),
		Zipcode:    customermodel.PtrString("75215"),
		CustomerId: customermodel.PtrInt32(s.CustomerID),
		Lat:        customermodel.PtrString("32.768392399999996"),
		Lng:        customermodel.PtrString("-96.77982329999999"),
	}

	result := s.createAddress(payload)
	addressID := *result.GetData().GetId()

	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	addressList := customerDetail.GetAddressList()
	s.Require().Len(addressList, 1)
	firstAddress := addressList[0]
	s.Require().Equal(addressID, *firstAddress.GetAddressId())
	s.Require().Equal("2434 South Harwood Street", *firstAddress.GetAddress1())
	s.Require().Equal("Dallas", *firstAddress.GetCity())
	s.Require().Equal("US", *firstAddress.GetCountry())
	s.Require().Equal("TX", *firstAddress.GetState())
	s.Require().Equal("75215", *firstAddress.GetZipcode())
	s.Require().Equal(s.CustomerID, *firstAddress.GetCustomerId())
	s.Require().Equal(int32(1), *firstAddress.GetIsPrimary())
}

func (s *CustomerAddressTestSuite) TestUpdatePrimaryAddress() {
	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	addressList := customerDetail.GetAddressList()
	s.Require().Len(addressList, 1)
	addressID := *addressList[0].GetAddressId()
	payload := &customermodel.ComMoegoServerCustomerWebVoCustomerAddressUpdateVo{
		Address1:          customermodel.PtrString("3425 West 1st Street"),
		City:              customermodel.PtrString("Los Angeles"),
		Country:           customermodel.PtrString("US"),
		State:             customermodel.PtrString("CA"),
		Zipcode:           customermodel.PtrString("90004"),
		CustomerAddressId: addressID,
		Lat:               customermodel.PtrString("34.0737573"),
		Lng:               customermodel.PtrString("-118.2876745"),
	}
	s.updateAddress(payload)

	customerDetail = s.GetCustomerDetailData(s.CustomerID)
	addressList = customerDetail.GetAddressList()
	s.Require().Len(addressList, 1)
	firstAddress := addressList[0]
	s.Require().Equal(addressID, *firstAddress.GetAddressId())
	s.Require().Equal("3425 West 1st Street", *firstAddress.GetAddress1())
	s.Require().Equal("Los Angeles", *firstAddress.GetCity())
	s.Require().Equal("US", *firstAddress.GetCountry())
	s.Require().Equal("CA", *firstAddress.GetState())
	s.Require().Equal("90004", *firstAddress.GetZipcode())
	s.Require().Equal(s.CustomerID, *firstAddress.GetCustomerId())
	s.Require().Equal(int32(1), *firstAddress.GetIsPrimary())
}

func (s *CustomerAddressTestSuite) TestDeleteAddress() {
	// create address
	payload := &customermodel.ComMoegoServerCustomerDtoCustomerAddressDto{
		Address1:   customermodel.PtrString("3425 1st Street"),
		City:       customermodel.PtrString("Los Angeles"),
		Country:    customermodel.PtrString("US"),
		State:      customermodel.PtrString("CA"),
		Zipcode:    customermodel.PtrString("90004"),
		CustomerId: customermodel.PtrInt32(s.CustomerID),
		Lat:        customermodel.PtrString("34.0737573"),
		Lng:        customermodel.PtrString("-118.2876745"),
	}
	result := s.createAddress(payload)
	addressID := *result.GetData().GetId()
	customerDetail := s.GetCustomerDetailData(s.CustomerID)
	addressList := customerDetail.GetAddressList()
	s.Require().Len(addressList, 2)
	secondAddress := addressList[1]
	s.Require().Equal(addressID, *secondAddress.GetAddressId())
	s.Require().Equal("3425 1st Street", *secondAddress.GetAddress1())
	s.Require().Equal("Los Angeles", *secondAddress.GetCity())
	s.Require().Equal("US", *secondAddress.GetCountry())
	s.Require().Equal("CA", *secondAddress.GetState())
	s.Require().Equal("90004", *secondAddress.GetZipcode())
	s.Require().Equal(s.CustomerID, *secondAddress.GetCustomerId())
	s.Require().Equal(int32(0), *secondAddress.GetIsPrimary())

	s.deleteAddress(addressID)
	customerDetail = s.GetCustomerDetailData(s.CustomerID)
	addressList = customerDetail.GetAddressList()
	s.Require().Len(addressList, 1)
}

func TestCustomerAddress(t *testing.T) {
	suite.Run(t, new(CustomerAddressTestSuite))
}
