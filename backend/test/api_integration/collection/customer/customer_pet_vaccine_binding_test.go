package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type PetVaccineBindingTestSuite struct {
	suite.Suite
	godomain.Context
	setupDone  bool
	CustomerID int32
	PetIDs     []int32
	PetID      int32
	VaccineID  int64
}

func (s *PetVaccineBindingTestSuite) getCustomerPetDetailList(petID int32) *[]customermodel.ComMoegoServerCustomerDtoCustomerPetDTO {
	result := &[]customermodel.ComMoegoServerCustomerDtoCustomerPetDTO{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/pet/detail/list").
		AddQuery("petIdList", strconv.FormatInt(int64(petID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result
}

func (s *PetVaccineBindingTestSuite) createPetVaccineBinding() int32 {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/pet/vaccine/binding").
		SetPayload(&customermodel.ComMoegoServerCustomerParamsVaccineBindingSaveVo{
			PetId:          s.PetID,
			VaccineId:      int32(s.VaccineID),
			ExpirationDate: "2025-06-20",
			DocumentUrls:   []string{},
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	bindingID := *result.GetData()
	return bindingID
}

func (s *PetVaccineBindingTestSuite) deletePetVaccineBinding(bindingID int32) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/pet/vaccine/binding").
		SetPayload(&customermodel.ComMoegoServerCustomerWebVoVaccineBindingDeleteVo{
			VaccineBindingId: bindingID,
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
}

func (s *PetVaccineBindingTestSuite) SetupSuite() {
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "PetVaccineBindingTestSuite",
	})

	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(2),
				Breed:     customermodel.PtrString("American Shorthair"),
			},
		},
	})
	s.PetID = s.PetIDs[0]
	vaccineIDs := s.GetVaccineIDsFromListPetVaccine()
	s.VaccineID = vaccineIDs[0]
	s.setupDone = true
}

func (s *PetVaccineBindingTestSuite) TestCreatePetVaccine() {
	bindingID := s.createPetVaccineBinding()

	petDetailList := s.getCustomerPetDetailList(s.PetID)
	petDetail := (*petDetailList)[0]
	petVaccine := petDetail.GetVaccineBindingList()[0]
	s.Require().Equal(int32(s.VaccineID), *petVaccine.GetVaccineId())
	s.Require().Equal(bindingID, *petVaccine.GetVaccineBindingId())

	s.deletePetVaccineBinding(bindingID)
}

func (s *PetVaccineBindingTestSuite) TestEditPetVaccine() {
	bindingID := s.createPetVaccineBinding()

	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/pet/vaccine/binding").
		SetPayload(&customermodel.ComMoegoServerCustomerWebVoVaccineBindingUpdateVo{
			VaccineBindingId: bindingID,
			VaccineId:        int32(s.VaccineID),
			ExpirationDate:   "2025-06-15",
			DocumentUrls:     []string{},
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().True(*result.GetData())

	petDetailList := s.getCustomerPetDetailList(s.PetID)
	petDetail := (*petDetailList)[0]
	vaccineBinding := petDetail.GetVaccineBindingList()[0]
	s.Require().Equal("2025-06-15", *vaccineBinding.GetExpirationDate())

	s.deletePetVaccineBinding(bindingID)
}

func (s *PetVaccineBindingTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *PetVaccineBindingTestSuite) mustTearDownSuite() {
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func TestPetVaccineBindingTestSuite(t *testing.T) {
	suite.Run(t, new(PetVaccineBindingTestSuite))
}
