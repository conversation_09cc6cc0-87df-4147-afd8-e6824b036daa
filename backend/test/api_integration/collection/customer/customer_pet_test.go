package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerPetTestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID int32
	PetIDs     []int32
	setupDone  bool
}

func (s *CustomerPetTestSuite) getPetList(customerID int32) []customermodel.ComMoegoServerCustomerDtoPetListDetailDto {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaUtilListComMoegoServerCustomerDtoPetListDetailDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/pet/list").
		AddQuery("customerId", strconv.FormatInt(int64(customerID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result.GetData()
}

func (s *CustomerPetTestSuite) getCustomerPetDetailList(petID int32) *[]customermodel.ComMoegoServerCustomerDtoCustomerPetDTO {
	result := &[]customermodel.ComMoegoServerCustomerDtoCustomerPetDTO{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/pet/detail/list").
		AddQuery("petIdList", strconv.FormatInt(int64(petID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result
}

func (s *CustomerPetTestSuite) deletePet(params *customermodel.ComMoegoServerCustomerServiceParamsCommonIdParams) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangBoolean()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/pet").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
}

func (s *CustomerPetTestSuite) updateCustomerPet(params *customermodel.ComMoegoServerCustomerParamsCustomerPetUpdateParams) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/pet").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	data := result.GetData()
	s.Require().Greater(*data, int32(0))
}

func (s *CustomerPetTestSuite) updatePetLifeStatus(params *customermodel.ComMoegoServerCustomerServiceParamsPetLifeStatusParams) {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoCommonResponseResponseResultJavaLangString()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/pet/lifeStatus").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
}

func (s *CustomerPetTestSuite) getPetDetail(petID int32) *customermodel.ComMoegoServerCustomerDtoCustomerPetDTO {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoCustomerPetDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/pet/detail").
		AddQuery("petId", strconv.FormatInt(int64(petID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().NotNil(result.GetData())

	return result.GetData()
}

func (s *CustomerPetTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerPetTestSuite",
	})

	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
	})

	s.setupDone = true
}

func (s *CustomerPetTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *CustomerPetTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *CustomerPetTestSuite) TestGetPetDetail() {
	petDetail := s.getPetDetail(s.PetIDs[0]).GetPetDetail()
	s.Require().Equal("Max", *petDetail.GetPetName())
	s.Require().Equal(int32(1), *petDetail.GetPetTypeId())
	s.Require().Equal("Dog", *petDetail.GetTypeName())
	s.Require().Equal("Affenpinscher", *petDetail.GetBreed())
}

func (s *CustomerPetTestSuite) TestGetPetList() {
	petList := s.getPetList(s.CustomerID)
	s.Require().NotEmpty(petList)
	firstPet := petList[0]
	s.Require().Equal(s.PetIDs[0], *firstPet.GetPetId())
	s.Require().Equal("Affenpinscher", *firstPet.GetBreed())
	s.Require().Equal("Max", *firstPet.GetPetName())
	s.Require().Equal(int32(1), *firstPet.GetPetTypeId())
}

func (s *CustomerPetTestSuite) TestGetPetDetailList() {
	petDetailList := s.getCustomerPetDetailList(s.PetIDs[0])
	s.Require().Len(*petDetailList, 1)
	petDetail := (*petDetailList)[0].GetPetDetail()
	s.Require().Equal("Max", *petDetail.GetPetName())
	s.Require().Equal("Affenpinscher", *petDetail.GetBreed())
	s.Require().Equal(int32(1), *petDetail.GetPetTypeId())
}

func (s *CustomerPetTestSuite) TestAddCustomerPet() {
	payload := &customermodel.ComMoegoServerCustomerParamsCustomerPetAddParams{
		CustomerId: s.CustomerID,
		PetName:    customermodel.PtrString("Mini"),
		PetTypeId:  customermodel.PtrInt32(1),
		Breed:      customermodel.PtrString("Jindo"),
	}
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/pet").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	petID := result.GetData()
	s.Require().Greater(*petID, int32(0))
	// check pet list
	petList := s.getPetList(s.CustomerID)
	s.Require().Len(petList, 2)

	// check pet detail
	petDetail := s.getPetDetail(*petID).GetPetDetail()
	s.Require().Equal("Mini", *petDetail.GetPetName())
	s.Require().Equal(int32(1), *petDetail.GetPetTypeId())
	s.Require().Equal("Jindo", *petDetail.GetBreed())

	// delete the pet created in this test
	s.deletePet(&customermodel.ComMoegoServerCustomerServiceParamsCommonIdParams{
		Id: *petID,
	})
	petList = s.getPetList(s.CustomerID)
	s.Require().Len(petList, 1)
}

func (s *CustomerPetTestSuite) TestAddPetNote() {
	payload := &customermodel.ComMoegoServerCustomerParamsPetNoteSaveVo{
		PetId: s.PetIDs[0],
		Note:  "This is a note",
	}
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/pet/note").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	nodeID := result.GetData()
	s.Require().Greater(*nodeID, int32(0))

	// get pet note list
	petNote := s.getPetDetail(s.PetIDs[0]).GetPetNoteList()[0]
	s.Require().Equal("This is a note", *petNote.GetNote())
}

func (s *CustomerPetTestSuite) TestAddMedical() {
	s.updateCustomerPet(&customermodel.ComMoegoServerCustomerParamsCustomerPetUpdateParams{
		Id:         s.PetIDs[0],
		VetName:    customermodel.PtrString("Vet Name"),
		VetPhone:   customermodel.PtrString("**********"),
		VetAddress: customermodel.PtrString("Vet Address"),
	})

	petMedical := s.getPetDetail(s.PetIDs[0]).GetMedicalInfo()
	s.Require().Equal("Vet Name", *petMedical.GetVetName())
	s.Require().Equal("**********", *petMedical.GetVetPhone())
	s.Require().Equal("Vet Address", *petMedical.GetVetAddress())
}

func (s *CustomerPetTestSuite) TestMarkAsPassedAway() {
	payload := &customermodel.ComMoegoServerCustomerServiceParamsPetLifeStatusParams{
		Id:         customermodel.PtrInt32(s.PetIDs[0]),
		LifeStatus: customermodel.PtrInt32(2),
	}

	s.updatePetLifeStatus(payload)

	petDetail := s.getPetDetail(s.PetIDs[0]).GetPetDetail()
	s.Require().Equal(int32(2), *petDetail.GetLifeStatus())
	s.Require().True(*petDetail.GetPassedAway())

	payload.LifeStatus = customermodel.PtrInt32(1)
	s.updatePetLifeStatus(payload)

	petDetail = s.getPetDetail(s.PetIDs[0]).GetPetDetail()
	s.Require().Equal(int32(1), *petDetail.GetLifeStatus())
	s.Require().Equal(false, *petDetail.GetPassedAway())
}

func (s *CustomerPetTestSuite) TestAddPetCode() {
	petCodeIDs := s.GetPetCodeIDsFromListPetCode()
	s.updateCustomerPet(&customermodel.ComMoegoServerCustomerParamsCustomerPetUpdateParams{
		Id:            s.PetIDs[0],
		PetCodeIdList: []int32{int32(petCodeIDs[0])},
	})

	petCodeList := s.getPetDetail(s.PetIDs[0]).GetPetCodeList()
	s.Require().Len(petCodeList, 1)
	s.Require().Equal(int32(petCodeIDs[0]), *petCodeList[0].GetPetCodeId())
}

func (s *CustomerPetTestSuite) TestAddPetVaccination() {
	vaccinationIDs := s.GetVaccineIDsFromListPetVaccine()
	// binding vaccination to pet
	payload := &customermodel.ComMoegoServerCustomerParamsVaccineBindingSaveVo{
		PetId:          s.PetIDs[0],
		VaccineId:      int32(vaccinationIDs[0]),
		ExpirationDate: "2025-03-15",
	}
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/pet/vaccine/binding").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	vaccinationList := s.getPetDetail(s.PetIDs[0]).GetVaccineBindingList()
	s.Require().Len(vaccinationList, 1)
	s.Require().Equal(int32(vaccinationIDs[0]), *vaccinationList[0].GetVaccineId())
	s.Require().Equal("2025-03-15", *vaccinationList[0].GetExpirationDate())
}

func TestClientPetTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerPetTestSuite))
}
