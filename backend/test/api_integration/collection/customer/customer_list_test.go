package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerListTestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID int32
	PetIDs     []int32
	setupDone  bool
}

func (s *CustomerListTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerListTestSuite",
	})

	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(2),
				Breed:     customermodel.PtrString("American Shorthair"),
			},
		},
	})

	s.setupDone = true
}

func (s *CustomerListTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *CustomerListTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *CustomerListTestSuite) getCustomerIDsFromCustomerList(params *customermodel.ComMoegoServerCustomerDtoCustomerSearchVo) []int32 {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerPageListDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/list").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	customerList := result.GetData().GetCustomerList()
	s.Require().NotNil(customerList)

	var customerIDs []int32
	for _, customer := range customerList {
		customerID := customer.GetCustomerId()
		s.Require().NotNil(customerID)
		s.Require().Greater(*customerID, int32(0))
		customerIDs = append(customerIDs, *customerID)
	}

	return customerIDs
}

func (s *CustomerListTestSuite) getCustomerListTotalData(params *customermodel.ComMoegoServerCustomerDtoCustomerSearchVo) *customermodel.ComMoegoServerCustomerWebVoCustomerListTotalDto {
	result := customermodel.NewComMoegoServerCustomerWebVoCustomerListTotalDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/list/total").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().NotNil(result)

	return result
}

func (s *CustomerListTestSuite) TestCustomerList() {
	customerIDs := s.getCustomerIDsFromCustomerList(&customermodel.ComMoegoServerCustomerDtoCustomerSearchVo{
		StatusSetting: &customermodel.ComMoegoServerCustomerParamsCustomerSearchStatusVo{
			ActiveClient: customermodel.PtrBool(true),
		},
		SortType: customermodel.PtrInt32(1),
		PageNum:  customermodel.PtrInt32(1),
		PageSize: customermodel.PtrInt32(10),
		Keyword:  customermodel.PtrString(""),
		TagIds:   []int32{},
	})

	s.Require().Contains(customerIDs, s.CustomerID)
}

func (s *CustomerListTestSuite) TestCustomerListWithKeyword() {
	payload := &customermodel.ComMoegoServerCustomerDtoCustomerSearchVo{
		StatusSetting: &customermodel.ComMoegoServerCustomerParamsCustomerSearchStatusVo{
			All: customermodel.PtrBool(true),
		},
		SortType: customermodel.PtrInt32(1),
		PageNum:  customermodel.PtrInt32(1),
		PageSize: customermodel.PtrInt32(30),
		Keyword:  customermodel.PtrString("Joshua"),
		TagIds:   []int32{},
	}
	customerIDs := s.getCustomerIDsFromCustomerList(payload)
	s.Require().Contains(customerIDs, s.CustomerID)

	payload.Keyword = customermodel.PtrString("<EMAIL>")
	customerIDs = s.getCustomerIDsFromCustomerList(payload)
	s.Require().Contains(customerIDs, s.CustomerID)

	payload.Keyword = customermodel.PtrString("**********")
	customerIDs = s.getCustomerIDsFromCustomerList(payload)
	s.Require().Contains(customerIDs, s.CustomerID)

	payload.Keyword = customermodel.PtrString("Max")
	customerIDs = s.getCustomerIDsFromCustomerList(payload)
	s.Require().Contains(customerIDs, s.CustomerID)
}

func (s *CustomerListTestSuite) TestCustomerListWithTags() {
	tagIDs := s.GetTagIDsFromListCustomerTag()

	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:        s.CustomerID,
		CustomerTagIdList: []int32{int32(tagIDs[0])},
	})

	payload := &customermodel.ComMoegoServerCustomerDtoCustomerSearchVo{
		StatusSetting: &customermodel.ComMoegoServerCustomerParamsCustomerSearchStatusVo{
			All: customermodel.PtrBool(true),
		},
		SortType: customermodel.PtrInt32(1),
		PageNum:  customermodel.PtrInt32(1),
		PageSize: customermodel.PtrInt32(30),
		Keyword:  customermodel.PtrString(""),
		TagIds:   []int32{int32(tagIDs[0])},
	}
	customerIDs := s.getCustomerIDsFromCustomerList(payload)
	s.Require().Contains(customerIDs, s.CustomerID)
}

func (s *CustomerListTestSuite) TestCustomerListWithCategory() {
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId: s.CustomerID,
		Inactive:   customermodel.PtrInt32(1),
	})

	payload := &customermodel.ComMoegoServerCustomerDtoCustomerSearchVo{
		StatusSetting: &customermodel.ComMoegoServerCustomerParamsCustomerSearchStatusVo{
			InactiveClient: customermodel.PtrBool(true),
		},
		SortType: customermodel.PtrInt32(1),
		PageNum:  customermodel.PtrInt32(1),
		PageSize: customermodel.PtrInt32(30),
		Keyword:  customermodel.PtrString(""),
		TagIds:   []int32{},
	}
	customerIDs := s.getCustomerIDsFromCustomerList(payload)
	s.Require().Contains(customerIDs, s.CustomerID)
}

func (s *CustomerListTestSuite) TestCustomerListTotal() {
	payload := &customermodel.ComMoegoServerCustomerDtoCustomerSearchVo{
		StatusSetting: &customermodel.ComMoegoServerCustomerParamsCustomerSearchStatusVo{
			All: customermodel.PtrBool(true),
		},
		SortType: customermodel.PtrInt32(1),
		PageNum:  customermodel.PtrInt32(1),
		PageSize: customermodel.PtrInt32(30),
		Keyword:  customermodel.PtrString(""),
		TagIds:   []int32{},
	}

	customerListTotalData := s.getCustomerListTotalData(payload)
	// contain default created customer: Demo Profile
	s.Require().Equal(int32(2), *customerListTotalData.GetCount())

	petCounstList := customerListTotalData.GetPetCountList()
	s.Require().NotNil(petCounstList)
	// check dog count
	s.Require().Equal(int32(1), *petCounstList[0].GetPetTypeId())
	s.Require().Equal(int32(2), *petCounstList[0].GetCount())
	s.Require().Equal("Dog", *petCounstList[0].GetTypeName())
	// check cat count
	s.Require().Equal(int32(2), *petCounstList[1].GetPetTypeId())
	s.Require().Equal(int32(1), *petCounstList[1].GetCount())
	s.Require().Equal("Cat", *petCounstList[1].GetTypeName())
}

func (s *CustomerListTestSuite) TestCustomerListTotalWithKeyword() {
	payload := &customermodel.ComMoegoServerCustomerDtoCustomerSearchVo{
		StatusSetting: &customermodel.ComMoegoServerCustomerParamsCustomerSearchStatusVo{
			All: customermodel.PtrBool(true),
		},
		SortType: customermodel.PtrInt32(1),
		PageNum:  customermodel.PtrInt32(1),
		PageSize: customermodel.PtrInt32(30),
		Keyword:  customermodel.PtrString("Joshua"),
		TagIds:   []int32{},
	}

	customerListTotalData := s.getCustomerListTotalData(payload)
	s.Require().Equal(int32(1), *customerListTotalData.GetCount())
	petCounstList := customerListTotalData.GetPetCountList()
	s.Require().NotNil(petCounstList)
	// check cat count
	s.Require().Equal(int32(2), *petCounstList[0].GetPetTypeId())
	s.Require().Equal(int32(1), *petCounstList[0].GetCount())
	s.Require().Equal("Cat", *petCounstList[0].GetTypeName())
}

func (s *CustomerListTestSuite) TestCustomerListTotalWithTags() {
	tagIDs := s.GetTagIDsFromListCustomerTag()

	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:        s.CustomerID,
		CustomerTagIdList: []int32{int32(tagIDs[1])},
	})

	payload := &customermodel.ComMoegoServerCustomerDtoCustomerSearchVo{
		StatusSetting: &customermodel.ComMoegoServerCustomerParamsCustomerSearchStatusVo{
			All: customermodel.PtrBool(true),
		},
		SortType: customermodel.PtrInt32(1),
		PageNum:  customermodel.PtrInt32(1),
		PageSize: customermodel.PtrInt32(30),
		Keyword:  customermodel.PtrString(""),
		TagIds:   []int32{int32(tagIDs[1])},
	}

	customerListTotalData := s.getCustomerListTotalData(payload)
	s.Require().Greater(*customerListTotalData.GetCount(), int32(1))
}

func (s *CustomerListTestSuite) TestCustomerListTotalWithCategory() {
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId: s.CustomerID,
		Inactive:   customermodel.PtrInt32(1),
	})

	payload := &customermodel.ComMoegoServerCustomerDtoCustomerSearchVo{
		StatusSetting: &customermodel.ComMoegoServerCustomerParamsCustomerSearchStatusVo{
			InactiveClient: customermodel.PtrBool(true),
		},
		SortType: customermodel.PtrInt32(1),
		PageNum:  customermodel.PtrInt32(1),
		PageSize: customermodel.PtrInt32(30),
		Keyword:  customermodel.PtrString(""),
		TagIds:   []int32{},
	}

	customerListTotalData := s.getCustomerListTotalData(payload)
	s.Require().Equal(int32(1), *customerListTotalData.GetCount())
	petCounstList := customerListTotalData.GetPetCountList()
	s.Require().NotNil(petCounstList)
	// check cat count
	s.Require().Equal(int32(2), *petCounstList[0].GetPetTypeId())
	s.Require().Equal(int32(1), *petCounstList[0].GetCount())
	s.Require().Equal("Cat", *petCounstList[0].GetTypeName())
}

func TestCustomerListTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerListTestSuite))
}
