package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerBasicTestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID int32
	PetIDs     []int32
	setupDone  bool
}

func (s *CustomerBasicTestSuite) getBasicData(customerID int32) *customermodel.ComMoegoServerCustomerDtoCustomerInfoDto {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoCustomerInfoDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/basic").
		AddQuery("customerId", strconv.FormatInt(int64(customerID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().NotNil(result.GetData())

	return result.GetData()
}

func (s *CustomerBasicTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerBasicTestSuite",
	})

	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
		PreferredFrequencyDay:  customermodel.PtrInt32(20),
		PreferredFrequencyType: customermodel.PtrInt32(0),
		PreferredDay:           []int32{0, 1, 2, 3},
		PreferredTime:          []int32{540, 1080},
		IsUnsubscribed:         customermodel.PtrInt32(1),
		SendAutoMessage:        customermodel.PtrInt32(1),
		SendAutoEmail:          customermodel.PtrInt32(1),
		ApptReminderByList:     []int32{},
		ClientColor:            customermodel.PtrString("#ff9800"),
	})

	s.setupDone = true
}

func (s *CustomerBasicTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *CustomerBasicTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *CustomerBasicTestSuite) TestCustomerBasic() {
	basicData := s.getBasicData(s.CustomerID)
	s.Require().Equal(s.CustomerID, *basicData.GetCustomerId())
	s.Require().Equal("Joshua", *basicData.GetFirstName())
	s.Require().Equal("Dewey", *basicData.GetLastName())
	s.Require().Equal("<EMAIL>", *basicData.GetEmail())
	s.Require().Equal(int32(20), *basicData.GetPreferredFrequencyDay())
	s.Require().Equal(int32(0), *basicData.GetPreferredFrequencyType())
	s.Require().Equal([]int32{0, 1, 2, 3}, basicData.GetPreferredDay())
	s.Require().Equal([]int32{540, 1080}, basicData.GetPreferredTime())
	s.Require().Equal([]int32{}, basicData.GetApptReminderByList())
	s.Require().Equal(int32(1), *basicData.GetSendAppAutoMessage())
	s.Require().Equal(int32(1), *basicData.GetSendAutoEmail())
	s.Require().Equal(int32(1), *basicData.GetIsUnsubscribed())
	s.Require().Equal("#ff9800", *basicData.GetClientColor())
}

func (s *CustomerBasicTestSuite) TestUpdateCustomerBasic() {
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:  s.CustomerID,
		FirstName:   customermodel.PtrString("John"),
		LastName:    customermodel.PtrString("Doe"),
		Email:       customermodel.PtrString("<EMAIL>"),
		PhoneNumber: customermodel.PtrString("**********"),
	})

	basicData := s.getBasicData(s.CustomerID)
	s.Require().Equal(s.CustomerID, *basicData.GetCustomerId())
	s.Require().Equal("John", *basicData.GetFirstName())
	s.Require().Equal("Doe", *basicData.GetLastName())
	s.Require().Equal("<EMAIL>", *basicData.GetEmail())
}

func TestCustomerBasicTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerBasicTestSuite))
}
