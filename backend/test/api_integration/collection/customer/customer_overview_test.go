package customer

import (
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	appointmentapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	appointmentmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerOverviewTestSuite struct {
	suite.Suite
	godomain.Context
	setupDone     bool
	CustomerID    int32
	PetIDs        []int32
	TagIDs        []int64
	Services      []*offeringpb.CustomizedServiceView
	AppointmentID int64
}

func (s *CustomerOverviewTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerOverviewTestSuite",
	})

	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
		Address1:               customermodel.PtrString("4313 Bylsma Circle"),
		City:                   customermodel.PtrString("Panama City"),
		Country:                customermodel.PtrString("US"),
		State:                  customermodel.PtrString("FL"),
		Zipcode:                customermodel.PtrString("32404"),
		PreferredFrequencyDay:  customermodel.PtrInt32(20),
		PreferredFrequencyType: customermodel.PtrInt32(0),
		PreferredDay:           []int32{0, 1, 2, 3},
		PreferredTime:          []int32{540, 1080},
		IsUnsubscribed:         customermodel.PtrInt32(1),
		SendAutoMessage:        customermodel.PtrInt32(1),
		SendAutoEmail:          customermodel.PtrInt32(1),
		ApptReminderByList:     []int32{},
		ClientColor:            customermodel.PtrString("#ff9800"),
	})

	s.TagIDs = s.GetTagIDsFromListCustomerTag()

	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:        s.CustomerID,
		CustomerTagIdList: []int32{int32(s.TagIDs[0])},
	})

	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	s.setupDone = true
}

func (s *CustomerOverviewTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *CustomerOverviewTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *CustomerOverviewTestSuite) TestCustomerOverview() {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerOverviewDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/overview").
		AddQuery("customerId", strconv.FormatInt(int64(s.CustomerID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	petList := result.GetData().GetPetList()
	// get pet detail
	petDetail := petList[0].GetPetDetail()

	s.Require().Equal(s.PetIDs[0], *petDetail.GetPetId())
	s.Require().Equal("Max", *petDetail.GetPetName())
	s.Require().Equal(int32(1), *petDetail.GetPetTypeId())
	s.Require().Equal("Affenpinscher", *petDetail.GetBreed())

	// get customer detail
	customerDetail := result.GetData().GetCustomerDetail()
	s.Require().Equal("Joshua", *customerDetail.GetFirstName())
	s.Require().Equal("Dewey", *customerDetail.GetLastName())
	s.Require().Equal("<EMAIL>", *customerDetail.GetEmail())
	s.Require().Equal("**********", *customerDetail.GetPhoneNumber())

	address := customerDetail.GetAddress()
	s.Require().Equal("4313 Bylsma Circle", *address.GetAddress1())
	s.Require().Equal("Panama City", *address.GetCity())
	s.Require().Equal("US", *address.GetCountry())
	s.Require().Equal("FL", *address.GetState())
	s.Require().Equal("32404", *address.GetZipcode())

	// get customer tags
	customerTags := result.GetData().GetCustomerTags()
	s.Require().Equal(int32(s.TagIDs[0]), *customerTags[0].GetId())
}

func (s *CustomerOverviewTestSuite) TestOverviewMetricWithCancelled() {
	// create an appointment
	today := time.Now().Format("2006-01-02")
	appointmentID := s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(2),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})

	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerOverviewDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/overview").
		AddQuery("customerId", strconv.FormatInt(int64(s.CustomerID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	appointmentInfo := result.GetData().GetAppointmentInfo()
	s.Require().Greater(*appointmentInfo.GetCancelled(), int32(0))
}

func (s *CustomerOverviewTestSuite) TestOverviewMetricWithNoShow() {
	// create an appointment
	today := time.Now().Format("2006-01-02")
	appointmentID := s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	s.CancelAppointment(&appointmentmodel.ComMoegoServerGroomingParamsCancelParams{
		Id:             appointmentmodel.PtrInt32(int32(appointmentID)),
		NoShow:         appointmentmodel.PtrInt32(1),
		CancelByType:   appointmentmodel.PtrInt32(0),
		CancelReason:   appointmentmodel.PtrString(""),
		ReleasePreAuth: appointmentmodel.PtrBool(true),
	})

	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerOverviewDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/overview").
		AddQuery("customerId", strconv.FormatInt(int64(s.CustomerID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	appointmentInfo := result.GetData().GetAppointmentInfo()
	s.Require().Greater(*appointmentInfo.GetCancelled(), int32(0))
}

func TestCustomerOverviewTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerOverviewTestSuite))
}
