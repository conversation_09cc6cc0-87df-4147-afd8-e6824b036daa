package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerPetNoteTestSuite struct {
	suite.Suite
	godomain.Context
	CustomerID int32
	PetIDs     []int32
	setupDone  bool
}

func (s *CustomerPetNoteTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerPetNoteTestSuite",
	})

	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(2),
				Breed:     customermodel.PtrString("American Shorthair"),
			},
		},
	})

	s.setupDone = true
}

func (s *CustomerPetNoteTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *CustomerPetNoteTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *CustomerPetNoteTestSuite) addCustomerNote(params *customermodel.ComMoegoServerCustomerParamsCustomerNoteSaveVo) {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerDtoAddResultDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/note").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
}

func (s *CustomerPetNoteTestSuite) addPetNote(params *customermodel.ComMoegoServerCustomerParamsPetNoteSaveVo) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/pet/note").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
}

func (s *CustomerPetNoteTestSuite) TestCustomerNotes() {
	s.addCustomerNote(&customermodel.ComMoegoServerCustomerParamsCustomerNoteSaveVo{
		CustomerId: s.CustomerID,
		Note:       "This is a customer note",
	})

	payload := &customermodel.ComMoegoServerCustomerWebVoCustomerPetNoteByVo{
		CustomerId: s.CustomerID,
		PetIdList:  s.PetIDs,
	}
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerPetNoteListDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/note/withPet").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	customerNotes := result.GetData().GetCustomerNotes()
	s.Require().Len(customerNotes, 1)
	s.Require().Equal("This is a customer note", *customerNotes[0].GetNote())
	s.Require().Equal(s.CustomerID, *customerNotes[0].GetCustomerId())
}

func (s *CustomerPetNoteTestSuite) TestPetNotes() {
	s.addPetNote(&customermodel.ComMoegoServerCustomerParamsPetNoteSaveVo{
		PetId: s.PetIDs[0],
		Note:  "This is a pet note",
	})

	payload := &customermodel.ComMoegoServerCustomerWebVoCustomerPetNoteByVo{
		CustomerId: s.CustomerID,
		PetIdList:  s.PetIDs,
	}
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoCustomerPetNoteListDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/note/withPet").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	petNotes := result.GetData().GetPetNotes()
	s.Require().Len(petNotes, 1)
	s.Require().Equal(s.PetIDs[0], *petNotes[0].GetPetId())
	notes := petNotes[0].GetNotes()
	s.Require().Equal("This is a pet note", *notes[0].GetNote())
	s.Require().Equal(s.PetIDs[0], *notes[0].GetPetId())
}

func TestCustomerPetNoteTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerPetNoteTestSuite))
}
