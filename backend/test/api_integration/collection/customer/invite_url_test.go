package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type InviteUrlTestSuite struct {
	suite.Suite
	godomain.Context
	setupDone  bool
	CustomerID int32
}

func (s *InviteUrlTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	// login go domain
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "InviteUrlTestSuite",
	})

	// create a customer for testing
	s.CustomerID, _ = s.CreateCustomerWithPets(
		&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
			PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
			PhoneNumber:         customermodel.PtrString("**********"),
			FirstName:           customermodel.PtrString("API"),
			LastName:            customermodel.PtrString("test"),
			Email:               customermodel.PtrString("<EMAIL>"),
			PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
				{
					PetName:   customermodel.PtrString("APet"),
					PetTypeId: customermodel.PtrInt32(1),
					Breed:     customermodel.PtrString("Affenpinscher"),
				},
			},
		})

	s.setupDone = true
}

func (s *InviteUrlTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *InviteUrlTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *InviteUrlTestSuite) TestInvireUrlWithNoCustomerCode() {
	result := customermodel.NewComMoegoServerCustomerWebDtoUrlDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/account/invite/url").
		AddQueries(map[string]string{
			"customerId":   strconv.FormatInt(int64(s.CustomerID), 10),
			"customerCode": "",
		}).
		SetResult(result).
		Send()

	s.Require().NoError(err)
	s.Require().True(response.IsSuccess())

	customerCode := s.GetCustomerDetailData(s.CustomerID).GetCustomerInfo().GetCustomerCode()
	url := result.GetUrl()
	s.Require().Equal(*url, "https://my.t2.moego.dev/invite?code="+*customerCode)
}

func (s *InviteUrlTestSuite) TestInvireUrlWithCustomerCode() {
	customerCode := s.GetCustomerDetailData(s.CustomerID).GetCustomerInfo().GetCustomerCode()
	result := customermodel.NewComMoegoServerCustomerWebDtoUrlDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/account/invite/url").
		AddQueries(map[string]string{
			"customerId":   strconv.FormatInt(int64(s.CustomerID), 10),
			"customerCode": *customerCode,
		}).
		SetResult(result).
		Send()

	s.Require().NoError(err)
	s.Require().True(response.IsSuccess())

	url := result.GetUrl()
	s.Require().Equal(*url, "https://my.t2.moego.dev/invite?code="+*customerCode)
}

func TestInviteUrlTestSuite(t *testing.T) {
	suite.Run(t, new(InviteUrlTestSuite))
}
