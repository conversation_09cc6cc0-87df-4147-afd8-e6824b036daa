package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/mydomain"
)

type CheckExistEmailTestSuite struct {
	suite.Suite
	MyCtx      mydomain.Context
	GoCtx      godomain.Context
	CustomerID int32
	setupDone  bool
}

func (s *CheckExistEmailTestSuite) SetupSuite() {
	defer func() {
		if !s.setupDone {
			s.mustTearDownSuite()
		}
	}()

	s.GoCtx.Setup(&s.Suite, nil)
	s.GoCtx.Login(&godomain.TestAccount{
		Email:    "<EMAIL>",
		Password: "Qwer@123",
	})

	s.CustomerID, _ = s.GoCtx.CreateCustomerWithPets(
		&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
			PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GoCtx.GetAuthInfo().BusinessID, 10)),
			PhoneNumber:         customermodel.PtrString("**********"),
			FirstName:           customermodel.PtrString("API"),
			LastName:            customermodel.PtrString("test"),
			Email:               customermodel.PtrString("<EMAIL>"),
			PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
				{
					PetName:   customermodel.PtrString("APet"),
					PetTypeId: customermodel.PtrInt32(1),
					Breed:     customermodel.PtrString("Affenpinscher"),
				},
			},
		})

	s.MyCtx.Setup(&s.Suite, &mydomain.TestAccount{
		Email:    "<EMAIL>",
		Password: "Qwer@123",
	})

	s.setupDone = true
}

func (s *CheckExistEmailTestSuite) TearDownSuite() {
	defer s.GoCtx.Teardown()
	s.mustTearDownSuite()
}

func (s *CheckExistEmailTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.GoCtx.DeleteCustomer(s.CustomerID)
	}
}

func (s *CheckExistEmailTestSuite) TestCheckExistEmail() {
	result := customermodel.NewComMoegoServerCustomerAccountWebDtoCheckEmailDTO()
	response, err := s.MyCtx.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/client/account/v2/login/checkEmailExist").
		AddQuery("email", "<EMAIL>").
		SetResult(result).
		Send()

	s.Require().NoError(err)
	s.Require().True(response.IsSuccess())

	s.Require().True(*result.GetExist())
}

func (s *CheckExistEmailTestSuite) TestCheckNotExistEmail() {
	result := customermodel.NewComMoegoServerCustomerAccountWebDtoCheckEmailDTO()
	response, err := s.MyCtx.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/client/account/v2/login/checkEmailExist").
		AddQuery("email", "<EMAIL>").
		SetResult(result).
		Send()

	s.Require().NoError(err)
	s.Require().True(response.IsSuccess())

	s.Require().False(*result.GetExist())
}

func (s *CheckExistEmailTestSuite) TestCheckEmailExistOnBusiness() {
	customerCode := s.GoCtx.GetCustomerDetailData(s.CustomerID).GetCustomerInfo().GetCustomerCode()

	result := customermodel.NewComMoegoServerCustomerAccountWebDtoCheckEmailDTO()
	response, err := s.MyCtx.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/client/account/v2/login/checkEmailExistOnBusiness").
		AddQueries(map[string]string{
			"email":        "<EMAIL>",
			"customerCode": *customerCode,
		}).
		SetResult(result).
		Send()

	s.Require().NoError(err)
	s.Require().True(response.IsSuccess())

	s.Require().False(*result.GetExist())
	s.Require().False(*result.GetExistOnBusiness())
}

func TestCheckExistEmailTestSuite(t *testing.T) {
	suite.Run(t, new(CheckExistEmailTestSuite))
}
