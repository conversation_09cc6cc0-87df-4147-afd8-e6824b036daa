package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type CustomerNearbyTestSuite struct {
	suite.Suite
	godomain.Context
	TagIDs []int64
}

func (s *CustomerNearbyTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "CustomerNearbyTestSuite",
	})

	s.TagIDs = s.GetTagIDsFromListCustomerTag()
}

func (s *CustomerNearbyTestSuite) getNearbyCustomerIDs(payload *customermodel.ComMoegoServerCustomerParamsCustomerNearByParams) []int32 {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaUtilListComMoegoServerCustomerDtoCustomerNearByDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/nearby").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	data := result.GetData()

	var customerIDs []int32
	for _, customer := range data {
		customerID := customer.GetCustomerId()
		s.Require().NotNil(customerID)
		s.Require().Greater(*customerID, int32(0))
		customerIDs = append(customerIDs, *customerID)
	}
	return customerIDs
}

func (s *CustomerNearbyTestSuite) TestCustomerNearby1mile() {
	customerID, _ := s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
		Address1: customermodel.PtrString("28 Canal Street"),
		City:     customermodel.PtrString("New York"),
		Country:  customermodel.PtrString("US"),
		State:    customermodel.PtrString("NY"),
		Zipcode:  customermodel.PtrString("10002"),
		Lat:      customermodel.PtrString("40.7143377"),
		Lng:      customermodel.PtrString("-73.99050989999999"),
	})

	payload := &customermodel.ComMoegoServerCustomerParamsCustomerNearByParams{
		Lat:            customermodel.PtrFloat64(40.7128),
		Lng:            customermodel.PtrFloat64(-74.0060),
		CustomerRadius: customermodel.PtrFloat64(1),
	}

	customerIDs := s.getNearbyCustomerIDs(payload)
	s.Require().Contains(customerIDs, customerID)
	s.DeleteCustomer(customerID)
}

func (s *CustomerNearbyTestSuite) TestCustomerNearby2miles() {
	customerID, _ := s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
		Address1: customermodel.PtrString("250 Bowery"),
		City:     customermodel.PtrString("New York"),
		Country:  customermodel.PtrString("US"),
		State:    customermodel.PtrString("NY"),
		Zipcode:  customermodel.PtrString("10012"),
		Lat:      customermodel.PtrString("40.7302"),
		Lng:      customermodel.PtrString("-73.9766"),
	})

	payload := &customermodel.ComMoegoServerCustomerParamsCustomerNearByParams{
		Lat:            customermodel.PtrFloat64(40.7128),
		Lng:            customermodel.PtrFloat64(-74.0060),
		CustomerRadius: customermodel.PtrFloat64(2),
	}

	customerIDs := s.getNearbyCustomerIDs(payload)
	s.Require().Contains(customerIDs, customerID)
	s.DeleteCustomer(customerID)
}

func (s *CustomerNearbyTestSuite) TestCustomerNearby5miles() {
	customerID, _ := s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
		Address1: customermodel.PtrString("100 3rd Ave"),
		City:     customermodel.PtrString("New York"),
		Country:  customermodel.PtrString("US"),
		State:    customermodel.PtrString("NY"),
		Zipcode:  customermodel.PtrString("10003"),
		Lat:      customermodel.PtrString("40.7306"),
		Lng:      customermodel.PtrString("-73.9906"),
	})

	payload := &customermodel.ComMoegoServerCustomerParamsCustomerNearByParams{
		Lat:            customermodel.PtrFloat64(40.7128),
		Lng:            customermodel.PtrFloat64(-74.0060),
		CustomerRadius: customermodel.PtrFloat64(5),
	}

	customerIDs := s.getNearbyCustomerIDs(payload)
	s.Require().Contains(customerIDs, customerID)
	s.DeleteCustomer(customerID)
}

func (s *CustomerNearbyTestSuite) TestCustomerNearByTags() {
	customerID, _ := s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
		Address1: customermodel.PtrString("100 3rd Ave"),
		City:     customermodel.PtrString("New York"),
		Country:  customermodel.PtrString("US"),
		State:    customermodel.PtrString("NY"),
		Zipcode:  customermodel.PtrString("10003"),
		Lat:      customermodel.PtrString("40.7306"),
		Lng:      customermodel.PtrString("-73.9906"),
	})
	// add tag
	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId:        customerID,
		CustomerTagIdList: []int32{int32(s.TagIDs[0])},
	})

	payload := &customermodel.ComMoegoServerCustomerParamsCustomerNearByParams{
		Lat:            customermodel.PtrFloat64(40.7128),
		Lng:            customermodel.PtrFloat64(-74.0060),
		CustomerRadius: customermodel.PtrFloat64(5),
		Tags:           customermodel.PtrString(strconv.FormatInt(s.TagIDs[0], 10)),
	}

	customerIDs := s.getNearbyCustomerIDs(payload)
	s.Require().Contains(customerIDs, customerID)
	s.DeleteCustomer(customerID)
}

func (s *CustomerNearbyTestSuite) TestCustomerNearByActiveStatus() {
	customerID, _ := s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
		Address1: customermodel.PtrString("28 Canal Street"),
		City:     customermodel.PtrString("New York"),
		Country:  customermodel.PtrString("US"),
		State:    customermodel.PtrString("NY"),
		Zipcode:  customermodel.PtrString("10002"),
		Lat:      customermodel.PtrString("40.7143377"),
		Lng:      customermodel.PtrString("-73.99050989999999"),
	})

	payload := &customermodel.ComMoegoServerCustomerParamsCustomerNearByParams{
		Lat:            customermodel.PtrFloat64(40.7128),
		Lng:            customermodel.PtrFloat64(-74.0060),
		CustomerRadius: customermodel.PtrFloat64(1),
		Status: &customermodel.ComMoegoServerCustomerParamsCustomerSearchStatusVo{
			ActiveClient: customermodel.PtrBool(true),
		},
	}

	customerIDs := s.getNearbyCustomerIDs(payload)
	s.Require().Contains(customerIDs, customerID)
	s.DeleteCustomer(customerID)
}

func (s *CustomerNearbyTestSuite) TestCustomerNearByInActiveStatus() {
	customerID, _ := s.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString("**********"),
		FirstName:           customermodel.PtrString("Joshua"),
		LastName:            customermodel.PtrString("Dewey"),
		Email:               customermodel.PtrString("<EMAIL>"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
		Address1: customermodel.PtrString("28 Canal Street"),
		City:     customermodel.PtrString("New York"),
		Country:  customermodel.PtrString("US"),
		State:    customermodel.PtrString("NY"),
		Zipcode:  customermodel.PtrString("10002"),
		Lat:      customermodel.PtrString("40.7143377"),
		Lng:      customermodel.PtrString("-73.99050989999999"),
	})

	s.UpdateCustomer(&customermodel.ComMoegoServerCustomerParamsUpdateCustomerInfoParams{
		CustomerId: customerID,
		Inactive:   customermodel.PtrInt32(1),
	})

	payload := &customermodel.ComMoegoServerCustomerParamsCustomerNearByParams{
		Lat:            customermodel.PtrFloat64(40.7128),
		Lng:            customermodel.PtrFloat64(-74.0060),
		CustomerRadius: customermodel.PtrFloat64(1),
		Status: &customermodel.ComMoegoServerCustomerParamsCustomerSearchStatusVo{
			InactiveClient: customermodel.PtrBool(true),
		},
	}

	customerIDs := s.getNearbyCustomerIDs(payload)
	s.Require().Contains(customerIDs, customerID)
	s.DeleteCustomer(customerID)
}

func TestCustomerNearbyTestSuite(t *testing.T) {
	suite.Run(t, new(CustomerNearbyTestSuite))
}
