package customer

import (
	"net/http"
	"strconv"
	"testing"
	"time"

	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	appointmentapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	appointmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/appointment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type SmartListTestSuite struct {
	suite.Suite
	godomain.Context
	setupDone     bool
	CustomerID    int32
	PetIDs        []int32
	Services      []*offeringpb.CustomizedServiceView
	AppointmentID int64
	TagIDs        []int64
}

func (s *SmartListTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	// login go domain
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "SmartListTestSuite",
	})

	// create a customer for testing
	s.CustomerID, s.PetIDs = s.CreateCustomerWithPets(
		&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
			PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
			PhoneNumber:         customermodel.PtrString("**********"),
			FirstName:           customermodel.PtrString("API"),
			LastName:            customermodel.PtrString("test"),
			Email:               customermodel.PtrString("<EMAIL>"),
			PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
				{
					PetName:   customermodel.PtrString("APet"),
					PetTypeId: customermodel.PtrInt32(1),
					Breed:     customermodel.PtrString("Affenpinscher"),
				},
			},
		})

	// list applicable grooming services
	s.Services = s.ListApplicableGroomingServices(s.PetIDs[0])

	// create an appointment
	today := time.Now().Format("2006-01-02")

	s.AppointmentID = s.CreateAppointment(&appointmentapipb.CreateAppointmentParams{
		BusinessId: s.GetAuthInfo().BusinessID,
		Appointment: &appointmentpb.AppointmentCreateDef{
			CustomerId: int64(s.CustomerID),
			Source:     appointmentpb.AppointmentSource_WEB,
		},
		PetDetails: []*appointmentpb.PetDetailDef{
			{
				PetId: int64(s.PetIDs[0]),
				Services: []*appointmentpb.SelectedServiceDef{
					{
						ServiceId:    s.Services[0].GetId(),
						StartDate:    today,
						EndDate:      &today,
						StartTime:    proto.Int32(600),
						EndTime:      proto.Int32(660),
						ServiceTime:  proto.Int32(s.Services[0].GetDuration()),
						ServicePrice: proto.Float64(s.Services[0].GetPrice()),
						StaffId:      &s.GetAuthInfo().StaffID,
					},
				},
			},
		},
	})

	s.TagIDs = s.GetTagIDsFromListCustomerTag()

	s.setupDone = true
}

func (s *SmartListTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *SmartListTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		s.DeleteCustomer(s.CustomerID)
	}
}

func (s *SmartListTestSuite) BeforeTest(suiteName, testName string) {
	log.InfoContextf(s.Ctx, "Start to run test %s.%s", suiteName, testName)
	s.MustGetCustomer(s.CustomerID)
}

func (s *SmartListTestSuite) AfterTest(suiteName, testName string) {
	log.InfoContextf(s.Ctx, "Finish running test %s.%s", suiteName, testName)
}

func (s *SmartListTestSuite) TestSmartListNoFilter() {
	// list all customers
	customerIDs := s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString(""),
			},
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		})
	s.Require().Contains(customerIDs, s.CustomerID)
}

//nolint:funlen
func (s *SmartListTestSuite) TestSmartListWithPetTypeFilter() {
	// list customers with filter
	// pet_type = 1 (dog), include the customer
	customerIDs := s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString(""),
			},
			Filters: &customermodel.ComMoegoCommonParamsFilterParams{
				Type: customermodel.PtrString("TYPE_AND"),
				Filters: []customermodel.ComMoegoCommonParamsFilterParams{
					{
						Property: customermodel.PtrString("pet_type"),
						Operator: customermodel.PtrString("OPERATOR_EQUAL"),
						Value:    customermodel.PtrString("1"),
					},
				},
			},
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		})
	s.Require().Contains(customerIDs, s.CustomerID)

	// pet_type = 2 (cat), not include the customer
	customerIDs = s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString(""),
			},
			Filters: &customermodel.ComMoegoCommonParamsFilterParams{
				Type: customermodel.PtrString("TYPE_AND"),
				Filters: []customermodel.ComMoegoCommonParamsFilterParams{
					{
						Property: customermodel.PtrString("pet_type"),
						Operator: customermodel.PtrString("OPERATOR_EQUAL"),
						Value:    customermodel.PtrString("2"),
					},
				},
			},
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		})
	s.Require().NotContains(customerIDs, s.CustomerID)

	// pet type and breed filter (dog + Affenpinscher), include the customer
	customerIDs = s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString(""),
			},
			Filters: &customermodel.ComMoegoCommonParamsFilterParams{
				Type: customermodel.PtrString("TYPE_AND"),
				Filters: []customermodel.ComMoegoCommonParamsFilterParams{
					{
						Property: customermodel.PtrString("pet_type"),
						Operator: customermodel.PtrString("OPERATOR_EQUAL"),
						Value:    customermodel.PtrString("1"),
					},
					{
						Property: customermodel.PtrString("pet_breed"),
						Operator: customermodel.PtrString("OPERATOR_EQUAL"),
						Value:    customermodel.PtrString("Affenpinscher"),
					},
				},
			},
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		})
	s.Require().Contains(customerIDs, s.CustomerID)

	// pet type and breed filter (dog + French Bulldog), not include the customer
	customerIDs = s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString(""),
			},
			Filters: &customermodel.ComMoegoCommonParamsFilterParams{
				Type: customermodel.PtrString("TYPE_AND"),
				Filters: []customermodel.ComMoegoCommonParamsFilterParams{
					{
						Property: customermodel.PtrString("pet_type"),
						Operator: customermodel.PtrString("OPERATOR_EQUAL"),
						Value:    customermodel.PtrString("1"),
					},
					{
						Property: customermodel.PtrString("pet_breed"),
						Operator: customermodel.PtrString("OPERATOR_EQUAL"),
						Value:    customermodel.PtrString("French Bulldog"),
					},
				},
			},
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		})
	s.Require().NotContains(customerIDs, s.CustomerID)
}

func (s *SmartListTestSuite) TestSmartListWithTotalAppointmentFilter() {
	// list customers with filter
	customerIDs := s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString(""),
			},
			Filters: &customermodel.ComMoegoCommonParamsFilterParams{
				Type: customermodel.PtrString("TYPE_AND"),
				Filters: []customermodel.ComMoegoCommonParamsFilterParams{
					{
						Property: customermodel.PtrString("total_appt_cnt"),
						Operator: customermodel.PtrString("OPERATOR_GREATER_THAN"),
						Value:    customermodel.PtrString("0"),
					},
				},
			},
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		})
	s.Require().Contains(customerIDs, s.CustomerID)
}

func (s *SmartListTestSuite) TestSmartListWithQueryEmail() {
	customerIDs := s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString("API-test"),
			},
			Filters: nil,
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		},
	)
	s.Require().Contains(customerIDs, s.CustomerID)
}

func (s *SmartListTestSuite) TestSmartListWithQueryPhone() {
	customerIDs := s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString("112233"),
			},
			Filters: nil,
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		},
	)
	s.Require().Contains(customerIDs, s.CustomerID)
}

func (s *SmartListTestSuite) TestSmartListWithQueryName() {
	customerIDs := s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Queries: &customermodel.ComMoegoCommonParamsQueryParams{
				Keyword: customermodel.PtrString("API"),
			},
			Filters: nil,
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		},
	)
	s.Require().Contains(customerIDs, s.CustomerID)
}

func (s *SmartListTestSuite) TestBatchAddTag() {
	payload := &customermodel.ComMoegoServerCustomerWebParamsClientsClientListBulkParams{
		Filters: &customermodel.ComMoegoCommonParamsFilterParams{
			Type: customermodel.PtrString("TYPE_AND"),
			Filters: []customermodel.ComMoegoCommonParamsFilterParams{
				{
					Property: customermodel.PtrString("client_id"),
					Operator: customermodel.PtrString("OPERATOR_IN"),
					Values:   []string{strconv.FormatInt(int64(s.CustomerID), 10)},
				},
			},
		},
		RelationIds: []int32{int32(s.TagIDs[0])},
	}
	_, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/smart-list/tag").
		SetPayload(payload).
		Send()

	s.Require().Nil(err)

	data := s.MustGetCustomer(s.CustomerID)
	resultTags := data.GetCustomerTags()
	s.Require().Equal(s.TagIDs[0], int64(*resultTags[0].GetId()))
}

func (s *SmartListTestSuite) TestUpdateInactive() {
	payload := &customermodel.ComMoegoServerCustomerWebParamsClientsClientListBulkParams{
		Filters: &customermodel.ComMoegoCommonParamsFilterParams{
			Type: customermodel.PtrString("TYPE_AND"),
			Filters: []customermodel.ComMoegoCommonParamsFilterParams{
				{
					Property: customermodel.PtrString("client_id"),
					Operator: customermodel.PtrString("OPERATOR_IN"),
					Values:   []string{strconv.FormatInt(int64(s.CustomerID), 10)},
				},
			},
		},
		Enable: customermodel.PtrInt32(1),
	}
	_, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/smart-list/inactive").
		SetPayload(payload).
		Send()

	s.Require().Nil(err)

	result := s.MustGetCustomer(s.CustomerID)
	isInactive := result.GetCustomerDetail().GetInactive()
	s.Require().Equal(int32(1), *isInactive)

	payload.Enable = customermodel.PtrInt32(0)
	_, err = s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/smart-list/inactive").
		SetPayload(payload).
		Send()

	s.Require().Nil(err)

	result = s.MustGetCustomer(s.CustomerID)
	isInactive = result.GetCustomerDetail().GetInactive()
	s.Require().Equal(int32(0), *isInactive)
}

func (s *SmartListTestSuite) TestBatchDeleteCustomer() {
	// create another customer
	anotherCustomerID, _ := s.CreateCustomerWithPets(
		&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
			PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GetAuthInfo().BusinessID, 10)),
			PhoneNumber:         customermodel.PtrString("**********"),
			FirstName:           customermodel.PtrString("xxx"),
			LastName:            customermodel.PtrString("test"),
			PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
				{
					PetName:   customermodel.PtrString("APet"),
					PetTypeId: customermodel.PtrInt32(1),
					Breed:     customermodel.PtrString("Affenpinscher"),
				},
			},
		})

	payload := &customermodel.ComMoegoServerCustomerWebParamsClientsClientListBulkParams{
		Filters: &customermodel.ComMoegoCommonParamsFilterParams{
			Type: customermodel.PtrString("TYPE_AND"),
			Filters: []customermodel.ComMoegoCommonParamsFilterParams{
				{
					Property: customermodel.PtrString("client_id"),
					Operator: customermodel.PtrString("OPERATOR_IN"),
					Values:   []string{strconv.FormatInt(int64(anotherCustomerID), 10)},
				},
			},
		},
	}
	_, err := s.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/smart-list/deletion").
		SetPayload(payload).
		Send()

	s.Require().Nil(err)

	customerIDs := s.GetCustomerIDsFromSmartList(
		&customermodel.ComMoegoServerCustomerParamsClientListParams{
			Sort: customermodel.ComMoegoCommonParamsSortParams{
				Property: "first_name",
				Order:    "asc",
			},
			PageNum:  1,
			PageSize: 20,
		},
	)

	s.Require().NotContains(customerIDs, anotherCustomerID)
}

func TestSmartListTestSuite(t *testing.T) {
	suite.Run(t, new(SmartListTestSuite))
}
