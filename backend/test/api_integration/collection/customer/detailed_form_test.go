package customer

import (
	"net/http"
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type DetailedFormTestSuite struct {
	suite.Suite
	godomain.Context
	formID int32
}

func (s *DetailedFormTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "DetailedFormTestSuite",
	})

	s.formID = s.addNewForm(&customermodel.ComMoegoServerCustomerWebVoIntakeFormVo{
		Title: "Test Form",
	})
}

func (s *DetailedFormTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *DetailedFormTestSuite) mustTearDownSuite() {
	if s.formID > 0 {
		s.deleteFormByID(s.formID)
	}
}

func (s *DetailedFormTestSuite) addNewForm(params *customermodel.ComMoegoServerCustomerWebVoIntakeFormVo) int32 {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/form/add").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().Greater(*result.GetData(), int32(0))

	return *result.GetData()
}

func (s *DetailedFormTestSuite) deleteFormByID(formID int32) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/form/delete").
		AddQuery("formId", strconv.FormatInt(int64(formID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().Equal(int32(1), *result.GetData())
}

func (s *DetailedFormTestSuite) getFormIDs() []int32 {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoFormDetailDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/form/getDetailedForm").
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().NotEmpty(result.GetData().GetFormList())

	var formIDs []int32
	for _, form := range result.GetData().GetFormList() {
		formIDs = append(formIDs, *form.GetFormId())
	}
	return formIDs
}

func (s *DetailedFormTestSuite) updateFormBasicInfo(params *customermodel.ComMoegoServerCustomerWebVoIntakeFormVo) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/form/update").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().Equal(int32(1), *result.GetData())
}

func (s *DetailedFormTestSuite) updateDetailedForm(params *customermodel.ComMoegoServerCustomerDtoIntakeFormDetailUpdateParam) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/form/updateDetailedForm").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().Equal(int32(1), *result.GetData())
}

func (s *DetailedFormTestSuite) getDetailedFormById(formID int32) *customermodel.ComMoegoServerCustomerServiceDtoIntakeFormDetailDto {
	result := customermodel.NewComMoegoCommonResponseResponseResultComMoegoServerCustomerServiceDtoIntakeFormDetailDto()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/form/getDetailedFormById").
		AddQuery("formId", strconv.FormatInt(int64(formID), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	return result.GetData()
}

func (s *DetailedFormTestSuite) deleteDetailedForm(formDetailId int32) {
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/customer/form/deleteDetailedForm").
		AddQuery("formDetailId", strconv.FormatInt(int64(formDetailId), 10)).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().Equal(int32(1), *result.GetData())
}

func (s *DetailedFormTestSuite) TestFormIDsContainsAddedForm() {
	formIDs := s.getFormIDs()
	s.Require().Contains(formIDs, s.formID)
}

func (s *DetailedFormTestSuite) TestUpdateFormBasicInfo() {
	s.updateFormBasicInfo(&customermodel.ComMoegoServerCustomerWebVoIntakeFormVo{
		FormId:     &s.formID,
		Id:         &s.formID,
		Title:      "Updated Form",
		Message:    customermodel.PtrString("Test message"),
		ThemeColor: customermodel.PtrString("#ff9800"),
	})

	form := s.getDetailedFormById(s.formID).GetForm()
	s.Require().Equal(s.formID, *form.GetId())
	s.Require().Equal("Updated Form", *form.GetTitle())
	s.Require().Equal("Test message", *form.GetMessage())
	s.Require().Equal("#ff9800", *form.GetThemeColor())
}

func (s *DetailedFormTestSuite) TestUpdateDetailedForm() {
	// get from details
	details := s.getDetailedFormById(s.formID).GetDetails()
	genderDetailId := details[3].GetId()

	s.updateDetailedForm(&customermodel.ComMoegoServerCustomerDtoIntakeFormDetailUpdateParam{
		FormDetailId: *genderDetailId,
		IsRequired:   customermodel.PtrInt32(1),
		IsShow:       customermodel.PtrInt32(1),
	})

	// get from details
	details = s.getDetailedFormById(s.formID).GetDetails()
	genderDetail := details[3]
	s.Require().Equal(*genderDetailId, *genderDetail.GetId())
	s.Require().Equal(int32(1), *genderDetail.GetIsRequired())
	s.Require().Equal(int32(1), *genderDetail.GetIsShow())
}

func (s *DetailedFormTestSuite) TestAddQuestion() {
	payload := &customermodel.ComMoegoServerCustomerDtoIntakeFormDetailParam{
		FormId:       s.formID,
		Question:     "Test Question",
		Placeholder:  customermodel.PtrString("Question"),
		QuestionType: int32(1),
		Type:         int32(1),
		IsShow:       customermodel.PtrInt32(1),
	}

	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/form/addQuestion").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().Greater(*result.GetData(), int32(0))

	// check question in detailIDs
	detailID := *result.GetData()
	details := s.getDetailedFormById(s.formID).GetDetails()
	var detailIDs []int32
	for _, detail := range details {
		detailIDs = append(detailIDs, *detail.GetId())
	}
	s.Require().Contains(detailIDs, detailID)

	// delete question
	s.deleteDetailedForm(detailID)
	details = s.getDetailedFormById(s.formID).GetDetails()
	detailIDs = nil
	for _, detail := range details {
		detailIDs = append(detailIDs, *detail.GetId())
	}
	s.Require().NotContains(detailIDs, detailID)
}

func (s *DetailedFormTestSuite) TestSortForm() {
	// get detailIDs
	details := s.getDetailedFormById(s.formID).GetDetails()
	var detailIDs []int32
	for _, detail := range details {
		detailIDs = append(detailIDs, *detail.GetId())
	}

	payload := &customermodel.ComMoegoCommonParamsSortIdListParams{
		IdList: detailIDs,
	}
	result := customermodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/form/sortDetailedForm").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	s.Require().Equal(int32(1), *result.GetData())
}

func TestDetailedForm(t *testing.T) {
	suite.Run(t, new(DetailedFormTestSuite))
}
