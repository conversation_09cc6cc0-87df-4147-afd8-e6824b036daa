package customer

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/types/known/timestamppb"

	businesscustomerapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/business_customer/v1"
	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type IncidentReportTestSuite struct {
	suite.Suite
	godomain.Context
	setupDone        bool
	CustomerID       int32
	PetIDs           []int64
	IncidentReportID int64
	IncidentTypeID   int64
}

func (s *IncidentReportTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	// login go domain
	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "IncidentReportTestSuite",
	})

	s.IncidentTypeID = s.CreateIncidentType(&businesscustomerapipb.CreatePetIncidentTypeParams{
		IncidentType: &businesscustomerpb.BusinessPetIncidentTypeCreateDef{
			Name: "test incident type",
		},
	})

	s.PetIDs = []int64{1, 2}
	// create a customer for testing
	s.IncidentReportID = s.CreateIncidentReport(
		&businesscustomerapipb.CreatePetIncidentReportParams{
			IncidentReport: &businesscustomerpb.BusinessPetIncidentReportCreateDef{
				PetIds:         s.PetIDs,
				IncidentTypeId: s.IncidentTypeID,
				IncidentTime:   &timestamppb.Timestamp{Seconds: **********},
				BusinessId:     s.GetAuthInfo().BusinessID,
				Description:    "test incident",
				AttachmentFiles: []*businesscustomerpb.PetIncidentAttachmentDef{
					{Name: "test.jpg", Url: "http://test.com/test.jpg"},
					{Name: "test2.jpg", Url: "http://test.com/test2.jpg"},
				},
				IsPetInjured:   true,
				IsStaffInjured: false,
				IsVetVisited:   false,
			},
		})

	s.setupDone = true
}

func (s *IncidentReportTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *IncidentReportTestSuite) mustTearDownSuite() {
	// delete the pet incident report created in SetupSuite
	if s.IncidentReportID > 0 {
		s.DeleteIncidentReport(s.IncidentReportID)
	}

	// delete the pet incident type created in SetupSuite
	if s.IncidentTypeID > 0 {
		s.DeleteIncidentType(s.IncidentTypeID)
	}
}

func (s *IncidentReportTestSuite) BeforeTest(suiteName, testName string) {
	log.InfoContextf(s.Ctx, "Start to run test %s.%s", suiteName, testName)
}

func (s *IncidentReportTestSuite) AfterTest(suiteName, testName string) {
	log.InfoContextf(s.Ctx, "Finish running test %s.%s", suiteName, testName)
}

func (s *IncidentReportTestSuite) CreateIncidentType(params *businesscustomerapipb.CreatePetIncidentTypeParams) int64 {
	result := &businesscustomerapipb.CreatePetIncidentTypeResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/CreatePetIncidentType").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	incidentTypeID := result.GetIncidentType().GetId()
	s.Require().Greater(incidentTypeID, int64(0))

	return incidentTypeID
}

func (s *IncidentReportTestSuite) CreateIncidentReport(params *businesscustomerapipb.CreatePetIncidentReportParams) int64 {
	result := &businesscustomerapipb.CreatePetIncidentReportResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessPetIncidentReportService/CreatePetIncidentReport").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	incidentReportID := result.GetIncidentReport().GetId()
	s.Require().Greater(incidentReportID, int64(0))

	return incidentReportID
}

func (s *IncidentReportTestSuite) DeleteIncidentReport(incidentID int64) {

	result := &businesscustomerapipb.DeletePetIncidentReportResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessPetIncidentReportService/DeletePetIncidentReport").
		SetPayload(&businesscustomerapipb.DeletePetIncidentReportParams{Id: incidentID}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
}

func (s *IncidentReportTestSuite) DeleteIncidentType(incidentTypeID int64) {

	result := &businesscustomerapipb.DeletePetIncidentTypeResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessPetIncidentTypeService/DeletePetIncidentType").
		SetPayload(&businesscustomerapipb.DeletePetIncidentTypeParams{Id: incidentTypeID}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
}

func (s *IncidentReportTestSuite) TestCreateIncidentReport_nullIncidentType() {
	// list all incident reports
	params := &businesscustomerapipb.CreatePetIncidentReportParams{
		IncidentReport: &businesscustomerpb.BusinessPetIncidentReportCreateDef{
			PetIds:         s.PetIDs,
			IncidentTypeId: 10, // invalid incident type id
			IncidentTime:   &timestamppb.Timestamp{Seconds: **********},
			BusinessId:     s.GetAuthInfo().BusinessID,
			Description:    "test incident",
			AttachmentFiles: []*businesscustomerpb.PetIncidentAttachmentDef{
				{Name: "test.jpg", Url: "http://test.com/test.jpg"},
				{Name: "test2.jpg", Url: "http://test.com/test2.jpg"},
			},
			IsPetInjured:   true,
			IsStaffInjured: false,
			IsVetVisited:   false,
		},
	}
	result := &businesscustomerapipb.ListPetIncidentReportResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessPetIncidentReportService/CreatePetIncidentReport").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().Equal(http.StatusBadRequest, response.GetStatusCode())
}

func (s *IncidentReportTestSuite) TestListIncidentReport() {
	result := s.listIncidentReports(&businesscustomerapipb.ListPetIncidentReportParams{
		PetId:      s.PetIDs[0],
		BusinessId: &s.GetAuthInfo().BusinessID,
	})

	// 验证返回结果
	s.Require().Greater(len(result.GetIncidentReports()), 0)
	report := result.GetIncidentReports()[0]
	s.Assert().Contains(report.GetPetIds(), s.PetIDs[0])
	s.Assert().Equal(s.IncidentTypeID, report.GetIncidentTypeId())
	s.Assert().Equal("test incident", report.GetDescription())
	s.Assert().Equal(2, len(report.GetAttachmentFiles()))
	s.Assert().True(report.GetIsPetInjured())
	s.Assert().False(report.GetIsStaffInjured())
	s.Assert().False(report.GetIsVetVisited())
}

func (s *IncidentReportTestSuite) TestUpdateIncidentReport() {
	updatedDesc := "updated incident description"
	s.updateIncidentReport(&businesscustomerapipb.UpdatePetIncidentReportParams{
		Id: s.IncidentReportID,
		IncidentReport: &businesscustomerpb.BusinessPetIncidentReportUpdateDef{
			PetIds:     s.PetIDs[1:],               // update to pet 2
			BusinessId: s.GetAuthInfo().BusinessID, // no update
			IncidentTime: &timestamppb.Timestamp{
				Seconds: **********,
			}, // no update
			IncidentTypeId: s.IncidentTypeID, // no update
			Description:    updatedDesc,      // update description
			IsPetInjured:   false,            // update pet injured = false
			AttachmentFiles: []*businesscustomerpb.PetIncidentAttachmentDef{
				{Name: "updated.jpg", Url: "http://test.com/updated.jpg"},
			}, // update attachment
		},
	})

	// 验证更新后的结果
	result := s.listIncidentReports(&businesscustomerapipb.ListPetIncidentReportParams{
		BusinessId: &s.GetAuthInfo().BusinessID,
		PetId:      s.PetIDs[1],
	})

	found := false
	for _, report := range result.GetIncidentReports() {
		if report.GetId() == s.IncidentReportID {
			found = true
			s.Assert().Equal(s.PetIDs[1:], report.GetPetIds())
			s.Assert().Equal(updatedDesc, report.GetDescription())
			s.Assert().False(report.GetIsPetInjured())
			s.Assert().Equal(1, len(report.GetAttachmentFiles()))
			s.Assert().Equal("updated.jpg", report.GetAttachmentFiles()[0].GetName())
			break
		}
	}

	s.Assert().True(found, "Updated report should be found")

}

func (s *IncidentReportTestSuite) listIncidentReports(params *businesscustomerapipb.ListPetIncidentReportParams) *businesscustomerapipb.ListPetIncidentReportResult {
	result := &businesscustomerapipb.ListPetIncidentReportResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessPetIncidentReportService/ListPetIncidentReport").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	return result
}

func (s *IncidentReportTestSuite) updateIncidentReport(params *businesscustomerapipb.UpdatePetIncidentReportParams) *businesscustomerapipb.UpdatePetIncidentReportResult {
	result := &businesscustomerapipb.UpdatePetIncidentReportResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.business_customer.v1.BusinessPetIncidentReportService/UpdatePetIncidentReport").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
	return result
}

func TestIncidentReportTestSuite(t *testing.T) {
	suite.Run(t, new(IncidentReportTestSuite))
}
