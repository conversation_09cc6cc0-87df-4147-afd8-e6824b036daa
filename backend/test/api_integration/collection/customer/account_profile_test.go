package customer

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"

	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/mydomain"
)

type AccountProfileTestSuite struct {
	suite.Suite
	mydomain.Context
}

func (s *AccountProfileTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, &mydomain.TestAccount{
		Email:    "<EMAIL>",
		Password: "Qwer@123",
	})
}

func (s *AccountProfileTestSuite) TearDownSuite() {
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/api/customer/client/account/v2/logout").
		Send()

	s.Require().NoError(err)
	s.Require().True(response.IsSuccess())

	s.Context.Teardown()
}

func (s *AccountProfileTestSuite) getAccountProfile() *customermodel.ComMoegoServerCustomerAccountWebDtoClientAccountAnonymousDTO {
	result := customermodel.NewComMoegoServerCustomerAccountWebDtoClientAccountAnonymousDTO()
	response, err := s.NewRequest().
		SetMethodPath(http.MethodGet, "/api/customer/client/account/v2/profile").
		SetResult(result).
		Send()

	s.Require().NoError(err)
	s.Require().True(response.IsSuccess())

	return result
}

func (s *AccountProfileTestSuite) TestUpdateProfileWithFirstName() {
	payload := &customermodel.ComMoegoServerCustomerAccountWebParamsProfileProfileUpdateParams{
		FirstName: customermodel.PtrString("Test1"),
		LastName:  customermodel.PtrString("PPP"),
	}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/client/account/v2/profile").
		SetPayload(payload).
		Send()

	s.Require().NoError(err)
	s.Require().True(response.IsSuccess())

	result := s.getAccountProfile()
	s.Require().False(*result.GetAnonymous())
	s.Require().Equal("<EMAIL>", *result.GetAccount().GetEmail())
	s.Require().Equal("Test1", *result.GetAccount().GetFirstName())
	s.Require().Equal("PPP", *result.GetAccount().GetLastName())
}

func (s *AccountProfileTestSuite) TestUpdateProfileWithLastName() {
	payload := &customermodel.ComMoegoServerCustomerAccountWebParamsProfileProfileUpdateParams{
		FirstName: customermodel.PtrString("Test"),
		LastName:  customermodel.PtrString("PPP1"),
	}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPut, "/api/customer/client/account/v2/profile").
		SetPayload(payload).
		Send()

	s.Require().NoError(err)
	s.Require().True(response.IsSuccess())

	result := s.getAccountProfile()
	s.Require().False(*result.GetAnonymous())
	s.Require().Equal("<EMAIL>", *result.GetAccount().GetEmail())
	s.Require().Equal("Test", *result.GetAccount().GetFirstName())
	s.Require().Equal("PPP1", *result.GetAccount().GetLastName())
}

func (s *AccountProfileTestSuite) TestGetAvatar() {
	result := s.getAccountProfile()
	s.Require().False(*result.GetAnonymous())
	s.Require().Equal("<EMAIL>", *result.GetAccount().GetEmail())
	s.Require().Equal("https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/1753263656770ead84f227463fa39588b0b681ba75.jpg",
		*result.GetAccount().GetAvatarPath())
}

func TestAccountProfileTestSuite(t *testing.T) {
	suite.Run(t, new(AccountProfileTestSuite))
}
