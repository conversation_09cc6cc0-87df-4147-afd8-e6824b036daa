package hello

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"

	todoapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/todo/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	businessmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/business/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

// HelloNewbiesSuite is a test suite to test hello API for newbies
type HelloNewbiesSuite struct {
	// you must embed suite.Suite for every test suite
	suite.Suite

	// embed domain context for every test suite
	// for example, you can use godomain.Context to manage context of `go.moego.pet` domain
	godomain.Context

	// you can add more fields here if needed as context or cache
	newbies map[string]bool
}

// SetupSuite is called before all tests in this suite, and will only be executed once
func (s *HelloNewbiesSuite) SetupSuite() {
	// You must always call `s.Context.Setup` to setup the test suite
	// You need to pass the suite (required) and the account (optional) for the test
	s.Context.Setup(&s.Suite, nil)

	// Then you can do your own setup stuff
	s.newbies = make(map[string]bool)
}

// TearDownSuite is called after all tests in this suite, and will only be executed once
func (s *HelloNewbiesSuite) TearDownSuite() {
	// You must do your own teardown stuff first before calling `s.Context.Teardown`
	for name, result := range s.newbies {
		log.InfoContextf(s.Ctx, "Hello %s %t", name, result)
	}

	// You must always call `s.Context.Teardown` at the end of the suite
	s.Context.Teardown()
}

// BeforeTest is called before every test in this suite.
func (s *HelloNewbiesSuite) BeforeTest(suiteName, testName string) {
	log.InfoContextf(s.Ctx, "Start to run test %s.%s", suiteName, testName)
	// You can do your own setup stuff here for every test
}

// AfterTest is called after every test in this suite.
func (s *HelloNewbiesSuite) AfterTest(suiteName, testName string) {
	log.InfoContextf(s.Ctx, "Finish running test %s.%s", suiteName, testName)
	// You can do your own teardown stuff here for every test
}

func (s *HelloNewbiesSuite) TestHelloArk() {
	// prepare a result object (pointer type) for the response
	// it is recommended to use the generated response object from the API definition
	result := &todoapipb.HelloArkResult{}
	// make a new request by calling `s.NewRequest()` rather than `api.NewRequest()`
	// the suite will automatically add the host and cookies and other session context
	response, err := s.NewRequest().
		// set the method (please use const in `http` package) and path of the request
		SetMethodPath(http.MethodPost, "/moego.api.todo.v1.TodoService/HelloArk").
		// if the request need a payload, you can set it here
		// the payload will be marshalled to JSON and sent as the request body
		// it is recommended to use the generated request object from the API definition
		// you should use a pointer type for the payload
		SetPayload(&todoapipb.HelloArkParams{
			Message: "hello ark",
		}).
		// set the result object for the response, then response body will be unmarshalled to this object
		SetResult(result).
		// send the request
		Send()

	// check response success
	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	// check result
	messages := result.GetHistoryMessages()
	s.Require().NotEmpty(messages)
	s.Require().Equal("hello ark", messages[len(messages)-1])

	// you can save the result to the suite for later use
	s.newbies["ark"] = true
}

func (s *HelloNewbiesSuite) TestHelloJett() {
	// prepare a result object (pointer type) for the response
	// it is recommended to use the generated response object from the API definition
	result := &todoapipb.HelloJettServiceResponse{}
	// make a new request by calling `s.NewRequest()` rather than `api.NewRequest()`
	// the suite will automatically add the host and cookies and other session context
	response, err := s.NewRequest().
		// set the method (please use const in `http` package) and path of the request
		SetMethodPath(http.MethodPost, "/moego.api.todo.v1.TodoService/HelloJett").
		// this API do not need a payload, so we do not set it
		// set the result object for the response, then response body will be unmarshalled to this object
		// it is recommended to use the generated response object from the API definition
		SetResult(result).
		// send the request
		Send()

	// check response success
	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	// check result
	messages := result.GetHelloJett()
	// 如果希望测试失败不中断运行，可以不使用 `s.Require()`，直接使用 assert 方法
	success := s.NotEmpty(messages)

	// you can save the result to the suite for later use
	s.newbies["jett"] = success
}

func (s *HelloNewbiesSuite) TestHelloWell() {
	// prepare a result object (pointer type) for the response
	// it is recommended to use the generated response object from the API definition
	result := &businessmodel.ComMoegoServerBusinessWebVoDebuggingHelloWellVO{}
	// make a new request by calling `s.NewRequest()` rather than `api.NewRequest()`
	// the suite will automatically add the host and cookies and other session context
	response, err := s.NewRequest().
		// set the method (please use const in `http` package) and path of the request
		// 对于老的 server 服务的接口，需要在 path 前面加上 '/api'
		SetMethodPath(http.MethodGet, "/api/business/debugging/well").
		// this API do not need a payload, so we do not set it
		// set the result object for the response, then response body will be unmarshalled to this object
		// it is recommended to use the generated response object from the API definition
		SetResult(result).
		// send the request
		Send()

	// check response success
	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	// check result
	// 如果希望测试失败不中断运行，可以不使用 `s.Require()`，直接使用 assert 方法
	success := s.Equal("Hello well!", result.GetMessage())

	// you can save the result to the suite for later use
	s.newbies["well"] = success
}

// TestHelloNewbiesTestSuite runs all tests in HelloNewbiesSuite
// Please do not forget to add this method to your xxx_test.go file
func TestHelloNewbiesTestSuite(t *testing.T) {
	suite.Run(t, new(HelloNewbiesSuite))
}
