load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "bff_demo_test",
    srcs = ["bff_demo_test.go"],
    tags = [
        "hello",
        "testing_only",
    ],
)

go_test(
    name = "hello_newbies_test",
    srcs = ["hello_newbies_test.go"],
    tags = [
        "hello",
        "testing_only",
    ],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/def/business/model",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/todo/v1:todo",
        "@com_github_stretchr_testify//suite",
    ],
)
