package hello

//import (
//	"net/http"
//	"testing"
//
//	"github.com/stretchr/testify/suite"
//
//	bffmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/bff/model"
//	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
//)
//
//type BffDemoTestSuite struct {
//	suite.Suite
//	godomain.Context
//}
//
//func (s *BffDemoTestSuite) SetupSuite() {
//	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
//		Borrower: "BffDemoTestSuite",
//		Email:    "<EMAIL>",
//		Shared:   true,
//	})
//}
//
//func (s *BffDemoTestSuite) TearDownSuite() {
//	defer s.Context.Teardown()
//}
//
//func (s *BffDemoTestSuite) TestOrder() {
//	result := &bffmodel.MoegoBffTestOrderGetTestOrderGet200Response{}
//	response, err := s.NewRequest().
//		SetMethodPath(http.MethodGet, "/moego.bff/test-order/getTestOrder").
//		AddQuery("id", "*********").
//		AddQuery("businessId", "108108").
//		SetResult(result).
//		Send()
//
//	s.Require().Nil(err)
//	s.Require().True(response.IsSuccess())
//
//	s.Require().NotNil(result.GetOrder())
//	s.Require().NotNil(result.GetCustomer())
//}
//
//func (s *BffDemoTestSuite) TestCreateBook() {
//	request := &bffmodel.MoegoBffBookCreateBookPostRequest{
//		BookName:   "MoeGo API Integration Test Guide",
//		BookAuthor: "Well",
//	}
//	result := &bffmodel.Book{}
//	response, err := s.NewRequest().
//		SetMethodPath(http.MethodPost, "/moego.bff/book/createBook").
//		SetPayload(request).
//		SetResult(result).
//		Send()
//
//	s.Require().Nil(err)
//	s.Require().True(response.IsSuccess())
//
//	s.Require().Equal(result.GetAuthor(), request.GetBookAuthor())
//	s.Require().Equal(result.GetName(), request.GetBookName())
//}
//
//func TestBffDemoTestSuite(t *testing.T) {
//	suite.Run(t, new(BffDemoTestSuite))
//}
