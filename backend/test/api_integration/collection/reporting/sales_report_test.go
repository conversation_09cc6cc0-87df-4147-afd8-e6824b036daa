package reporting

import (
	"testing"

	"github.com/stretchr/testify/suite"
	"google.golang.org/genproto/googleapis/type/calendarperiod"
	"google.golang.org/genproto/googleapis/type/interval"
	"google.golang.org/protobuf/types/known/timestamppb"

	reportingapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/reporting/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type SalesReportTestSuite struct {
	suite.Suite
	godomain.Context
}

func (s *SalesReportTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, nil)
	account := &godomain.TestAccount{
		Email:    "<EMAIL>",
		Password: "Qwer@123",
	}
	s.<PERSON>(account)
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByCareType() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"care_type"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	s.Require().Len(rows, 4)
	// check daycare row
	daycareRowData := rows[0].GetData()
	s.Require().Equal("Daycare", daycareRowData["care_type"].GetValue().GetString_())
	s.Require().Equal(int64(30), daycareRowData["total_collected"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), daycareRowData["total_collected"].GetValue().GetMoney().GetNanos())
	// check boarding row
	boardingRowData := rows[1].GetData()
	s.Require().Equal("Boarding", boardingRowData["care_type"].GetValue().GetString_())
	s.Require().Equal(int64(290), boardingRowData["total_gross_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), boardingRowData["total_gross_sale"].GetValue().GetMoney().GetNanos())
	// check grooming row
	groomingRowData := rows[2].GetData()
	s.Require().Equal("Grooming", groomingRowData["care_type"].GetValue().GetString_())
	s.Require().Equal(int64(213), groomingRowData["total_collected"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(940000000), groomingRowData["total_collected"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(180), groomingRowData["total_gross_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), groomingRowData["total_gross_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(176), groomingRowData["total_net_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), groomingRowData["total_net_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(3), groomingRowData["total_discount"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), groomingRowData["total_discount"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(32), groomingRowData["total_tips"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), groomingRowData["total_tips"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(5), groomingRowData["total_tax"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), groomingRowData["total_tax"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(4), groomingRowData["total_refund"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), groomingRowData["total_refund"].GetValue().GetMoney().GetNanos())
	// check non-service row
	nonServiceRowData := rows[3].GetData()
	s.Require().Equal("Non-service sales", nonServiceRowData["care_type"].GetValue().GetString_())
	s.Require().Equal(int64(11), nonServiceRowData["outstanding_balance"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), nonServiceRowData["outstanding_balance"].GetValue().GetMoney().GetNanos())
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByBusiness() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"business_name"},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	rowData := rows[0].GetData()
	s.Require().Equal(int64(930), rowData["total_expected"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(440000000), rowData["total_expected"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(919), rowData["total_collected"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(440000000), rowData["total_collected"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(11), rowData["outstanding_balance"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), rowData["outstanding_balance"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(862), rowData["total_gross_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), rowData["total_gross_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(847), rowData["total_net_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), rowData["total_net_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(3), rowData["total_discount"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), rowData["total_discount"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(24), rowData["total_tax"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), rowData["total_tax"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(47), rowData["total_tips"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), rowData["total_tips"].GetValue().GetMoney().GetNanos())
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupBySaleDate() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"sale_datetime"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	s.Require().Len(rows, 2)
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByPaymentStatus() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_status"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	s.Require().Len(rows, 2)
	// check partial paid row
	unpaidRowData := rows[1].GetData()
	s.Require().Equal("Partial paid", unpaidRowData["payment_status"].GetValue().GetString_())
	s.Require().Equal(int64(11), unpaidRowData["outstanding_balance"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), unpaidRowData["outstanding_balance"].GetValue().GetMoney().GetNanos())
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByClientName() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"client_name"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	s.Require().Len(rows, 2)
	// check first row data
	firstRowData := rows[0].GetData()
	s.Require().Equal("test report", firstRowData["client_name"].GetValue().GetString_())
	s.Require().Equal(int64(751), firstRowData["total_collected"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(540000000), firstRowData["total_collected"].GetValue().GetMoney().GetNanos())
	// check second row data
	secondRowData := rows[1].GetData()
	s.Require().Equal("Demo Profile", secondRowData["client_name"].GetValue().GetString_())
	s.Require().Equal(int64(167), secondRowData["total_collected"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), secondRowData["total_collected"].GetValue().GetMoney().GetNanos())
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByService() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"service"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	s.Require().Len(rows, 5)
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByProduct() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"product"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	s.Require().Len(rows, 2)
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByAddOn() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"addon"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByAssignedStaff() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"assigned_staff"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByCategory() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"category"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByCity() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"city"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByZipcode() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"zipcode"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByVan() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"van"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func (s *SalesReportTestSuite) TestSalesSummaryGroupByAppointmentStatus() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_sales_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"appointment_status"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func TestSalesReportSuite(t *testing.T) {
	suite.Run(t, new(SalesReportTestSuite))
}
