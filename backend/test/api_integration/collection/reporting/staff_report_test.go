package reporting

import (
	"github.com/stretchr/testify/suite"
	"google.golang.org/genproto/googleapis/type/interval"
	"google.golang.org/protobuf/types/known/timestamppb"

	reportingapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/reporting/v2"
	reportingpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type StaffReportSuite struct {
	suite.Suite
	godomain.Context
}

func (s *StaffReportSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, nil)
	account := &godomain.TestAccount{
		Email:    "<EMAIL>",
		Password: "Qwer@123",
	}
	s.<PERSON><PERSON>(account)
}

func (s *StaffReportSuite) TestStaffPerformanceGroupByName() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_staff_performance",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"staff_name"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	s.Require().Len(rows, 3)
}

func (s *StaffReportSuite) TestStaffPerformanceWithName() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_staff_performance",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"staff_name"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey:     "staff_name",
				InvertSelect: pointer.Get(false),
				Operator:     reportingpb.Operator_EQUAL,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "Staff 1",
						},
					},
				},
			},
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	s.Require().Len(rows, 1)
}

func (s *StaffReportSuite) TestStaffPerformanceWithStatus() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_staff_performance",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"staff_name"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "staff_status",
				Operator: reportingpb.Operator_EQUAL,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "Normal",
						},
					},
				},
			},
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	s.Require().Len(rows, 2)
}
