package reporting

import (
	"testing"

	"github.com/stretchr/testify/suite"
	"google.golang.org/genproto/googleapis/type/calendarperiod"
	"google.golang.org/genproto/googleapis/type/interval"
	money "google.golang.org/genproto/googleapis/type/money"
	"google.golang.org/protobuf/types/known/timestamppb"

	reportingapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/reporting/v2"
	reportingpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type PaymentReportTestSuite struct {
	suite.Suite
	godomain.Context
}

func (s *PaymentReportTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, nil)
	account := &godomain.TestAccount{
		Email:    "<EMAIL>",
		Password: "Qwer@123",
	}
	s.Login(account)
}

func (s *PaymentReportTestSuite) TestPaymentSummaryGroupByTransactionDate() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"transaction_datetime"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)

	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check transaction_datetime
	transactionDatetime := firstRowData["transaction_datetime"]
	s.Require().Equal("03/20/2025", transactionDatetime.GetValue().GetString_())

	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(919), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(440000000), totalCollected.GetValue().GetMoney().GetNanos())

	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(496), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalServiceSales.GetValue().GetMoney().GetNanos())

	// check total add-on sales
	totalAddOnSales := firstRowData["total_addon_sale"]
	s.Require().Equal(int64(10), totalAddOnSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalAddOnSales.GetValue().GetMoney().GetNanos())

	// check total service charge sales
	totalServiceChargeSales := firstRowData["total_service_charge_sale"]
	s.Require().Equal(int64(10), totalServiceChargeSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalServiceChargeSales.GetValue().GetMoney().GetNanos())

	// check total product sales
	totalProductSales := firstRowData["total_product_sale"]
	s.Require().Equal(int64(11), totalProductSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalProductSales.GetValue().GetMoney().GetNanos())

	// check total package sales
	totalPackageSales := firstRowData["total_package_sale"]
	s.Require().Equal(int64(320), totalPackageSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalPackageSales.GetValue().GetMoney().GetNanos())

	// check total tips
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(47), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTips.GetValue().GetMoney().GetNanos())

	// check total tax
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(24), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryGroupByPaymentMethod() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check transaction_datetime
	paymentMethod := firstRowData["payment_method"]
	s.Require().Equal("Cash", paymentMethod.GetValue().GetString_())

	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(919), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(440000000), totalCollected.GetValue().GetMoney().GetNanos())

	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(496), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalServiceSales.GetValue().GetMoney().GetNanos())

	// check total add-on sales
	totalAddOnSales := firstRowData["total_addon_sale"]
	s.Require().Equal(int64(10), totalAddOnSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalAddOnSales.GetValue().GetMoney().GetNanos())

	// check total service charge sales
	totalServiceChargeSales := firstRowData["total_service_charge_sale"]
	s.Require().Equal(int64(10), totalServiceChargeSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalServiceChargeSales.GetValue().GetMoney().GetNanos())

	// check total product sales
	totalProductSales := firstRowData["total_product_sale"]
	s.Require().Equal(int64(11), totalProductSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalProductSales.GetValue().GetMoney().GetNanos())

	// check total package sales
	totalPackageSales := firstRowData["total_package_sale"]
	s.Require().Equal(int64(320), totalPackageSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalPackageSales.GetValue().GetMoney().GetNanos())

	// check total tips
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(47), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTips.GetValue().GetMoney().GetNanos())

	// check total tax
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(24), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryGroupByCareType() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"care_type"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)

	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	s.Require().GreaterOrEqual(len(rows), 4) // 确保至少有4行数据

	// check Daycare row（1st row）
	daycareRowData := rows[0].GetData()
	s.Require().Equal("Daycare", daycareRowData["care_type"].GetValue().GetString_())
	s.Require().Equal(int64(30), daycareRowData["total_collected"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), daycareRowData["total_collected"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(30), daycareRowData["total_service_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), daycareRowData["total_service_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(0), daycareRowData["total_tax_amount"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), daycareRowData["total_tax_amount"].GetValue().GetMoney().GetNanos())

	// check Boarding row（2nd row）
	boardingRowData := rows[1].GetData()
	s.Require().Equal("Boarding", boardingRowData["care_type"].GetValue().GetString_())
	s.Require().Equal(int64(313), boardingRowData["total_collected"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), boardingRowData["total_collected"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(290), boardingRowData["total_service_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), boardingRowData["total_service_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(15), boardingRowData["total_tip_amount"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), boardingRowData["total_tip_amount"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(8), boardingRowData["total_tax_amount"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), boardingRowData["total_tax_amount"].GetValue().GetMoney().GetNanos())

	// check Grooming row（3rd row）
	groomingRowData := rows[2].GetData()
	s.Require().Equal("Grooming", groomingRowData["care_type"].GetValue().GetString_())
	s.Require().Equal(int64(213), groomingRowData["total_collected"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), groomingRowData["total_collected"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(176), groomingRowData["total_service_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), groomingRowData["total_service_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(32), groomingRowData["total_tip_amount"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), groomingRowData["total_tip_amount"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(5), groomingRowData["total_tax_amount"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), groomingRowData["total_tax_amount"].GetValue().GetMoney().GetNanos())

	// check Non-service sales row（4th row）
	nonServiceRowData := rows[3].GetData()
	s.Require().Equal("Non-service sales", nonServiceRowData["care_type"].GetValue().GetString_())
	s.Require().Equal(int64(360), nonServiceRowData["total_collected"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), nonServiceRowData["total_collected"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(10), nonServiceRowData["total_addon_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), nonServiceRowData["total_addon_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(10), nonServiceRowData["total_service_charge_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), nonServiceRowData["total_service_charge_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(11), nonServiceRowData["total_product_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), nonServiceRowData["total_product_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(320), nonServiceRowData["total_package_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), nonServiceRowData["total_package_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(9), nonServiceRowData["total_tax_amount"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), nonServiceRowData["total_tax_amount"].GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithTransactionType() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "transaction_type",
				Operator: reportingpb.Operator_EQUAL,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "Refund",
						},
					},
				},
			},
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(-4), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(-800000000), totalCollected.GetValue().GetMoney().GetNanos())
	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(-4), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(-20000000), totalServiceSales.GetValue().GetMoney().GetNanos())
	// check total add-on sales
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(0), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(-*********), totalTips.GetValue().GetMoney().GetNanos())
	// check total service charge sales
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(0), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(-*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithBusinessName() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "business_name",
				Operator: reportingpb.Operator_EQUAL,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "Only for data",
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	//check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(919), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(440000000), totalCollected.GetValue().GetMoney().GetNanos())
	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(496), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalServiceSales.GetValue().GetMoney().GetNanos())
	// check total add-on sales
	totalAddOnSales := firstRowData["total_addon_sale"]
	s.Require().Equal(int64(10), totalAddOnSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalAddOnSales.GetValue().GetMoney().GetNanos())
	// check total service charge sales
	totalServiceChargeSales := firstRowData["total_service_charge_sale"]
	s.Require().Equal(int64(10), totalServiceChargeSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalServiceChargeSales.GetValue().GetMoney().GetNanos())
	// check total product sales
	totalProductSales := firstRowData["total_product_sale"]
	s.Require().Equal(int64(11), totalProductSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalProductSales.GetValue().GetMoney().GetNanos())
	// check total package sales
	totalPackageSales := firstRowData["total_package_sale"]
	s.Require().Equal(int64(320), totalPackageSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalPackageSales.GetValue().GetMoney().GetNanos())
	// check total tips
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(47), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTips.GetValue().GetMoney().GetNanos())
	// check total tax
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(24), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithProcessedBy() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "processed_by",
				Operator: reportingpb.Operator_EQUAL,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "only data",
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	//check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(919), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(440000000), totalCollected.GetValue().GetMoney().GetNanos())
	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(496), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalServiceSales.GetValue().GetMoney().GetNanos())
	// check total add-on sales
	totalAddOnSales := firstRowData["total_addon_sale"]
	s.Require().Equal(int64(10), totalAddOnSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalAddOnSales.GetValue().GetMoney().GetNanos())
	// check total service charge sales
	totalServiceChargeSales := firstRowData["total_service_charge_sale"]
	s.Require().Equal(int64(10), totalServiceChargeSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalServiceChargeSales.GetValue().GetMoney().GetNanos())
	// check total product sales
	totalProductSales := firstRowData["total_product_sale"]
	s.Require().Equal(int64(11), totalProductSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalProductSales.GetValue().GetMoney().GetNanos())
	// check total package sales
	totalPackageSales := firstRowData["total_package_sale"]
	s.Require().Equal(int64(320), totalPackageSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalPackageSales.GetValue().GetMoney().GetNanos())
	// check total tips
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(47), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTips.GetValue().GetMoney().GetNanos())
	// check total tax
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(24), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithTransactionDate() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "transaction_datetime",
				Operator: reportingpb.Operator_LESS_THAN,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_Int64{
							Int64: 2,
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	//check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(919), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(440000000), totalCollected.GetValue().GetMoney().GetNanos())
	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(496), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalServiceSales.GetValue().GetMoney().GetNanos())
	// check total add-on sales
	totalAddOnSales := firstRowData["total_addon_sale"]
	s.Require().Equal(int64(10), totalAddOnSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalAddOnSales.GetValue().GetMoney().GetNanos())
	// check total service charge sales
	totalServiceChargeSales := firstRowData["total_service_charge_sale"]
	s.Require().Equal(int64(10), totalServiceChargeSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalServiceChargeSales.GetValue().GetMoney().GetNanos())
	// check total product sales
	totalProductSales := firstRowData["total_product_sale"]
	s.Require().Equal(int64(11), totalProductSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalProductSales.GetValue().GetMoney().GetNanos())
	// check total package sales
	totalPackageSales := firstRowData["total_package_sale"]
	s.Require().Equal(int64(320), totalPackageSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalPackageSales.GetValue().GetMoney().GetNanos())
	// check total tips
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(47), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTips.GetValue().GetMoney().GetNanos())
	// check total tax
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(24), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithSaleDate() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "sale_datetime",
				Operator: reportingpb.Operator_ON,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_Timestamp{
							Timestamp: &timestamppb.Timestamp{
								Seconds: 1742515200,
							},
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(195), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalCollected.GetValue().GetMoney().GetNanos())
	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(190), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalServiceSales.GetValue().GetMoney().GetNanos())
	// check total add-on sales
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(5), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithAssignedStaff() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "assigned_staff",
				Operator: reportingpb.Operator_ARRAY_CONTAINS,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "only data",
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(110), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalCollected.GetValue().GetMoney().GetNanos())
	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(90), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalServiceSales.GetValue().GetMoney().GetNanos())
	// check total tips
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(18), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalTips.GetValue().GetMoney().GetNanos())
	// check total tax
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(2), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithCareType() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "care_type",
				Operator: reportingpb.Operator_ARRAY_CONTAINS,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "Grooming",
						},
					},
				},
				InvertSelect: pointer.Get(false),
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(213), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalCollected.GetValue().GetMoney().GetNanos())
	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(176), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalServiceSales.GetValue().GetMoney().GetNanos())
	// check total add-on sales
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(32), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTips.GetValue().GetMoney().GetNanos())
	// check total service charge sales
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(5), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithCategory() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "category",
				Operator: reportingpb.Operator_ARRAY_CONTAINS,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "Add-on",
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(10), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalCollected.GetValue().GetMoney().GetNanos())
	// check total add-on sales
	totalAddOnSales := firstRowData["total_addon_sale"]
	s.Require().Equal(int64(10), totalAddOnSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalAddOnSales.GetValue().GetMoney().GetNanos())
	// check total tax
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(0), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithService() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "service",
				Operator: reportingpb.Operator_ARRAY_CONTAINS,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "Full service - small",
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(213), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalCollected.GetValue().GetMoney().GetNanos())
	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(176), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalServiceSales.GetValue().GetMoney().GetNanos())
	// check total tips
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(32), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTips.GetValue().GetMoney().GetNanos())
	// check total tax
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(5), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithProduct() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "product",
				Operator: reportingpb.Operator_ARRAY_CONTAINS,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "Dog food",
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(11), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalCollected.GetValue().GetMoney().GetNanos())
	// check total product sales
	totalProductSales := firstRowData["total_product_sale"]
	s.Require().Equal(int64(11), totalProductSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalProductSales.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithAddon() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "addon",
				Operator: reportingpb.Operator_ARRAY_CONTAINS,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "Extra care",
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(10), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalCollected.GetValue().GetMoney().GetNanos())
	// check total add-on sales
	totalAddOnSales := firstRowData["total_addon_sale"]
	s.Require().Equal(int64(10), totalAddOnSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalAddOnSales.GetValue().GetMoney().GetNanos())
	// check total tax
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(0), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithCity() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "city",
				Operator: reportingpb.Operator_LIKE,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_String_{
							String_: "Los Angeles",
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()

	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(167), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalCollected.GetValue().GetMoney().GetNanos())

	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(120), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalServiceSales.GetValue().GetMoney().GetNanos())

	// check total add-on sales
	totalAddOnSales := firstRowData["total_addon_sale"]
	s.Require().Equal(int64(10), totalAddOnSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalAddOnSales.GetValue().GetMoney().GetNanos())

	// check total service charge sales
	totalServiceChargeSales := firstRowData["total_service_charge_sale"]
	s.Require().Equal(int64(5), totalServiceChargeSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalServiceChargeSales.GetValue().GetMoney().GetNanos())

	// check total product sales
	totalProductSales := firstRowData["total_product_sale"]
	s.Require().Equal(int64(11), totalProductSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalProductSales.GetValue().GetMoney().GetNanos())

	// check total tips
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(18), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalTips.GetValue().GetMoney().GetNanos())

	// check total tax
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(3), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithTotalCollected() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "total_collected",
				Operator: reportingpb.Operator_GREATER_THAN,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_Money{
							Money: &money.Money{
								Units:        919,
								Nanos:        *********,
								CurrencyCode: "$",
							},
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()

	// check total collected
	totalCollected := firstRowData["total_collected"]
	s.Require().Equal(int64(919), totalCollected.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(440000000), totalCollected.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithTotalServiceSales() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "total_service_sale",
				Operator: reportingpb.Operator_GREATER_THAN,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_Money{
							Money: &money.Money{
								Units:        496,
								Nanos:        *********,
								CurrencyCode: "$",
							},
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total service sales
	totalServiceSales := firstRowData["total_service_sale"]
	s.Require().Equal(int64(496), totalServiceSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalServiceSales.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithTotalAddOnSales() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "total_addon_sale",
				Operator: reportingpb.Operator_LESS_THAN,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_Money{
							Money: &money.Money{
								Units:        11,
								Nanos:        0,
								CurrencyCode: "$",
							},
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total add-on sales
	totalAddOnSales := firstRowData["total_addon_sale"]
	s.Require().Equal(int64(10), totalAddOnSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalAddOnSales.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithTotalServiceChargeSales() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "total_service_charge_sale",
				Operator: reportingpb.Operator_EQUAL,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_Money{
							Money: &money.Money{
								Units:        10,
								Nanos:        0,
								CurrencyCode: "$",
							},
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total service charge sales
	totalServiceChargeSales := firstRowData["total_service_charge_sale"]
	s.Require().Equal(int64(10), totalServiceChargeSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalServiceChargeSales.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithTotalProductSales() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "total_product_sale",
				Operator: reportingpb.Operator_GREATER_THAN,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_Money{
							Money: &money.Money{
								Units:        10,
								Nanos:        *********,
								CurrencyCode: "$",
							},
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total product sales
	totalProductSales := firstRowData["total_product_sale"]
	s.Require().Equal(int64(11), totalProductSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalProductSales.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithTotalPackageSales() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "total_package_sale",
				Operator: reportingpb.Operator_GREATER_THAN,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_Money{
							Money: &money.Money{
								Units:        319,
								Nanos:        *********,
								CurrencyCode: "$",
							},
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total package sales
	totalPackageSales := firstRowData["total_package_sale"]
	s.Require().Equal(int64(320), totalPackageSales.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(0), totalPackageSales.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithTotalTips() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "total_tip_amount",
				Operator: reportingpb.Operator_GREATER_THAN,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_Money{
							Money: &money.Money{
								Units:        47,
								Nanos:        *********,
								CurrencyCode: "$",
							},
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total tips
	totalTips := firstRowData["total_tip_amount"]
	s.Require().Equal(int64(47), totalTips.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTips.GetValue().GetMoney().GetNanos())
}

func (s *PaymentReportTestSuite) TestPaymentSummaryWithTotalTaxes() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_payment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_method"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
		Filters: []*reportingpb.FilterRequest{
			{
				FieldKey: "total_tax_amount",
				Operator: reportingpb.Operator_GREATER_THAN,
				Values: []*reportingpb.Value{
					{
						Value: &reportingpb.Value_Money{
							Money: &money.Money{
								Units:        24,
								Nanos:        *********,
								CurrencyCode: "$",
							},
						},
					},
				},
			},
		},
	}
	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
	firstRowData := rows[0].GetData()
	// check total taxes
	totalTax := firstRowData["total_tax_amount"]
	s.Require().Equal(int64(24), totalTax.GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(*********), totalTax.GetValue().GetMoney().GetNanos())
}

func TestPaymentSummaryReportSuite(t *testing.T) {
	suite.Run(t, new(PaymentReportTestSuite))
}
