package reporting

import (
	"github.com/stretchr/testify/suite"
	"google.golang.org/genproto/googleapis/type/calendarperiod"
	"google.golang.org/genproto/googleapis/type/interval"
	"google.golang.org/protobuf/types/known/timestamppb"

	reportingapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/reporting/v2"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type AppointmentReportTestSuite struct {
	suite.Suite
	godomain.Context
}

func (s *AppointmentReportTestSuite) SetupSuite() {
	s.Context.Setup(&s.Suite, nil)
	account := &godomain.TestAccount{
		Email:    "<EMAIL>",
		Password: "Qwer@123",
	}
	s.<PERSON><PERSON>(account)
}

func (s *AppointmentReportTestSuite) TestGroomingAppointmentGroupByDate() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_appointment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"appointment_date"},
		GroupByPeriod:    pointer.Get(calendarperiod.CalendarPeriod_DAY),
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)

	s.Require().Equal(int64(5), rows[0].GetData()["total_appointments"].GetValue().GetInt64())
	s.Require().Equal(int64(4), rows[0].GetData()["total_clients"].GetValue().GetInt64())
	s.Require().Equal(int64(4), rows[0].GetData()["total_pets"].GetValue().GetInt64())
	s.Require().Equal(int64(232), rows[0].GetData()["total_net_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(400000000), rows[0].GetData()["total_net_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(70), rows[0].GetData()["avg_price_per_ticket"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(530000000), rows[0].GetData()["avg_price_per_ticket"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(1), rows[0].GetData()["total_cancelled"].GetValue().GetInt64())
	s.Require().Equal(int64(32), rows[0].GetData()["total_tips"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(250000000), rows[0].GetData()["total_tips"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(float64(0.07), rows[0].GetData()["avg_tip_rate"].GetValue().GetDouble())
	s.Require().Equal(float64(0.5), rows[0].GetData()["rebook_rate"].GetValue().GetDouble())
}

func (s *AppointmentReportTestSuite) TestGroomingAppointmentGroupByBusinessName() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_appointment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"business_name"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)

	s.Require().Equal(int64(5), rows[0].GetData()["total_appointments"].GetValue().GetInt64())
	s.Require().Equal(int64(4), rows[0].GetData()["total_clients"].GetValue().GetInt64())
	s.Require().Equal(int64(4), rows[0].GetData()["total_pets"].GetValue().GetInt64())
	s.Require().Equal(int64(232), rows[0].GetData()["total_net_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(400000000), rows[0].GetData()["total_net_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(70), rows[0].GetData()["avg_price_per_ticket"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(530000000), rows[0].GetData()["avg_price_per_ticket"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(1), rows[0].GetData()["total_cancelled"].GetValue().GetInt64())
	s.Require().Equal(int64(32), rows[0].GetData()["total_tips"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(250000000), rows[0].GetData()["total_tips"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(float64(0.07), rows[0].GetData()["avg_tip_rate"].GetValue().GetDouble())
	s.Require().Equal(float64(0.5), rows[0].GetData()["rebook_rate"].GetValue().GetDouble())
}

func (s *AppointmentReportTestSuite) TestGroomingAppointmentGroupByCreateDate() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_appointment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"create_date"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)

	s.Require().Equal(int64(5), rows[0].GetData()["total_appointments"].GetValue().GetInt64())
	s.Require().Equal(int64(4), rows[0].GetData()["total_clients"].GetValue().GetInt64())
	s.Require().Equal(int64(4), rows[0].GetData()["total_pets"].GetValue().GetInt64())
	s.Require().Equal(int64(232), rows[0].GetData()["total_net_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(400000000), rows[0].GetData()["total_net_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(70), rows[0].GetData()["avg_price_per_ticket"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(530000000), rows[0].GetData()["avg_price_per_ticket"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(1), rows[0].GetData()["total_cancelled"].GetValue().GetInt64())
	s.Require().Equal(int64(32), rows[0].GetData()["total_tips"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(250000000), rows[0].GetData()["total_tips"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(float64(0.07), rows[0].GetData()["avg_tip_rate"].GetValue().GetDouble())
	s.Require().Equal(float64(0.5), rows[0].GetData()["rebook_rate"].GetValue().GetDouble())
}

func (s *AppointmentReportTestSuite) TestGroomingAppointmentGroupByAssignedStaff() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_appointment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"assigned_staff"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func (s *AppointmentReportTestSuite) TestGroomingAppointmentGroupByCreateStaff() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_appointment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"create_staff"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)

	s.Require().Equal(int64(5), rows[0].GetData()["total_appointments"].GetValue().GetInt64())
	s.Require().Equal(int64(4), rows[0].GetData()["total_clients"].GetValue().GetInt64())
	s.Require().Equal(int64(4), rows[0].GetData()["total_pets"].GetValue().GetInt64())
	s.Require().Equal(int64(232), rows[0].GetData()["total_net_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(400000000), rows[0].GetData()["total_net_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(70), rows[0].GetData()["avg_price_per_ticket"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(530000000), rows[0].GetData()["avg_price_per_ticket"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(1), rows[0].GetData()["total_cancelled"].GetValue().GetInt64())
	s.Require().Equal(int64(32), rows[0].GetData()["total_tips"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(250000000), rows[0].GetData()["total_tips"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(float64(0.07), rows[0].GetData()["avg_tip_rate"].GetValue().GetDouble())
	s.Require().Equal(float64(0.5), rows[0].GetData()["rebook_rate"].GetValue().GetDouble())
}

func (s *AppointmentReportTestSuite) TestGroomingAppointmentGroupByService() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_appointment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"service"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func (s *AppointmentReportTestSuite) TestGroomingAppointmentGroupByAppointmentStatus() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_appointment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"appointment_status"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func (s *AppointmentReportTestSuite) TestGroomingAppointmentGroupByPaymentStatus() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_appointment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"payment_status"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)
}

func (s *AppointmentReportTestSuite) TestGroomingAppointmentGroupBySource() {
	payload := &reportingapipb.FetchReportDataRequest{
		DiagramId:   "reports_appointment_summary",
		BusinessIds: []uint64{120843},
		CurrentPeriod: &interval.Interval{
			StartTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
			EndTime: &timestamppb.Timestamp{
				Seconds: **********,
			},
		},
		GroupByFieldKeys: []string{"source"},
		Pagination: &utilsV2.PaginationRequest{
			PageNum:  pointer.Get(int32(1)),
			PageSize: pointer.Get(int32(10)),
		},
	}

	tableData := s.GetTableData(payload)
	rows := tableData.GetRows()
	s.Require().NotNil(rows)

	s.Require().Equal(int64(5), rows[0].GetData()["total_appointments"].GetValue().GetInt64())
	s.Require().Equal(int64(4), rows[0].GetData()["total_clients"].GetValue().GetInt64())
	s.Require().Equal(int64(4), rows[0].GetData()["total_pets"].GetValue().GetInt64())
	s.Require().Equal(int64(232), rows[0].GetData()["total_net_sale"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(400000000), rows[0].GetData()["total_net_sale"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(70), rows[0].GetData()["avg_price_per_ticket"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(530000000), rows[0].GetData()["avg_price_per_ticket"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(int64(1), rows[0].GetData()["total_cancelled"].GetValue().GetInt64())
	s.Require().Equal(int64(32), rows[0].GetData()["total_tips"].GetValue().GetMoney().GetUnits())
	s.Require().Equal(int32(250000000), rows[0].GetData()["total_tips"].GetValue().GetMoney().GetNanos())
	s.Require().Equal(float64(0.07), rows[0].GetData()["avg_tip_rate"].GetValue().GetDouble())
	s.Require().Equal(float64(0.5), rows[0].GetData()["rebook_rate"].GetValue().GetDouble())
}
