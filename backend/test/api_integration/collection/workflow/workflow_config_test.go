package workflow

import (
	"net/http"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"

	automationapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/automation/v1"
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/automation/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type WorkflowConfigTestSuite struct {
	suite.Suite
	godomain.Context
	workflowID int64
	setupDone  bool
}

func (s *WorkflowConfigTestSuite) createWorkflow(params *automationapipb.CreateWorkflowParams) int64 {
	result := &automationapipb.CreateWorkflowResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.automation.v1.WorkflowService/CreateWorkflow").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	workflowID := result.GetWorkflow().GetId()
	s.Require().NotNil(workflowID)

	return workflowID
}

func (s *WorkflowConfigTestSuite) upateWorkflow(params *automationapipb.UpdateWorkflowInfoParams) {
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.automation.v1.WorkflowService/UpdateWorkflowInfo").
		SetPayload(params).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
}

func (s *WorkflowConfigTestSuite) getWorkflowList(params *automationapipb.ListWorkflowsParams) []int64 {
	result := &automationapipb.ListWorkflowsResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.automation.v1.WorkflowService/ListWorkflows").
		SetPayload(params).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	var workflowIDList []int64
	workflowList := result.GetWorkflows()
	for _, workflow := range workflowList {
		workflowID := workflow.GetId()
		s.Require().NotNil(workflowID)
		workflowIDList = append(workflowIDList, workflowID)
	}

	return workflowIDList
}

func (s *WorkflowConfigTestSuite) SetupSuite() {
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	s.Context.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "WorkflowConfigTestSuite",
	})

	uuid1, _ := uuid.NewV7()
	uuid2, _ := uuid.NewV7()
	s.workflowID = s.createWorkflow(&automationapipb.CreateWorkflowParams{
		Workflow: &v1.CreateWorkflowDef{
			Name:        "Test Workflow",
			Description: "Test Workflow Description",
			Steps: []*v1.CreateStepDef{
				{
					Id:   uuid1.String(),
					Type: v1.Step_TRIGGER,
					Data: &v1.Step_Data{
						Data: &v1.Step_Data_Trigger{
							Trigger: &v1.Trigger{
								Type: v1.Trigger_EVENT,
								Name: "Create Appointment",
								Data: &v1.Trigger_Data{
									Data: &v1.Trigger_Data_Event{
										Event: &v1.Event{
											Category: v1.Event_APPOINTMENT,
											Trigger:  v1.Event_APPOINTMENT_CREATED,
										},
									},
								},
							},
						},
					},
				},
				{
					Id:   uuid2.String(),
					Type: v1.Step_MESSAGE,
					Data: &v1.Step_Data{
						Data: &v1.Step_Data_Message{
							Message: &v1.Message{
								Type:     v1.Message_SMS,
								Category: v1.Message_CAMPAIGN,
								Data: &v1.Message_Data{
									Data: &v1.Message_Data_SmsData{
										SmsData: &v1.SMSData{
											Content: "Hello, this is a test message",
										},
									},
								},
							},
						},
					},
					ParentId: uuid1.String(),
				},
			},
		},
	})

	s.setupDone = true
}

func (s *WorkflowConfigTestSuite) TearDownSuite() {
	defer s.Context.Teardown()
	s.mustTearDownSuite()
}

func (s *WorkflowConfigTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.workflowID > 0 {
		s.upateWorkflow(&automationapipb.UpdateWorkflowInfoParams{
			WorkflowId: s.workflowID,
			Status:     pointer.Get(v1.Workflow_DELETED),
		})
	}
}

func (s *WorkflowConfigTestSuite) TestGetWorkflowInfo() {
	result := &automationapipb.GetWorkflowInfoResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.automation.v1.WorkflowService/GetWorkflowInfo").
		SetPayload(&automationapipb.GetWorkflowInfoParams{
			WorkflowId: s.workflowID,
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	workflowID := result.GetWorkflow().GetId()
	s.Require().Equal(s.workflowID, workflowID)
}

func (s *WorkflowConfigTestSuite) TestGetWorkflowConfig() {
	result := &automationapipb.GetWorkflowConfigResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.automation.v1.WorkflowService/GetWorkflowConfig").
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	filterGroups := result.GetFilterGroups()
	s.Require().NotEmpty(filterGroups)
	workflowConfigs := result.GetWorkflowConfigs()
	s.Require().NotEmpty(workflowConfigs)
}

func (s *WorkflowConfigTestSuite) TestGetWorkflowList() {
	payload := &automationapipb.ListWorkflowsParams{
		Pagination: &utilsV2.PaginationRequest{
			PageSize: pointer.Get(int32(10)),
			PageNum:  pointer.Get(int32(1)),
		},
		Filter: &automationapipb.ListWorkflowsParams_Filter{
			Name: pointer.Get(""),
			Status: []v1.Workflow_Status{
				v1.Workflow_ACTIVE,
				v1.Workflow_INACTIVE,
				v1.Workflow_DRAFT,
			},
		},
	}
	result := &automationapipb.ListWorkflowsResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.automation.v1.WorkflowService/ListWorkflows").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	workflowList := result.GetWorkflows()
	s.Require().NotEmpty(workflowList)
	fisrtWorkflow := workflowList[0]
	s.Require().Equal(s.workflowID, fisrtWorkflow.GetId())
	s.Require().Equal("Test Workflow", fisrtWorkflow.GetName())
	s.Require().Equal("Test Workflow Description", fisrtWorkflow.GetDescription())
	s.Require().Equal(v1.Trigger_EVENT, fisrtWorkflow.GetTriggerType())
}

func (s *WorkflowConfigTestSuite) TestFilterWorkflowWithName() {
	payload := &automationapipb.ListWorkflowsParams{
		Pagination: &utilsV2.PaginationRequest{
			PageSize: pointer.Get(int32(10)),
			PageNum:  pointer.Get(int32(1)),
		},
		Filter: &automationapipb.ListWorkflowsParams_Filter{
			Name: pointer.Get("Test"),
			Status: []v1.Workflow_Status{
				v1.Workflow_ACTIVE,
				v1.Workflow_INACTIVE,
				v1.Workflow_DRAFT,
			},
		},
	}

	workflowIDList := s.getWorkflowList(payload)

	s.Require().Contains(workflowIDList, s.workflowID)
}

func (s *WorkflowConfigTestSuite) TestFilterWorkflowWithStatus() {
	// set workflow status to active
	s.upateWorkflow(&automationapipb.UpdateWorkflowInfoParams{
		WorkflowId: s.workflowID,
		Status:     pointer.Get(v1.Workflow_ACTIVE),
	})

	payload := &automationapipb.ListWorkflowsParams{
		Pagination: &utilsV2.PaginationRequest{
			PageSize: pointer.Get(int32(10)),
			PageNum:  pointer.Get(int32(1)),
		},
		Filter: &automationapipb.ListWorkflowsParams_Filter{
			Name: pointer.Get(""),
			Status: []v1.Workflow_Status{
				v1.Workflow_ACTIVE,
			},
		},
	}

	workflowIDList := s.getWorkflowList(payload)

	s.Require().Contains(workflowIDList, s.workflowID)

	// set workflow status to inactive
	s.upateWorkflow(&automationapipb.UpdateWorkflowInfoParams{
		WorkflowId:           s.workflowID,
		Status:               pointer.Get(v1.Workflow_INACTIVE),
		ShutDownPendingSteps: pointer.Get(true),
	})

	payload.Filter.Status = []v1.Workflow_Status{
		v1.Workflow_INACTIVE,
	}

	workflowIDList = s.getWorkflowList(payload)

	s.Require().Contains(workflowIDList, s.workflowID)
}

func (s *WorkflowConfigTestSuite) TestListWorkflowTemplates() {
	payload := &automationapipb.ListWorkflowTemplatesParams{
		Pagination: &utilsV2.PaginationRequest{
			PageSize: pointer.Get(int32(10)),
			PageNum:  pointer.Get(int32(1)),
		},
		Filter: &automationapipb.ListWorkflowTemplatesParams_Filter{
			RecommendType: pointer.Get(v1.Workflow_HOME_PAGE),
		},
	}
	result := &automationapipb.ListWorkflowTemplatesResult{}
	response, err := s.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.automation.v1.WorkflowService/ListWorkflowTemplates").
		SetPayload(payload).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	workflows := result.GetWorkflows()
	s.Require().NotEmpty(workflows)
}

func TestWorkflowConfigTestSuite(t *testing.T) {
	suite.Run(t, new(WorkflowConfigTestSuite))
}
