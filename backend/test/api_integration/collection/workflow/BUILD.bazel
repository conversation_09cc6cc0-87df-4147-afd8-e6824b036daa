load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "workflow_config_test",
    srcs = ["workflow_config_test.go"],
    tags = ["workflow"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/common/utils/pointer",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_google_uuid//:uuid",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/automation/v1:automation",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/automation/v1:automation",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v2:utils",
        "@com_github_stretchr_testify//suite",
    ],
)
