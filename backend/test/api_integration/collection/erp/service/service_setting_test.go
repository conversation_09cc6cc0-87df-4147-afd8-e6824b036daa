package service

import (
	"net/http"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	offeringapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	groomingmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type ServiceSettingTestSuite struct {
	suite.Suite
	goCtx godomain.Context

	groupClassService *offeringpb.ServiceModel
	boardingService   *offeringpb.ServiceModel
	daycareService    *offeringpb.ServiceModel
	groomingService   *offeringpb.ServiceModel
	dogWalkingService *offeringpb.ServiceModel

	serviceIdsToDelete []int64
}

func (s *ServiceSettingTestSuite) SetupSuite() {
	// 初始化上下文
	s.goCtx.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		// 指定测试套件的名称
		Borrower: "ServiceSettingTestSuite",
	})
}

func (s *ServiceSettingTestSuite) TearDownSuite() {
	// 清理上下文释放资源
	defer s.goCtx.Teardown()

	for _, serviceId := range s.serviceIdsToDelete {
		s.goCtx.DeleteService(serviceId)
	}
}

func (s *ServiceSettingTestSuite) TestCreateGroupClassService() {
	// 设置入参
	serviceName := "Test Service" + uuid.New().String()
	servicePrice := 20.0
	serviceDuration := 30
	numSessions := 6
	durationSessionMin := 30
	capacity := 6

	taxes := s.goCtx.ListTaxes()
	if len(taxes) == 0 {
		s.Suite.T().Fatalf("before creating service, please create a tax")
	}
	// 创建服务
	result := &offeringapipb.CreateServiceResult{}
	resp, err := s.goCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/CreateService").
		SetPayload(offeringapipb.CreateServiceParams{
			Service: &offeringpb.CreateServiceDef{
				Name:                       serviceName,
				Inactive:                   false,
				ServiceItemType:            offeringpb.ServiceItemType_GROUP_CLASS.Enum(),
				Price:                      servicePrice,
				PriceUnit:                  offeringpb.ServicePriceUnit_SERVICE_PRICE_UNIT_UNSPECIFIED,
				TaxId:                      taxes[0].GetId(),
				Duration:                   int32(serviceDuration),
				AddToCommissionBase:        false,
				CanTip:                     false,
				IsAllLocation:              true,
				BreedFilter:                false,
				PetSizeFilter:              false,
				CoatFilter:                 false,
				RequireDedicatedLodging:    false,
				LodgingFilter:              false,
				Type:                       offeringpb.ServiceType_SERVICE.Enum(),
				RequireDedicatedStaff:      false,
				AvailableForAllStaff:       false,
				Source:                     offeringpb.ServiceModel_MOEGO_PLATFORM.Enum(),
				NumSessions:                proto.Int32(int32(numSessions)),
				DurationSessionMin:         proto.Int32(int32(durationSessionMin)),
				Capacity:                   proto.Int32(int32(capacity)),
				IsRequirePrerequisiteClass: false,
				PrerequisiteClassIds:       []int64{},
			},
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())

	// 检查返回的服务
	service := result.GetService()
	s.Require().NotNil(service, "service should not be nil")
	s.groupClassService = service
}

func (s *ServiceSettingTestSuite) TestCreateBoardingService() {
	// 设置入参
	serviceName := "Test create boarding service" + uuid.New().String()
	servicePrice := 60.0

	taxes := s.goCtx.ListTaxes()
	if len(taxes) == 0 {
		s.Suite.T().Fatalf("before creating service, please create a tax")
	}

	// 创建寄宿服务
	result := &offeringapipb.CreateServiceResult{}
	resp, err := s.goCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/CreateService").
		SetPayload(offeringapipb.CreateServiceParams{
			Service: &offeringpb.CreateServiceDef{
				Name:                    serviceName,
				Inactive:                false,
				ServiceItemType:         offeringpb.ServiceItemType_BOARDING.Enum(),
				Price:                   servicePrice,
				PriceUnit:               offeringpb.ServicePriceUnit_PER_NIGHT,
				TaxId:                   taxes[0].GetId(),
				AddToCommissionBase:     false,
				CanTip:                  false,
				IsAllLocation:           true,
				BreedFilter:             false,
				PetSizeFilter:           false,
				CoatFilter:              false,
				RequireDedicatedLodging: true,
				LodgingFilter:           false,
				Type:                    offeringpb.ServiceType_SERVICE.Enum(),
				RequireDedicatedStaff:   false,
				AvailableForAllStaff:    true,
				Source:                  offeringpb.ServiceModel_MOEGO_PLATFORM.Enum(),
				Description:             proto.String("Test create boarding service"),
				ColorCode:               proto.String("#7AC5FF"),
			},
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())

	// 检查返回的服务
	service := result.GetService()
	s.Require().NotNil(service, "service should not be nil")
	s.boardingService = service
}

func (s *ServiceSettingTestSuite) TestCreateDaycareService() {
	// 设置入参
	serviceName := "Test create daycare service" + uuid.New().String()
	servicePrice := 20.0
	maxDuration := 720

	taxes := s.goCtx.ListTaxes()
	if len(taxes) == 0 {
		s.Suite.T().Fatalf("before creating service, please create a tax")
	}

	// 创建日托服务
	result := &offeringapipb.CreateServiceResult{}
	resp, err := s.goCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/CreateService").
		SetPayload(offeringapipb.CreateServiceParams{
			Service: &offeringpb.CreateServiceDef{
				Name:                    serviceName,
				Inactive:                false,
				ServiceItemType:         offeringpb.ServiceItemType_DAYCARE.Enum(),
				Price:                   servicePrice,
				PriceUnit:               offeringpb.ServicePriceUnit_PER_SESSION,
				TaxId:                   taxes[0].GetId(),
				AddToCommissionBase:     false,
				CanTip:                  false,
				IsAllLocation:           true,
				BreedFilter:             false,
				PetSizeFilter:           false,
				CoatFilter:              false,
				RequireDedicatedLodging: false,
				LodgingFilter:           false,
				Type:                    offeringpb.ServiceType_SERVICE.Enum(),
				RequireDedicatedStaff:   false,
				AvailableForAllStaff:    true,
				Source:                  offeringpb.ServiceModel_MOEGO_PLATFORM.Enum(),
				Description:             proto.String("Test create daycare service"),
				ColorCode:               proto.String("#FFB865"),
				MaxDuration:             proto.Int32(int32(maxDuration)),
			},
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())

	// 检查返回的服务
	service := result.GetService()
	s.Require().NotNil(service, "service should not be nil")
	s.daycareService = service
}

func (s *ServiceSettingTestSuite) TestCreateGroomingService() {
	// 设置入参
	serviceName := "Test create grooming service" + uuid.New().String()
	servicePrice := 20.0
	serviceDuration := 60

	taxes := s.goCtx.ListTaxes()
	if len(taxes) == 0 {
		s.Suite.T().Fatalf("before creating service, please create a tax")
	}

	// 创建美容服务
	result := &offeringapipb.CreateServiceResult{}
	resp, err := s.goCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/CreateService").
		SetPayload(offeringapipb.CreateServiceParams{
			Service: &offeringpb.CreateServiceDef{
				Name:                    serviceName,
				Inactive:                false,
				ServiceItemType:         offeringpb.ServiceItemType_GROOMING.Enum(),
				Price:                   servicePrice,
				PriceUnit:               offeringpb.ServicePriceUnit_PER_SESSION,
				TaxId:                   taxes[0].GetId(),
				Duration:                int32(serviceDuration),
				AddToCommissionBase:     true,
				CanTip:                  true,
				IsAllLocation:           true,
				BreedFilter:             false,
				PetSizeFilter:           false,
				CoatFilter:              false,
				RequireDedicatedLodging: false,
				LodgingFilter:           false,
				Type:                    offeringpb.ServiceType_SERVICE.Enum(),
				RequireDedicatedStaff:   true,
				AvailableForAllStaff:    true,
				Source:                  offeringpb.ServiceModel_MOEGO_PLATFORM.Enum(),
				Description:             proto.String("Test create grooming service"),
				ColorCode:               proto.String("#000000"),
			},
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())

	// 检查返回的服务
	service := result.GetService()
	s.Require().NotNil(service, "service should not be nil")
	s.groomingService = service
}

func (s *ServiceSettingTestSuite) TestCreateWalkingService() {
	// 设置入参
	serviceName := "Test create walking service" + uuid.New().String()
	servicePrice := 20.0
	serviceDuration := 74

	taxes := s.goCtx.ListTaxes()
	if len(taxes) == 0 {
		s.Suite.T().Fatalf("before creating service, please create a tax")
	}

	//businessID := s.goCtx.GetAuthInfo().BusinessID
	// 创建遛狗服务
	result := &offeringapipb.CreateServiceResult{}
	resp, err := s.goCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/CreateService").
		SetPayload(offeringapipb.CreateServiceParams{
			Service: &offeringpb.CreateServiceDef{
				Name:                    serviceName,
				Inactive:                false,
				ServiceItemType:         offeringpb.ServiceItemType_DOG_WALKING.Enum(),
				Price:                   servicePrice,
				PriceUnit:               offeringpb.ServicePriceUnit_PER_SESSION,
				TaxId:                   taxes[0].GetId(),
				Duration:                int32(serviceDuration),
				AddToCommissionBase:     true,
				CanTip:                  true,
				IsAllLocation:           true,
				BreedFilter:             false,
				PetSizeFilter:           false,
				CoatFilter:              false,
				RequireDedicatedLodging: false,
				LodgingFilter:           false,
				Type:                    offeringpb.ServiceType_SERVICE.Enum(),
				RequireDedicatedStaff:   false,
				AvailableForAllStaff:    true,
				Source:                  offeringpb.ServiceModel_MOEGO_PLATFORM.Enum(),
				Description:             proto.String("Test create walking service"),
				ColorCode:               proto.String("#000000"),
			},
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())

	// 检查返回的服务
	service := result.GetService()
	s.Require().NotNil(service, "service should not be nil")
	s.dogWalkingService = service
}

func (s *ServiceSettingTestSuite) TestGetServiceList() {
	result := &offeringapipb.GetServiceListResult{}
	resp, err := s.goCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/GetServiceList").
		SetPayload(offeringapipb.GetServiceListParams{
			BusinessIds:     []int64{},
			StaffIds:        []int64{},
			ServiceType:     offeringpb.ServiceType_SERVICE.Enum(),
			ServiceItemType: offeringpb.ServiceItemType_GROUP_CLASS.Enum(),
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())
}

func (s *ServiceSettingTestSuite) TestDeleteService() {
	// delete group class service
	groupClassServiceId := s.groupClassService.ServiceId
	response, err := s.goCtx.NewRequest().
		SetMethodPath(http.MethodDelete, "api/grooming/service").
		SetPayload(&groomingmodel.ComMoegoServerGroomingWebVoDeleteIdVo{
			Id: int32(groupClassServiceId),
		}).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	// delete boarding service
	boardingServiceId := s.boardingService.ServiceId
	response, err = s.goCtx.NewRequest().
		SetMethodPath(http.MethodDelete, "api/grooming/service").
		SetPayload(&groomingmodel.ComMoegoServerGroomingWebVoDeleteIdVo{
			Id: int32(boardingServiceId),
		}).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	// delete daycare service
	daycareServiceId := s.daycareService.ServiceId
	response, err = s.goCtx.NewRequest().
		SetMethodPath(http.MethodDelete, "api/grooming/service").
		SetPayload(&groomingmodel.ComMoegoServerGroomingWebVoDeleteIdVo{
			Id: int32(daycareServiceId),
		}).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	// delete grooming service
	groomingServiceId := s.groomingService.ServiceId
	response, err = s.goCtx.NewRequest().
		SetMethodPath(http.MethodDelete, "api/grooming/service").
		SetPayload(&groomingmodel.ComMoegoServerGroomingWebVoDeleteIdVo{
			Id: int32(groomingServiceId),
		}).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	// delete dog walking service
	dogWalkingServiceId := s.dogWalkingService.ServiceId
	response, err = s.goCtx.NewRequest().
		SetMethodPath(http.MethodDelete, "api/grooming/service").
		SetPayload(&groomingmodel.ComMoegoServerGroomingWebVoDeleteIdVo{
			Id: int32(dogWalkingServiceId),
		}).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())
}

func (s *ServiceSettingTestSuite) TestAdditionalServiceSetting() {
	// arrange
	tempService := s.goCtx.CreateService()
	s.serviceIdsToDelete = append(s.serviceIdsToDelete, tempService.ServiceId)
	// act
	service := s.goCtx.CreateService(func(service *offeringpb.CreateServiceDef) {
		service.AdditionalServiceRule = &offeringpb.AdditionalServiceRule{
			Enable:        true,
			MinStayLength: 3,
			ApplyRules: []*offeringpb.AdditionalServiceRule_ApplyRule{
				{
					ServiceId:      tempService.ServiceId,
					DateType:       offeringpb.DateType_LAST_DAY,
					QuantityPerDay: 2,
				},
			},
		}
	})
	s.serviceIdsToDelete = append(s.serviceIdsToDelete, service.ServiceId)

	// assert
	s.Require().NotNil(service)
	s.Require().Equal(service.AdditionalServiceRule.GetEnable(), true)
	s.Require().Equal(service.AdditionalServiceRule.GetMinStayLength(), int32(3))
	s.Require().Equal(len(service.AdditionalServiceRule.GetApplyRules()), 1)
	s.Require().Equal(service.AdditionalServiceRule.GetApplyRules()[0].GetServiceId(), tempService.ServiceId)
	s.Require().Equal(service.AdditionalServiceRule.GetApplyRules()[0].GetDateType(), offeringpb.DateType_LAST_DAY)
	s.Require().Equal(service.AdditionalServiceRule.GetApplyRules()[0].GetQuantityPerDay(), int32(2))
}

func TestServiceSettingTestSuite(t *testing.T) {
	suite.Run(t, new(ServiceSettingTestSuite))
}
