package service

import (
	"net/http"
	"testing"

	"github.com/stretchr/testify/suite"
	"google.golang.org/protobuf/proto"

	groomingmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type OBSettingTestSuite struct {
	suite.Suite
	goCtx godomain.Context

	serviceID int64
}

func (s *OBSettingTestSuite) SetupSuite() {
	s.goCtx.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "OBSettingTestSuite",
	})

	service := s.goCtx.CreateService()
	s.serviceID = service.GetServiceId()
}

func (s *OBSettingTestSuite) TearDownSuite() {
	defer s.goCtx.Teardown()

	s.goCtx.DeleteService(s.serviceID)
}

func (s *OBSettingTestSuite) TestGetOBSetting() {
	result := &groomingmodel.ComMoegoServerGroomingWebDtoServiceServiceCategoryGroupedResultDto{}
	resp, err := s.goCtx.NewRequest().SetMethodPath(http.MethodGet, "api/grooming/serviceCategory/service?type=1").
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())
}

func (s *OBSettingTestSuite) TestOBSetting() {
	// empty setting
	resp, err := s.goCtx.NewRequest().
		SetMethodPath(http.MethodPut, "api/grooming/service/location/ob/setting").
		SetPayload(groomingmodel.ComMoegoServerGroomingServiceDtoCompanyServiceOBSettingUpdateDto{
			ServiceId:  int32(s.serviceID),
			LocationId: int32(s.goCtx.GetAuthInfo().BusinessID),
		}).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())

	// set show base price
	resp, err = s.goCtx.NewRequest().
		SetMethodPath(http.MethodPut, "api/grooming/service/location/ob/setting").
		SetPayload(groomingmodel.ComMoegoServerGroomingServiceDtoCompanyServiceOBSettingUpdateDto{
			ServiceId:     int32(s.serviceID),
			LocationId:    int32(s.goCtx.GetAuthInfo().BusinessID),
			ShowBasePrice: proto.Int32(1),
		}).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())

	// set book online available
	resp, err = s.goCtx.NewRequest().
		SetMethodPath(http.MethodPut, "api/grooming/service/location/ob/setting").
		SetPayload(groomingmodel.ComMoegoServerGroomingServiceDtoCompanyServiceOBSettingUpdateDto{
			ServiceId:           int32(s.serviceID),
			LocationId:          int32(s.goCtx.GetAuthInfo().BusinessID),
			BookOnlineAvailable: proto.Int32(1),
		}).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())

	// set is all staff
	resp, err = s.goCtx.NewRequest().
		SetMethodPath(http.MethodPut, "api/grooming/service/location/ob/setting").
		SetPayload(groomingmodel.ComMoegoServerGroomingServiceDtoCompanyServiceOBSettingUpdateDto{
			ServiceId:  int32(s.serviceID),
			LocationId: int32(s.goCtx.GetAuthInfo().BusinessID),
			IsAllStaff: proto.Int32(1),
		}).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())

	// set allow booking with other care type
	resp, err = s.goCtx.NewRequest().
		SetMethodPath(http.MethodPut, "api/grooming/service/location/ob/setting").
		SetPayload(groomingmodel.ComMoegoServerGroomingServiceDtoCompanyServiceOBSettingUpdateDto{
			ServiceId:                     int32(s.serviceID),
			LocationId:                    int32(s.goCtx.GetAuthInfo().BusinessID),
			AllowBookingWithOtherCareType: proto.Bool(true),
		}).
		Send()

	s.Require().Nil(err)
	s.Require().True(resp.IsSuccess())
}

func TestOBSettingTestSuite(t *testing.T) {
	suite.Run(t, new(OBSettingTestSuite))
}
