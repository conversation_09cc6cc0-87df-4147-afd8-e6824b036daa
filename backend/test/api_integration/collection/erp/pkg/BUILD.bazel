load("@io_bazel_rules_go//go:def.bzl", "go_test")

go_test(
    name = "ob_package_test",
    srcs = ["ob_package_test.go"],
    tags = ["package"],
    deps = [
        "//backend/test/api_integration/utils/suite/bookingdomain",
        "//backend/test/api_integration/utils/suite/clientdomain",
        "//backend/test/api_integration/utils/suite/godomain",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/client/package/v1:package",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/models/package/v1:package",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/utils/v2:utils",
        "@com_github_stretchr_testify//suite",
    ],
)
