package pkg

import (
	"net/http"
	"regexp"
	"strings"
	"testing"

	"github.com/stretchr/testify/suite"

	packageapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/package/v1"
	packagepb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/package/v1"
	utilsv2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/bookingdomain"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/clientdomain"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type OBPackageSuite struct {
	suite.Suite
	goCtx      godomain.Context
	bookingCtx bookingdomain.Context
	clientCtx  clientdomain.Context
}

const (
	phoneNumber = "**********"
	email       = "<EMAIL>"
	obName      = "AutoTest107866"
	password    = "Test123!"
)

func (s *OBPackageSuite) SetupSuite() {
	// firstly, borrow a B account
	s.goCtx.Setup(&s.Suite, nil)
	s.goCtx.Login(&godomain.TestAccount{
		Email:    email,
		Password: password,
	})

	// secondly, login to OB
	obName := s.goCtx.GetOBSettingInfo().GetBookOnlineInfo().GetBookOnlineName()
	s.bookingCtx.Setup(&s.Suite, *obName, phoneNumber)

	// set up client domain
	s.clientCtx.Setup(&s.Suite)
}

func (s *OBPackageSuite) listOBPackages() []*packagepb.PackageModel {
	pageSize := int32(10)
	page := int32(1)

	result := &packageapipb.ListPackagesResult{}
	response, err := s.bookingCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.client.package.v1.PackageService/ListPackages").
		SetPayload(&packageapipb.ListPackagesParams{
			Anonymous: &packageapipb.ListPackagesParams_Name{
				Name: obName,
			},
			Pagination: &utilsv2.PaginationRequest{
				PageSize: &pageSize,
				PageNum:  &page,
			},
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result.GetPackages()
}

func (s *OBPackageSuite) TestListOBPackages() {
	// arrange
	expectedPackageID := int64(8762)
	expectedPackageName := "ob package 01"
	expectedPackagePrice := float64(15)
	expectedExpirationDays := int32(30)
	expectedPackageItems := []*packagepb.Item{
		{
			ServiceIds: []int64{1180973},
			Quantity:   3,
		},
	}
	expectedTaxId := int64(9413)

	// act
	packages := s.listOBPackages()

	// assert
	s.Require().Equal(1, len(packages))
	s.Require().Equal(expectedPackageID, packages[0].GetId())
	s.Require().Equal(expectedPackageName, packages[0].GetName())
	s.Require().Equal(expectedPackagePrice, packages[0].GetPrice())
	s.Require().Equal(1, len(packages[0].GetItems()))
	s.Require().Equal(expectedPackageItems[0].GetServiceIds(), packages[0].GetItems()[0].GetServiceIds())
	s.Require().Equal(expectedPackageItems[0].GetQuantity(), packages[0].GetItems()[0].GetQuantity())
	s.Require().Equal(expectedExpirationDays, packages[0].GetExpirationDays())
	s.Require().Equal(expectedTaxId, packages[0].GetTaxId())
}

func (s *OBPackageSuite) createSellLink(items []*packageapipb.CreateSellLinkParams_Item) string {
	result := &packageapipb.CreateSellLinkResult{}
	response, err := s.bookingCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.client.package.v1.PackageService/CreateSellLink").
		SetPayload(&packageapipb.CreateSellLinkParams{
			Items: items,
			Anonymous: &packageapipb.CreateSellLinkParams_Name{
				Name: obName,
			},
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result.GetSellLink()
}

func (s *OBPackageSuite) TestCreateSellLink() {
	// arrange
	packages := s.listOBPackages()

	// act
	sellLink := s.createSellLink([]*packageapipb.CreateSellLinkParams_Item{
		{
			PackageId: packages[0].GetId(),
			Quantity:  2,
		},
	})

	// assert
	s.Require().NotNil(sellLink)
	s.Require().NotEmpty(sellLink)

	// Validate sell link format
	expectedPattern := regexp.MustCompile(`^https?://[^/]+/package/buy/[0-9a-f]{32}$`)
	s.Require().True(expectedPattern.MatchString(sellLink),
		"Sell link should match format {domain}/package/buy/{32-char-hex}, got: %s", sellLink)
}

func (s *OBPackageSuite) getSellLinkPublicInfo(sellLink string) *packageapipb.GetSellLinkPublicInfoResult {
	result := &packageapipb.GetSellLinkPublicInfoResult{}
	response, err := s.clientCtx.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.client.package.v1.PackageService/GetSellLinkPublicInfo").
		SetPayload(&packageapipb.GetSellLinkPublicInfoParams{
			PublicToken: sellLink,
		}).
		SetResult(result).
		Send()

	s.Require().Nil(err)
	s.Require().True(response.IsSuccess())

	return result
}

func (s *OBPackageSuite) TestGetSellLinkPublicInfo() {
	// arrange
	packages := s.listOBPackages()
	sellLink := s.createSellLink([]*packageapipb.CreateSellLinkParams_Item{
		{
			PackageId: packages[0].GetId(),
			Quantity:  2,
		},
	})
	// use the last / to get the public token
	publicToken := strings.Split(sellLink, "/")[len(strings.Split(sellLink, "/"))-1]

	// act
	publicInfo := s.getSellLinkPublicInfo(publicToken)

	// assert
	s.Require().NotNil(publicInfo)
	s.Require().NotEmpty(publicInfo)
}

func (s *OBPackageSuite) TearDownSuite() {
	s.goCtx.Teardown()
	s.bookingCtx.Teardown()
}

func TestOBPackageSuite(t *testing.T) {
	suite.Run(t, new(OBPackageSuite))
}
