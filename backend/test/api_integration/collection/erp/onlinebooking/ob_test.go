package onlinebooking

import (
	"strconv"
	"testing"

	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	testaccountpb "github.com/MoeGolibrary/moego/backend/proto/test_account/v1"
	customermodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/customer/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/bookingdomain"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/godomain"
)

type OBTestSuite struct {
	suite.Suite
	GoCtx      godomain.Context
	BookingCtx bookingdomain.Context
	CustomerID int32
	PetIDs     []int32
	setupDone  bool
}

func (s *OBTestSuite) SetupSuite() {
	// 为了避免后续的 setup 失败导致无法 teardown，这里 defer 一个必须执行的 teardown
	defer func() {
		if !s.setupDone {
			log.WarnContext(s.GoCtx.Ctx, "SetupSuite failed, teardown suite manually")
			s.mustTearDownSuite()
		}
	}()

	// 先借用一个 B 端测试账号
	s.GoCtx.Setup(&s.Suite, &godomain.BorrowAccountOptions{
		Borrower: "OBTestSuite",
		Attributes: &testaccountpb.Attributes{
			// 使用 AU 地区的测试账号，避免发送短信验证码
			RegionCode: pointer.Get("AU"),
			// 从测试账号池借用一个开启了 OB 的账号
			EnableOnlineBooking: pointer.Get(true),
		},
	})

	// B 端初始化测试数据
	phoneNumber := "**********"
	s.prepareClient(phoneNumber)
	// 如果需要准备其他数据，可以多加几个 prepare
	// s.prepareXXX()

	// 查询这个 B 端测试账号的 OB 域名
	obName := s.GoCtx.GetOBSettingInfo().GetBookOnlineInfo().GetBookOnlineName()

	// 用刚才创建的 client 登录 OB
	s.BookingCtx.Setup(&s.Suite, *obName, phoneNumber)

	s.setupDone = true
}

func (s *OBTestSuite) TearDownSuite() {
	// 每个 domain 的 context 都需要 teardown
	defer s.BookingCtx.Teardown()
	defer s.GoCtx.Teardown()

	s.mustTearDownSuite()
}

func (s *OBTestSuite) mustTearDownSuite() {
	// delete the customer created in SetupSuite
	if s.CustomerID > 0 {
		// 使用 go 域名的 context 执行删除 customer 逻辑
		s.GoCtx.DeleteCustomer(s.CustomerID)
	}
}

func (s *OBTestSuite) TestOB() {
	// 使用 booking 域名的 context 发起请求
	info := s.BookingCtx.GetOBClientInfo()
	s.Require().NotNil(info)
	s.Require().Equal(s.CustomerID, info.GetCustomer().GetId())
}

func (s *OBTestSuite) prepareClient(phoneNumber string) {
	// 使用 go 域名的 context 创建 client & pet
	s.CustomerID, s.PetIDs = s.GoCtx.CreateCustomerWithPets(&customermodel.ComMoegoServerCustomerParamsSaveWithPetCustomerVo{
		PreferredBusinessId: customermodel.PtrString(strconv.FormatInt(s.GoCtx.GetAuthInfo().BusinessID, 10)),
		PhoneNumber:         customermodel.PtrString(phoneNumber),
		FirstName:           customermodel.PtrString("OB"),
		LastName:            customermodel.PtrString("Test"),
		PetList: []customermodel.ComMoegoServerCustomerParamsSaveWithPetPetVo{
			{
				PetName:   customermodel.PtrString("Max"),
				PetTypeId: customermodel.PtrInt32(1),
				Breed:     customermodel.PtrString("Affenpinscher"),
			},
		},
	})
}

func (s *OBTestSuite) prepareXXX() {
	// TODO
}

func TestOBTestSuite(t *testing.T) {
	suite.Run(t, new(OBTestSuite))
}
