package types

import (
	"encoding/json"
	"strings"
	"time"
)

type DateTime struct {
	time.Time
}

// RFC3339WithoutTZ 兼容 Java 的 LocalDateTime 产生的数据
const RFC3339WithoutTZ = "2006-01-02T15:04:05"

var layouts = []string{
	time.RFC3339,
	time.RFC3339N<PERSON>,
	RFC3339WithoutTZ,
}

func (dt *DateTime) UnmarshalJSON(b []byte) error {
	var err error = nil
	s := strings.Trim(string(b), "\"")
	for _, layout := range layouts {
		t, e := time.Parse(layout, s)
		if e == nil {
			dt.Time = t
			return nil
		}
		//fmt.Println(e)
		err = e
	}
	return err
}

func (dt *DateTime) MarshalJSON() ([]byte, error) {
	return json.Marshal(dt)
}
