package types

import (
	"encoding/json"
	"testing"

	"github.com/bytedance/sonic"
	"github.com/stretchr/testify/assert"
)

type datetime struct {
	WithoutTZ  *DateTime `json:"withoutTZ"`
	WithZ      *DateTime `json:"withZ"`
	WithTZ     *DateTime `json:"withTZ"`
	NanoWithZ  *DateTime `json:"nanoWithZ"`
	NanoWithTZ *DateTime `json:"nanoWithTZ"`
}

func TestDateTime(t *testing.T) {
	j := `
		{
			"withoutTZ":  "2025-01-20T15:04:05",
			"withZ":      "2025-01-20T15:04:05Z",
			"withTZ":      "2025-01-20T15:04:05+08:00",
			"nanoWithZ":  "2025-01-20T15:04:05.999999999Z",
			"nanoWithTZ": "2025-01-20T15:04:05.999999999+08:00"
		}
		`

	dtSonic := &datetime{}
	dtJson := &datetime{}
	err := sonic.Unmarshal([]byte(j), dtSonic)
	assert.Nil(t, err)
	err = json.Unmarshal([]byte(j), dtJson)
	assert.<PERSON>l(t, err)

	for _, dt := range []*datetime{dtSonic, dtJson} {
		assert.Equal(t, "2025-01-20 15:04:05 +0000 UTC", dt.WithoutTZ.String())
		assert.Equal(t, "2025-01-20 15:04:05 +0000 UTC", dt.WithZ.String())
		assert.Equal(t, "2025-01-20 15:04:05 +0800 CST", dt.WithTZ.String())
		assert.Equal(t, "2025-01-20 15:04:05.999999999 +0000 UTC", dt.NanoWithZ.String())
		assert.Equal(t, "2025-01-20 15:04:05.999999999 +0800 CST", dt.NanoWithTZ.String())
	}
}
