load("@io_bazel_rules_go//go:def.bzl", "go_library", "go_test")

go_library(
    name = "types",
    srcs = ["datetime.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/test/api_integration/utils/types",
    visibility = ["//visibility:public"],
)

go_test(
    name = "types_test",
    srcs = ["datetime_test.go"],
    embed = [":types"],
    deps = [
        "@com_github_bytedance_sonic//:sonic",
        "@com_github_stretchr_testify//assert",
    ],
)
