package session

import (
	"net/http"

	"github.com/google/uuid"

	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/env"
)

type AuthInfo struct {
	CompanyID  int64
	BusinessID int64
	StaffID    int64
}

type Context struct {
	host              string
	sessionCookieName string
	deviceCookie      *http.Cookie
	sessionCookie     *http.Cookie
	authInfo          *AuthInfo
}

func NewContext(descriptor *env.DomainDescriptor) *Context {
	return &Context{
		host:              descriptor.GetHost(),
		sessionCookieName: descriptor.GetCookieName(),
		deviceCookie:      generateDeviceCookie(),
	}
}

func (ctx *Context) GetHost() string {
	return ctx.host
}

func (ctx *Context) GetSessionCookieName() string {
	return ctx.sessionCookieName
}

func (ctx *Context) GetCookies() []*http.Cookie {
	var cookies []*http.Cookie
	if ctx.deviceCookie != nil {
		cookies = append(cookies, ctx.deviceCookie)
	}
	if ctx.sessionCookie != nil {
		cookies = append(cookies, ctx.sessionCookie)
	}
	return cookies
}

func (ctx *Context) UpdateSessionCookie(cookie *http.Cookie) {
	ctx.sessionCookie = cookie
}

func (ctx *Context) GetAuthInfo() *AuthInfo {
	return ctx.authInfo
}

func (ctx *Context) UpdateAuthInfo(authInfo *AuthInfo) {
	ctx.authInfo = authInfo
}

func generateDeviceCookie() *http.Cookie {
	value := ""
	uid, err := uuid.NewUUID()
	if err == nil {
		value = uid.String()
	}

	return &http.Cookie{
		Name:  "MGDID",
		Value: value,
	}
}
