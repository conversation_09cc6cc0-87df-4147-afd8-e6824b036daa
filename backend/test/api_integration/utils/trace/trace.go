package trace

import (
	"context"

	"github.com/DataDog/dd-trace-go/v2/ddtrace/tracer"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

func NewContextWithTrace() context.Context {
	// Start a root span for tracing
	span := tracer.StartSpan("api-integration-testing")
	traceID := span.Context().TraceID()

	ctx := tracer.ContextWithSpan(context.Background(), span)
	return log.WithContextFields(ctx, log.Field{Key: "trace_id", Value: traceID})
}
