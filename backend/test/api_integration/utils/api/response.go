package api

import (
	"context"
	"io"
	"net/http"
	"os"
	"strconv"

	"github.com/bytedance/sonic"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
)

var (
	logRes           = os.Getenv("LOG_RESPONSE") == "true"
	protoUnmarshaler = protojson.UnmarshalOptions{DiscardUnknown: true}
)

type Response struct {
	statusCode    int
	moeStatusCode int
	bodyBytes     []byte
	header        http.Header
	cookies       []*http.Cookie
	requestID     string
}

func NewResponse(ctx context.Context, result any, resp *http.Response) (*Response, error) {
	requestID := resp.Header.Get("X-Request-Id")

	defer func(Body io.ReadCloser) {
		err := Body.Close()
		if err != nil {
			log.ErrorContextf(ctx, "failed to close response body: %v", err)
		}
	}(resp.Body)

	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	if logRes {
		log.InfoContextf(ctx, "reqeust id: %s, %s %s %s", requestID, resp.Request.Method, resp.Request.URL.String(), string(bodyBytes))
	} else {
		log.InfoContextf(ctx, "reqeust id: %s, %s %s", requestID, resp.Request.Method, resp.Request.URL.String())
	}

	if pb, ok := result.(proto.Message); ok {
		if err := protoUnmarshaler.Unmarshal(bodyBytes, pb); err != nil {
			log.ErrorContextf(ctx, "failed to unmarshal response body to proto message: %s", string(bodyBytes))
			return nil, err
		}
	} else if result != nil {
		if err := sonic.Unmarshal(bodyBytes, result); err != nil {
			log.ErrorContextf(ctx, "failed to unmarshal response body to struct: %s", string(bodyBytes))
			return nil, err
		}
	}

	moeStatusCode, _ := strconv.Atoi(resp.Header.Get("X-MOE-STATUS"))

	return &Response{
		statusCode:    resp.StatusCode,
		bodyBytes:     bodyBytes,
		header:        resp.Header,
		cookies:       resp.Cookies(),
		requestID:     requestID,
		moeStatusCode: moeStatusCode,
	}, nil
}

func (r *Response) IsSuccess() bool {
	return r.statusCode == http.StatusOK && r.moeStatusCode == 0
}

func (r *Response) GetStatusCode() int {
	return r.statusCode
}

func (r *Response) GetBodyBytes() []byte {
	return r.bodyBytes
}

func (r *Response) GetHeader() http.Header {
	return r.header
}

func (r *Response) GetCookie(name string) *http.Cookie {
	for _, cookie := range r.cookies {
		if cookie == nil {
			continue
		}
		if cookie.Name == name {
			return cookie
		}
	}
	return nil
}
