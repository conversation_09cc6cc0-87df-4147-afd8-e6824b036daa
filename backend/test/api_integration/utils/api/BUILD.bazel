load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "api",
    srcs = [
        "request.go",
        "response.go",
    ],
    importpath = "github.com/MoeGolibrary/moego/backend/test/api_integration/utils/api",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/http",
        "//backend/common/rpc/framework/log",
        "@com_github_bytedance_sonic//:sonic",
        "@org_golang_google_protobuf//encoding/protojson",
        "@org_golang_google_protobuf//proto",
    ],
)
