package env

import (
	"fmt"
	"os"
)

const (
	Testing    Environment = "testing"
	Staging    Environment = "staging"
	Production Environment = "production"
)

var (
	env = initEnv()
)

type Environment string

func initEnv() Environment {
	environment := Environment(os.Getenv("ENV"))
	if environment == "" {
		fmt.Println("ENV is not set, default use Testing")
		return Testing
	}

	if environment != Testing && environment != Staging && environment != Production {
		panic(fmt.Sprintf("Invalid ENV: %s", environment))
	}

	fmt.Printf("Use env: %s\n", environment)
	return environment
}
