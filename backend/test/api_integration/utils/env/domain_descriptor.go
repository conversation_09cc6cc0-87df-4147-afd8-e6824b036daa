package env

import (
	"fmt"
	"os"
)

var (
	Go            = &DomainDescriptor{subDomain: "go", cookiePrefix: "MGSID-B"}
	My            = &DomainDescriptor{subDomain: "my", cookiePrefix: "MGSID-C"}
	Booking       = &DomainDescriptor{subDomain: "booking", cookiePrefix: "MGSID-OB"}
	Enterprise    = &DomainDescriptor{subDomain: "enterprise", cookiePrefix: "MGSID-B"}
	PlatformTools = &DomainDescriptor{subDomain: "platform-tools", cookiePrefix: ""}
	Client        = &DomainDescriptor{subDomain: "client", cookiePrefix: ""}
)

type DomainDescriptor struct {
	subDomain    string
	cookiePrefix string
}

func (c *DomainDescriptor) GetCookieName() string {
	switch env {
	case Testing:
		return fmt.Sprintf("%s-T2", c.cookiePrefix)
	case Staging:
		return fmt.Sprintf("%s-S1", c.cookiePrefix)
	case Production:
		return c.cookiePrefix
	default:
		return fmt.Sprintf("%s-T2", c.cookiePrefix)
	}
}

func (c *DomainDescriptor) GetHost() string {
	switch env {
	case Testing:
		greyName := os.Getenv("GREY_NAME")
		if len(greyName) > 0 {
			return fmt.Sprintf("%s-grey-%s.t2.moego.dev", greyName, c.subDomain)
		}
		return fmt.Sprintf("%s.t2.moego.dev", c.subDomain)
	case Staging:
		return fmt.Sprintf("%s.s1.moego.dev", c.subDomain)
	case Production:
		return fmt.Sprintf("%s.moego.pet", c.subDomain)
	default:
		return fmt.Sprintf("%s.t2.moego.dev", c.subDomain)
	}
}
