package godomain

import (
	"net/http"
	"strconv"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/common/utils/pointer"
	paymentmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/payment/model"
)

func (c *Context) CashTakePayment(invoiceId int32, amount float32) *paymentmodel.ComMoegoCommonDtoPaymentSummaryPaymentDto {
	param := &paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams{
		InvoiceId:   invoiceId,
		Amount:      amount,
		Description: pointer.Get("paid in api-integration"),
		IsOnline:    pointer.Get(false),
		Method:      pointer.Get("Cash"),
		MethodId:    pointer.Get(int32(2)),
		Module:      pointer.Get("grooming"),
		PaidBy:      pointer.Get("api-integration-payment-in-godomian"),
	}
	result := paymentmodel.NewComMoegoCommonResponseResponseResultComMoegoCommonDtoPaymentSummaryPaymentDto()
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/api/payment/payment/create").
		SetPayload(param).
		SetResult(result).
		Send()
	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
	return result.GetData()
}

func (c *Context) BookkeepingTakePayment(params *paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams) *paymentmodel.ComMoegoCommonDtoPaymentSummaryPaymentDto {
	result := &paymentmodel.ComMoegoCommonResponseResponseResultComMoegoCommonDtoPaymentSummaryPaymentDto{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/api/payment/payment/create").
		AddQuery("tokenBusinessId", strconv.FormatInt(c.GetAuthInfo().BusinessID, 10)).
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
	return result.GetData()
}

func (c *Context) StripeTakePayment(params *paymentmodel.ComMoegoServerPaymentParamsCreatePaymentParams, allowFailure bool) *paymentmodel.ComMoegoCommonDtoPaymentSummaryPaymentDto {
	result := &paymentmodel.ComMoegoCommonResponseResponseResultComMoegoCommonDtoPaymentSummaryPaymentDto{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/api/payment/payment/createAndConfirm").
		AddQuery("tokenBusinessId", strconv.FormatInt(c.GetAuthInfo().BusinessID, 10)).
		SetPayload(params).
		SetResult(result).
		Send()

	if !allowFailure {
		c.suite.Require().Nil(err)
		c.suite.Require().True(response.IsSuccess())
	} else {
		if *result.GetCode() == 400 {
			log.InfoContextf(c.Ctx, "allowFailure=true: Payment rejection is expected behavior")
		} else {
			// Handle non-OK responses
			c.suite.Require().Nil(err) // Request itself should not fail
			c.suite.Require().True(response.IsSuccess())
		}
	}
	return result.GetData()
}

func (c *Context) SquareTakePayment(params *paymentmodel.ComMoegoServerPaymentDtoSquareSquarePaymentRequest, allowFailure bool) *paymentmodel.ComMoegoServerPaymentDtoSquareSquareTakePaymentResponse {
	result := &paymentmodel.ComMoegoCommonResponseResponseResultComMoegoServerPaymentDtoSquareSquareTakePaymentResponse{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/api/payment/square/payments").
		SetPayload(params).
		SetResult(result).
		Send()

	if !allowFailure {
		c.suite.Require().Nil(err)
		c.suite.Require().True(response.IsSuccess())
	} else {
		if *result.GetCode() == 400 {
			log.InfoContextf(c.Ctx, "allowFailure=true: Payment rejection is expected behavior")
		}
	}
	return result.GetData()
}

// 相当于调用 client and pets dashboard 里面的 payment list 接口
func (c *Context) GetPaymentListByCustomerId(customerId int32, pageNum int32, pageSize int32) *paymentmodel.ComMoegoServerPaymentDtoPaymentListDto {
	result := &paymentmodel.ComMoegoCommonResponseResponseResultComMoegoServerPaymentDtoPaymentListDto{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/payment/payment/list").
		AddQuery("customerId", strconv.FormatInt(int64(customerId), 10)).
		AddQuery("order", "desc").
		AddQuery("pageNum", strconv.FormatInt(int64(pageNum), 10)).
		AddQuery("pageSize", strconv.FormatInt(int64(pageSize), 10)).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetData()
}

// 修改 fee by client 设置
func (c *Context) SetPaymentSettingInfo(params *paymentmodel.ComMoegoServerPaymentDtoPaymentSettingDTO) *paymentmodel.ComMoegoServerPaymentDtoPaymentSettingDTO {
	result := &paymentmodel.ComMoegoServerPaymentDtoPaymentSettingDTO{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPut, "/api/payment/setting/info").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
	return result
}
