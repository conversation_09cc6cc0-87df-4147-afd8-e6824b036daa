package godomain

import (
	"net/http"

	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"

	offeringapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1"
	orderapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	groomingmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
)

func (c *Context) ListApplicableGroomingServices(petID int32) []*offeringpb.CustomizedServiceView {
	result := &offeringapipb.GetApplicableServiceListResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList").
		SetPayload(&offeringapipb.GetApplicableServiceListParams{
			BusinessId:      &c.GetAuthInfo().BusinessID,
			Inactive:        proto.Bool(false),
			OnlyAvailable:   true,
			PetId:           proto.Int64(int64(petID)),
			ServiceItemType: offeringpb.ServiceItemType_GROOMING.Enum(),
			ServiceType:     offeringpb.ServiceType_SERVICE,
		}).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	var services []*offeringpb.CustomizedServiceView
	for _, category := range result.GetCategoryList() {
		services = append(services, category.GetServices()...)
	}

	return services
}

func (c *Context) ListApplicableDaycareServices(petID int32) []*offeringpb.CustomizedServiceView {
	result := &offeringapipb.GetApplicableServiceListResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList").
		SetPayload(&offeringapipb.GetApplicableServiceListParams{
			BusinessId:      &c.GetAuthInfo().BusinessID,
			Inactive:        proto.Bool(false),
			OnlyAvailable:   true,
			PetId:           proto.Int64(int64(petID)),
			ServiceItemType: offeringpb.ServiceItemType_DAYCARE.Enum(),
			ServiceType:     offeringpb.ServiceType_SERVICE,
		}).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	var services []*offeringpb.CustomizedServiceView
	for _, category := range result.GetCategoryList() {
		services = append(services, category.GetServices()...)
	}

	return services
}

func (c *Context) ListApplicableBoardingServices(petID int32) []*offeringpb.CustomizedServiceView {
	result := &offeringapipb.GetApplicableServiceListResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/GetApplicableServiceList").
		SetPayload(&offeringapipb.GetApplicableServiceListParams{
			BusinessId:      &c.GetAuthInfo().BusinessID,
			Inactive:        proto.Bool(false),
			OnlyAvailable:   true,
			PetId:           proto.Int64(int64(petID)),
			ServiceItemType: offeringpb.ServiceItemType_BOARDING.Enum(),
			ServiceType:     offeringpb.ServiceType_SERVICE,
		}).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	var services []*offeringpb.CustomizedServiceView
	for _, category := range result.GetCategoryList() {
		services = append(services, category.GetServices()...)
	}

	return services
}

type serviceOption func(service *offeringpb.CreateServiceDef)

func (c *Context) CreateService(opts ...serviceOption) *offeringpb.ServiceModel {
	serviceName := "Test Service" + uuid.New().String()
	servicePrice := 15.0
	serviceDuration := 30

	taxes := c.ListTaxes()
	if len(taxes) == 0 {
		c.suite.T().Fatalf("before creating service, please create a tax")
	}

	request := &offeringpb.CreateServiceDef{
		Name:                    serviceName,
		Inactive:                false,
		ServiceItemType:         offeringpb.ServiceItemType_GROOMING.Enum(),
		Price:                   servicePrice,
		PriceUnit:               offeringpb.ServicePriceUnit_PER_SESSION,
		TaxId:                   taxes[0].GetId(),
		Duration:                int32(serviceDuration),
		AddToCommissionBase:     false,
		CanTip:                  false,
		IsAllLocation:           true,
		BreedFilter:             false,
		PetSizeFilter:           false,
		WeightFilter:            false,
		CoatFilter:              false,
		RequireDedicatedLodging: false,
		LodgingFilter:           false,
		Type:                    offeringpb.ServiceType_SERVICE.Enum(),
		RequireDedicatedStaff:   false,
		AvailableForAllStaff:    false,
		Source:                  offeringpb.ServiceModel_MOEGO_PLATFORM.Enum(),
	}

	for _, opt := range opts {
		opt(request)
	}

	result := &offeringapipb.CreateServiceResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.offering.v1.ServiceManagementService/CreateService").
		SetPayload(offeringapipb.CreateServiceParams{
			Service: request,
		}).SetResult(result).Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetService()
}

func (c *Context) DeleteService(serviceID int64) {
	response, err := c.NewRequest().
		SetMethodPath(http.MethodDelete, "api/grooming/service").
		SetPayload(&groomingmodel.ComMoegoServerGroomingWebVoDeleteIdVo{
			Id: int32(serviceID),
		}).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
}

/*
@desc：创建 service charges
*/
func (c *Context) AddCompanyServiceCharge(params *orderapipb.AddCompanyServiceChargeParams) *orderpb.ServiceCharge {
	result := &orderpb.ServiceCharge{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.order.v1.ServiceChargeCompanyService/AddCompanyServiceCharge").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}

func (c *Context) DeleteServiceCharge(params *orderapipb.DeleteServiceChargeRequest) *orderapipb.OperateServiceChargeResponse {
	result := &orderapipb.OperateServiceChargeResponse{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.order.v1.ServiceChargeService/DeleteServiceCharge").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}

/*
@desc：添加 service charges 到 order 里面去
*/
func (c *Context) AddServiceChargeToOrder(params *orderapipb.AddOrRemoveServiceChargeRequest) {
	result := &orderapipb.OperateServiceChargeToOrderResponse{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.order.v1.OrderService/AddServiceChargeToOrder").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
}

/*
@desc：删除 service charges
*/
func (c *Context) RemoveServiceChargeFromOrder(params *orderapipb.AddOrRemoveServiceChargeRequest) {
	result := &orderapipb.OperateServiceChargeToOrderResponse{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.order.v1.OrderService/RemoveServiceChargeFromOrder").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
}
