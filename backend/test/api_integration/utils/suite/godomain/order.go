package godomain

import (
	"net/http"
	"strconv"

	orderapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v1"
	groomingmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
	retailmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/retail/model"
)

func (c *Context) ListOrders(businessID, orderID int64) *orderapipb.ListOrdersResult {
	result := orderapipb.ListOrdersResult{}
	listOrdersParams := orderapipb.ListOrdersParams{
		BusinessId:    businessID,
		OriginOrderId: orderID,
	}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.order.v1.OrderService/ListOrders").
		SetPayload(&listOrdersParams).
		SetResult(&result).
		Send()
	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
	return &result
}

func (c *Context) ListOrderDetailsV2(orderID int32) *groomingmodel.ComMoegoServerGroomingDtoOrderInvoiceSummaryDTO {
	result := &groomingmodel.ComMoegoServerGroomingDtoOrderInvoiceSummaryDTO{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/grooming/invoice/v2/order/detail").
		AddQuery("orderId", strconv.Itoa(int(orderID))).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}

func (c *Context) SetTips(params *orderapipb.SetTipsRequest) *orderapipb.SetTipsResponse {
	result := &orderapipb.SetTipsResponse{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.order.v1.OrderService/SetTips").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}

func (c *Context) Revert(params *groomingmodel.ComMoegoServerGroomingParamsApptReopenParams) {
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPut, "/api/grooming/invoice/revert").
		SetPayload(params).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
}

func (c *Context) SetDiscount(params *retailmodel.ComMoegoServerRetailServiceParamsSetDiscountParams) *retailmodel.ComMoegoServerRetailServiceDtoUpdateOrderDTO {
	result := &retailmodel.ComMoegoServerRetailServiceDtoUpdateOrderDTO{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPut, "/api/retail/order/set-discount/v2").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}

func (c *Context) PutRetailsItemsOnOrder(params *retailmodel.ComMoegoServerRetailServiceParamsSaveOrderRetailItemsParams) *retailmodel.ComMoegoServerPaymentDtoRefundChannelDTO {
	result := &retailmodel.ComMoegoServerRetailServiceDtoUpdateOrderDTO{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPut, "/api/retail/order/items/v2").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetRefundChannel()
}

func (c *Context) PreviewRefundOrder(params *orderapipb.PreviewRefundOrderParams) *orderapipb.PreviewRefundOrderResult {
	result := &orderapipb.PreviewRefundOrderResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.order.v1.OrderService/PreviewRefundOrder").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}

func (c *Context) RefundOrder(params *orderapipb.RefundOrderParams) *orderapipb.RefundOrderResult {
	result := &orderapipb.RefundOrderResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.order.v1.OrderService/RefundOrder").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}

func (c *Context) PreviewRefundOrderPayments(params *orderapipb.PreviewRefundOrderPaymentsParams) *orderapipb.PreviewRefundOrderPaymentsResult {
	result := &orderapipb.PreviewRefundOrderPaymentsResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.order.v1.OrderService/PreviewRefundOrderPayments").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}
