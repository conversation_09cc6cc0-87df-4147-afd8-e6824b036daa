package godomain

import (
	"net/http"
	"strconv"

	retailjavapb "github.com/MoeGolibrary/moego/backend/test/api_integration/def/retail/model"
)

func (c *Context) ListRetailSuppliers() []retailjavapb.ComMoegoServerRetailMapperbeanSupplier {
	result := &retailjavapb.ComMoegoCommonResponseResponseResultJavaUtilListComMoegoServerRetailMapperbeanSupplier{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/retail/supplier/list").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetData()
}

func (c *Context) ListRetailCategories() []retailjavapb.ComMoegoServerRetailMapperbeanCategory {
	result := &retailjavapb.ComMoegoCommonResponseResponseResultJavaUtilListComMoegoServerRetailMapperbeanCategory{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/retail/category/list").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetData()
}

func (c *Context) UpdateRetailProduct(params *retailjavapb.ComMoegoServerRetailServiceParamsUpdateProductParams) *retailjavapb.ComMoegoServerRetailServiceDtoProductInfoDto {
	result := &retailjavapb.ComMoegoCommonResponseResponseResultComMoegoServerRetailServiceDtoProductInfoDto{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPut, "/api/retail/product").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetData()
}

func (c *Context) DeleteRetailProduct(params *retailjavapb.ComMoegoServerRetailServiceParamsProductIdParams) {
	response, err := c.NewRequest().
		SetMethodPath(http.MethodDelete, "/api/retail/product").
		AddQueries(map[string]string{
			"productId": strconv.Itoa(int(params.ProductId)),
		}).
		SetPayload(params).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
}

func (c *Context) AddRetailProduct(params *retailjavapb.ComMoegoServerRetailServiceParamsProductParams) *retailjavapb.ComMoegoServerRetailServiceDtoProductInfoDto {
	result := &retailjavapb.ComMoegoCommonResponseResponseResultComMoegoServerRetailServiceDtoProductInfoDto{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/api/retail/product").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetData()
}

func (c *Context) SearchRetailProducts() []retailjavapb.ComMoegoServerRetailServiceDtoProductInfoDto {
	result := &retailjavapb.ComMoegoServerRetailServiceDtoProductQueryDTO{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/retail/product/query").
		AddQuery("keyword", "").
		AddQuery("order", "desc").
		AddQuery("sort", "").
		AddQuery("pageNum", "1").
		AddQuery("pageSize", "100").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetResultList()
}
