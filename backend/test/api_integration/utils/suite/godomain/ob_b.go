package godomain

import (
	"net/http"

	groomingmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
)

func (c *Context) GetOBSettingInfo() *groomingmodel.ComMoegoServerGroomingWebDtoObSettingInfoDto {
	result := groomingmodel.NewComMoegoCommonResponseResponseResultComMoegoServerGroomingWebDtoObSettingInfoDto()
	response, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/grooming/bookOnline/setting/info").
		AddQuery("withoutClientNotification", "true").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetData()
}
