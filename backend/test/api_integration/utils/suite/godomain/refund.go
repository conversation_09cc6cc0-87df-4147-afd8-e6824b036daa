package godomain

import (
	"net/http"

	refundmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/payment/model"
)

func (c *Context) CreateRefund(params *refundmodel.ComMoegoServerPaymentParamsCreateRefundParams) *refundmodel.ComMoegoServerPaymentDtoPaymentDTO {
	result := &refundmodel.ComMoegoCommonResponseResponseResultComMoegoServerPaymentDtoPaymentDTO{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/api/payment/refund/create").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetData()
}

func (c *Context) SubmitRefund(params *refundmodel.ComMoegoServerPaymentWebVoSubmitInvoiceRefundVo) {
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/api/payment/invoice/refund/submit").
		SetPayload(params).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
}
