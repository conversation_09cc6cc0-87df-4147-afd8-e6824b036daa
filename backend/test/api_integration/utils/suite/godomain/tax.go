package godomain

import (
	"net/http"

	organizationapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/organization/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
)

func (c *Context) ListTaxes() []*organizationpb.TaxRuleModel {
	result := organizationapipb.GetTaxRuleListResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.organization.v1.CompanyService/GetTaxRuleList").
		SetResult(&result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.GetTaxRule()
}

func (c *Context) AddTaxRule(params *organizationapipb.AddTaxRuleParams) *organizationapipb.AddTaxRuleResult {
	result := organizationapipb.AddTaxRuleResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.organization.v1.CompanyService/AddTaxRule").
		SetPayload(params).
		SetResult(&result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return &result
}

func (c *Context) DeleteTaxRule(params *organizationapipb.DeleteTaxRuleParams) *organizationapipb.DeleteTaxRuleResult {
	result := organizationapipb.DeleteTaxRuleResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.organization.v1.CompanyService/DeleteTaxRule").
		SetPayload(params).
		SetResult(&result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return &result
}
