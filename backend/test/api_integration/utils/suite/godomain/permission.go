package godomain

import (
	"net/http"

	permissionapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/permission/v1"
)

func (c *Context) GetRoleList(params *permissionapipb.GetRoleListParams) *permissionapipb.GetRoleListResult {
	result := &permissionapipb.GetRoleListResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.permission.v1.PermissionService/GetRoleList").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}

func (c *Context) GetRoleDetail(params *permissionapipb.GetRoleDetailParams) *permissionapipb.GetRoleDetailResult {
	result := &permissionapipb.GetRoleDetailResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.permission.v1.PermissionService/GetRoleDetail").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}

func (c *Context) EditPermissions(params *permissionapipb.EditPermissionsParams) *permissionapipb.EditPermissionsResult {
	result := &permissionapipb.EditPermissionsResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.permission.v1.PermissionService/EditPermissions").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}
