package godomain

import (
	"net/http"
	"strconv"

	appointmentapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/appointment/v1"
	appointmentmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
)

func (c *Context) CreateAppointment(params *appointmentapipb.CreateAppointmentParams) int64 {
	result := &appointmentapipb.CreateAppointmentResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.appointment.v1.AppointmentService/CreateAppointment").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	appointmentID := result.GetAppointmentId()
	c.suite.Require().Greater(appointmentID, int64(0))

	return appointmentID
}

func (c *Context) CancelAppointment(params *appointmentmodel.ComMoegoServerGroomingParamsCancelParams) {
	result := appointmentmodel.NewComMoegoCommonResponseResponseResultJavaLangInteger()
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPut, "/api/grooming/appointment/cancel").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	isSuccess := result.GetSuccess()
	c.suite.Require().True(*isSuccess)
}

func (c *Context) GetAppointmentDetail(appointmentID int64) *appointmentmodel.ComMoegoServerGroomingDtoGroomingTicketDetailDTO {
	result := appointmentmodel.NewComMoegoCommonResponseResponseResultComMoegoServerGroomingDtoGroomingTicketDetailDTO()
	_, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/grooming/appintment/detail/pup").
		AddQuery("id", strconv.FormatInt(appointmentID, 10)).
		SetResult(result).
		Send()
	c.suite.Require().Nil(err)
	//TODO https://moego.atlassian.net/browse/IFRBE-1580 failed for Java enum transfered as string rather than int
	//c.suite.Require().True(response.IsSuccess())
	return result.Data
}

func (c *Context) GetAppointment(params *appointmentapipb.GetAppointmentParams) *appointmentapipb.GetAppointmentResult {
	result := &appointmentapipb.GetAppointmentResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.appointment.v1.AppointmentService/GetAppointment").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
	return result
}

/*
desc: 保存或者更新 pet details 高频使用的通用接口
*/
func (c *Context) SaveOrUpdatePetDetails(params *appointmentapipb.SaveOrUpdatePetDetailsParams) *appointmentapipb.SaveOrUpdatePetDetailsResult {
	result := &appointmentapipb.SaveOrUpdatePetDetailsResult{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.appointment.v1.PetDetailService/SaveOrUpdatePetDetails").
		SetPayload(params).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
	return result
}
