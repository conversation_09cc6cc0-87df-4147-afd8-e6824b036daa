package godomain

import (
	"context"
	"net/http"
	"time"

	"github.com/DataDog/dd-trace-go/v2/ddtrace/tracer"
	"github.com/stretchr/testify/suite"

	accountapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/account/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	testaccountpb "github.com/MoeGolibrary/moego/backend/proto/test_account/v1"
	businessmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/business/model"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/api"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/env"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/session"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/trace"
)

type Context struct {
	suite      *suite.Suite
	account    *TestAccount
	sessionCtx *session.Context
	Ctx        context.Context
}

type TestAccount struct {
	ID       int64
	Email    string
	Password string

	ContractID int64
}

type BorrowAccountOptions struct {
	ID         int64
	Email      string
	Attributes *testaccountpb.Attributes
	Borrower   string
	Shared     bool
}

func (c *Context) borrowTestAccount(options *BorrowAccountOptions) *TestAccount {
	c.suite.Require().NotNil(options)

	req := &testaccountpb.BorrowTestAccountRequest{
		Borrower:   options.Borrower,
		Shared:     &options.Shared,
		Attributes: options.Attributes,
	}
	if options.ID != 0 {
		req.Identifier = &testaccountpb.BorrowTestAccountRequest_Id{
			Id: options.ID,
		}
	} else if len(options.Email) > 0 {
		req.Identifier = &testaccountpb.BorrowTestAccountRequest_Email{
			Email: options.Email,
		}
	}

	result := &testaccountpb.BorrowTestAccountResponse{}
	response, err := c.NewRequest().
		SetHost(env.PlatformTools.GetHost()).
		SetMethodPath(http.MethodPost, "/moego.bff/devops/test-account/borrowTestAccount").
		SetPayload(req).
		SetResult(result).
		SetTimeout(time.Second * 10).
		SetRetry(5).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return &TestAccount{
		ID:         result.GetId(),
		Email:      result.GetEmail(),
		Password:   result.GetPassword(),
		ContractID: result.GetContractId(),
	}
}

func (c *Context) returnTestAccount(account *TestAccount) {
	c.suite.Require().NotNil(account)

	req := &testaccountpb.ReturnTestAccountRequest{
		ContractId: account.ContractID,
	}

	response, err := c.NewRequest().
		SetHost(env.PlatformTools.GetHost()).
		SetMethodPath(http.MethodPost, "/moego.bff/devops/test-account/returnTestAccount").
		SetPayload(req).
		SetTimeout(time.Second * 10).
		SetRetry(3).
		Send()

	// Log the error but do not fail the test
	if err != nil {
		c.suite.T().Logf("failed to return test account. contract id: %d, error: %v", account.ContractID, err)
	} else if !response.IsSuccess() {
		c.suite.T().Logf("failed to return test account. contract id: %d, status code: %d",
			account.ContractID, response.GetStatusCode())
	}
}

func (c *Context) Login(account *TestAccount) {
	c.suite.Require().NotNil(account)

	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.account.v1.AccountAccessService/Login").
		SetPayload(&accountapipb.AccountLoginRequest{
			LoginMethod: &accountapipb.AccountLoginRequest_ByEmailPassword{
				ByEmailPassword: &accountapipb.EmailPasswordDef{
					Email:    account.Email,
					Password: account.Password,
				},
			},
		}).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	cookieName := c.sessionCtx.GetSessionCookieName()
	cookie := response.GetCookie(cookieName)
	c.suite.Require().NotNil(cookie, "session cookie %s not found", cookieName)

	c.sessionCtx.UpdateSessionCookie(cookie)

	// 会话的 session context 初始化是异步的，加上 authz 连读库，
	// 太快获取上下文可能导致读不到，因此 sleep 一会等待一下
	time.Sleep(500 * time.Millisecond)

	c.initAuthInfo()
}

func (c *Context) initAuthInfo() {
	result := businessmodel.NewComMoegoServerBusinessServiceDtoAccountRelatedInformationDto()
	response, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/business/account/v2/info").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	var companyID, businessID, staffID int64
	if result.GetCompany().GetId() != nil {
		companyID = int64(*result.GetCompany().GetId())
	}
	if result.GetBusiness().GetId() != nil {
		businessID = int64(*result.GetBusiness().GetId())
	}
	if result.GetStaff().GetStaffId() != nil {
		staffID = int64(*result.GetStaff().GetStaffId())
	}

	c.sessionCtx.UpdateAuthInfo(&session.AuthInfo{
		CompanyID:  companyID,
		BusinessID: businessID,
		StaffID:    staffID,
	})
}

func (c *Context) Setup(suite *suite.Suite, options *BorrowAccountOptions) {
	c.Ctx = trace.NewContextWithTrace()
	log.InfoContext(c.Ctx, "Setup suite")

	c.suite = suite
	c.sessionCtx = session.NewContext(env.Go)

	// Borrow a test account and login
	if options != nil {
		c.account = c.borrowTestAccount(options)
		c.Login(c.account)
	}
}

func (c *Context) Teardown() {
	// Finish the root span
	span, ok := tracer.SpanFromContext(c.Ctx)
	if ok {
		defer span.Finish()
	}

	// Return the borrowed test account
	if c.account != nil {
		c.returnTestAccount(c.account)
	}

	log.InfoContext(c.Ctx, "Finish teardown suite")
}

func (c *Context) GetAuthInfo() *session.AuthInfo {
	return c.sessionCtx.GetAuthInfo()
}

func (c *Context) NewRequest() *api.Request {
	request := api.NewRequest(c.Ctx, c.sessionCtx.GetHost(), c.sessionCtx.GetCookies()...)
	// TODO: init query of ~c ~b
	return request
}
