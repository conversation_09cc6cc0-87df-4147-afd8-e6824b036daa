package godomain

import (
	"net/http"

	reportingapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/reporting/v2"
	reportingpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/reporting/v2"
)

func (c *Context) GetTableData(payload *reportingapipb.FetchReportDataRequest) *reportingpb.TableData {
	result := &reportingapipb.FetchReportDataResponse{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.reporting.v2.ReportService/FetchReportData").
		SetPayload(payload).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	tableData := result.GetTableData()
	return tableData
}

func (c *Context) GetDiagramData(payload *reportingapipb.FetchDashboardDataRequest) []*reportingpb.DiagramData {
	result := &reportingapipb.FetchDashboardDataResponse{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.reporting.v2.ReportService/FetchReportData").
		SetPayload(payload).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	diagramData := result.GetDiagramData()
	return diagramData
}
