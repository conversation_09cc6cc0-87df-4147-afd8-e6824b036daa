load("@io_bazel_rules_go//go:def.bzl", "go_library")

go_library(
    name = "mydomain",
    srcs = ["context.go"],
    importpath = "github.com/MoeGolibrary/moego/backend/test/api_integration/utils/suite/mydomain",
    visibility = ["//visibility:public"],
    deps = [
        "//backend/common/rpc/framework/log",
        "//backend/test/api_integration/utils/api",
        "//backend/test/api_integration/utils/env",
        "//backend/test/api_integration/utils/session",
        "//backend/test/api_integration/utils/trace",
        "@com_github_datadog_dd_trace_go_v2//ddtrace/tracer",
        "@com_github_moegolibrary_moego_api_definitions//out/go/moego/api/account/v1:account",
        "@com_github_stretchr_testify//suite",
    ],
)
