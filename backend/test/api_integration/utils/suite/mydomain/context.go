package mydomain

import (
	"context"
	"net/http"
	"time"

	"github.com/DataDog/dd-trace-go/v2/ddtrace/tracer"
	"github.com/stretchr/testify/suite"

	accountapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/account/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/api"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/env"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/session"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/trace"
)

type Context struct {
	suite      *suite.Suite
	sessionCtx *session.Context
	Ctx        context.Context
}

type TestAccount struct {
	ID       int64
	Email    string
	Password string

	ContractID int64
}

func (c *Context) Setup(suite *suite.Suite, account *TestAccount) {
	c.Ctx = trace.NewContextWithTrace()
	log.InfoContext(c.Ctx, "Setup suite")

	c.suite = suite
	c.sessionCtx = session.NewContext(env.My)
	c.Login(account)
}

func (c *Context) Teardown() {
	// Finish the root span
	span, ok := tracer.SpanFromContext(c.Ctx)
	if ok {
		defer span.Finish()
	}

	log.InfoContext(c.Ctx, "Finish teardown suite")
}

func (c *Context) NewRequest() *api.Request {
	request := api.NewRequest(c.Ctx, c.sessionCtx.GetHost(), c.sessionCtx.GetCookies()...)
	return request
}

func (c *Context) Login(account *TestAccount) {
	c.suite.Require().NotNil(account)

	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.account.v1.AccountAccessService/Login").
		SetPayload(&accountapipb.AccountLoginRequest{
			LoginMethod: &accountapipb.AccountLoginRequest_ByEmailPassword{
				ByEmailPassword: &accountapipb.EmailPasswordDef{
					Email:    account.Email,
					Password: account.Password,
				},
			},
		}).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	cookieName := c.sessionCtx.GetSessionCookieName()
	cookie := response.GetCookie(cookieName)
	c.suite.Require().NotNil(cookie, "session cookie %s not found", cookieName)

	c.sessionCtx.UpdateSessionCookie(cookie)

	// 会话的 session context 初始化是异步的，加上 authz 连读库，
	// 太快获取上下文可能导致读不到，因此 sleep 一会等待一下
	time.Sleep(500 * time.Millisecond)
}
