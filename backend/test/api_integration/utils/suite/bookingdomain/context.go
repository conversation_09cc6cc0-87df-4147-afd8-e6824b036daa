package bookingdomain

import (
	"context"
	"net/http"
	"time"

	"github.com/DataDog/dd-trace-go/v2/ddtrace/tracer"
	"github.com/stretchr/testify/suite"

	onlinebookingapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1"
	onlinebookingpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/online_booking/v1"
	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/api"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/env"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/session"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/trace"
)

type Context struct {
	suite      *suite.Suite
	obName     string
	sessionCtx *session.Context
	Ctx        context.Context
}

func (c *Context) Setup(suite *suite.Suite, obName, phoneNumber string) {
	c.Ctx = trace.NewContextWithTrace()
	log.InfoContext(c.Ctx, "Setup suite")

	c.suite = suite
	c.obName = obName
	c.sessionCtx = session.NewContext(env.Booking)

	c.initOnlineBookingSessionCookie()
	c.Login(phoneNumber)

	// 会话的 session context 初始化是异步的，加上 authz 连读库，
	// 太快获取上下文可能导致读不到，因此 sleep 一会等待一下
	time.Sleep(500 * time.Millisecond)
}

func (c *Context) Teardown() {
	// Finish the root span
	span, ok := tracer.SpanFromContext(c.Ctx)
	if ok {
		defer span.Finish()
	}

	log.InfoContext(c.Ctx, "Finish teardown suite")
}

func (c *Context) NewRequest() *api.Request {
	request := api.NewRequest(c.Ctx, c.sessionCtx.GetHost(), c.sessionCtx.GetCookies()...)
	if len(c.obName) > 0 {
		request.AddQuery("name", c.obName)
	}
	return request
}

func (c *Context) Login(phoneNumber string) {

	token, success := c.sendVerificationCodeForOBLogin(phoneNumber)
	// 非美区用户，用 6 个 0 代替验证码
	code := "000000"
	if success {
		// TODO: 美区用户暂时没法拿到验证码
		// code = "123456"
	}

	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.online_booking.v1.OBAccessService/Login").
		SetPayload(&onlinebookingapipb.OBLoginRequest{
			LoginMethod: &onlinebookingapipb.OBLoginRequest_ByVerificationCode{
				ByVerificationCode: &onlinebookingapipb.OBLoginByVerificationCodeDef{
					Identifier: &onlinebookingapipb.OBLoginByVerificationCodeDef_PhoneNumber{
						PhoneNumber: phoneNumber,
					},
					AccessType: onlinebookingpb.AccessType_ACCESS_TYPE_BY_PHONE,
					Code:       code,
					Token:      token,
				},
			},
		}).Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())
}

func (c *Context) initOnlineBookingSessionCookie() {
	// 请求前端主页面，拿到 cookie
	resp, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/ol/book").
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(resp.IsSuccess())

	cookieName := c.sessionCtx.GetSessionCookieName()
	cookie := resp.GetCookie(cookieName)
	c.suite.Require().NotNil(cookie, "session cookie %s not found", cookieName)

	c.sessionCtx.UpdateSessionCookie(cookie)
}

func (c *Context) sendVerificationCodeForOBLogin(phoneNumber string) (string, bool) {

	result := &onlinebookingapipb.OBSendVerificationCodeResponse{}

	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.online_booking.v1.OBAccessService/SendVerificationCode").
		SetPayload(&onlinebookingapipb.OBSendVerificationCodeRequest{
			Identifier: &onlinebookingapipb.OBSendVerificationCodeRequest_PhoneNumber{
				PhoneNumber: phoneNumber,
			},
			AccessType: onlinebookingpb.AccessType_ACCESS_TYPE_BY_PHONE,
		}).
		SetResult(result).
		Send()

	log.Infof("err: %v", err)
	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result.Token, result.Success
}
