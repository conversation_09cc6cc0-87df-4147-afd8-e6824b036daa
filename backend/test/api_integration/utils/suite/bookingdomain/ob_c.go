package bookingdomain

import (
	"net/http"

	onlinebookingapipb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/online_booking/v1"
	groomingmodel "github.com/MoeGolibrary/moego/backend/test/api_integration/def/grooming/model"
)

func (c *Context) GetOBClientInfo() *onlinebookingapipb.OBClientInfoResponse {
	result := &onlinebookingapipb.OBClientInfoResponse{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodPost, "/moego.api.online_booking.v1.OBClientService/GetOBClientInfo").
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}

func (c *Context) GetOBSettingInfo() *groomingmodel.ComMoegoServerGroomingWebDtoObInfoDto {
	result := &groomingmodel.ComMoegoServerGroomingWebDtoObInfoDto{}
	response, err := c.NewRequest().
		SetMethodPath(http.MethodGet, "/api/grooming/ob/v2/client/business/info").
		AddQuery("name", c.obName).
		SetResult(result).
		Send()

	c.suite.Require().Nil(err)
	c.suite.Require().True(response.IsSuccess())

	return result
}
