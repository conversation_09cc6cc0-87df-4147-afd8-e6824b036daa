package clientdomain

import (
	"context"

	"github.com/DataDog/dd-trace-go/v2/ddtrace/tracer"
	"github.com/stretchr/testify/suite"

	"github.com/MoeGolibrary/moego/backend/common/rpc/framework/log"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/api"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/env"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/session"
	"github.com/MoeGolibrary/moego/backend/test/api_integration/utils/trace"
)

type Context struct {
	suite      *suite.Suite
	sessionCtx *session.Context
	Ctx        context.Context
}

func (c *Context) Setup(suite *suite.Suite) {
	c.Ctx = trace.NewContextWithTrace()
	log.InfoContext(c.Ctx, "Setup suite")

	c.suite = suite
	c.sessionCtx = session.NewContext(env.Client)
}

func (c *Context) Teardown() {
	// Finish the root span
	span, ok := tracer.SpanFromContext(c.Ctx)
	if ok {
		defer span.Finish()
	}

	log.InfoContext(c.Ctx, "Finish teardown suite")
}

func (c *Context) NewRequest() *api.Request {
	request := api.NewRequest(c.Ctx, c.sessionCtx.GetHost(), c.sessionCtx.GetCookies()...)
	return request
}
