package com.moego.server.business.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Data
public class BusinessPayrollSettingDTO {

    private Integer id;
    private Integer businessId;
    private Long companyId;

    @Schema(description = "business设置的split tips method: 1-by service, 2-by equally")
    private Byte splitTipsMethod;

    private TipAllocationType tipAllocationType;

    @Schema(description = "新 payroll setting 白名单开关")
    private Boolean newPayrollEnable;

    @Schema(description = "service commission 计算基数: 1-actual payment, 2-finish appointment")
    private Byte serviceCommissionBased;

    private Integer createTime;
    private Integer updateTime;

    @RequiredArgsConstructor
    @Getter
    public enum TipAllocationType {
        TIP_ALLOCATION_TYPE_UNSPECIFIED(0),
        /**
         * 所有 tips 都给 staff
         */
        ALL_TO_STAFF(1),
        /**
         * 按照 service 分配：
         * - 如果 service 没有 staff，则 tips 分配给 business
         * - 如果 service 有 staff，则 tips 分配给 staff
         */
        BY_SERVICE(2);

        private final int value;

        /**
         * Get TipAllocationMethod by value.
         *
         * @param value value
         * @return TipAllocationMethod
         * @throws IllegalArgumentException if no matching TipAllocationMethod is found
         */
        public static TipAllocationType fromValue(int value) {
            for (TipAllocationType method : TipAllocationType.values()) {
                if (method.getValue() == value) {
                    return method;
                }
            }
            throw new IllegalArgumentException("No matching TipAllocationMethod for value: " + value);
        }
    }
}
